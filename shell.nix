{ pkgs ? import <nixpkgs> {} }:

let
  # Pin nixpkgs to ensure consistent versions
  pinnedPkgs = import (pkgs.fetchFromGitHub {
    owner = "NixOS";
    repo = "nixpkgs";
    # This revision should provide packages close to Ubuntu 20.04 versions
    rev = "e8039594435c68eb4f780f3e9bf33785922bd2d9";
    sha256 = "0c7nf3923irixz5lkpwmh9flbxfz7x3r3vak3zr1x1dfdnc2qg3k";
  }) {};

  # Python with specific version
  python38 = pkgs.python38.withPackages (ps: with ps; [
    pip setuptools wheel numpy pyyaml
    cryptography dbus distro
    # Additional Python packages from requirements_lock.in
    pydantic cattrs click ipython loguru matplotlib
    pillow scipy tqdm pytest networkx pyopenssl tabulate
    # Add more Python packages as needed
  ]);
  
  # Create a custom clang-16 package if needed to match exact version
  clang16 = pkgs.clang_16;
  
  # Create a custom gcc-9 package to match the version in container
  gcc9 = pkgs.gcc9;
  
  # Create a custom cmake package to match version 3.23.5
  cmake_custom = pkgs.cmake.overrideAttrs (oldAttrs: {
    version = "3.23.5";
    src = pkgs.fetchurl {
      url = "https://cmake.org/files/v3.23/cmake-3.23.5.tar.gz";
      sha256 = "0vqm6mvhwpfk97hcmcm8yv45lc1x5m9qz1yfn9c8w7d5mmn35skk";
    };
  });
in
pkgs.mkShell {
  buildInputs = with pkgs; [
    # Core build tools with specific versions
    cmake_custom
    gcc9
    clang16
    ninja-build
    pkg-config
    gnumake
    autoconf
    automake
    libtool
    
    # Python with specific version
    python38
    
    # Libraries from container-packages.txt
    openssl
    zlib
    boost
    eigen
    nlohmann_json
    libwebsocketpp
    asio
    
    # Development tools
    git
    git-lfs
    gdb
    doxygen
    graphviz
    
    # Debugging and analysis tools
    valgrind
    strace
    tcpdump
    
    # Terminal utilities
    tmux
    vim
    emacs
    htop
    less
    
    # Libraries from container-packages.txt
    babeltrace
    binutils
    binutils-dev
    bzip2
    curl
    libglew2
    libglfw3
    libglfw3-dev
    libbfd
    libcurl4-gnutls-dev
    libevent-dev
    libncurses6
    libpcre3-dev
    libtbb2
    libtbb-dev
    
    # Additional tools
    cppcheck
    pre-commit
    
    # ROS dependencies (these might need to be installed separately)
    # ros-humble-ros-base
    
    # Qt dependencies
    qt5.qtbase
    qt5.qttools
    
    # OpenCV
    opencv4
    
    # Java runtime
    jdk11
    
    # Additional libraries
    lttng-tools
    lttng-ust
  ];

  # Environment variables
  shellHook = ''
    # Set compiler preferences (similar to Dockerfile)
    export CC=clang
    export CXX=clang++
    
    # Set up Bazel cache directory
    export BAZEL_CACHE_DIR="$HOME/.cache/bazel"
    mkdir -p $BAZEL_CACHE_DIR
    
    # Set up Python environment
    export PYTHONPATH="$PYTHONPATH:$PWD/third_party/apex/src"
    
    # Set up local pip installation to prevent global pollution (like in Docker)
    export PYTHONUSERBASE=/tmp/local_pip
    export PATH="$PATH:$PYTHONUSERBASE/bin"
    
    # Additional environment variables from docker/docker_minimal_20_04/env.sh
    export BROWSER="/usr/bin/firefox"
    
    # ROS environment setup (if ROS is installed separately)
    # source /opt/ros/humble/setup.bash
    
    echo "Development environment initialized!"
    echo "Note: ROS 2 dependencies need to be installed separately or through overlay."
    echo "Warning: Some Apex.AI specific components might still require additional setup."
  '';
}