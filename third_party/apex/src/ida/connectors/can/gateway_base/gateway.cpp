// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include "ida/connectors/can/gateway_base/gateway.hpp"

#include "ida/base/core/error.hpp"
#include "ida/base/core/logging.hpp"
#include "ida/base/core/memory.hpp"
#include "ida/base/core/optional.hpp"
#include "ida/base/core/string_view.hpp"
#include "ida/base/core/utility.hpp"
#include "ida/common/allocator.hpp"
#include "ida/common/logging.hpp"
#include "ida/connectors/can/gateway_base/config.hpp"
#include "ida/connectors/can/gateway_base/pipes.hpp"
#include "ida/plexus/common/endpoint_id.hpp"
#include "ida/plexus/condition/trigger_id_generators.hpp"
#include "ida/plexus/connector_plugins/can/common/can_channel_message.hpp"
#include "ida/plexus/connector_plugins/can/common/can_model_info.hpp"
#include "ida/plexus/connector_plugins/can/common/can_protocol.hpp"
#include "ida/plexus/discovery/entities/events.hpp"
#include "ida/plexus/plugin_gateway_transport/common.hpp"
#include "ida/plexus/publisher/data_writer_qos.hpp"
#include "ida/plexus/subscriber/data_reader_qos.hpp"


namespace apex::connectors::can
{

namespace
{

plexus::discovery::ProtocolSupport get_protocol_support()
{
  return {plexus::discovery::Protocols::CONNECTOR, {CAN_PROTOCOL_NAME}};
}

plexus::discovery::ReaderInfo make_reader_info(plexus::DomainId domain_id,
                                               base::string_view topic,
                                               base::string_view type,
                                               CanId can_id,
                                               const QoS & qos,
                                               const iox::FileName & notify_address)
{
  const auto can_metadata = ModelInfo{can_id};

  auto reader_qos = plexus::DataReaderQoS{};
  reader_qos.set(qos.history);
  reader_qos.set(qos.durability);
  reader_qos.set(qos.reliability);

  const auto reader_id = plexus::EndpointId::generate();

  return plexus::discovery::ReaderInfo::Builder(
           reader_id,
           topic,
           type,
           type,
           domain_id)  // For connectors the type hash is same as the type name
    .set_qos(reader_qos)
    .set_metadata(encode_metadata(can_metadata))
    .set_protocol_support(get_protocol_support())
    .set_waitset_info(plexus::discovery::WaitSetInfo{notify_address.as_string()})
    .set_unset_components_to_default()
    .build();
}

plexus::discovery::WriterInfo make_writer_info(plexus::DomainId domain_id,
                                               base::string_view topic,
                                               base::string_view type,
                                               CanId can_id,
                                               const QoS & qos)
{
  const auto can_metadata = ModelInfo{can_id};

  auto writer_qos = plexus::DataWriterQoS{};
  writer_qos.set(qos.history);
  writer_qos.set(qos.durability);
  writer_qos.set(qos.reliability);
  writer_qos.set(plexus::qos::ResourceLimits{qos.history.depth().value_or(0) + 1, 160});

  const auto writer_id = plexus::EndpointId::generate();

  return plexus::discovery::WriterInfo::Builder(
           writer_id,
           topic,
           type,
           type,
           domain_id)  // For connectors the type hash is same as the type name
    .set_qos(writer_qos)
    .set_metadata(encode_metadata(can_metadata))
    .set_protocol_support(get_protocol_support())
    .set_unset_components_to_default()
    .build();
}

}  // namespace

ConnectorGateway::ConnectorGateway(
  base::badge<base::creator>,
  base::optional<base::error> & maybe_error,
  base::unique_not_null<ConnectorGatewayConfig> config,
  base::not_null<base::allocator *> allocator,
  base::not_null<plexus::ResourceAcquisition *> resource_acquisition) noexcept
: m_gateway_config(base::move(config)),
  m_id_to_reader_pipes(*allocator),
  m_id_to_writer_pipes(*allocator),
  m_resource_acquisition(resource_acquisition),
  m_allocator(*allocator)
{
  if (auto stopped_result = allocator->make_unique<base::atomic_bool>();
      stopped_result.has_error()) {
    maybe_error = base::error("Failed to allocate stop flag", stopped_result.error());
    return;
  } else {
    m_stopped = base::move(stopped_result.value());
  }

  // discovery ---------------------------------------------------------------//
  if (auto discovery_result =
        common::create_discovery(m_allocator, *m_resource_acquisition, m_gateway_config->domain_id);
      discovery_result.has_error()) {
    maybe_error = base::error("Failed to create discovery module");
    return;
  } else {
    m_discovery = base::move(*discovery_result);
  }

  // reactor -----------------------------------------------------------------//
  if (auto listener_create_result =
        plexus::IpcConfig::Event::Creator(
          plexus::detail::create_ipc_listener_filename(m_discovery->id().value(),
                                                       plexus::detail::get_next_listener_id()))
          .create();
      listener_create_result.has_error()) {
    maybe_error = base::error(
      "Failed to create gateway listener",
      APEX_ERROR_FROM_ENUM(ipc::core::event::EventCreateError, listener_create_result.error()));
    return;
  } else {
    m_reactor_listener_guard = base::move(*listener_create_result);
  }
  if (auto listener_open_result =
        plexus::IpcConfig::Event::Opener(m_reactor_listener_guard->name()).open_listener();
      listener_open_result.has_error()) {
    maybe_error = base::error(
      "Failed to open gateway listener",
      APEX_ERROR_FROM_ENUM(ipc::core::event::EventOpenError, listener_open_result.error()));
    return;
  } else {
    if (auto reactor_result = create_unique<plexus::StatefulReactor>(
          *allocator, base::move(*listener_open_result), *allocator);
        reactor_result.has_error()) {
      maybe_error =
        base::error("Failed to create gateway reactor", base::move(reactor_result.error()));
      return;
    } else {
      m_reactor = base::move(*reactor_result);
    }
  }
}

Result<plexus::EndpointId> ConnectorGateway::add_reader(plexus::DomainId domain_id,
                                                        base::string_view interface_name,
                                                        base::string_view topic,
                                                        base::string_view topic_type,
                                                        CanId can_id,
                                                        const QoS & qos)
{
  BASE_LOG(INFO, "Adding new reader " << topic << " " << topic_type);
  auto reader_info =
    make_reader_info(domain_id, topic, topic_type, can_id, qos, m_reactor->listener_name());

  const auto endpoint_id = reader_info.id();

  const auto reader_pipe_insertion_result = m_id_to_reader_pipes.insert(
    endpoint_id, ReaderPipe{m_allocator, interface_name, base::move(reader_info)});

  if (reader_pipe_insertion_result.has_error()) {
    return err("Failed to add new reader for endpoint ID ", endpoint_id);
  }

  auto inserted = m_id_to_reader_pipes.get(endpoint_id);
  if (!inserted) {
    return err("The readers dictionary does not contain endpoint ID ", endpoint_id);
  }

  const auto & inserted_info = inserted.value().reader_info;

  if (const auto result = m_discovery->signal_available_reader(inserted_info); result.has_error()) {
    return err("Failed to signal reader for topic ", base::string_view(topic));
  }

  BASE_LOG(DEBUG,
           "New available reader (" << inserted_info.id().as_string() << ") on topic "
                                    << inserted_info.topic());

  return base::ok(endpoint_id);
}

Result<plexus::EndpointId> ConnectorGateway::add_writer(plexus::DomainId domain_id,
                                                        base::string_view interface_name,
                                                        base::string_view topic,
                                                        base::string_view topic_type,
                                                        CanId can_id,
                                                        const QoS & qos)
{
  BASE_LOG(INFO, "Adding new writer " << topic << " " << topic_type);

  auto writer_info = make_writer_info(domain_id, topic, topic_type, can_id, qos);

  const auto endpoint_id = writer_info.id();

  const auto writer_pipe_insertion_result = m_id_to_writer_pipes.insert(
    endpoint_id, WriterPipe{m_allocator, interface_name, base::move(writer_info)});

  if (writer_pipe_insertion_result.has_error()) {
    return err("Failed to add new writer for endpoint ID ", endpoint_id);
  }

  auto inserted = m_id_to_writer_pipes.get(endpoint_id);
  if (!inserted) {
    return err("The writers dictionary does not contain endpoint ID ", endpoint_id);
  }

  const auto & inserted_info = inserted.value().writer_info;

  if (const auto result = m_discovery->signal_available_writer(inserted_info); result.has_error()) {
    return err("Failed to signal writer for topic ", topic);
  }

  BASE_LOG(DEBUG,
           "New available writer (" << inserted_info.id().as_string() << ") on topic "
                                    << inserted_info.topic());

  return base::ok(endpoint_id);
}

Result<void> ConnectorGateway::init_from_channel(const TransportChannelConfig & channel)
{
  base::optional<plexus::EndpointId> raw_reader_endpoint;
  base::optional<plexus::EndpointId> raw_writer_endpoint;

  if (channel.raw_forwarder) {
    const auto & can_fwd_it =
      base::find_if(m_gateway_config->raw_forwarders.begin(),
                    m_gateway_config->raw_forwarders.end(),
                    [&raw_forwarder_name = channel.raw_forwarder.value()](const auto & v) {
                      return v.name == raw_forwarder_name;
                    });
    if (can_fwd_it == m_gateway_config->raw_forwarders.end()) {
      return err("Failed to get CAN forwarder from channel value");
    }

    const auto & can_fwd = *can_fwd_it;
    {
      const base::string_view writer_topic = can_fwd.writer_topic_name;
      const base::string_view writer_type = can_fwd.writer_topic_type;

      const auto & writer_qos = can_fwd.writer_qos;
      auto maybe_reader_id = add_reader(m_gateway_config->domain_id,
                                        channel.interface_name,
                                        writer_topic,
                                        writer_type,
                                        InvalidCanId,
                                        writer_qos);
      if (maybe_reader_id.has_error()) {
        return base::err(base::move(maybe_reader_id.error()));
      }
      raw_reader_endpoint = maybe_reader_id.value();
    }
    {
      const base::string_view reader_topic = can_fwd.reader_topic_name;
      const base::string_view reader_type = can_fwd.reader_topic_type;
      const auto & reader_qos = can_fwd.reader_qos;

      auto maybe_writer_id = add_writer(m_gateway_config->domain_id,
                                        channel.interface_name,
                                        reader_topic,
                                        reader_type,
                                        InvalidCanId,
                                        reader_qos);
      if (maybe_writer_id.has_error()) {
        return base::err(base::move(maybe_writer_id.error()));
      }
      raw_writer_endpoint = maybe_writer_id.value();
    }
  }

  auto register_messages_to_receive = [this, &channel](const auto & database) -> Result<void> {
    for (const auto & message_to_receive : database.messages_to_receive) {
      const base::string_view topic = message_to_receive.topic_name;
      const base::string_view type = message_to_receive.topic_type;
      const auto & qos = message_to_receive.qos;

      auto maybe_writer_id = add_writer(m_gateway_config->domain_id,
                                        channel.interface_name,
                                        topic,
                                        type,
                                        static_cast<CanId>(message_to_receive.can_id),
                                        qos);
      if (maybe_writer_id.has_error()) {
        return err(base::move(maybe_writer_id.error()));
      }
      auto inserted = m_id_to_writer_pipes.get(maybe_writer_id.value());
      if (!inserted) {
        return err("The writers dictionary does not contain endpoint ID ", maybe_writer_id.value());
      }
      if (auto result = inserted.value().add_can_id(message_to_receive.can_id);
          result.has_error()) {
        return err(base::move(result.error()));
      }
    }
    return base::ok();
  };

  auto register_messages_to_send = [this, &channel](const auto & database) -> Result<void> {
    for (const auto & message_to_send : database.messages_to_send) {
      const base::string_view topic = message_to_send.topic_name;
      const base::string_view type = message_to_send.topic_type;
      const auto & qos = message_to_send.qos;

      auto maybe_reader_id = add_reader(m_gateway_config->domain_id,
                                        channel.interface_name,
                                        topic,
                                        type,
                                        static_cast<CanId>(message_to_send.can_id),
                                        qos);
      if (maybe_reader_id.has_error()) {
        return err(base::move(maybe_reader_id.error()));
      }
      auto inserted = m_id_to_reader_pipes.get(maybe_reader_id.value());
      if (!inserted) {
        return err("The readers dictionary does not contain endpoint ID ", maybe_reader_id.value());
      }
      if (auto result = inserted.value().add_can_id(message_to_send.can_id); result.has_error()) {
        return err(base::move(result.error()));
      }
    }
    return base::ok();
  };

  auto register_messages_to_forward =
    [this, &raw_reader_endpoint, &raw_writer_endpoint](const auto & database) -> Result<void> {
    ReaderPipe * raw_reader = nullptr;
    WriterPipe * raw_writer = nullptr;

    if (raw_reader_endpoint) {
      auto inserted = m_id_to_reader_pipes.get(raw_reader_endpoint.value());
      if (!inserted) {
        return err("The readers dictionary does not contain endpoint ID ",
                   raw_reader_endpoint.value());
      }
      raw_reader = &inserted.value();
    }

    if (raw_writer_endpoint) {
      auto inserted = m_id_to_writer_pipes.get(raw_writer_endpoint.value());
      if (!inserted) {
        return err("The writers dictionary does not contain endpoint ID ",
                   raw_writer_endpoint.value());
      }
      raw_writer = &inserted.value();
    }

    for (const auto & forwarded_message : database.messages_to_forward) {
      if (raw_reader != nullptr) {
        if (auto result = raw_reader->add_can_id(forwarded_message.can_id); result.has_error()) {
          return err(base::move(result.error()));
        }
      }
      if (raw_writer != nullptr) {
        if (auto result = raw_writer->add_can_id(forwarded_message.can_id); result.has_error()) {
          return err(base::move(result.error()));
        }
      }
    }
    return base::ok();
  };

  for (const auto & database : channel.can_databases) {
    if (auto result = register_messages_to_receive(*database); result.has_error()) {
      return err(base::move(result.error()));
    }
    if (auto result = register_messages_to_send(*database); result.has_error()) {
      return err(base::move(result.error()));
    }
    if (auto result = register_messages_to_forward(*database); result.has_error()) {
      return err(base::move(result.error()));
    }
  }

  return base::ok();
}

Result<void> ConnectorGateway::init(base::shared_not_null<CanStackInterface> can_interface)
{
  if (m_stopped->load()) {
    return err("ConnectorGateway is stopped!");
  }

  if (m_can_stack) {
    return err("ConnectorGateway is already initialized!");
  }

  m_can_stack = base::move(can_interface.get());

  if (auto init_result = m_can_stack->init(*m_gateway_config, *m_reactor);
      init_result.has_error()) {
    return err("Failed to initialize CAN stack", base::move(init_result.error()));
  }

  for (const auto & channel : m_gateway_config->transport.channels) {
    if (auto result = init_from_channel(channel); result.has_error()) {
      return err(base::move(result.error()));
    }
  }

  return base::ok();
}

void ConnectorGateway::run_until_stopped(base::function_ref<bool()> poll_interrupt)
{
  if (m_stopped->load()) {
    return;
  }

  while (!(m_stopped->load() || poll_interrupt())) {
    spin_once();
  }

  // stop explicitly in case we were interrupted
  (void)stop();
}

void ConnectorGateway::spin_once()
{
  if (m_stopped->load()) {
    return;
  }

  using namespace base::literals;

  auto triggers = m_reactor->wait(10ms);

  for (const auto & trigger_id : triggers) {
    for (auto & [k, reader_pipe] : m_id_to_reader_pipes) {
      if (reader_pipe.has_channel_with_trigger_id(trigger_id)) {
        (void)forward_plugin_message_to_can(reader_pipe);
      }
    }

    m_can_stack->read_can_frames(trigger_id, [this](const ChannelFrame & frame) {
      (void)forward_can_frame_to_plugin(frame.interface,
                                        frame.id,
                                        base::span<const base::byte>(*frame.payload),
                                        frame.source_timestamp);
      // Don't print error message, the unknown CAN ID is not an error
    });
  }

  // Poll for discovery events unconditionally.
  // TODO(tobias.stark) #24537 Do this only if a discovery trigger arrives. This requires the
  //                    waitset-discovery mechanism to tell other discovery instances that they
  //                    should notify the gateway's main listener, not a special discovery listener
  //                    (or, said another way, it requires single-thread-mode support in discovery)
  if (auto result = m_discovery->process_notifications(); result.has_error()) {
    BASE_LOG(ERROR, "Failed to process discovery notifications: " << result.error());
  }

  for (auto maybe_event = m_discovery->read_event(); maybe_event;
       maybe_event = m_discovery->read_event()) {
    base::visit(
      *maybe_event,
      base::overloaded_call{
        [](const base::monostate & /*match*/) {
          BASE_LOG(WARN, "[gateway::discovery] uninitialized match variant value");
        },
        [this](const plexus::discovery::MatchFound & match) { discovery_match_found(match.info); },
        [this](const plexus::discovery::MatchLost & match) { discovery_match_lost(match.info); },
        [this](const plexus::discovery::WaitSetChanged & ws_event) {
          reader_waitset_changed(ws_event);
        },
        [](auto) {
          // not interesting to gateway
        },
      });
  }
}

Result<void> ConnectorGateway::forward_can_frame_to_plugin(base::string_view interface,
                                                           CanId can_id,
                                                           base::span<const base::byte> payload,
                                                           Timestamp source_timestamp)
{
  // Read data CAN sockets -> m_id_to_writer_pipes
  const auto payload_length = payload.size();

  const auto payload_layout = determine_channel_message_layout(payload_length);

  auto writer_it = base::find_if(m_id_to_writer_pipes.begin(),
                                 m_id_to_writer_pipes.end(),
                                 [interface = interface, can_id = can_id](const auto & p) {
                                   return (interface == p.second.interface) &&
                                          (p.second.can_ids.find(can_id) != p.second.can_ids.end());
                                 });

  if (writer_it == m_id_to_writer_pipes.end()) {
    BASE_LOG(
      DEBUG,
      "No writer is found for interface=" << interface << ", can_id=" << iox::log::hex(can_id));
    return err("No writer is found");
  }

  // If the iterator is not equal to the end iterator of the map, it must point to a not-null value.
  auto & writer_pipe = writer_it->second;

  base::optional<base::error> last_error;
  for (auto & [endpoint_id, writer_desc] : writer_pipe.channels) {
    auto message_result = writer_desc.channel.allocate_message(payload_layout);
    if (message_result.has_error()) {
      last_error =
        base::error("Failed to allocate IPC message ", base::move(message_result.error()));
      BASE_LOG(ERROR, *last_error);
      continue;
    }

    message_result->template emplace<ChannelMessage>();
    /*
     AXIVION DISABLE STYLE MisraC++2023-8.2.6: Reason: Code Quality (Functional suitability),
     Justification: allow casting from void pointer to ChannelMessage pointer. It is necessary
     to implement CAN communcation stack
     */
    auto * const message_ptr = static_cast<ChannelMessage *>(message_result->get().as_ptr());

    message_ptr->magic = MagicMessageHeader::CAN;
    message_ptr->id = can_id;
    message_ptr->payload_length = payload_length;

    auto sample_header = plexus::SampleHeader{};
    sample_header.source_timestamp = source_timestamp;
    sample_header.identity = {endpoint_id, writer_desc.sequence_number};
    sample_header.tag = 0;

    message_ptr->sample_header = sample_header;
    /*
     AXIVION Next CodeLine MisraC++2023-7.11.2: Reason: Code Quality (Functional suitability),
     Justification: Accept byte[] array conversion to pointer since the payload size is passed
     correctly.
     */
    (void)base::memcpy(message_ptr->payload, payload.data(), payload.size());

    if (auto result = writer_desc.channel.write(base::move(*message_result)); result.has_value()) {
      writer_desc.sequence_number++;
    } else {
      last_error = base::error("Failed to send IPC message", base::move(result.error()));
      BASE_LOG(ERROR, *last_error);
    }
  }

  if (last_error) {
    return base::err(base::move(*last_error));
  }

  return base::ok();
}

Result<void> ConnectorGateway::forward_plugin_message_to_can(ReaderPipe & reader_pipe)
{
  // Write data m_id_to_reader_pipes -> CAN sockets
  base::optional<base::error> last_error;

  for (auto & [endpoint_id, transport_reader] : reader_pipe.channels) {
    while (true) {
      auto message_result = transport_reader.read();
      if (message_result.has_error()) {
        last_error =
          base::error("Failed to read transport message", base::move(message_result.error()));
        BASE_LOG(ERROR, *last_error);
        break;
      }

      auto maybe_message = base::move(*message_result);
      if (!maybe_message) {
        // Channel is exhausted
        break;
      }

      const auto & channel_message =
        *static_cast<ChannelMessage *>(maybe_message.value().get().as_ptr());
      // AXIVION ENABLE STYLE MisraC++2023-8.2.5, MisraC++2023-8.2.8
      // AXIVION ENABLE STYLE MisraC++2023-8.2.6

      if (reader_pipe.can_ids.find(channel_message.id) == reader_pipe.can_ids.end()) {
        // The CAN ID is not mapped to this reader in the gateway, the message must be dropped
        BASE_LOG(ERROR,
                 "Unmapped CAN ID received from endpoint_id="
                   << endpoint_id
                   << ", interface=" << reader_pipe.interface << ", can_id=" << channel_message.id);
        break;
      }

      const auto payload =
        base::span<const base::byte>(channel_message.payload, channel_message.payload_length);

      BASE_LOG(DEBUG, "Sending message to CAN");

      auto write_result =
        m_can_stack->write_can_frame(reader_pipe.interface, channel_message.id, payload);

      if (write_result.has_error()) {
        last_error = base::error("Failed to send request", base::move(write_result.error()));
        BASE_LOG(ERROR, *last_error);
      }
    }
  }

  if (last_error) {
    return base::err(base::move(*last_error));
  }
  return base::ok();
}

Result<void> ConnectorGateway::stop()
{
  if (m_stopped->load()) {
    return err("ConnectorGateway is already stopped!");
  }

  m_stopped->store(true);
  m_reactor.reset();
  m_can_stack.reset();

  return base::ok();
}

void ConnectorGateway::discovery_match_found(const plexus::discovery::MatchInfo & info)
{
  BASE_LOG(DEBUG, "[ConnectorGateway] Discovery match found");
  auto trigger_result = discovered_match(info);

  if (trigger_result.has_error()) {
    BASE_LOG(ERROR, trigger_result.error());
  }
}

void ConnectorGateway::discovery_match_lost(
  [[maybe_unused]] const plexus::discovery::MatchInfo & info)
{
  BASE_LOG(DEBUG, "[ConnectorGateway] Discovery match lost");
}

void ConnectorGateway::reader_waitset_changed(const plexus::discovery::WaitSetChanged & ws_event)
{
  auto maybe_writer_pipe = m_id_to_writer_pipes.get(ws_event.writer_ids.endpoint_id);
  if (!maybe_writer_pipe.has_value()) {
    BASE_LOG(ERROR, "Could not find WriterPipe");
    return;
  }

  auto maybe_writer = maybe_writer_pipe->find_channel(ws_event.reader_ids.endpoint_id);
  if (!maybe_writer.has_value()) {
    BASE_LOG(ERROR, "Could not find TransportWriter");
    return;
  }

  if (auto result =
        maybe_writer->channel.switch_notification_address(ws_event.waitset_info.get_name());
      result.has_error()) {
    BASE_LOG(ERROR, "Failed to switch notification address" << result.error());
  };
}

Result<void> ConnectorGateway::discovered_match(const plexus::discovery::MatchInfo & info)
{
  const common::TransportParams transport_params{info.channel_info()};

  // Add new transport reader for the writer matched to existing reader
  if (auto maybe_reader_pipe = m_id_to_reader_pipes.get(info.reader_ids().endpoint_id);
      maybe_reader_pipe.has_value()) {
    auto & reader_pipe = maybe_reader_pipe.value();
    if (!reader_pipe.find_channel(info.writer_ids().endpoint_id)) {
      BASE_LOG(INFO, "discovered_match reader");
      auto channel_result = base::create<common::TransportReader>(
        transport_params, &m_allocator, m_resource_acquisition.get());
      if (channel_result.has_error()) {
        return err("Failed to create transport reader", base::move(channel_result.error()));
      }
      if (auto result =
            reader_pipe.set_channel(info.writer_ids().endpoint_id, base::move(*channel_result));
          result.has_error()) {
        return err("Failed to add reader channel", base::move(result.error()));
      }
    }

    return base::ok();
  }

  // Add new transport writer for the reader matched to existing writer
  if (auto maybe_writer_pipe = m_id_to_writer_pipes.get(info.writer_ids().endpoint_id);
      maybe_writer_pipe.has_value()) {
    auto & writer_pipe = maybe_writer_pipe.value();

    if (!writer_pipe.find_channel(info.reader_ids().endpoint_id)) {
      BASE_LOG(INFO, "discovered_match writer");
      auto channel_result = base::create<common::TransportWriter>(
        transport_params, &m_allocator, m_resource_acquisition.get());
      if (channel_result.has_error()) {
        return err("Failed to create transport writer", base::move(channel_result.error()));
      }
      if (auto result =
            writer_pipe.set_channel(info.reader_ids().endpoint_id, base::move(*channel_result));
          result.has_error()) {
        return err("Failed to add writer channel", base::move(result.error()));
      }
    }

    return base::ok();
  }
  return err("Failed to find match");
}

}  // namespace apex::connectors::can
