// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include "ida/connectors/can/gateway_base/test/can_stack_mock.hpp"

#include "ida/common/allocator.hpp"
#include "ida/common/test_util.hpp"

namespace apex::connectors::can
{

[[nodiscard]] Result<base::shared_ptr<CanStackInterface>> create_can_stack(
  base::allocator & allocator)
{
  return base::ok(base::shared_ptr<CanStackInterface>(testing::g_can_stack));
}

}  // namespace apex::connectors::can

namespace testing
{

void setup_can_stack_global(base::allocator & allocator)
{
  g_can_stack = apex::test::force_ok(apex::create_shared<testing::CanStackMock>(allocator));
}

void tear_down_can_stack_global()
{
  g_can_stack.reset();
}

base::shared_ptr<CanStackMock> g_can_stack{nullptr};

}  // namespace testing
