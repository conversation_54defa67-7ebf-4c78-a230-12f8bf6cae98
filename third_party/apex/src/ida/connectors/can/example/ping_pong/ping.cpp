/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include <chrono>
#include <thread>

//! [generated_header_includes]
#include "ida/connectors/can/example/ping_pong/example_dds.hpp"  // (1)!
#include "ida/connectors/can/example/ping_pong/example_dds_helpers.hpp"  // (2)!
#include "ida/connectors/can/example/ping_pong/example_pseudo.hpp"  // (3)!
//! [generated_header_includes]

// Apex.Ida headers
#include "ida/base/core/heap_allocator.hpp"
#include "ida/base/core/utility.hpp"
#include "ida/common/allocator.hpp"
#include "ida/common/logging.hpp"
#include "ida/plexus/condition/conditions.hpp"
#include "ida/plexus/condition/waitset.hpp"
#include "ida/plexus/context/runtime.hpp"
#include "ida/plexus/domain/data_reader.hpp"
#include "ida/plexus/domain/data_writer.hpp"
#include "ida/plexus/domain/participant.hpp"
#include "ida/plexus/domain/publisher.hpp"
#include "ida/plexus/domain/subscriber.hpp"

namespace base = apex::base;

static base::atomic_bool g_running{true};

void signal_handler(int signum, siginfo_t *, void *)
{
  g_running = false;
}

uint64_t now()
{
  return std::chrono::duration_cast<std::chrono::microseconds>(
           std::chrono::system_clock::now().time_since_epoch())
    .count();
}

//! [topic_type_declaration]
using PingTopicType = example_dbc::ping_message;
using PongTopicType = example_dbc::pong_message;
//! [topic_type_declaration]

//! [write_loaned_sample]
void write_ping(apex::plexus::DataWriter<PingTopicType> & ping_writer)
{
  static uint64_t ping_count{0};

  BASE_LOG(INFO, "PING (" << ping_count++ << ")");

  auto loaned_sample = ping_writer.loan();
  if (loaned_sample.has_error()) {
    BASE_LOG(ERROR, "Failed to loan: " << loaned_sample.error().message().c_str());
    return;
  }
  auto & message = loaned_sample->get();
  apex::example_dbc::ping_message::message::init(message);  // (1)!

  message.timestamp(now());

  if (const auto result = ping_writer.write(base::move(*loaned_sample)); result.has_error()) {
    BASE_LOG(ERROR, "Failed to write: " << result.error().message().c_str());
    return;
  }
  if (const auto result = ping_writer.flush(); result.has_error()) {
    BASE_LOG(ERROR, "Failed to flush: " << result.error().message().c_str());
    return;
  }
}
//! [write_loaned_sample]

//! [read_sample]
void read_pong(apex::plexus::DataReader<PongTopicType> & pong_reader)
{
  static uint64_t pong_count{0};

  const auto samples = pong_reader.take();

  for (const auto & read_sample : samples) {
    const auto & data = read_sample.data();
    const auto & info = read_sample.info();

    const auto start = data.timestamp();
    const auto stop = now();
    auto delta = stop - start;

    const auto can_travel_time = std::chrono::system_clock::now().time_since_epoch() -
                                 info.source_timestamp().time_since_epoch();  // (2)!
    BASE_LOG(
      INFO,
      "PONG (" << pong_count << ") travel time from CAN: "
               << std::chrono::duration_cast<std::chrono::microseconds>(can_travel_time).count()
               << "us, round-trip time: " << delta << " us");

    ++pong_count;
  }
}
//! [read_sample]

int main()
{
  using namespace apex::base::literals;

  struct ::sigaction act;
  memset(&act, 0, sizeof(act));
  act.sa_sigaction = &signal_handler;
  // The SA_SIGINFO flag tells sigaction() to use the sa_sigaction field, not sa_handler
  act.sa_flags = SA_SIGINFO;

  ::sigaction(SIGINT, &act, nullptr);
  ::sigaction(SIGTERM, &act, nullptr);

  //! [pre_init]
  base::log::Logger::init(base::log::logLevelFromEnvOr(base::log::MINIMAL_LOG_LEVEL));

  auto runtime = apex::plexus::Runtime();

  auto allocator_res = base::create<base::heap_allocator>(50_MB);
  if (allocator_res.has_error()) {
    base::panic(
      "Failed to create allocator: ",
      base::err_fault(APEX_ERROR_FROM_ENUM(base::basic_allocator_error, allocator_res.error())));
  }
  auto allocator = base::move(*allocator_res);

  auto participant_res = base::create<apex::plexus::DomainParticipant>(0, &allocator, runtime);
  if (participant_res.has_error()) {
    base::panic("Failed to create domain participant", base::err_fault(participant_res.error()));
  }
  auto participant = base::move(*participant_res);
  //! [pre_init]

  //! [create_data_writer]
  auto writer_qos = apex::plexus::DataWriterQoS{};  // (1)!
  writer_qos.set(apex::plexus::qos::Durability::volatile_durability());
  writer_qos.set(apex::plexus::qos::Reliability::reliable());
  writer_qos.set(apex::plexus::qos::History::keep_last(1));
  writer_qos.set(apex::plexus::qos::ResourceLimits{10, 128});

  auto publisher_res = participant.create_publisher();  // (2)!
  if (publisher_res.has_error()) {
    base::panic("Failed to create publisher", base::err_fault(publisher_res.error()));
  }

  auto & publisher = *publisher_res;
  publisher.set_default_qos(writer_qos);

  auto ping_topic_res = participant.create_topic<PingTopicType>(  // (3)!
    "ping");
  if (ping_topic_res.has_error()) {
    base::panic("Failed to create topic", base::err_fault(ping_topic_res.error()));
  }

  auto writer_res = publisher.create_datawriter(*ping_topic_res);  // (4)!
  if (writer_res.has_error()) {
    base::panic("Failed to create writer", base::err_fault(writer_res.error()));
  }
  auto ping_writer = base::move(*writer_res);
  //! [create_data_writer]

  //! [create_data_reader]
  auto reader_qos = apex::plexus::DataReaderQoS{};  // (1)!
  reader_qos.set(apex::plexus::qos::Durability::volatile_durability());
  reader_qos.set(apex::plexus::qos::Reliability::reliable());
  reader_qos.set(apex::plexus::qos::History::keep_last(1));
  reader_qos.set(apex::plexus::qos::ResourceLimits{10, 128});

  auto subscriber_res = participant.create_subscriber();  // (2)!
  if (subscriber_res.has_error()) {
    base::panic("Failed to create subscriber", base::err_fault(subscriber_res.error()));
  }

  auto & subscriber = *subscriber_res;
  subscriber.set_default_qos(reader_qos);

  auto pong_topic_res = participant.create_topic<PongTopicType>(  // (3)!
    "pong");
  if (pong_topic_res.has_error()) {
    base::panic("Failed to create topic", base::err_fault(pong_topic_res.error()));
  }

  auto reader_res = subscriber.create_datareader(*pong_topic_res);  // (4)!
  if (reader_res.has_error()) {
    base::panic("Failed to create reader", base::err_fault(reader_res.error()));
  }
  auto pong_reader = base::move(*reader_res);

  auto cond_res = allocator.make_unique<apex::plexus::ReadCondition>(pong_reader);  // (5)!
  if (cond_res.has_error()) {
    base::panic(
      "Failed to create read condition",
      base::err_fault(APEX_ERROR_FROM_ENUM(base::basic_allocator_error, allocator_res.error())));
  }
  auto read_condition = base::move(*cond_res);
  //! [create_data_reader]

  //! [create_waitset_and_attach]
  auto waitset_res = apex::create_unique<apex::plexus::WaitSet>(  // (1)!
    allocator,
    runtime);
  if (waitset_res.has_error()) {
    base::panic("Failed to allocate waitset", base::err_fault(waitset_res.error()));
  }
  auto waitset = base::move(*waitset_res);

  if (const auto result = waitset->attach(&*read_condition); result.has_error()) {  // (2)!
    base::panic("Failed to attach read condition", base::err_fault(result.error()));
  }
  //! [create_waitset_and_attach]

  //! [emulate_timer]
  apex::plexus::GuardCondition timer_condition;  // (1)!

  if (const auto result = waitset->attach(&timer_condition); result.has_error()) {
    base::panic("Failed to attach guard condition", base::err_fault(result.error()));
  }

  auto timer_thread = std::thread([&] {  // (2)!
    while (g_running) {
      std::this_thread::sleep_for(std::chrono::seconds{1});
      timer_condition.set_trigger_value(true);
    }
  });
  //! [emulate_timer]

  //! [main_loop]
  while (g_running) {
    auto active_conditions = waitset->wait(1s);
    for (auto & condition : *active_conditions) {
      if (condition == &timer_condition) {  // (1)!
        timer_condition.set_trigger_value(false);
        write_ping(ping_writer);
      } else if (condition == &*read_condition) {  // (2)!
        read_pong(pong_reader);
      }
    }
  }
  //! [main_loop]

  timer_thread.join();

  return 0;
}
