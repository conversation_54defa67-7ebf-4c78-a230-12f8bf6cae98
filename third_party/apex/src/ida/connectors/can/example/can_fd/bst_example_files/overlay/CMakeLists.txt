cmake_minimum_required(VERSION 3.19)
project(can_fd_example VERSION 0.1.0)

find_package(ida_can_serializer CONFIG REQUIRED)
find_package(ida_connector_can CONFIG REQUIRED)
find_package(ida_e2e_verifier CONFIG REQUIRED)

# Enable code generator support
find_package(apex_codegen_cmake CONFIG REQUIRED)

# Building with Bazel
experimental_apex_codegen(
    codegen
    BUILD_FILE "codegen.BUILD"
    FILES
        example.dbc
        mapping.yaml
)

add_executable(can_fd_pong
    socketcan_main.cpp
    $<TARGET_PROPERTY:serialization,SOURCES>
)

target_link_libraries(can_fd_pong
    ida_connector_can::socketcan
    ida_common::common
    serialization
)

add_executable(can_fd_ping
    ping.cpp
    $<TARGET_PROPERTY:serialization,SOURCES>
)

target_link_libraries(can_fd_ping
    ida_com::waitset
    serialization
    example_dbc
)

target_link_options(can_fd_ping PRIVATE "LINKER:--no-as-needed")

get_target_property(GATEWAY_CONFIG_YAML ping_pong_gateway_gateway_config SOURCES)

install(
    FILES
        ${GATEWAY_CONFIG_YAML}
    DESTINATION
        share/${PROJECT_NAME}/config
)

install(
    TARGETS 
        can_fd_pong
        can_fd_ping
)

INSTALL(
    FILES
        ${PROJECT_SOURCE_DIR}/run_example.sh
    DESTINATION
        bin
    RENAME
        run_${PROJECT_NAME}
    PERMISSIONS
        OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE 
)

INSTALL(
    FILES
        ${PROJECT_SOURCE_DIR}/tmux.conf
    DESTINATION
        share/${PROJECT_NAME}/config
)

find_package(ament_cmake QUIET)
if (ament_cmake_FOUND)
    ament_package()
endif()
