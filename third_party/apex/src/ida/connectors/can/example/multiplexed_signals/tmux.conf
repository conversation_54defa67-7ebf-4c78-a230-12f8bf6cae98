# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

set -g prefix2 C-a
bind r source-file ~/.tmux.conf \; display "Reloaded!"
set -g default-terminal screen-256color

# split window
# vertical split (prefix -)
bind - splitw -v
bind | splitw -h # horizontal split (prefix |)

# select pane
bind k selectp -U # above (prefix k)
bind j selectp -D # below (prefix j)
bind h selectp -L # left (prefix h)
bind l selectp -R # right (prefix l)

# bind send to all window
bind e setw synchronize-panes
#bind E setw synchronize-panes off

# resize pane
bind -r ^k resizep -U 10 # upward (prefix Ctrl+k)
bind -r ^j resizep -D 10 # downward (prefix Ctrl+j)
bind -r ^h resizep -L 10 # to the left (prefix Ctrl+h)
bind -r ^l resizep -R 10 # to the right (prefix Ctrl+l)

# increase history size
set -g history-limit 10000

# Enable mouse
set -g mouse on

# Enable vi mode copy
set -g mode-keys vi

# Disable integration with system clipboard
set-option -g set-clipboard off

# enable border status
set -g pane-border-status top
