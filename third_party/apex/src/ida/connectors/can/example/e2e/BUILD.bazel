load("@apex//ida/bazel:defs.bzl", "ida_dbc")
load("@apex//ida/bazel:defs.bzl", "socketcan_gateway")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary")

# These files are used by both inproc and ipc variants
exports_files([
    "ping_mapping.yaml",
    "pong_mapping.yaml",
    "tmux.conf",
])

# 1. Load CAN messages from DBC files and generate
#    everything needed to use them in all supported contexts.
ida_dbc(
    name = "example_dbc",
    srcs = [
        "example.dbc",
    ],
    e2e_config = "e2e_config.json",
    visibility = [":__subpackages__"],
)

# 2. Two simple Apex.Ida applications ping and pong
#    By linking the CAN plugin, Plexus will automatically use it when discovery tells it to
cc_binary(
    name = "ping",
    srcs = ["ping.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":example_dbc",
        "//ida/plexus/condition:waitset",
        "//ida/plexus/domain",
    ],
)

cc_binary(
    name = "pong",
    srcs = ["pong.cpp"],
    tags = ["exclude_sca"],
    deps = [
        ":example_dbc",
        "//ida/plexus/condition:waitset",
        "//ida/plexus/domain",
    ],
)

socketcan_gateway(
    name = "ping_gateway",
    config = "ping_mapping.yaml",
    deps = [":example_dbc"],
)

socketcan_gateway(
    name = "pong_gateway",
    config = "pong_mapping.yaml",
    deps = [":example_dbc"],
)

# CAN packet sniffer and modifier
py_binary(
    name = "gremlin",
    srcs = ["gremlin.py"],
    deps = [requirement("python-can")],
)

# 3. Run this target to run the demo, executing ping and pong respectively.
#    CAN communication happens over a virtual network interface.
sh_binary(
    name = "e2e",
    srcs = ["run_example.sh"],
    data = [
        "bashrc-tmux",
        "record_replay.sh",
        "tmux.conf",
        ":gremlin",
        ":ping",
        ":ping_gateway",
        ":pong",
        ":pong_gateway",
        "@apex//ida/resource_creator",
    ],
    visibility = ["//visibility:public"],
)
