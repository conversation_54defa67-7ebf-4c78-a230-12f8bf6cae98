// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#ifndef APEX__CONNECTORS__RTPS__CYCLONE_GATEWAY__RPC_HEADER__HPP
#define APEX__CONNECTORS__RTPS__CYCLONE_GATEWAY__RPC_HEADER__HPP

#include <vector>

#include "ida/base/core/def.hpp"
#include "ida/base/core/span.hpp"
#include "ida/serializers/cdr/serialization.hpp"

namespace apex::connectors::rtps
{
// This structure matches the sample header added by rmw_cyclone to service structs:
// https://github.com/ros2/rmw_cyclonedds/blob/galactic/rmw_cyclonedds_cpp/src/serdata.hpp#L71
struct alignas(8) RpcSampleHeader final
{
  uint64_t client_guid;
  int64_t sequence_number;

  bool operator==(const RpcSampleHeader & other) const
  {
    return client_guid == other.client_guid && sequence_number == other.sequence_number;
  }
};
static_assert(base::is_trivially_copyable<RpcSampleHeader>::value);

/**
 * @brief Extracts the encoded header from the payload.
 * After the decode, the actual payload is moved to the front of the memory region.
 */
void decode_rpc_sample_header_and_adapt_payload(base::span<base::byte> payload,
                                                RpcSampleHeader & header)
{
  base::panic_if("CDR payload is too small to contain a RpcSampleHeader!",
                 [&] { return payload.size() <= sizeof(header); });

  // we need to skip the CDR header here
  auto * after_cdr_header = payload.data() + cdr_serialization::CDR_HEADER_SIZE;

  std::memcpy(&header, after_cdr_header, sizeof(header));

  std::memmove(after_cdr_header,
               after_cdr_header + sizeof(header),
               payload.size() - (sizeof(header) + cdr_serialization::CDR_HEADER_SIZE));
}

/**
 * @brief Encodes the header into the payload.
 */
std::vector<base::byte> encode_rpc_sample_header(base::span<const base::byte> payload,
                                                 const RpcSampleHeader & header)
{
  std::vector<base::byte> buffer;
  buffer.resize(payload.size() + sizeof(header));

  auto * destination = buffer.data();

  // first copy the cdr header
  std::memcpy(destination, payload.data(), cdr_serialization::CDR_HEADER_SIZE);
  destination += cdr_serialization::CDR_HEADER_SIZE;

  // then copy the rpc header
  std::memcpy(destination, &header, sizeof(header));
  destination += sizeof(header);

  // last, copy the actual payload
  std::memcpy(destination,
              payload.data() + cdr_serialization::CDR_HEADER_SIZE,
              payload.size() - cdr_serialization::CDR_HEADER_SIZE);

  return buffer;
}

}  // namespace apex::connectors::rtps

#endif
