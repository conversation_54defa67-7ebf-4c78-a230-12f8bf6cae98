# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

import os
from pathlib import Path
from typing import List

from ida.connectors.common.gateway.config_backend.validation import ValidationError


class DuplicateMappingError(ValidationError):
    def __init__(self, mapping_type: str, duplicate_entry: str) -> None:
        super().__init__(
            f'Duplicate mapping for "{mapping_type}": "{duplicate_entry}" found'
        )


class ConfigFileNotFoundError(Exception):
    def __init__(self, file: Path, search_paths: List[Path]) -> None:
        super().__init__(
            f"Cannot find file {file} in search paths "
            + ", ".join(os.fspath(p) for p in search_paths)
        )


class DuplicateTopicError(ValidationError):
    def __init__(self, topic: str) -> None:
        super().__init__(
            f"Duplicate topic '{topic}' found in mapping. Each mapping requires a "
            "unique topic!"
        )
