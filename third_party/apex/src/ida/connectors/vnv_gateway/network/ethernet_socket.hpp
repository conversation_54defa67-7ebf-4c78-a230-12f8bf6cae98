// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#ifndef APEX_CONNECTORS_TECMP_ETHERNET_SOCKET_HPP
#define APEX_CONNECTORS_TECMP_ETHERNET_SOCKET_HPP

#include "ida/base/core/function_ref.hpp"
#include "ida/common/error.hpp"
#include "ida/connectors/vnv_gateway/network/types.hpp"
#include "ida/plexus/condition/stateful_reactor.hpp"

namespace apex::connectors::vnv
{

class EthernetSocket
{
public:
  EthernetSocket(base::badge<base::creator> badge,
                 base::optional<base::error> & maybe_error,
                 base::allocator & allocator);

  EthernetSocket(const EthernetSocket &) = delete;
  EthernetSocket & operator=(const EthernetSocket &) = delete;
  EthernetSocket(EthernetSocket &&) noexcept;
  EthernetSocket & operator=(EthernetSocket &&) & noexcept;
  ~EthernetSocket() noexcept;

  [[nodiscard]] Result<void> init(const char * interface_name,
                                  const char * multicast_mac,
                                  plexus::StatefulReactor &) noexcept;

  void read_frames(ipc::core::event::TriggerId trigger,
                   base::function_ref<void(const EthernetFrame &)> callback) noexcept;

private:
  struct Impl;
  base::unique_ptr<Impl> m_impl;
};

}  // namespace apex::connectors::vnv

#endif  // APEX_CONNECTORS_TECMP_ETHERNET_SOCKET_HPP
