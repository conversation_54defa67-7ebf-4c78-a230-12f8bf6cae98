// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#ifndef APEX_CONNECTORS_TECMP_GATEWAY__GATEWAY_HPP
#define APEX_CONNECTORS_TECMP_GATEWAY__GATEWAY_HPP

#include "ida/base/core/atomic.hpp"
#include "ida/base/core/dict.hpp"
#include "ida/base/core/ring_buffer.hpp"
#include "ida/common/error.hpp"
#include "ida/connectors/common/discovery/discovery.hpp"
#include "ida/connectors/vnv_gateway/config.hpp"
#include "ida/connectors/vnv_gateway/network/ethernet_socket.hpp"
#include "ida/connectors/vnv_gateway/network/service_discovery.hpp"
#include "ida/connectors/vnv_gateway/network/tecmp.hpp"
#include "ida/connectors/vnv_gateway/network/tp_reassembly.hpp"
#include "ida/connectors/vnv_gateway/pipes.hpp"
#include "ida/connectors/vnv_gateway/report.hpp"
#include "ida/plexus/common/endpoint_id.hpp"
#include "ida/plexus/condition/stateful_reactor.hpp"
#include "ida/plexus/connector_plugins/can/common/can_config.hpp"
#include "ida/plexus/discovery/entities/endpoint_info.hpp"
#include "ida/plexus/resource_acquisition/resource_acquisition.hpp"

namespace apex::connectors::vnv
{

/// @brief The VNV gateway implementation
class ConnectorGateway
{
public:
  ConnectorGateway(base::badge<base::creator> badge,
                   base::optional<base::error> & maybe_error,
                   base::unique_not_null<GatewayConfig> config,
                   base::not_null<base::allocator *> allocator,
                   base::not_null<plexus::ResourceAcquisition *> resource_acquisition) noexcept;

  ConnectorGateway(const ConnectorGateway &) = delete;
  ConnectorGateway & operator=(const ConnectorGateway &) = delete;
  ConnectorGateway(ConnectorGateway &&) noexcept = delete;
  ConnectorGateway & operator=(ConnectorGateway &&) noexcept = delete;

  Result<void> init();
  Result<void> init_internal_io();
  void run_until_stopped(base::function_ref<bool()> poll_interrupt);
  void spin_once();
  Result<void> stop();

  void handle_ethernet_frame(EthernetFrame frame);

  [[nodiscard]] const GatewayConfig & config() const
  {
    return *m_gateway_config;
  }

  [[nodiscard]] Result<size_t> channel_count_for_topic(base::string_view topic) const;

private:
  Result<void> init_can_mappings();
  Result<void> init_fx_mappings();
  Result<void> init_pdu_mappings();
  Result<void> init_someip_mappings();

  void discovery_match_found(const plexus::discovery::MatchInfo & info);
  void discovery_match_lost(const plexus::discovery::MatchInfo & info);
  void reader_waitset_changed(const plexus::discovery::WaitSetChanged & ws_event);
  BaseWriterPipe * find_pipe_for_endpoint(const plexus::EndpointId & endpoint);

  struct Payload
  {
    base::span<const base::byte> data;
    base::optional<uint32_t> channel_id;
    base::time_point<os::system_clock> source_timestamp;
  };

  void forward_can_frame(const TecmpFrame::CanMetaData & metadata, const Payload & payload);
  void forward_fx_frame(const TecmpFrame::FxMetaData & metadata, const Payload & payload);
  void forward_someip_message(const SomeipMessage & message, const Payload & payload);
  void forward_pdu(const Pdu & message, const Payload & payload);

  enum class NetworkPacketTreatment : uint8_t
  {
    TREAT_AS_SOMEIP,
    TREAT_AS_SOAD,
    IGNORE
  };

  [[nodiscard]] NetworkPacketTreatment determine_network_packet_treatment(
    const NetworkPacket & packet) const;

  void handle_ethernet_payload(const Payload & payload);

  base::unique_not_null<GatewayConfig> m_gateway_config;
  base::unique_ptr<EthernetSocket> m_socket{base::make_empty_unique_ptr<EthernetSocket>()};

  base::shared_ptr<common::Discovery> m_discovery{};

  base::unique_ptr<plexus::StatefulReactor> m_reactor{
    base::make_empty_unique_ptr<plexus::StatefulReactor>()};
  base::optional<plexus::IpcConfig::Event::Guard> m_reactor_listener_guard;

  Result<base::not_null<CanWriterPipe *>> add_can_writer(plexus::DomainId domain_id,
                                                         base::optional<uint32_t> channel_id,
                                                         base::string_view topic,
                                                         base::string_view topic_type,
                                                         uint32_t can_id,
                                                         const can::QoS & qos);

  Result<base::not_null<FxWriterPipe *>> add_fx_writer(plexus::DomainId domain_id,
                                                       base::optional<uint32_t> channel_id,
                                                       base::string_view topic,
                                                       base::string_view topic_type,
                                                       uint32_t virtual_id,
                                                       const can::QoS & qos);

  Result<base::not_null<SomeipWriterPipe *>> add_someip_writer(plexus::DomainId domain_id,
                                                               base::optional<uint32_t> channel_id,
                                                               base::string_view topic,
                                                               base::string_view topic_type,
                                                               uint16_t service_id,
                                                               uint16_t instance_id,
                                                               uint16_t event_id,
                                                               const can::QoS & qos);

  Result<base::not_null<PduWriterPipe *>> add_pdu_writer(plexus::DomainId domain_id,
                                                         base::optional<uint32_t> channel_id,
                                                         base::string_view topic,
                                                         base::string_view topic_type,
                                                         uint32_t id,
                                                         const can::QoS & qos);

  template <typename PipeType>
  Result<base::not_null<PipeType *>> add_writer(
    base::dict<plexus::EndpointId, base::unique_ptr<PipeType>> & map,
    base::string_view topic,
    base::unique_ptr<PipeType> pipe)
  {
    auto pipe_ptr = pipe.get();
    const auto endpoint_id = pipe->writer_info.id();
    const auto writer_pipe_insertion_result = map.insert(endpoint_id, base::move(pipe));

    if (writer_pipe_insertion_result.has_error()) {
      return err("Failed to add new writer for endpoint ID ", endpoint_id);
    }

    const auto topic_map_insertion_result = m_topic_to_pipe.insert(topic, pipe_ptr);
    if (topic_map_insertion_result.has_error()) {
      return err("Failed to insert pipe into topic map ", topic_map_insertion_result.error());
    }

    // check for duplicate mapping (which should already be validated during config loading, but
    // let's be safe here)
    if (topic_map_insertion_result->has_value()) {
      return err("Unexpected duplicate mapping for topic ", topic);
    }

    if (const auto writer_result = m_discovery->signal_available_writer(pipe_ptr->writer_info);
        writer_result.has_error()) {
      return err("Failed to signal writer for topic ", topic);
    }

    BASE_LOG(DEBUG,
             "New available writer (" << pipe_ptr->writer_info.id().as_string() << ") for topic "
                                      << topic);

    return base::ok(pipe_ptr);
  }

  base::unique_ptr<base::atomic_bool> m_stopped{base::make_empty_unique_ptr<base::atomic_bool>()};

  base::dict<plexus::EndpointId, base::unique_ptr<CanWriterPipe>> m_can_pipes;
  base::dict<plexus::EndpointId, base::unique_ptr<FxWriterPipe>> m_fx_pipes;
  base::dict<plexus::EndpointId, base::unique_ptr<SomeipWriterPipe>> m_someip_pipes;
  base::dict<plexus::EndpointId, base::unique_ptr<PduWriterPipe>> m_pdu_pipes;
  base::dict<base::string_view, base::not_null<BaseWriterPipe *>> m_topic_to_pipe;

  StatusReport m_status_report;

  base::not_null<plexus::ResourceAcquisition *> m_resource_acquisition;

  base::allocator & m_allocator;

  SomeipSd m_someip_sd;
  SomeipTp m_someip_tp;

  KnownEndpointSet m_known_soad_udp_endpoints{m_allocator};
  KnownEndpointSet m_known_soad_tcp_endpoints{m_allocator};
};

}  // namespace apex::connectors::vnv

#endif  // APEX_CONNECTORS_TECMP_GATEWAY__GATEWAY_HPP
