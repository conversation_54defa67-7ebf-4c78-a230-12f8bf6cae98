load("@apex//common/bazel/aspects/dependencies:locator.bzl", "locator_for_label")
load("@apex//common/bazel/aspects/dependencies:locator.bzl", "locators_for_label_list")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//ida/bazel:detail/helpers.bzl", "workspace_path_to_label")
load("@apex//ida/bazel:detail/providers.bzl", "CANModelInfo", "CanModel", "TypeModel")
load("@bazel_skylib//lib:sets.bzl", "sets")

def get_include_directories(ctx):
    """Returns a sorted list of directories to set as the include paths of the frontend."""
    private_include_directories = sets.make([
        workspace_path_to_label(ctx.label) if include == "." else include
        for include in ctx.attr.includes
    ])

    dependant_include_directories = sets.make([
        workspace_path_to_label(d.label)
        for d in ctx.attr.data
    ])

    return sets.to_list(private_include_directories) + sets.to_list(dependant_include_directories)

def get_can_models(dep):
    if CANModelInfo in dep:
        return dep[CANModelInfo].can_model[CanModel], dep[CANModelInfo].type_model[TypeModel]
    return dep[CanModel], dep[TypeModel]

def _run_generator(ctx, ordered_include_directories, additional_yamls):
    base_name = ctx.attr.base_name if ctx.attr.base_name else ctx.attr.name
    args_json = ctx.actions.declare_file(base_name + "_arguments.json")

    generated_config = ctx.actions.declare_file(base_name + "_gateway_config.yaml")

    models = []
    input_deps = []
    for dep in ctx.attr.deps:
        can_model, type_model = get_can_models(dep)
        dep_type_models = [t.serialized_ast for t in type_model.typemodel_deps.to_list()]
        models.append({
            "type_model_path": can_model.serialized_type_model.path,
            "can_model_path": can_model.serialized_can_model.path,
            "dep_typemodels": [t.path for t in dep_type_models],
        })
        input_deps.extend(dep_type_models)
        input_deps.append(can_model.serialized_type_model)
        input_deps.append(can_model.serialized_can_model)

    output_config_location_path = None
    if ctx.outputs.gateway_config_locations:
        output_config_location_path = ctx.outputs.gateway_config_locations.path

    config_content = json.encode({
        "main_config": ctx.file.main_config.path,
        "search_paths": ordered_include_directories,
        "models": models,
        "output_gateway_config": generated_config.path,
        "output_gateway_config_runtime_path": generated_config.short_path,
        "output_config_locations": output_config_location_path,
        # FIXME(mziegler): we should pass apply_ros_transformations as a part of "models"
        # dict entries, see #31829
        "apply_ros_transformations": ctx.attr.deps[0][TypeModel].apply_ros_transformations,
    })

    ctx.actions.write(args_json, config_content)

    args = ctx.actions.args()
    args.add("--config", args_json.path)

    input_deps = depset(
        [args_json, ctx.file.main_config] + input_deps,
        transitive = additional_yamls,
    )

    outputs = [generated_config]
    if ctx.outputs.gateway_config_locations:
        outputs.append(ctx.outputs.gateway_config_locations)
    ctx.actions.run(
        executable = ctx.executable._gateway_config_backend,
        arguments = [args],
        inputs = input_deps,
        outputs = outputs,
    )
    return generated_config

def _can_gateway_config(ctx):
    additional_yamls = [d.files for d in ctx.attr.data]
    ordered_include_directories = get_include_directories(ctx)
    generated_config = _run_generator(ctx, ordered_include_directories, additional_yamls)

    return [
        DefaultInfo(files = depset(
            [generated_config],
        )),
        OutputGroupInfo(
            all_files = depset([generated_config]),
        ),
        RuleMetaInfo(
            srcs = [locator_for_label(ctx.attr, "main_config")] +
                   locators_for_label_list(ctx.attr, "data"),
            deps = locators_for_label_list(ctx.attr, "deps"),
            tool_deps = [locator_for_label(ctx.attr, "_gateway_config_backend")],
        ),
    ]

can_gateway_config = rule(
    implementation = _can_gateway_config,
    attrs = {
        "base_name": attr.string(
            doc = "Base name used for prefixing the output files. Defaults to the name of the target.",
        ),
        "main_config": attr.label(
            mandatory = True,
            allow_single_file = True,
        ),
        "includes": attr.string_list(doc = "Add these locations to the search path", default = ["."]),
        "gateway_config_locations": attr.output(
            doc = "Optional output that yields C++ code containing the location of the generated configurations",
            mandatory = False,
        ),
        "data": attr.label_list(
            doc = "Additional yaml files to import",
            allow_files = [".yaml"],
        ),
        "deps": attr.label_list(
            doc = "A target that provides a CAN model",
            mandatory = True,
            providers = [[CanModel, TypeModel], [CANModelInfo, TypeModel]],
        ),
        "_gateway_config_backend": attr.label(
            default = "@apex//ida/connectors/can/gateway_base/config_backend:backend",
            executable = True,
            cfg = "exec",
        ),
    },
    provides = [DefaultInfo, OutputGroupInfo],
)
