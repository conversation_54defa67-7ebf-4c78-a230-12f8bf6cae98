# Copyright Copyright 2023 Apex.AI, Inc.
# All rights reserved.

# Pretty printers for the base library datatypes. Load this into gdb using the `source` command.
import gdb
import gdb.printing
import re

class ErrorPrettyPrinter:
    "Prints an base::error"
    def __init__(self, val):
        self.val = val

    def to_string(self):
        if self.val['m_impl'].cast(gdb.lookup_type('unsigned long long')) == 0:
            return 'empty'
        else:
            return str(self.val['m_impl'].dereference())

class FixedStringPrettyPrinter:
    "Prints an base::fixed_string"
    def __init__(self, val):
        self.val = val

    def to_string(self):
        char_array = gdb.lookup_type('char').array(self.val['m_rawstringSize'])
        as_array = self.val['m_rawstring'].address.cast(char_array.pointer()).dereference()
        return f'"{gdb.Value.string(as_array)}"'

class OptionalPrettyPrinter:
    "Prints an base::optional"
    def __init__(self, val):
        self.val = val
        self.contained_type = self.val.type.template_argument(0)
            
    def to_string(self):
        if self.val['m_has_value']:
            return str(self.val['m_data'].cast(self.contained_type.reference()).referenced_value())
        else:
            return 'base::nullopt'

class VariantPrettyPrinter:
    "Prints an base::variant"
    def __init__(self, val):
        self.val = val
            
    def to_string(self):
        active_type = self.val.type.template_argument(self.val['m_type_index'])
        return str(self.val['m_storage'].cast(active_type.reference()).referenced_value())
        
class ExpectedPrettyPrinter:
    "Prints an base::expected"
    def __init__(self, val):
        self.val = val
        self.value_type = self.val.type.template_argument(0)
        self.error_type = self.val.type.template_argument(1)
    def to_string(self):
        stored_variant = self.val["m_store"]["data"]
        if stored_variant["m_type_index"] == 0:
            if self.value_type.code == gdb.TYPE_CODE_VOID:
                return 'base::ok()'
            else:
                return f'base::ok({stored_variant})'
        elif stored_variant["m_type_index"] == 1:
            return f'base::err({stored_variant})'
        else:
            return f'base::invalid_variant'

class DictSlotPrettyPrinter:
    "Prints an base::dict<...>::slot"
    def __init__(self, val):
        self.val = val
    def to_string(self):
        if self.val['psl'] == self.val['unused_marker']:
            return '{empty}'
        elif self.val['psl'] == self.val['erased_marker']:
            return f'{erased}'
        else:
            return str(self.val['entry_storage'])

class IoxNewTypePrinter:
    "Prints an iox::NewType"
    def __init__(self, val):
        self.val = val
    def to_string(self):
        return str(self.val['m_value'])
        
def build_pretty_printer():
    pp = gdb.printing.RegexpCollectionPrettyPrinter(
        "base")
    pp.add_printer('optional', '^base::optional<.*>$', OptionalPrettyPrinter)
    pp.add_printer('variant', '^base::variant<.*>$', VariantPrettyPrinter)
    pp.add_printer('expected', '^base::expected<.*>$', ExpectedPrettyPrinter)
    pp.add_printer('dict_slot', '^base::static_dict<.*>::slot$', DictSlotPrettyPrinter)
    pp.add_printer('error', '^base::error$', ErrorPrettyPrinter)
    pp.add_printer('fixed_string', '^base::fixed_string<.*>$', FixedStringPrettyPrinter)
    pp.add_printer('NewType', '^iox::NewType<.*>$', IoxNewTypePrinter)
    return pp

                       
def register_printers(objfile=None):
    gdb.printing.register_pretty_printer(
        objfile, build_pretty_printer())

# Main code
try:
    register_printers()
except RuntimeError as e:
    if 'pretty-printer already registered' in str(e):
        # Ignore already-registered errors, just do nothing then.
        pass
    else:
        raise
