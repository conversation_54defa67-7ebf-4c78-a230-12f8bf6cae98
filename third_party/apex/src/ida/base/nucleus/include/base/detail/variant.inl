// Copyright (c) 2019 by <PERSON>. All rights reserved.
// Copyright (c) 2021 - 2022 by Apex.AI Inc. All rights reserved.
// Copyright (c) 2021 by Perforce All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// SPDX-License-Identifier: Apache-2.0
#ifndef APEX_BASE_NUCLEUS_DETAIL_VARIANT_INL
#define APEX_BASE_NUCLEUS_DETAIL_VARIANT_INL

#include "base/panic.hpp"
#include "base/variant.hpp"

namespace apex::base
{
template <typename... Types>
inline constexpr variant<Types...>::variant() : m_type_index(0)
{
  using T = typename internal::get_type_at_index<0, 0, Types...>::type;

  /*
   AXIVION Next Codeline MisraC++2023-21.6.2: Reason: Code Quality (Functional suitability),
   Justification: Dynamic memory allocation is necessary.
   */
  new (&m_storage) T();
}

template <typename... Types>
// constructor delegation is not feasible here due to lack of sufficient common initialization
inline constexpr variant<Types...>::variant(const variant & rhs) noexcept
: m_type_index(rhs.m_type_index)
{
  internal::call_at_index<0, Types...>::copyConstructor(m_type_index, &rhs.m_storage, &m_storage);
}

template <typename... Types>
template <uint64_t N, typename... CTorArguments>
// NOLINTJUSTIFICATION First param is helper struct only
// NOLINTNEXTLINE(hicpp-named-parameter)
// A move of 'unique_ptr' is implicit as the copy
// c'tor is deleted. 'args' is a forwarding reference and not necessarily always a rvalue.
inline constexpr variant<Types...>::variant(const in_place_index<N> &, CTorArguments &&... args)
{
  construct_at_index<N>(std::forward<CTorArguments>(args)...);
}

template <typename... Types>
template <typename T, typename... CTorArguments>
// NOLINTJUSTIFICATION First param is helper struct only
// NOLINTNEXTLINE(hicpp-named-parameter)
inline constexpr variant<Types...>::variant(const in_place_type<T> &, CTorArguments &&... args)
{
  construct<T>(std::forward<CTorArguments>(args)...);
}

template <typename... Types>
template <typename T,
          typename,
          typename std::enable_if_t<!internal::is_in_place_index<std::decay_t<T>>::value, bool>,
          typename std::enable_if_t<!internal::is_in_place_type<std::decay_t<T>>::value, bool>>
inline constexpr variant<Types...>::variant(T && arg)
: variant(in_place_type<std::decay_t<T>>(), std::forward<T>(arg))
{
}

// Overload excluded via std::enable_if in operator=(T&& rhs)
template <typename... Types>
inline constexpr variant<Types...> & variant<Types...>::operator=(const variant & rhs) & noexcept
{
  if (this != &rhs) {
    if (m_type_index != rhs.m_type_index) {
      call_element_destructor();
      m_type_index = rhs.m_type_index;
      internal::call_at_index<0, Types...>::copyConstructor(
        m_type_index, &rhs.m_storage, &m_storage);
    } else {
      internal::call_at_index<0, Types...>::copy(m_type_index, &rhs.m_storage, &m_storage);
    }
  }
  return *this;
}

template <typename... Types>
// 'panic' used inside this method does not throw in production
inline constexpr variant<Types...>::variant(variant && rhs) noexcept
: m_type_index{std::move(rhs.m_type_index)}
{
  internal::call_at_index<0, Types...>::moveConstructor(m_type_index, &rhs.m_storage, &m_storage);
}

// Overload excluded via std::enable_if in operator=(T&& rhs)
template <typename... Types>
inline constexpr variant<Types...> & variant<Types...>::operator=(variant && rhs) & noexcept
{
  if (this != &rhs) {
    if (m_type_index != rhs.m_type_index) {
      call_element_destructor();
      m_type_index = std::move(rhs.m_type_index);
      internal::call_at_index<0, Types...>::moveConstructor(
        m_type_index, &rhs.m_storage, &m_storage);
    } else {
      internal::call_at_index<0, Types...>::move(m_type_index, &rhs.m_storage, &m_storage);
    }
  }
  return *this;
}

template <typename... Types>
inline variant<Types...>::~variant() noexcept
{
  call_element_destructor();
}

template <typename... Types>
inline void variant<Types...>::call_element_destructor() noexcept
{
  internal::call_at_index<0, Types...>::destructor(m_type_index, &m_storage);
}

// NOLINTJUSTIFICATION Correct return type is used through enable_if
// NOLINTNEXTLINE(cppcoreguidelines-c-copy-assignment-signature)
template <typename... Types>
template <typename T>
inline
  typename std::enable_if<!std::is_same<T, variant<Types...> &>::value, variant<Types...>>::type &
  variant<Types...>::operator=(T && rhs) &
{
  if (!has_bad_variant_element_access<T>()) {
    /*
     AXIVION Next Construct MisraC++2023-8.2.6: Reason: Code Quality (Functional suitability),
     Justification: conversion to typed pointer is intentional, it is correctly aligned and points
     to sufficient memory for a T by design
     */
    auto storage = static_cast<T *>(static_cast<void *>(&m_storage));
    *storage = std::forward<T>(rhs);
  } else {
    panic("wrong variant type assignment, another type is already set in variant");
  }

  return *this;
}

template <typename... Types>
template <uint64_t TypeIndex, typename... CTorArguments>
inline void variant<Types...>::emplace_at_index(CTorArguments &&... args)
{
  call_element_destructor();

  construct_at_index<TypeIndex>(std::forward<CTorArguments>(args)...);
}

template <typename... Types>
template <uint64_t TypeIndex, typename... CTorArguments>
inline void variant<Types...>::construct_at_index(CTorArguments &&... args)
{
  static_assert(TypeIndex <= sizeof...(Types), "TypeIndex is out of bounds");
  using T = typename internal::get_type_at_index<0, TypeIndex, Types...>::type;
  /*
   AXIVION Next Construct MisraC++2023-21.6.3: Reason: Code Quality
   (Functional suitability), Justification: It is acceptable to call "new" since m_storage
   that is the byte array member variable in this class is used.
   */
  new (&m_storage) T(std::forward<CTorArguments>(args)...);
  m_type_index = TypeIndex;
}

template <typename... Types>
template <typename T, typename... CTorArguments>
inline T & variant<Types...>::emplace(CTorArguments &&... args)
{
  call_element_destructor();

  construct<T>(std::forward<CTorArguments>(args)...);
  return *get<T>();
}

template <typename... Types>
template <typename T, typename... CTorArguments>
inline void variant<Types...>::construct(CTorArguments &&... args)
{
  static_assert(internal::does_contain_type<T, Types...>::value,
                "variant does not contain given type");
  /*
   AXIVION Next Construct MisraC++2023-7.11.2: Reason: Code Quality (Functional suitability),
   Justification: Accept array conversion to pointer if CTorArguments is an array reference
   */
  /*
   AXIVION Next Construct MisraC++2023-21.6.3: Reason: Code Quality
   (Functional suitability), Justification: It is acceptable to call "new" since m_storage
   that is the byte array member variable in this class is used.
   */
  new (&m_storage) T(std::forward<CTorArguments>(args)...);
  m_type_index = internal::get_index_of_type<0, T, Types...>::index;
}

template <typename... Types>
template <uint64_t TypeIndex>
inline typename internal::get_type_at_index<0, TypeIndex, Types...>::type *
variant<Types...>::get_at_index() noexcept
{
  if (TypeIndex != m_type_index) {
    return nullptr;
  }

  using T = typename internal::get_type_at_index<0, TypeIndex, Types...>::type;

  /*
   AXIVION Next Codeline MisraC++2023-8.2.6, MisraC++2023-8.2.8: Reason: Code Quality
   (Functional suitability), Justification: conversion to typed pointer
   is intentional, it is correctly aligned and points to sufficient memory for a T by design
   */
  return static_cast<T *>(static_cast<void *>(&m_storage));
}

/*
 AXIVION DISABLE STYLE MisraC++2023-8.2.3: Reason: Code Quality (Functional suitability),
 Justification: avoid code duplication
 */
template <typename... Types>
template <uint64_t TypeIndex>
inline const typename internal::get_type_at_index<0, TypeIndex, Types...>::type *
variant<Types...>::get_at_index() const noexcept
{
  using T = typename internal::get_type_at_index<0, TypeIndex, Types...>::type;
  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-const-cast)
  return const_cast<const T *>(const_cast<variant *>(this)->template get_at_index<TypeIndex>());
}

template <typename... Types>
template <typename T>
inline const T * variant<Types...>::get() const noexcept
{
  if (has_bad_variant_element_access<T>()) {
    return nullptr;
  }
  /*
   AXIVION Next Codeline MisraC++2023-8.2.6, MisraC++2023-8.2.8: Reason: Code Quality
   (Functional suitability), Justification: conversion to typed pointer
   is intentional, it is correctly aligned and points to sufficient memory for a T by design
   */
  return static_cast<const T *>(static_cast<const void *>(&m_storage));
}

template <typename... Types>
template <typename T>
inline T * variant<Types...>::get() noexcept
{
  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-const-cast)
  return const_cast<T *>(const_cast<const variant *>(this)->get<T>());
}

template <typename... Types>
template <typename T>
inline T * variant<Types...>::value_or(T * defaultValue) noexcept
{
  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-const-cast)
  return const_cast<T *>(
    const_cast<const variant *>(this)->value_or<T>(const_cast<const T *>(defaultValue)));
}
// AXIVION ENABLE STYLE MisraC++2023-8.2.3

template <typename... Types>
template <typename T>
inline const T * variant<Types...>::value_or(const T * defaultValue) const noexcept
{
  if (has_bad_variant_element_access<T>()) {
    return defaultValue;
  }

  return this->get<T>();
}

template <typename... Types>
constexpr uint64_t variant<Types...>::index() const noexcept
{
  return m_type_index;
}

template <typename... Types>
template <typename T>
inline bool variant<Types...>::has_bad_variant_element_access() const noexcept
{
  static_assert(internal::does_contain_type<T, Types...>::value,
                "variant does not contain given type");
  return (m_type_index != internal::get_index_of_type<0, T, Types...>::index);
}

template <typename T, typename... Types>
inline constexpr bool holds_alternative(const variant<Types...> & variant) noexcept
{
  return variant.template get<T>() != nullptr;
}

template <typename... Types>
inline constexpr bool operator==(const variant<Types...> & lhs,
                                 const variant<Types...> & rhs) noexcept
{
  if (lhs.index() != rhs.index()) {
    return false;
  }
  return internal::call_at_index<0, Types...>::equality(
    lhs.index(), &lhs.m_storage, &rhs.m_storage);
}

template <typename... Types>
inline constexpr bool operator!=(const variant<Types...> & lhs,
                                 const variant<Types...> & rhs) noexcept
{
  return !(lhs == rhs);
}

// Implementation in terms of other operator not feasible because the return value
// is explicitly defined in the STL
inline constexpr bool operator<(monostate, monostate) noexcept
{
  return false;
}

inline constexpr bool operator>(monostate, monostate) noexcept
{
  return false;
}

inline constexpr bool operator<=(monostate, monostate) noexcept
{
  return true;
}

inline constexpr bool operator>=(monostate, monostate) noexcept
{
  return true;
}

inline constexpr bool operator==(monostate, monostate) noexcept
{
  return true;
}

inline constexpr bool operator!=(monostate, monostate) noexcept
{
  return false;
}

template <typename T, typename... Types>
inline constexpr T & get(variant<Types...> & v) noexcept(BASE_NO_EXCEPTIONS)
{
  auto * ptr = v.template get<T>();
  base::panic_if("wrong variant type to get", [ptr] { return !ptr; });
  return *ptr;
}

template <typename T, typename... Types>
inline constexpr T && get(variant<Types...> && v) noexcept(BASE_NO_EXCEPTIONS)
{
  auto * ptr = v.template get<T>();
  base::panic_if("wrong variant type to get", [ptr] { return !ptr; });
  return base::move(*ptr);
}

template <typename T, typename... Types>
inline constexpr const T & get(const variant<Types...> & v) noexcept(BASE_NO_EXCEPTIONS)
{
  const auto * ptr = v.template get<T>();
  base::panic_if("wrong variant type to get", [ptr] { return !ptr; });
  return *ptr;
}

template <typename T, typename... Types>
inline constexpr const T && get(const variant<Types...> && v) noexcept(BASE_NO_EXCEPTIONS)
{
  const auto * ptr = v.template get<T>();
  base::panic_if("wrong variant type to get", [ptr] { return !ptr; });
  return base::move(*ptr);
}

}  // namespace apex::base

#endif  // APEX_BASE_NUCLEUS_DETAIL_VARIANT_INL
