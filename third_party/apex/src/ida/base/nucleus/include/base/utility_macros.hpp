/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Helper macros

#ifndef APEX_BASE_NUCLEUS_UTILITY_MACROS_HPP
#define APEX_BASE_NUCLEUS_UTILITY_MACROS_HPP

/*
 AXIVION DISABLE STYLE MisraC++2023-19.0.2: Reason: Code Quality (Functional suitability),
 Justification: We accept a function-like macros since they don't require the type-checking and this
 Macro cannot be replace by constexpr functions
 */
// NOLINTBEGIN(*-macro-usage)

#if defined(__cpp_exceptions)
  #define BASE_NO_EXCEPTIONS false
  #if !__cpp_exceptions
    #define BASE_NO_EXCEPTIONS true
  #endif
#endif

#if BASE_NO_EXCEPTIONS == false
  #define BASE_TRY try
  #define BASE_CATCH(x) catch (x)
#else
  #define BASE_TRY if (true)
  #define BASE_CATCH(x) if (false)
#endif

#ifdef NDEBUG
  #define PRINT_STACKTRACE false
  #define BASE_RELEASE
#else
  #define PRINT_STACKTRACE true
#endif

// NOLINTEND(*-macro-usage)
// AXIVION ENABLE STYLE MisraC++2023-19.0.2

#endif  // APEX_BASE_NUCLEUS_UTILITY_MACROS_HPP
