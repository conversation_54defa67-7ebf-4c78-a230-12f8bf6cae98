/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.

#include "ida/base/stacktrace/stacktrace.hpp"

#include <cxxabi.h>
#include <dlfcn.h>
#ifndef QNX
  #include <execinfo.h>
#endif

#include <cstdio>
#include <cstdlib>
#include <cstring>

namespace apex::base
{
/*
AXIVION DISABLE STYLE MisraC++2023-30.0.1: Reason: Code Quality (functional suitability),
Justification: This file is not used in any ASIL application and purely for development
*/
void print_stack_trace() noexcept
{
#ifndef QNX
  constexpr int MAX_FRAMES = 128;
  void * stack_frames[MAX_FRAMES];
  /*
   AXIVION Next CodeLine MisraC++2023-7.11.2: Reason: Code Quality (Functional suitability),
   Justification: Acceptable Array to pointer decay since the array size is passed to backtrace
   function.
   */
  int num_frames = backtrace(stack_frames, MAX_FRAMES);
  char ** symbols = backtrace_symbols(stack_frames, num_frames);
  if (symbols == nullptr) {
    (void)fprintf(stderr, "Failed to get backtrace symbols\n");
    return;
  }

  (void)fprintf(stderr, "    #### STACKTRACE START ####\n\n");
  bool past_main = false;
  bool recommend_linkopts = false;
  for (int i = 0; i < num_frames; ++i) {
    Dl_info info;
    if (dladdr(stack_frames[i], &info) != 0 && info.dli_sname != nullptr) {
      int result;
      char * demangled = abi::__cxa_demangle(info.dli_sname, nullptr, nullptr, &result);
      if (result == 0) {
        (void)fprintf(stderr, "    #%i %s\n", i, demangled);
        /*
         AXIVION Next CodeLine MisraC++2023-21.6.2: Reason: Code Quality (Functional suitability),
         Justification: ::free from <cstdlib> should be used for the stack trace.
         */
        ::free(demangled);
        continue;
      }
      /*
       AXIVION Next CodeLine MisraC++2023-21.2.2: Reason: Code Quality (Functional suitability),
       Justification: ::strncmp should be used for the stack trace.
       */
      past_main = (past_main) || (::strncmp(info.dli_sname, "main", 4U) == 0);
      if (!past_main) {
        recommend_linkopts = true;
      }
    }
    (void)fprintf(stderr, "    #%i %s\n", i, symbols[i]);
  }
  /*
   AXIVION Next CodeLine MisraC++2023-21.6.2: Reason: Code Quality (Functional suitability),
   Justification: ::free from <cstdlib> should be used for the stack trace.
   */
  ::free(symbols);
  symbols = nullptr;
  (void)fprintf(stderr, "\n    #### STACKTRACE END ####\n");

  if (recommend_linkopts) {
    (void)fprintf(stderr,
                  "Be sure to add linkopts = [\"-rdynamic\"] to your target to get symbol names\n");
  }
#else
  (void)fprintf(stderr, "Stacktrace printing is not available on QNX\n");
#endif
}
// AXIVION Enable MisraC++2023-30.0.1
}  // namespace apex::base
