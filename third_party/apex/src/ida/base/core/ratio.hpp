/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Definitions of ratios

#ifndef APEX_BASE_CORE_RATIO_HP
#define APEX_BASE_CORE_RATIO_HP

#include <ratio>

#include "ida/base/core/def.hpp"

namespace apex::base
{

template <ptrdiff_t Numerator, ptrdiff_t Denominator>
using ratio = std::ratio<Numerator, Denominator>;

template <typename R1, typename R2>
using ratio_add = std::ratio_add<R1, R2>;

template <typename R1, typename R2>
using ratio_subtract = std::ratio_subtract<R1, R2>;

template <typename R1, typename R2>
using ratio_multiply = std::ratio_multiply<R1, R2>;

template <typename R1, typename R2>
using ratio_divide = std::ratio_divide<R1, R2>;

template <typename R1, typename R2>
using ratio_equal = std::ratio_equal<R1, R2>;

template <typename R1, typename R2>
using ratio_not_equal = std::ratio_not_equal<R1, R2>;

template <typename R1, typename R2>
using ratio_less = std::ratio_less<R1, R2>;

template <typename R1, typename R2>
using ratio_less_equal = std::ratio_less_equal<R1, R2>;

template <typename R1, typename R2>
using ratio_greater = std::ratio_greater<R1, R2>;

template <typename R1, typename R2>
using ratio_greater_equal = std::ratio_greater_equal<R1, R2>;

using pico = std::pico;
using nano = std::nano;
using micro = std::micro;
using milli = std::milli;
using centi = std::centi;
using deci = std::deci;
using unit = ratio<1, 1>;
using deca = std::deca;
using hecto = std::hecto;
using kilo = std::kilo;
using mega = std::mega;
using giga = std::giga;
using tera = std::tera;
using peta = std::peta;

}  // namespace apex::base

#endif  // APEX_BASE_CORE_RATIO_HP
