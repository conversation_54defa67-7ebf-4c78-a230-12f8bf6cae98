/// \copyright Copyright 2017 The Abseil Authors.
///
/// Licensed under the Apache License, Version 2.0 (the "License");
/// you may not use this file except in compliance with the License.
/// You may obtain a copy of the License at
///
///      https://www.apache.org/licenses/LICENSE-2.0
///
/// Unless required by applicable law or agreed to in writing, software
/// distributed under the License is distributed on an "AS IS" BASIS,
/// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
/// See the License for the specific language governing permissions and
/// limitations under the License.
///
/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
///
/// \file
/// \brief This file defines dynamic annotations for use with dynamic analysis tool such as
/// sanitizers, valgrind, etc.
///
/// Implementation is copied from
/// https://github.com/abseil/abseil-cpp/blob/master/absl/base/dynamic_annotations.h with some
/// adaptations.
///
/// Dynamic annotation is a source code annotation that affects the generated code (that is, the
/// annotation is not a comment). Each such annotation is attached to a particular instruction
/// and/or to a particular object (address) in the program.
///
/// The annotations that should be used by users are macros in all upper-case
/// (e.g., IDA_BASE_ANNOTATE_THREAD_NAME).
///
/// This file supports the following configurations:
/// - Dynamic Annotations enabled (with static thread-safety warnings disabled).
///   In this case, macros expand to functions implemented by Thread Sanitizer,
///   when building with TSan. When not provided an external implementation,
///   dynamic_annotations.hpp provides no-op implementations.
///
/// - Static Clang thread-safety warnings enabled.
///   When building with a Clang compiler that supports thread-safety warnings,
///   a subset of annotations can be statically-checked at compile-time. We
///   expand these macros to static-inline functions that can be analyzed for
///   thread-safety, but afterwards elided when building the final binary.
///
/// - All annotations are disabled.
///   If neither Dynamic Annotations nor Clang thread-safety warnings are
///   enabled, then all annotation-macros expand to empty.

#ifndef APEX_BASE_CORE_DYNAMIC_ANNOTATIONS_HPP
#define APEX_BASE_CORE_DYNAMIC_ANNOTATIONS_HPP

/*
 AXIVION DISABLE STYLE MisraC++2023-19.0.2: Reason: Code Quality (Functional suitability),
 Justification: We accept a function-like macros for
 builds with Sanitizers
 */

#ifdef __has_feature
  #define IDA_BASE_HAS_FEATURE(f) __has_feature(f)
#else
  #define IDA_BASE_HAS_FEATURE(f) 0
#endif

#ifdef IDA_BASE_HAVE_THREAD_SANITIZER
  #error "IDA_BASE_HAVE_THREAD_SANITIZER cannot be directly set."
#elif defined(__SANITIZE_THREAD__)
  #define IDA_BASE_HAVE_THREAD_SANITIZER 1
#elif IDA_BASE_HAS_FEATURE(thread_sanitizer)
  #define IDA_BASE_HAVE_THREAD_SANITIZER 1
#endif

// -------------------------------------------------------------------------
// Decide which features are enabled.

#ifdef IDA_BASE_HAVE_THREAD_SANITIZER

  #define IDA_BASE_INTERNAL_RACE_ANNOTATIONS_ENABLED 1
  #define IDA_BASE_INTERNAL_READS_ANNOTATIONS_ENABLED 1
  #define IDA_BASE_INTERNAL_WRITES_ANNOTATIONS_ENABLED 1
  #define IDA_BASE_INTERNAL_ANNOTALYSIS_ENABLED 0
  #define IDA_BASE_INTERNAL_READS_WRITES_ANNOTATIONS_ENABLED 1

#else

  #define IDA_BASE_INTERNAL_RACE_ANNOTATIONS_ENABLED 0
  #define IDA_BASE_INTERNAL_READS_ANNOTATIONS_ENABLED 0
  #define IDA_BASE_INTERNAL_WRITES_ANNOTATIONS_ENABLED 0

  // Clang provides limited support for static thread-safety analysis through a
  // feature called Annotalysis. We configure macro-definitions according to
  // whether Annotalysis support is available. When running in opt-mode, GCC
  // will issue a warning, if these attributes are compiled. Only include them
  // when compiling using Clang.

  #if defined(__clang__)
    #define IDA_BASE_INTERNAL_ANNOTALYSIS_ENABLED 1
    #if !defined(SWIG)
      #define IDA_BASE_INTERNAL_IGNORE_READS_ATTRIBUTE_ENABLED 1
    #endif
  #else
    #define IDA_BASE_INTERNAL_ANNOTALYSIS_ENABLED 0
  #endif

  // Read/write annotations are enabled in Annotalysis mode; disabled otherwise.
  #define IDA_BASE_INTERNAL_READS_WRITES_ANNOTATIONS_ENABLED IDA_BASE_INTERNAL_ANNOTALYSIS_ENABLED

#endif  // IDA_BASE_HAVE_THREAD_SANITIZER

#ifdef __cplusplus
  #define IDA_BASE_INTERNAL_BEGIN_EXTERN_C extern "C" {
  #define IDA_BASE_INTERNAL_END_EXTERN_C }  // extern "C"
  /*
   AXIVION Next CodeLine MisraC++2023-19.3.4: Reason: Code Quality (Usability), Justification:
   brackets around macro parameter would lead to compile time failures in this case
   */
  #define IDA_BASE_INTERNAL_GLOBAL_SCOPED(F) ::F
  #define IDA_BASE_INTERNAL_STATIC_INLINE inline
#else
  #define IDA_BASE_INTERNAL_BEGIN_EXTERN_C  // empty
  #define IDA_BASE_INTERNAL_END_EXTERN_C  // empty
  #define IDA_BASE_INTERNAL_GLOBAL_SCOPED(F) F
  #define IDA_BASE_INTERNAL_STATIC_INLINE static inline
#endif

// -------------------------------------------------------------------------
// Define race annotations.

#if IDA_BASE_INTERNAL_RACE_ANNOTATIONS_ENABLED == 1
  // Annotations that suppress errors. It is usually better to express the
  // program's synchronization using the other annotations, but these can be used
  // when all else fails.

  // Report that we may have a benign race at `pointer`, with size
  // "sizeof(*(pointer))". `pointer` must be a non-void* pointer. Insert at the
  // point where `pointer` has been allocated, preferably close to the point
  // where the race happens. See also IDA_BASE_ANNOTATE_BENIGN_RACE_STATIC.
  #define IDA_BASE_ANNOTATE_BENIGN_RACE(pointer, description) \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateBenignRaceSized)  \
    (__FILE__, __LINE__, pointer, sizeof(*(pointer)), description)

  // Same as IDA_BASE_ANNOTATE_BENIGN_RACE(`address`, `description`), but applies to
  // the memory range [`address`, `address`+`size`).
  #define IDA_BASE_ANNOTATE_BENIGN_RACE_SIZED(address, size, description) \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateBenignRaceSized)              \
    (__FILE__, __LINE__, address, size, description)

  // Enable (`enable`!=0) or disable (`enable`==0) race detection for all threads.
  // This annotation could be useful if you want to skip expensive race analysis
  // during some period of program execution, e.g. during initialization.
  #define IDA_BASE_ANNOTATE_ENABLE_RACE_DETECTION(enable)        \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateEnableRaceDetection) \
    (__FILE__, __LINE__, enable)

  // -------------------------------------------------------------
  // Annotations useful for debugging.

  // Report the current thread `name` to a race detector.
  #define IDA_BASE_ANNOTATE_THREAD_NAME(name) \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateThreadName)(__FILE__, __LINE__, name)

  // -------------------------------------------------------------
  // Annotations useful when implementing locks. They are not normally needed by
  // modules that merely use locks. The `lock` argument is a pointer to the lock
  // object.

  // Report that a lock has been created at address `lock`.
  #define IDA_BASE_ANNOTATE_RWLOCK_CREATE(lock) \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateRWLockCreate)(__FILE__, __LINE__, lock)

  // Report that a linker initialized lock has been created at address `lock`.
  #ifdef IDA_BASE_HAVE_THREAD_SANITIZER
    #define IDA_BASE_ANNOTATE_RWLOCK_CREATE_STATIC(lock)          \
      IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateRWLockCreateStatic) \
      (__FILE__, __LINE__, lock)
  #else
    #define IDA_BASE_ANNOTATE_RWLOCK_CREATE_STATIC(lock) IDA_BASE_ANNOTATE_RWLOCK_CREATE(lock)
  #endif

  // Report that the lock at address `lock` is about to be destroyed.
  #define IDA_BASE_ANNOTATE_RWLOCK_DESTROY(lock) \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateRWLockDestroy)(__FILE__, __LINE__, lock)

  // Report that the lock at address `lock` has been acquired.
  // `is_w`=1 for writer lock, `is_w`=0 for reader lock.
  #define IDA_BASE_ANNOTATE_RWLOCK_ACQUIRED(lock, is_w)     \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateRWLockAcquired) \
    (__FILE__, __LINE__, lock, is_w)

  // Report that the lock at address `lock` is about to be released.
  // `is_w`=1 for writer lock, `is_w`=0 for reader lock.
  #define IDA_BASE_ANNOTATE_RWLOCK_RELEASED(lock, is_w)     \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateRWLockReleased) \
    (__FILE__, __LINE__, lock, is_w)

  // Apply IDA_BASE_ANNOTATE_BENIGN_RACE_SIZED to a static variable `static_var`.
  #define IDA_BASE_ANNOTATE_BENIGN_RACE_STATIC(static_var, description)   \
    namespace                                                             \
    {                                                                     \
    class static_var##_annotator                                          \
    {                                                                     \
    public:                                                               \
      static_var##_annotator()                                            \
      {                                                                   \
        IDA_BASE_ANNOTATE_BENIGN_RACE_SIZED(                              \
          &static_var, sizeof(static_var), #static_var ": " description); \
      }                                                                   \
    };                                                                    \
    static static_var##_annotator the##static_var##_annotator;            \
    }  // namespace

// Function prototypes of annotations provided by the compiler-based sanitizer
// implementation.
IDA_BASE_INTERNAL_BEGIN_EXTERN_C
void AnnotateRWLockCreate(const char * file, int line, const volatile void * lock);
void AnnotateRWLockCreateStatic(const char * file, int line, const volatile void * lock);
void AnnotateRWLockDestroy(const char * file, int line, const volatile void * lock);
void AnnotateRWLockAcquired(const char * file, int line, const volatile void * lock, long is_w);
void AnnotateRWLockReleased(const char * file, int line, const volatile void * lock, long is_w);
void AnnotateBenignRace(const char * file,
                        int line,
                        const volatile void * address,
                        const char * description);
void AnnotateBenignRaceSized(const char * file,
                             int line,
                             const volatile void * address,
                             size_t size,
                             const char * description);
void AnnotateThreadName(const char * file, int line, const char * name);
void AnnotateEnableRaceDetection(const char * file, int line, int enable);
IDA_BASE_INTERNAL_END_EXTERN_C

#else  // IDA_BASE_INTERNAL_RACE_ANNOTATIONS_ENABLED == 0

  #define IDA_BASE_ANNOTATE_RWLOCK_CREATE(lock)  // empty
  #define IDA_BASE_ANNOTATE_RWLOCK_CREATE_STATIC(lock)  // empty
  #define IDA_BASE_ANNOTATE_RWLOCK_DESTROY(lock)  // empty
  #define IDA_BASE_ANNOTATE_RWLOCK_ACQUIRED(lock, is_w)  // empty
  #define IDA_BASE_ANNOTATE_RWLOCK_RELEASED(lock, is_w)  // empty
  #define IDA_BASE_ANNOTATE_BENIGN_RACE(address, description)  // empty
  #define IDA_BASE_ANNOTATE_BENIGN_RACE_SIZED(address, size, description)  // empty
  #define IDA_BASE_ANNOTATE_THREAD_NAME(name)  // empty
  #define IDA_BASE_ANNOTATE_ENABLE_RACE_DETECTION(enable)  // empty
  #define IDA_BASE_ANNOTATE_BENIGN_RACE_STATIC(static_var, description)  // empty

#endif  // IDA_BASE_INTERNAL_RACE_ANNOTATIONS_ENABLED

// -------------------------------------------------------------------------
// Define IGNORE_READS_BEGIN/_END attributes.

#if defined(IDA_BASE_INTERNAL_IGNORE_READS_ATTRIBUTE_ENABLED)

  #define IDA_BASE_INTERNAL_IGNORE_READS_BEGIN_ATTRIBUTE __attribute((exclusive_lock_function("*")))
  #define IDA_BASE_INTERNAL_IGNORE_READS_END_ATTRIBUTE __attribute((unlock_function("*")))

#else  // !defined(IDA_BASE_INTERNAL_IGNORE_READS_ATTRIBUTE_ENABLED)

  #define IDA_BASE_INTERNAL_IGNORE_READS_BEGIN_ATTRIBUTE  // empty
  #define IDA_BASE_INTERNAL_IGNORE_READS_END_ATTRIBUTE  // empty

#endif  // defined(IDA_BASE_INTERNAL_IGNORE_READS_ATTRIBUTE_ENABLED)

// -------------------------------------------------------------------------
// Define IGNORE_READS_BEGIN/_END annotations.

#if IDA_BASE_INTERNAL_READS_ANNOTATIONS_ENABLED == 1
  // Request the analysis tool to ignore all reads in the current thread until
  // IDA_BASE_ANNOTATE_IGNORE_READS_END is called. Useful to ignore intentional racey
  // reads, while still checking other reads and all writes.
  // See also IDA_BASE_ANNOTATE_UNPROTECTED_READ.
  #define IDA_BASE_ANNOTATE_IGNORE_READS_BEGIN()              \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateIgnoreReadsBegin) \
    (__FILE__, __LINE__)

  // Stop ignoring reads.
  #define IDA_BASE_ANNOTATE_IGNORE_READS_END()              \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateIgnoreReadsEnd) \
    (__FILE__, __LINE__)

// Function prototypes of annotations provided by the compiler-based sanitizer
// implementation.
IDA_BASE_INTERNAL_BEGIN_EXTERN_C
void AnnotateIgnoreReadsBegin(const char * file,
                              int line) IDA_BASE_INTERNAL_IGNORE_READS_BEGIN_ATTRIBUTE;
void AnnotateIgnoreReadsEnd(const char * file,
                            int line) IDA_BASE_INTERNAL_IGNORE_READS_END_ATTRIBUTE;
IDA_BASE_INTERNAL_END_EXTERN_C

#elif defined(IDA_BASE_INTERNAL_ANNOTALYSIS_ENABLED)

  // When Annotalysis is enabled without Dynamic Annotations, the use of
  // static-inline functions allows the annotations to be read at compile-time,
  // while still letting the compiler elide the functions from the final build.

  #define IDA_BASE_ANNOTATE_IGNORE_READS_BEGIN() \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(IdaBaseInternalAnnotateIgnoreReadsBegin)()

  #define IDA_BASE_ANNOTATE_IGNORE_READS_END() \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(IdaBaseInternalAnnotateIgnoreReadsEnd)()

// No-op function is intended
/*
 AXIVION DISABLE STYLE MisraC++2023-6.0.3: Must be in global namespace
 */
IDA_BASE_INTERNAL_STATIC_INLINE void IdaBaseInternalAnnotateIgnoreReadsBegin()
  IDA_BASE_INTERNAL_IGNORE_READS_BEGIN_ATTRIBUTE
{
}

IDA_BASE_INTERNAL_STATIC_INLINE void IdaBaseInternalAnnotateIgnoreReadsEnd()
  IDA_BASE_INTERNAL_IGNORE_READS_END_ATTRIBUTE
{
}
  // AXIVION ENABLE STYLE MisraC++2023-6.0.3

#else
  #define IDA_BASE_ANNOTATE_IGNORE_READS_BEGIN() ((void)0)  // empty
  #define IDA_BASE_ANNOTATE_IGNORE_READS_END() ((void)0)  // empty
#endif

// -------------------------------------------------------------------------
// Define IGNORE_WRITES_BEGIN/_END annotations.

#if IDA_BASE_INTERNAL_WRITES_ANNOTATIONS_ENABLED == 1

  // Similar to IDA_BASE_ANNOTATE_IGNORE_READS_BEGIN, but ignore writes instead.
  #define IDA_BASE_ANNOTATE_IGNORE_WRITES_BEGIN() \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateIgnoreWritesBegin)(__FILE__, __LINE__)

  // Stop ignoring writes.
  #define IDA_BASE_ANNOTATE_IGNORE_WRITES_END() \
    IDA_BASE_INTERNAL_GLOBAL_SCOPED(AnnotateIgnoreWritesEnd)(__FILE__, __LINE__)

// Function prototypes of annotations provided by the compiler-based sanitizer
// implementation.
IDA_BASE_INTERNAL_BEGIN_EXTERN_C
void AnnotateIgnoreWritesBegin(const char * file, int line);
void AnnotateIgnoreWritesEnd(const char * file, int line);
IDA_BASE_INTERNAL_END_EXTERN_C

#else
  #define IDA_BASE_ANNOTATE_IGNORE_WRITES_BEGIN() ((void)0)  // empty
  #define IDA_BASE_ANNOTATE_IGNORE_WRITES_END() ((void)0)  // empty
#endif

// AXIVION ENABLE STYLE MisraC++2023-19.0.2

// -------------------------------------------------------------------------
// Undefine the macros intended only for this file.

#undef IDA_BASE_INTERNAL_RACE_ANNOTATIONS_ENABLED
#undef IDA_BASE_INTERNAL_READS_ANNOTATIONS_ENABLED
#undef IDA_BASE_INTERNAL_WRITES_ANNOTATIONS_ENABLED
#undef IDA_BASE_INTERNAL_ANNOTALYSIS_ENABLED
#undef IDA_BASE_INTERNAL_READS_WRITES_ANNOTATIONS_ENABLED
#undef IDA_BASE_INTERNAL_BEGIN_EXTERN_C
#undef IDA_BASE_INTERNAL_END_EXTERN_C
#undef IDA_BASE_INTERNAL_STATIC_INLINE

#endif  // APEX_BASE_CORE_DYNAMIC_ANNOTATIONS_HPP
