/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.

#ifndef APEX_BASE_TEST_HELPERS_LOGGER_UTIL_HPP
#define APEX_BASE_TEST_HELPERS_LOGGER_UTIL_HPP

#include "base/optional.hpp"
#include "iceoryx_hoofs/testing/testing_logger.hpp"

namespace apex::test
{
/**
 * @brief Searches log messages for the given substring.
 * @note The search is performed backwards: from the latest to the earliest messages and stops
 * either when the matching message is found, or when the search end position is reached.
 *
 * @param [in] level Message log level
 * @param [in] expected_string The substring to search for
 * @param [in] maybe_start_pos Search starting position (inclusive). Specify when you want to skip
 * latest log messages. nullopt means search starting from the last log message.
 * @param [in] maybe_end_pos Search end position (exclusive). Specify when you want to omit earlier
 * log messages. nullopt means search up to the earliest log message.
 * @param [out] pos If the message is found and `pos` is not `nullptr`, it is updated with the index
 * of the message.
 * @return Test assertion result containing the description of the search outcome.
 */
::testing::AssertionResult checkIfStringIsInAnyLogMessage(
  const base::log::LogLevel level,
  std::string_view expected_string,
  base::optional<size_t> maybe_start_pos = base::nullopt,
  base::optional<size_t> maybe_end_pos = base::nullopt,
  size_t * const pos = nullptr);
}  // namespace apex::test

#endif  // APEX_BASE_TEST_HELPERS_LOGGER_UTIL_HPP
