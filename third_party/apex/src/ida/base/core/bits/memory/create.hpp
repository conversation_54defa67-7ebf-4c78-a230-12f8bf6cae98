/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Helper functions for the creation of objects

#ifndef APEX_BASE_CORE_BITS_MEMORY_CREATE_HPP
#define APEX_BASE_CORE_BITS_MEMORY_CREATE_HPP

//==========================================================================
//                       expected<T, ErrorT> base::create<T>()
//
// This function provides a unified construction call to create types that
// may or may not report failures in their construction.
//
// A type can report construction failure by taking an base::optional<ErrorType> &
// argument and assigning an error object to it.
//
// To inform base::create about this constructor, the type needs to announce the
// used error type by defining a public constructor_error type alias.
//
// The constructor further needs to take an base::badge<base::creator> as its first argument in
// order to ensure that only the base::create variants can call the constructor. Since base::create
// enforces that an object with construction failure is destroyed immediately after creation, the
// type T may assume that only its destructor will be called after an error is returned from
// construction; there is thus no need to check for invalid state in any other method.
//
// For example:
//
// class MyObject {
// public:
//   using constructor_error = ErrorT;  // Can be left out if ErrorT is base::error
//   MyObject(base::badge<base::creator>, base::optional<ErrorT> & err, bool arg) {
//     if (some_failure_condition_holds) {
//       err.emplace("Some failure happened");
//       return;
//     }
//     // some initialization
//     if (initialization_failed) {
//       err.emplace("Some initialization failed");
//     }
//   }
// };
//
//
// base::create<T>() and its sibling base::create_in_place<T> will detect if T is such a type, and,
// if its construction fails, will propagate the error back through its result.
//
// For types that do not report construction failure, base::create<T>() will
// simply construct them and return an OK.
//
//
//  See also:
//    - test_create.cpp for more examples of how to define such a constructor
//    - base::allocator::create_unique<T>()
//    - base::allocator::create_shared<T>()

//
//==========================================================================/

#include "ida/base/core/error.hpp"
#include "ida/base/core/expected.hpp"
#include "ida/base/core/optional.hpp"
#include "ida/base/core/type_traits.hpp"
#include "ida/base/core/utility.hpp"

namespace apex::base
{

class creator;

/**
 * @brief Determine if a type declares a public constructor_error field
 * @tparam T The type
 */
template <typename T>
struct declares_constructor_error
{
private:
  template <typename U>
  static auto test(uint8_t) -> typename U::constructor_error;

  template <typename U>
  static auto test(...) -> void;

public:
  using constructor_error = decltype(test<T>(0));
  static constexpr bool value{!is_void<constructor_error>::value};
};

/**
 * @brief Determine if a type has a T(badge<creator>, optional<ErrorType> &, ...) constructor
 *
 * @tparam T The type
 * @tparam ErrorType The error type
 * @tparam Args The type's creation arguments
 */
template <typename T, typename ErrorType, typename... Args>
using has_optional_error_ref_constructor =
  is_constructible<T, badge<creator>, optional<ErrorType> &, Args...>;

/**
 * @brief Determine if a type has a static create method
 *
 * @tparam T The type to be checked
 * @tparam Args The 'T::create' arguments
 * @deprecated Support for static create methods will be removed in the future
 */
template <typename T, typename... Args>
struct has_static_create_method
{
private:
  template <typename U, typename Expected>
  struct returns_expected : base::false_type
  {
  };

  template <typename U, typename Error>
  struct returns_expected<U, expected<U, Error>> : base::true_type
  {
  };

  template <typename U, typename... As>
  static auto test(uint8_t) -> returns_expected<U, decltype(U::create(base::declval<As>()...))>;

  template <typename U, typename... As>
  static auto test(...) -> base::false_type;

public:
  static constexpr bool value{decltype(test<T, Args...>(0))::value};
};


/**
 * @brief A namespacing struct collecting those implementations of base::create that need a badge
 *
 * The main point of this class is to simplify badge declarations in other classes. That is,
 * by specifying "base::badge<base::creator>", classes can easily give access to their
 * constructors from base::create but prevent access to these constructors from elsewhere
 */
class creator
{
public:
  template <typename T, typename ErrorType, typename... Args>
  static auto create_using_optional_error_ref(Args &&... args) -> base::expected<T, ErrorType>
  {
    base::optional<ErrorType> maybe_error;

    // Check that we can't pass an rvalue in; this suggests that the user forgot to take
    // by reference instead of by value, which would mean that we never receive the reported
    // error
    static_assert(
      is_constructible<T, badge<creator>, base::optional<ErrorType> &, Args...>::value &&
        !is_constructible<T, badge<creator>, base::optional<ErrorType> &&, Args...>::value,
      "constructor accepts base::optional<ErrorType> rvalue as first argument. "
      "Did you mean to take a reference?");

    expected<T, ErrorType> ret{
      in_place, badge<creator>{}, maybe_error, base::forward<Args>(args)...};
    if (maybe_error) {
      // Construction failed. Return an error, thereby implicitly destroying the object when "ret"
      // goes out of scope
      return base::err(base::move(*maybe_error));
    }
    return ret;
  }

  template <typename StorageRef, typename T, typename ErrorType, typename... Args>
  static auto create_in_place_using_optional_error_ref(StorageRef & storage, Args &&... args)
    -> base::expected<void, ErrorType>
  {
    base::optional<ErrorType> maybe_error;

    static_assert(
      is_constructible<T, badge<creator>, base::optional<ErrorType> &, Args...>::value &&
        !is_constructible<T, badge<creator>, base::optional<ErrorType> &&, Args...>::value,
      "constructor accepts base::optional<ErrorType> rvalue as first argument. "
      "Did you mean to take a reference?");

    storage.emplace(badge<creator>{}, maybe_error, base::forward<Args>(args)...);
    if (maybe_error) {
      // Construction failed. Destroy the object again and return the error instead
      storage.reset();
      return base::err(base::move(*maybe_error));
    }
    return base::ok();
  }
};

namespace detail
{

/**
 * @brief Create an object in-place into an arbitrary storage type
 * @tparam StorageRef The storage type (which must support "emplace" and "reset")
 * @param storage The storage into which to construct
 * @param args The constructor arguments
 * @return an expected<void, ErrorType> reporting the construction result.
 */
template <typename StorageRef, typename T, typename... Args>
static auto create_in_place_into_any_storage(StorageRef & storage, Args &&... args)
{
  // Go through the different creation methods and pick the one that works
  if constexpr (is_constructible<T, Args...>::value) {
    // The element has a constructor that cannot fail. This is always preferred over any other
    // method
    storage.emplace(base::forward<Args>(args)...);
    return expected<void, false_type>{in_place};
  } else if constexpr (declares_constructor_error<T>::value) {
    // An explicit constructor error was defined. This reflects an explicit user choice and is
    // respected above all other potentially-failing options.
    // Use the optional<ErrorType>&-style constructor and fail if it does not exist
    using ErrorType = typename declares_constructor_error<T>::constructor_error;
    if constexpr (has_optional_error_ref_constructor<T, ErrorType, Args...>::value) {
      return creator::create_in_place_using_optional_error_ref<StorageRef, T, ErrorType, Args...>(
        storage, base::forward<Args>(args)...);
    } else {
      static_assert(dependent_false<T>::value,
                    "constructor_error declaration without a matching available constructor");
    }
  } else if constexpr (has_optional_error_ref_constructor<T, base::error, Args...>::value) {
    // In Apex.Ida, many classes use base::error as the error type. So if we can find
    // an optional<base::error>&-style constructor, use it.
    return creator::create_in_place_using_optional_error_ref<StorageRef, T, base::error, Args...>(
      storage, base::forward<Args>(args)...);
  } else if constexpr (has_static_create_method<T, Args...>::value) {
    static_assert(dependent_false<T>::value,
                  "in-place creation is not supported with T::create methods");
  } else {
    static_assert(dependent_false<T>::value, "Don't know how to construct this type");
  }
}

/**
 * @brief A utility class that provides "emplace" and "reset" methods for an uninitialized pointer.
 *        This allows passing raw references into create_in_place_into_any_storage
 * @param ref A reference to the object which will be initialized at a later point in time
 * @tparam T The type of object which the pointer points to
 *
 * @note The destructor of T is not automatically called by the destructor of emplace_wrapper.
 */
template <typename T>
class emplace_wrapper
{
public:
  explicit emplace_wrapper(T & ref) noexcept : m_ref(ref) {}
  ~emplace_wrapper() noexcept = default;
  emplace_wrapper(const emplace_wrapper &) = delete;
  emplace_wrapper & operator=(const emplace_wrapper &) = delete;
  emplace_wrapper(emplace_wrapper &&) noexcept = delete;
  emplace_wrapper & operator=(emplace_wrapper &&) noexcept = delete;

  template <typename... Args>
  void emplace(Args &&... args)
  {
    if (m_constructor_called) {
      /*
       AXIVION Next Construct MisraC++2023-21.6.3: Reason: Code Quality (Functional suitability),
       Justification: It is acceptable to call destructor since the custom
       allocator that doesn't use the dynamic memory allocation is not used.
       */
      m_ref.~T();
    }
    /*
     AXIVION Next Construct MisraC++2023-21.6.3: Reason: Code Quality
     (Functional suitability), Justification: It is acceptable to call "new" since the custom
     allocator that doesn't use the dynamic memory allocation is not used.
     */
    new (&m_ref) T(base::forward<Args>(args)...);
    m_constructor_called = true;
  };

  void reset() noexcept
  {
    if (m_constructor_called) {
      /*
       AXIVION Next Construct MisraC++2023-21.6.3: Reason: Code Quality (Functional suitability),
       Justification: It is acceptable to call destructor since the custom
       allocator that doesn't use the dynamic memory allocation is not used.
       */
      m_ref.~T();
      m_constructor_called = false;
    }
  }

private:
  T & m_ref;
  bool m_constructor_called{false};
};

/**
 * @brief The implementation of the non-optional create_in_place function.
 *
 * The only reason this is extracted to a separate function so we have a place to attach enable_if.
 * create_in_place has variadic arguments *and* an automatically-derived return type, meaning there
 * is literally no place one could attach enable_if to.
 */
template <typename T, typename... Args>
auto create_in_place_impl(T & storage,
                          typename enable_if<!is_optional<T>::value, bool>::type,
                          Args &&... args)
{
  detail::emplace_wrapper<T> wrapper(storage);
  return detail::create_in_place_into_any_storage<detail::emplace_wrapper<T>, T, Args...>(
    wrapper, base::forward<Args>(args)...);
}

/**
 * @brief The implementation of the optional create_in_place function.
 *
 * The only reason this is extracted to a separate function so we have a place to attach enable_if.
 * create_in_place has variadic arguments *and* an automatically-derived return type, meaning there
 * is literally no place one could attach enable_if to.
 */
template <typename T, typename... Args>
auto create_in_place_impl(optional<T> & storage, bool, Args &&... args)
{
  return detail::create_in_place_into_any_storage<optional<T>, T, Args...>(
    storage, base::forward<Args>(args)...);
}

}  // namespace detail

/**
 * @brief Construct a type whose construction may fail
 *
 * @tparam T The type to create
 * @tparam Args Construction arguments
 * @param args Construction arguments
 * @return The result of the type's construction (as an expected<T, ErrorType>)
 */
template <typename T, typename... Args>
auto create(Args &&... args)
{
  // Go through the different creation methods and pick the one that works
  if constexpr (is_constructible<T, Args...>::value) {
    // The element has a constructor that cannot fail. This is always preferred over any other
    // method
    return base::expected<T, base::false_type>(in_place, base::forward<Args>(args)...);
  } else if constexpr (declares_constructor_error<T>::value) {
    // An explicit constructor error was defined. This reflects an explicit user choice and is
    // respected above all other potentially-failing options.
    // Use the optional<ErrorType>&-style constructor and fail if it does not exist
    using ErrorType = typename declares_constructor_error<T>::constructor_error;
    if constexpr (has_optional_error_ref_constructor<T, ErrorType, Args...>::value) {
      return creator::create_using_optional_error_ref<T, ErrorType, Args...>(
        base::forward<Args>(args)...);
    } else {
      static_assert(dependent_false<T>::value,
                    "constructor_error declaration without a matching available constructor");
    }
  } else if constexpr (has_optional_error_ref_constructor<T, base::error, Args...>::value) {
    // An optional<base::error>&-style constructor is available
    return creator::create_using_optional_error_ref<T, base::error, Args...>(
      base::forward<Args>(args)...);
  } else if constexpr (has_static_create_method<T, Args...>::value) {
    // A T::create method is available
    return T::create(base::forward<Args>(args)...);
  } else {
    static_assert(dependent_false<T>::value, "Don't know how to construct this type");
  }
}

/**
 * @brief In-place construct a type whose construction may fail
 *
 * @tparam T The type to create
 * @tparam Args Construction arguments
 * @param storage A reference to the pre-allocated storage area to fill in. If this method returns
 *                successfully, the storage area contains the created object. If the method returns
 *                an error, the contents of the storage area are unspecified.
 * @param args Construction arguments
 * @return The result of the type's construction as an expected<void, ErrorType>
 */
template <typename T, typename... Args>
auto create_in_place(T & storage, Args &&... args)
{
  return detail::create_in_place_impl<T, Args...>(storage, false, base::forward<Args>(args)...);
}

/**
 * @brief In-place construct an optional of a type whose construction may fail
 *
 * @tparam T The type to create
 * @tparam Args Construction arguments
 * @param storage An optional to fill in. When this method returns, the optional will either be
 * empty (if construction failed) or contain the initialized value (if construction succeeded)
 * @param args Construction arguments
 * @return The result of the type's construction as an expected<void, ErrorType>
 */
template <typename T, typename... Args>
auto create_in_place(optional<T> & storage, Args &&... args)
{
  return detail::create_in_place_impl<T, Args...>(storage, false, base::forward<Args>(args)...);
}

}  // namespace apex::base
#endif  // APEX_BASE_CORE_BITS_MEMORY_CREATE_HPP
