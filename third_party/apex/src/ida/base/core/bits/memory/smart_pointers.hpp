/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Smart pointer definitions

#ifndef APEX_BASE_CORE_BITS_MEMORY_SMART_POINTERS_HPP
#define APEX_BASE_CORE_BITS_MEMORY_SMART_POINTERS_HPP

#include <memory>

#include "ida/base/core/not_null.hpp"
#include "ida/base/core/utility.hpp"

namespace apex::base
{

template <typename T>
class allocator_delete;

template <typename T, typename Deleter = allocator_delete<T>>
using unique_ptr = std::unique_ptr<T, Deleter>;

template <typename T>
using unique_not_null = not_null<unique_ptr<T>>;

template <typename T>
unique_ptr<T> make_empty_unique_ptr()
{
  return std::unique_ptr<T, allocator_delete<T>>(nullptr, allocator_delete<T>{nullptr});
}

/**
 * @brief The shared pointer implementation
 * @warning Always create shared pointers through `allocator.make_shared` or
 * `allocator.create_shared`. Calling the `shared_ptr` constructor by hand may allocate a control
 * block on the system heap.
 */
template <typename T>
using shared_ptr = std::shared_ptr<T>;

template <typename T>
using weak_ptr = std::weak_ptr<T>;

template <typename T>
using shared_not_null = not_null<shared_ptr<T>>;

using std::atomic_exchange;
using std::atomic_exchange_explicit;
using std::atomic_is_lock_free;
using std::atomic_load;
using std::atomic_load_explicit;
using std::atomic_store;
using std::atomic_store_explicit;

/**========================================================================
 *                         Smart Pointer Casts
 *========================================================================**/

template <typename T, typename U>
auto static_pointer_cast(const shared_ptr<U> & ptr)
{
  return shared_ptr<T>(ptr, static_cast<T *>(ptr.get()));
}

template <typename T, typename U>
auto static_pointer_cast(shared_ptr<U> && ptr)
{
  return shared_ptr<T>(base::move(ptr), static_cast<T *>(ptr.get()));
}

template <typename T, typename U>
auto const_pointer_cast(const shared_ptr<U> & ptr)
{
  // NOLINTNEXTLINE(*-const-cast)
  return shared_ptr<T>(ptr, const_cast<T *>(ptr.get()));
}

template <typename T, typename U>
auto const_pointer_cast(shared_ptr<U> && ptr)
{
  // NOLINTNEXTLINE(*-const-cast)
  return shared_ptr<T>(base::move(ptr), const_cast<T *>(ptr.get()));
}

template <typename T, typename U>
auto reinterpret_pointer_cast(const shared_ptr<U> & ptr)
{
  // NOLINTNEXTLINE(*-reinterpret-cast)
  return shared_ptr<T>(ptr, reinterpret_cast<T *>(ptr.get()));
}

template <typename T, typename U>
auto reinterpret_pointer_cast(shared_ptr<U> && ptr)
{
  // NOLINTNEXTLINE(*-reinterpret-cast)
  return shared_ptr<T>(base::move(ptr), reinterpret_cast<T *>(ptr.get()));
}

}  // namespace apex::base
#endif  // APEX_BASE_CORE_BITS_MEMORY_SMART_POINTERS_HPP
