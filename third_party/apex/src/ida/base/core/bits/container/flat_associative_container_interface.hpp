/**
 * @copyright Copyright 2023 Apex.AI, Inc.
 * All rights reserved.
 */

#ifndef FLAT_ASSOCIATIVE_CONTAINER_INTERFACE_HPP
#define FLAT_ASSOCIATIVE_CONTAINER_INTERFACE_HPP

#include "ida/base/core/comparer.hpp"
#include "ida/base/core/def.hpp"
#include "ida/base/core/noncopyable.hpp"
#include "ida/base/core/ratio.hpp"
#include "ida/base/core/utility.hpp"
#include "ida/base/core/vector.hpp"

namespace apex::base
{
template <typename Derived, typename TValue, typename Comparer>
class flat_associative_container_interface : private apex::base::NonCopyable
{
protected:
  template <typename Self>
  class basic_iterator;
  ~flat_associative_container_interface() noexcept = default;

public:
  using size_type = size_t;
  using iterator = basic_iterator<Derived>;
  using const_iterator = basic_iterator<const Derived>;

  flat_associative_container_interface() = default;
  /**
   * @brief Return an iterator to the first element in the container
   *
   * The traversal order for container's iterators is unspecified.
   *
   * @return iterator A mutable iterator
   *
   * @see iterator-invalidation
   */
  [[nodiscard]] iterator begin() noexcept
  {
    return iterator{static_cast<Derived *>(this), self().m_entries.begin(), self().m_entries.end()};
  }

  /**
   * @brief Return an iterator to the first element in the container
   *
   * The traversal order for container's iterators is unspecified.
   *
   * @return const_iterator A const iterator
   *
   * @see iterator-invalidation
   */
  [[nodiscard]] const_iterator begin() const noexcept
  {
    return const_iterator{
      static_cast<const Derived *>(this), self().m_entries.cbegin(), self().m_entries.cend()};
  }

  /**
   * @brief Return an iterator to the first element in the container
   *
   * The traversal order for container's iterators is unspecified.
   *
   * @return const_iterator A const iterator
   *
   * @see iterator-invalidation
   */
  [[nodiscard]] const_iterator cbegin() const noexcept
  {
    return const_iterator{
      static_cast<const Derived *>(this), self().m_entries.cbegin(), self().m_entries.cend()};
  }

  /**
   * @brief Return an iterator to the end of the container (past the last element)
   *
   * The traversal order for container's iterators is unspecified.
   *
   * @return iterator A mutable iterator
   *
   * @see iterator-invalidation
   */
  [[nodiscard]] iterator end() noexcept
  {
    return iterator{static_cast<Derived *>(this), self().m_entries.end(), self().m_entries.end()};
  }

  /**
   * @brief Return an iterator to the the end of the container (past the last element)
   *
   * The traversal order for container's iterators is unspecified.
   *
   * @return const_iterator A mutable iterator
   *
   * @see iterator-invalidation
   */
  [[nodiscard]] const_iterator end() const noexcept
  {
    return const_iterator{
      static_cast<const Derived *>(this), self().m_entries.cend(), self().m_entries.cend()};
  }

  /**
   * @brief Return an iterator to the the end of the container (past the last element)
   *
   * The traversal order for container's iterators is unspecified.
   *
   * @return const_iterator A mutable iterator
   *
   * @see iterator-invalidation
   */
  [[nodiscard]] const_iterator cend() const noexcept
  {
    return const_iterator{
      static_cast<const Derived *>(this), self().m_entries.cend(), self().m_entries.cend()};
  }

  /**
   * @brief Return true if the container is empty
   *
   * @return true No keys are set in the container
   * @return false At least one key is set in the container
   */
  [[nodiscard]] constexpr bool empty() const noexcept
  {
    return m_size == 0;
  }

  /**
   * @brief Return true if the container is full
   *
   * @return true The number of entries has reach the max load factor
   * @return false The number of entries is below the max load factor
   */
  [[nodiscard]] constexpr bool full() const noexcept
  {
    return m_size == m_max_size;
  }

  /**
   * @brief Return the current number of unique elements
   *
   * @return size_type Number of unique keys
   */
  [[nodiscard]] constexpr size_type size() const noexcept
  {
    return m_size;
  }

  /**
   * @brief Return the maximum possible number of elements
   */
  [[nodiscard]] constexpr size_type max_size() const noexcept
  {
    return m_max_size;
  }

  /**
   * @brief Erase all elements
   *
   * @note Entries are non-throw destructible
   */
  void clear() noexcept
  {
    for (auto & entry : self().m_entries) {
      entry.reset();
    }
    this->size_ref() = 0;
  }

  /**
   * @brief Exchanges the contents of the container with those of other
   * @param[in] other another flat_associative_container_interface object
   */
  constexpr void swap(flat_associative_container_interface & other) noexcept
  {
    base::swap(m_size, other.m_size);
    base::swap(m_size, other.m_max_size);
  }

protected:
  struct entry
  {
    entry() = default;
    ~entry() = default;

    entry(const entry &) = delete;
    entry & operator=(const entry &) = delete;

    explicit constexpr entry(TValue && value) noexcept : m_value(base::move(value)) {}

    constexpr entry(entry && other) noexcept = default;
    constexpr entry & operator=(entry && other) noexcept = default;

    constexpr bool has_value() const noexcept
    {
      return m_value.has_value();
    }

    void reset() noexcept
    {
      needs_rehash = false;
      tombstone = false;
      m_value.reset();
    }

    bool needs_rehash{false};
    bool tombstone{false};
    optional<TValue> m_value{nullopt};
  };

  /**
   * @brief Rehashes the marked entries.
   */
  void rehash() noexcept
  {
    for (size_type i = 0; i < self().m_entries.size(); ++i) {
      auto & current = self().m_entries[i];
      current.tombstone = false;
      if (current.needs_rehash) {
        current.needs_rehash = false;
        auto value = base::move(current.m_value.value());
        current.m_value.reset();
        self().rehash_entry(base::move(value));
      }
    }
  }

  template <typename TKey, typename TFilter>
  optional<size_type> linear_probe(const TKey & key, TFilter && predicate) const noexcept
  {
    if (self().m_entries.empty()) {
      return nullopt;
    }

    const auto array_size = self().m_entries.size();
    const auto start = Comparer::hash_code(key) % array_size;
    auto current = start;

    while (true) {
      if (base::forward<TFilter>(predicate)(self().m_entries[current])) {
        return current;
      }

      current = (current + 1) % array_size;

      if (current == start) {
        break;
      }
    }

    return nullopt;
  }

  template <typename Self>
  class basic_iterator
  {
    using internal_iterator = conditional_t<is_const_v<Self>,
                                            typename vector<entry>::const_iterator,
                                            typename vector<entry>::iterator>;

  public:
    explicit constexpr basic_iterator(Self * self,
                                      internal_iterator cursor,
                                      internal_iterator end) noexcept
    : m_self(self), m_end(end), m_cursor(find_next_entry(cursor))
    {
    }

    using iterator_category = std::bidirectional_iterator_tag;
    using difference_type = std::ptrdiff_t;
    using value_type = TValue;
    using reference = conditional_t<is_const_v<Self>, const TValue &, TValue &>;
    using pointer = value_type *;

    reference operator*() noexcept
    {
      return *(m_cursor->m_value);
    }

    reference operator*() const noexcept
    {
      return *(m_cursor->m_value);
    }

    pointer operator->() noexcept
    {
      return reinterpret_cast<pointer>(&(m_cursor->m_value.value()));
    }

    basic_iterator & operator++() & noexcept
    {
      m_cursor = find_next_entry(m_cursor + 1);
      return *this;
    }

    basic_iterator operator++(int) noexcept
    {
      auto copy = *this;
      this->operator++();
      return copy;
    }

    friend bool operator==(const basic_iterator & lhs, const basic_iterator & rhs) noexcept
    {
      return lhs.m_cursor == rhs.m_cursor;
    }

    friend bool operator!=(const basic_iterator & lhs, const basic_iterator & rhs) noexcept
    {
      return !(lhs == rhs);
    }

  private:
    internal_iterator find_next_entry(internal_iterator start) const noexcept
    {
      for (auto it = start; it != m_end; ++it) {
        if (it->has_value()) {
          return it;
        }
      }
      return m_end;
    }

    Self * m_self;
    internal_iterator m_end;
    internal_iterator m_cursor;
  };

  constexpr size_type & size_ref() noexcept
  {
    return m_size;
  }

  constexpr size_type & max_size_ref() noexcept
  {
    return m_max_size;
  }

  constexpr size_type size_with_load_factor_padding(size_t count) const noexcept
  {
    using padding_factor =
      ratio_add<ratio<1, 1>, ratio_subtract<ratio<1, 1>, typename Derived::max_load_factor>>;
    return (count * padding_factor::num) / padding_factor::den;
  }

private:
  // TODO(simon.hoinkis): #25965 Replace the two methods below with 'iox/crtp_helpers.hpp' once in
  // the base library
  constexpr Derived & self() noexcept
  {
    return *static_cast<Derived *>(this);
  }

  constexpr const Derived & self() const noexcept
  {
    return *static_cast<const Derived *>(this);
  }

  size_type m_size{0};  //!< Number of entries with value
  size_type m_max_size{0};  //!< Maximum number of entries with value allowed by
                            //!< the load factor (not the same as capacity)
};

}  // namespace apex::base
#endif  // FLAT_ASSOCIATIVE_CONTAINER_INTERFACE_HPP
