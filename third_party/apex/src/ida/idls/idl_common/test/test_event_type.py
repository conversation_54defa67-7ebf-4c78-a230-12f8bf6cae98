# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

from idl_common.common_model import ParseLocation
from idl_common import type_model


def make_test_event() -> type_model.Event:
    event_type = type_model.Event(name="TestEvent", location=ParseLocation.make())
    field = type_model.EventMember(
        name="field1", location=ParseLocation.make(), type=type_model.BaseTypes.BOOL
    )
    event_type.add_child(field)
    return event_type


def test_event_type_can_be_created():
    event_type = make_test_event()
    assert event_type.name == "TestEvent"


def test_type_visitor_visits_event_type():
    class EventVisitor(type_model.TypeNodeVisitor):
        def __init__(self) -> None:
            self.visited_event = None
            self.visited_event_member = None

        def visit_event_member(self, at: type_model.EventMember) -> bool:
            self.visited_event_member = at.name
            return True

        def visit_event(self, at: type_model.Event) -> bool:
            self.visited_event = at.name
            return True

    test_event = make_test_event()
    event_visitor = EventVisitor()

    test_event.traverse_breadth_first(event_visitor)
    assert event_visitor.visited_event == test_event.name
    assert event_visitor.visited_event_member == "field1"

    class PureMemberVisitor(type_model.TypeNodeVisitor):
        def visit_member(self, at: type_model.Member) -> bool:
            self.visited = at.name
            return True

    member_visitor = PureMemberVisitor()
    test_event.traverse_breadth_first(member_visitor)
    assert member_visitor.visited == "field1"
