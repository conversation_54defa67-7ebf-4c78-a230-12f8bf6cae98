load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")

apex_py_test(
    name = "test",
    srcs = [
        "test_apexlog.py",
        "test_ast_node_sorting.py",
        "test_convert_case.py",
        "test_event_type.py",
        "test_include_computation.py",
        "test_misc_helpers.py",
        "test_models.py",
        "test_namespace_transformations.py",
        "test_type_model.py",
        "test_typemodel_joining.py",
        "test_visitors.py",
        "type_model_fixtures.py",
    ],
    deps = [
        "//ida/idls/idl_common",
        "//ida/idls/idl_common:idl_common_testing_helpers",
    ],
)
