load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_library")

ros_pkg(
    name = "idl_common_pkg",
    description = "Common code surrounding Apex connectors.",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    py_libraries = [
        ":idl_common",
    ],
    version = "0.1.0",
    visibility = ["//visibility:public"],
    deps = [],
)

py_library(
    name = "idl_common",
    srcs = [
        "idl_common/__init__.py",
        "idl_common/apexlog.py",
        "idl_common/common_model.py",
        "idl_common/convert_case.py",
        "idl_common/decorators.py",
        "idl_common/include_computation.py",
        "idl_common/misc.py",
        "idl_common/type_model.py",
    ],
    data = [":idl_common_pkg.wheel_data"],
    imports = ["."],
    tags = ["typecheck"],
    visibility = ["//visibility:public"],
    deps = [
        requirement("pydantic"),
    ],
)

py_library(
    name = "idl_common_testing_helpers",
    srcs = [
        "idl_common/testing/idl_helpers.py",
    ],
    tags = ["typecheck"],
    visibility = ["//visibility:public"],
    deps = [
        ":idl_common",
    ],
)
