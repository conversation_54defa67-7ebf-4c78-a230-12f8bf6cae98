# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

from dataclasses import dataclass
from pathlib import Path
from typing import List, Mapping, OrderedDict, Union
from pyfranca import ast, franca_processor

from idl_common import type_model, common_model


@dataclass
class MappingContext:
    processor: franca_processor.Processor
    base_dir: Path
    root: type_model.RootNode


_franca_primitive_to_typemodel_mapping: Mapping[str, type_model.BaseTypes] = {
    ast.Int8.__name__: type_model.BaseTypes.INT_8,
    ast.Int16.__name__: type_model.BaseTypes.INT_16,
    ast.Int32.__name__: type_model.BaseTypes.INT_32,
    ast.Int64.__name__: type_model.BaseTypes.INT_64,
    ast.UInt8.__name__: type_model.BaseTypes.U_INT_8,
    ast.UInt16.__name__: type_model.BaseTypes.U_INT_16,
    ast.UInt32.__name__: type_model.BaseTypes.U_INT_32,
    ast.UInt64.__name__: type_model.BaseTypes.U_INT_64,
    ast.<PERSON>olean.__name__: type_model.BaseTypes.BOOL,
    ast.Float.__name__: type_model.BaseTypes.FLOAT_32,
    ast.Double.__name__: type_model.BaseTypes.FLOAT_64,
}


def franca_primitive_to_typemodel(
    franca_type: ast.PrimitiveType,
) -> Union[type_model.BaseTypes, type_model.BytesType, type_model.StringType]:
    typename = type(franca_type).__name__

    primitive_type = _franca_primitive_to_typemodel_mapping.get(typename)
    if primitive_type is not None:
        return primitive_type

    if isinstance(franca_type, ast.ByteBuffer):
        return type_model.BytesType()

    if isinstance(franca_type, ast.String):
        return type_model.StringType()
    raise NotImplementedError(f"Unsupported type {typename}")


def add_description(node: type_model.AstNode, comments: OrderedDict) -> None:
    if "@" + common_model.DecorHelpers.DECOR_DESCRIPTION in comments:
        node.decorator.description = comments[
            "@" + common_model.DecorHelpers.DECOR_DESCRIPTION
        ]


def add_flags(node: type_model.AstNode, flags: List[str]) -> None:
    if common_model.DecorHelpers.DECOR_FLAGS in node.decorators:
        node.decorator.flags.extend(flags)
    else:
        node.decorator.flags = flags
