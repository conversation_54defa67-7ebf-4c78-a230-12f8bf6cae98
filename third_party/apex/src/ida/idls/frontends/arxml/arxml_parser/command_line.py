# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

import argparse
import sys
from pathlib import Path

from ida.idls.frontends.arxml.arxml_parser.arxml.arxml import Arxml
from ida.idls.frontends.arxml.arxml_parser.autosar.autosar_exception import <PERSON>sar<PERSON>rror
from idl_common import apexlog

__version__ = "0.1.1"

apexlog.initialize_logging(name="arxml_parser")
_logger = apexlog.getLogger()


def run_internal(prog, *argv):
    parser = argparse.ArgumentParser(prog=prog)
    subparsers = parser.add_subparsers(
        title="subcommand", dest="subcommand", required=True, help="Choose subcommand"
    )
    validate_parser = subparsers.add_parser("validate")
    validate_parser.add_argument(
        "input_file", type=Path, nargs=1, help="Input filename"
    )
    merge_parser = subparsers.add_parser("merge")
    merge_parser.add_argument("input_file", type=Path, nargs="+", help="Input filename")
    merge_parser.add_argument("output_file", type=Path, nargs=1, help="Output filename")
    print_parser = subparsers.add_parser("print")
    print_parser.add_argument("input_file", type=Path, nargs=1, help="Input filename")
    print_parser = subparsers.add_parser("type_model")
    print_parser.add_argument("input_file", type=Path, nargs=1, help="Input filename")

    args = parser.parse_args(*argv)

    try:
        if args.subcommand == "validate":
            arxml = Arxml.parse(args.input_file[0])
        elif args.subcommand == "merge":
            arxml = Arxml.merge(args.input_file, False, False)
            arxml.save(output_filename=args.output_file[0])
        elif args.subcommand == "print":
            arxml = Arxml.parse(args.input_file[0])
            arxml.print()
        else:
            _logger.error(f'Unknown subcommand "{args.subcommand}". Aborting.')
            return 1
    except ValueError as ve:
        _logger.error(f"Invalid argument for ARXML parsing! {ve}")
        return 1
    except AutosarError as e:
        _logger.error(f"Parsing of ARXML failed with error: {e.message}")
        return 1

    return 0


def run():
    return run_internal(sys.argv[0], sys.argv[1:])


if __name__ == "__main__":
    sys.exit(run())
