# Copyright 2025 Apex.AI, Inc.
# All rights reserved.

"""
Import Classic ARXML into type and connector models.
"""

from pathlib import Path
from typing import Iterable, List, Optional, Dict, Tuple, cast
from ida.verifiers.e2e.model import e2e_model
from idl_common import apexlog, common_model, misc, type_model
from ida.connectors.someip.model import someip_model
from ida.idls.frontends.arxml.classic import parser, import_helper, import_common

_log = apexlog.getLogger()


def _parse_files(
    arxmls: Iterable[Path],
    search_paths: Iterable[Path],
) -> parser.ClassicArxml:
    arxml_classic = parser.ClassicArxml.make()
    for arxml in arxmls:
        located_arxml = misc.find_file_in_search_paths(arxml, search_paths)
        new_arxml = parser.DispatchingParser().parse(located_arxml, arxml)
        arxml_classic.absorb(new_arxml)
    return arxml_classic


def _typeref_from_ar_ref(
    ref: str,
) -> <PERSON>ple[type_model.TypeRef, List[common_model.AstNodePath]]:
    """This returns both a typeref and the Astnode path if it's a complex type.

    The returned path may not be part of the model. If you are sure it is, you can
    ignore the returned path.
    """
    assert ref
    if import_common.is_ar_platform_reference(ref):
        return (parser.autosar_platform_type_to_base_type(ref), [])
    else:
        path = import_common._ast_node_path_from_ar_path(ref)
        assert path
        return (common_model.AstNodeRef(path), [path])


def _event_member_type_for_parser_event(
    event: parser.Event,
    tm: type_model.IdlModel,
    type_finder: import_helper.ClassicArxmlEntityFinder,
) -> Optional[type_model.TypeRef]:
    type_ref, _ = _typeref_from_ar_ref(event.impl_type_ref)
    if isinstance(type_ref, common_model.AstNodeRef):
        tm_node = tm.ast.root.lookup_descendant(type_ref.get_path())

        if tm_node is None:  # noqa SIM102 : a single 'if' is not more readable...
            if impl_type := type_finder.arxml_classic.implementation_data_types.get(
                event.impl_type_ref
            ):
                type_ref = common_model.AstNodeRef(
                    type_ref.get_path()[:-1] + [impl_type.symbol_name]
                )
                tm_node = tm.ast.root.lookup_descendant(type_ref.get_path())

        if tm_node is None:
            _log.warning(
                "Cannot find type '%s' for Event: '%s', skipping...",
                type_ref.get_path(),
                event.fqn,
            )
            # E.g. nested sequences are removed from the type model, so we can't add
            # the event that uses the type either
            return None
    return type_ref


def _create_someip_interface(  # noqa: PLR0912
    instance: parser.ProvidedServiceInstance,
    tm: type_model.IdlModel,
    tlv_configs: Dict[import_common.ARPath, import_common.TLVStructConfig],
    type_finder: import_helper.ClassicArxmlEntityFinder,
) -> someip_model.ServiceInterface:
    # first create the type model Interface
    interface = tm.ast.root.add_child(
        type_model.Interface(name=instance.short_name, location=instance.location)
    )

    # then the someip model ServiceInterface
    someip_interface = someip_model.ServiceInterface(
        name=instance.short_name + "_SOMEIP",
        location=instance.location,
        service_id=instance.service_id,
        event_groups=[],  # todo
        interface_type_ref=type_model.AstNodeRef(interface),
        version=None,  # set below in event properties
        tlv_structs={k.ast_fqn for k in tlv_configs},
    )

    for event in instance.events.values():
        assert isinstance(event, parser.Event)
        if (
            type_ref := _event_member_type_for_parser_event(event, tm, type_finder)
        ) is None:
            continue

        # again, first the TM event
        tm_event = interface.add_child(
            type_model.Event(name=event.event_name, location=event.location)
        )
        member = tm_event.add_child(
            type_model.EventMember(
                name="data",
                location=event.location,
                type=type_ref,
            )
        )
        member.decorator.index = 0

        properties = type_finder.serialization_properties_for_event(event.fqn)
        entity_serialization_properties = someip_model.EntitySerializationProperties()
        if properties:
            prop = properties[0]

            if prop.interface_version is not None:
                if someip_interface.version is None:
                    someip_interface.version = someip_model.SomeIpVersion(
                        major=prop.interface_version, minor=0
                    )
                elif someip_interface.version.major != prop.interface_version:
                    raise ValueError(
                        f"Interface version mismatch: {someip_interface.version.major} "
                        f"vs {prop.interface_version}"
                    )

            if prop.size_of_array_length_fields is not None:
                entity_serialization_properties.sequence_size_of_length_field = (
                    someip_model.SizeOfLengthField(prop.size_of_array_length_fields)
                )

            if prop.size_of_struct_length_fields is not None:
                entity_serialization_properties.struct_size_of_length_field = (
                    someip_model.SizeOfLengthField(prop.size_of_struct_length_fields)
                )

            if prop.size_of_union_length_fields is not None:
                entity_serialization_properties.union_size_of_length_field = (
                    someip_model.SizeOfLengthField(prop.size_of_union_length_fields)
                )

            if transformer := type_finder.arxml_classic.transformation_technologies.get(
                prop.transformer_ref
            ):
                someip_desc = cast(
                    parser.SOMEIPTransformationDescription,
                    transformer.transformation_description,
                )
                if (
                    someip_desc.byte_order
                    == parser.ByteOrderEnum.MOST_SIGNIFICANT_BYTE_FIRST
                ):
                    entity_serialization_properties.byte_order = (
                        someip_model.ByteOrder.BIG_ENDIAN
                    )
                elif (
                    someip_desc.byte_order
                    == parser.ByteOrderEnum.MOST_SIGNIFICANT_BYTE_LAST
                ):
                    entity_serialization_properties.byte_order = (
                        someip_model.ByteOrder.LITTLE_ENDIAN
                    )
                else:
                    # default to big endian if not specified
                    entity_serialization_properties.byte_order = (
                        someip_model.ByteOrder.BIG_ENDIAN
                    )
            else:
                _log.warning(
                    "Referenced transformation technology '%s' could not be found",
                    str(prop.transformer_ref),
                )

        # then the SM event
        someip_interface.add_child(
            someip_model.SOMEIPEvent(
                name=event.event_name,
                location=event.location,
                type_ref=type_model.AstNodeRef(tm_event),
                event_id=event.event_group_id,
                event_groups=[],
                transport_protocol=someip_model.TransportProtocolType.UDP,
                is_field=False,
                serialization_properties=entity_serialization_properties,
            )
        )

    return someip_interface


def _validate_someip_interface(
    instance: parser.ProvidedServiceInstance, interface: someip_model.ServiceInterface
):
    # TODO: Add interface validation
    ...


def _fill_someip_model(
    sm: common_model.AstModel,
    tm: type_model.IdlModel,
    type_finder: import_helper.ClassicArxmlEntityFinder,
):
    # first find or create the ServiceInterface for each instance
    tlv_configs = type_finder.build_tlv_configs()

    interfaces: Dict[int, someip_model.ServiceInterface] = {}
    for service in type_finder.deployed_instances:
        if service.service_id in interfaces:
            _validate_someip_interface(service, interfaces[service.service_id])
        else:
            interfaces[service.service_id] = _create_someip_interface(
                service, tm, tlv_configs, type_finder
            )

    for interface in interfaces.values():
        sm.root.add_child(interface)

    e2e_config = sm.root.add_child(
        someip_model.E2EConfiguration.make(type_finder.arxml_classic.location)
    )

    # now create the InstanceDeployment...
    deployment = sm.root.add_child(
        someip_model.InstanceDeployment(
            name="deployments",
            location=common_model.ParseLocation.make(),
        )
    )

    # ... and add the ServiceInstances
    for service in type_finder.deployed_instances:
        ipv6_addr = type_finder.ipv6_addr_for_service(service)
        interface = interfaces[service.service_id]
        instance = deployment.add_child(
            someip_model.ServiceInstance(
                name=service.short_name,
                location=service.location,
                instance_identifier=service.short_name,
                # we can use the short-name here
                # because it is unique in classic arxml. At least in the files that we
                # got. If we use the fqn, we get a too-long-filename error
                instance_id=service.instance_id,
                service_interface_ref=type_model.AstNodeRef(interface),
                event_groups=[],
                udp_port=service.udp_port,
                unicast_address=ipv6_addr,
            )
        )
        e2e_mappings = _create_e2e_mappings_for_instance(
            instance, service.fqn, type_finder
        )
        e2e_config.add_children(*e2e_mappings)

    for struct_fqn, tlv_config in tlv_configs.items():
        tm_struct = tm.ast.root.lookup_descendant(struct_fqn.ast_node_path)
        if tm_struct is None:
            continue
        for key, tlv_id in tlv_config.tlv_ids.items():
            member = tm_struct.lookup(key)
            if not isinstance(member, type_model.StructMember):
                raise ValueError(
                    f"did not find struct member '{key}' in {tm_struct.get_fqn()}, "
                    f"found instead: {member}"
                )

            member.decorator.index = tlv_id
    tm.ast.resolve()
    misc.OwnNodesCollector.assign_nodes_to_model(tm)


def _create_e2e_mappings_for_instance(
    instance: someip_model.ServiceInstance,
    service_arxml_fqn: str,
    type_finder: import_helper.ClassicArxmlEntityFinder,
) -> List[someip_model.E2EMapping]:
    service_type = instance.service_interface_ref.node
    assert isinstance(service_type, someip_model.ServiceInterface)

    class EventsInInterface(someip_model.Visitor):
        def __init__(self) -> None:
            self.events: List[someip_model.SOMEIPEvent] = []

        def visit_someip_event(self, event: someip_model.SOMEIPEvent) -> bool:
            self.events.append(event)
            return True

    events = service_type.traverse_breadth_first(EventsInInterface()).events

    e2e_mappings = [
        m
        for e in events
        if (m := _e2e_mapping_for_event(e, instance, service_arxml_fqn, type_finder))
        is not None
    ]

    return e2e_mappings


def _e2e_mapping_for_event(
    event: someip_model.SOMEIPEvent,
    instance: someip_model.ServiceInstance,
    service_arxml_fqn: str,
    type_finder: import_helper.ClassicArxmlEntityFinder,
) -> Optional[someip_model.E2EMapping]:
    e2e_properties = type_finder.e2e_properties_for_event(
        service_arxml_fqn + "/" + event.name
    )

    if e2e_properties:
        e2e_iref_prop = e2e_properties[0]
        e2e_transformer = type_finder.e2e_profile_for_ref(e2e_iref_prop.transformer_ref)
        assert isinstance(
            e2e_transformer.transformation_description,
            parser.EndToEndTransformationDescription,
        )

        if e2e_transformer.protocol != "E2E":
            # There are all kinds of transformers (predefined like E2E or SOMEIP but
            # also custum ones), we are only interested in the E2E one.
            return None
        if (
            e2e_transformer.transformation_description.profile_name
            != parser.E2EProfileName.PROFILE_04
        ):
            _log.warning(
                "Unsupported E2E profile '%s' at '%s'",
                e2e_transformer.transformation_description.profile_name.value,
                e2e_iref_prop.location,
            )
            return None

        mapping_name = (
            f"{event.get_fqn(separator='_',omit_root=True)}_"
            f"{e2e_transformer.short_name}"
        )

        assert e2e_transformer.transformation_description.offset is not None
        assert e2e_transformer.transformation_description.max_delta_counter is not None
        assert e2e_iref_prop.max_data_length_bits is not None
        assert e2e_iref_prop.min_data_length_bits is not None
        assert e2e_iref_prop.data_ids
        offset_in_bytes = int(e2e_transformer.transformation_description.offset / 8)
        min_data_length_bytes = int(e2e_iref_prop.min_data_length_bits / 8)
        max_data_length_bytes = int(e2e_iref_prop.max_data_length_bits / 8)
        e2e_profile = e2e_model.E2EProfile4(
            offset=offset_in_bytes,
            max_delta_counter=e2e_transformer.transformation_description.max_delta_counter,
            data_id=e2e_iref_prop.data_ids[0],
            is_big_endian=True,
            min_data_length=min_data_length_bytes,
            max_data_length=max_data_length_bytes,
        )
        mapping = someip_model.E2EMapping(
            name=mapping_name,
            location=e2e_iref_prop.location,
            entity_ref=common_model.AstNodeRef(event),
            service_instance_ref=common_model.AstNodeRef(instance),
            e2e_profile=e2e_profile,
        )
        return mapping
    return None


def import_types(
    arxml_classic: parser.ClassicArxml,
    typemodel: type_model.IdlModel,
    someipmodel: common_model.ConnectorModel,
) -> None:
    type_finder = import_helper.ClassicArxmlEntityFinder(arxml_classic)

    # We go from VariableDataPrototype -> ApplicationDataType -> ImplementationDataType

    used_impl_type_refs = type_finder.compute_used_implementation_refs()
    while used_impl_type_refs:
        used_implementation_types = (
            (t, "")
            for t in type_finder.implementation_types_for_implementation_type_refs(
                used_impl_type_refs
            )
        )
        dependant_types = import_common.add_implementation_types_to_type_model(
            used_implementation_types, arxml_classic, typemodel
        )

        used_impl_type_refs.clear()

        for dependant_type in dependant_types:
            if (
                typemodel.ast.root.lookup_descendant(dependant_type.ast_node_path)
                is None
            ):
                used_impl_type_refs.append(dependant_type)

    typemodel.ast.resolve()

    import_common.apply_hacks_to_typemodel(typemodel)

    _fill_someip_model(someipmodel.ast, typemodel, type_finder)


def import_arxmls(
    arxmls: Iterable[Path],
    *,
    search_paths: Iterable[Path],
    typemodel: type_model.IdlModel,
    someipmodel: common_model.ConnectorModel,
) -> None:
    """Add types found in `arxml` to `typemodel` and SOME/IP data to `someipmodel`."""

    arxml_classic = _parse_files(arxmls, search_paths)
    import_types(arxml_classic, typemodel, someipmodel)
