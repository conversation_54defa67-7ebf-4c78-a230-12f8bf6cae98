# Copyright 2025 Apex.AI, Inc.
# All rights reserved.

"""SAX parser for ARXML classic files."""
from __future__ import annotations

from typing import (
    Dict,
    Generic,
    List,
    Mapping,
    Optional,
    Tuple,
    Type,
    TypeVar,
    Union,
    cast,
)
from typing_extensions import TypeAlias
import xml.parsers.expat
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
from idl_common import common_model, apexlog
from idl_common.type_model import BaseTypes

# print is ok
# too long lines are ok here (E501)
# TODO: reduce number of branches
# ruff: noqa: T201, PLR0912, PLR0915, TCH002, E501

_log = apexlog.getLogger()

_TYPE_MAPPING: Mapping[str, BaseTypes] = {
    "sint8": BaseTypes.INT_8,
    "sint16": BaseTypes.INT_16,
    "sint32": BaseTypes.INT_32,
    "sint64": BaseTypes.INT_64,
    "uint8": BaseTypes.U_INT_8,
    "uint16": BaseTypes.U_INT_16,
    "uint32": BaseTypes.U_INT_32,
    "uint64": BaseTypes.U_INT_64,
    "float32": BaseTypes.FLOAT_32,
    "float64": BaseTypes.FLOAT_64,
    "boolean": BaseTypes.BOOL,
}


def autosar_platform_type_to_base_type(platform_type: str) -> BaseTypes:
    end = platform_type.split("/")[-1]
    base_type = _TYPE_MAPPING.get(end)
    if base_type is None:
        raise ValueError(f"Cannot find base type for platform type {platform_type}")
    return base_type


T = TypeVar("T")
V = TypeVar("V")


class Ref(Generic[T]):
    """A typed ARXML-ref representation"""

    def __init__(self, ref: str, *t: Type[T]) -> None:
        super().__init__()
        self._ref = ref
        self._types = t

    def __hash__(self) -> int:
        hash_ = self._ref.__hash__()
        for t in self._types:
            hash_ ^= str(t).__hash__()
        return hash_

    def __eq__(self, value: object, /) -> bool:
        if isinstance(value, Ref):
            return self._ref == value._ref and self._types == value._types
        return False

    def __str__(self) -> str:
        return self._ref

    def __repr__(self) -> str:
        return f'{self._ref} <{", ".join([str(t) for t in self._types])}>'

    def rebind(self, new_type: Type[V]) -> Ref[V]:
        if not issubclass(new_type, self._types):
            raise ValueError(
                f"Cannot rebind. {new_type} is not a subclass of {self._types}!"
            )
        return Ref[V](self._ref, new_type)  # type: ignore


class Category(str, Enum):
    """
    Valid categories are defined in the 4.3.1 Software Component Template
    https://www.autosar.org/fileadmin/standards/R4.3.1/CP/AUTOSAR_TPS_SoftwareComponentTemplate.pdf
    table 5.7 starting on page 234

    SWC template defines properties for each category in table 5.18 on page 271
    """

    UNKNOWN = "UNKNOWN"
    VALUE = "VALUE"
    STRING = "STRING"  # only in application type
    BOOLEAN = "BOOLEAN"
    ARRAY = "ARRAY"
    STRUCTURE = "STRUCTURE"
    UNION = "UNION"
    TYPE_REFERENCE = "TYPE_REFERENCE"
    DATA_REFERENCE = "DATA_REFERENCE"
    FUNCTION_REFERENCE = "FUNCTION_REFERENCE"


class Absorbing:
    """
    Mixin class that adds an `absorb()` method.

    Absorb is the basis for merging multiple dict-heavy ClassicArxml objects.
    `dict.update()` does not recursively merge dictionaries, but instead simply
    overwrites values in case the key is already known.

    `absorb()` on the other hand:
    - merges dictionaries recursively
    - in case values are not dictionaries, assert that they are identical.
    """

    def absorb(self, other: Absorbing) -> None:
        """Merges dicts in `other` into dicts in self."""
        for prop_name, prop in other.__dict__.items():
            if isinstance(prop, dict):
                own_dict = getattr(self, prop_name)
                for dict_key in prop:
                    if dict_key not in own_dict:
                        own_dict[dict_key] = prop[dict_key]
                    elif isinstance(
                        own_dict[dict_key], (str, int, float, list, set, Enum)
                    ):
                        assert own_dict[dict_key] == prop[dict_key]
                    elif isinstance(prop[dict_key], Absorbing):
                        own_dict[dict_key].absorb(prop[dict_key])
            elif isinstance(prop, (list, set)):
                own_list = getattr(self, prop_name)
                own_list.extend(list(prop))


@dataclass
class NetworkEndpoint(Absorbing):
    fqn: str = ""
    ipv6_addr: str = ""
    ipv4_addr: str = ""


@dataclass
class ApplicationPrimitiveDataType(Absorbing):
    short_name: str = ""
    fqn: str = ""
    category: Category = Category.UNKNOWN


@dataclass
class ApplicationArrayDataType(Absorbing):
    short_name: str = ""
    fqn: str = ""
    category: Category = Category.ARRAY
    element_type_ref: str = ""


@dataclass
class ApplicationRecordDataType(Absorbing):
    short_name: str = ""
    fqn: str = ""
    category: Category = Category.UNKNOWN
    elements: Dict[str, ApplicationRecordElement] = field(default_factory=dict)


@dataclass
class ApplicationRecordElement(ApplicationRecordDataType, Absorbing):
    short_name: str = ""
    fqn: str = ""
    category: Category = Category.UNKNOWN
    parent: Optional[ApplicationRecordDataType] = None
    type_ref: str = ""
    idx: int = 0


@dataclass
class SomeipTpConnection(Absorbing):
    tp_sdu_ref: Ref[PduTriggering] = None  # type: ignore
    transport_pdu_ref: Ref[PduTriggering] = None  # type: ignore


@dataclass
class ISignalTriggering(Absorbing):
    fqn: str = ""
    isignal_ref: Ref[ISignal] = None  # type: ignore


@dataclass
class PduTriggering(Absorbing):
    fqn: str = ""
    isignal_triggering_refs: List[Ref[ISignalTriggering]] = field(default_factory=list)


@dataclass
class SocketConnectionIPduIdentifier(Absorbing):
    pdu_triggering_ref: Ref[PduTriggering] = None  # type: ignore
    routing_group_refs: List[Ref[SoAdRoutingGroup]] = field(default_factory=list)
    header_id: int = 0
    """
    [constr_3272]
    SocketConnectionIpduIdentifier.headerId setting for SD SocketConnectionBundles

    The SocketConnectionIpduIdentifier.headerId of SD SocketConnectionBundles defined
    in [TPS_SYST_02119] shall always be set to 0xFFFF8100 for SD messages.

    Together with:

    [PRS_SOMEIPSD_00251]
    The SOME/IP-SD Header Format shall follow:
    • Message ID (Service ID/Method ID) [32 bit]: 0xFFFF 8100
    ...

    means: the header_id == SOME/IP Message ID in case of SOME/IP
    """


@dataclass
class CompositionSwComponent(Absorbing):
    ...


ApplicationDataTypes = Union[
    ApplicationArrayDataType,
    ApplicationRecordDataType,
    ApplicationRecordElement,
    ApplicationPrimitiveDataType,
]


@dataclass
class ClassicArxml(Absorbing):
    location: common_model.ParseLocation

    soad_routing_groups: Dict[Ref[SoAdRoutingGroup], SoAdRoutingGroup]

    tp_connections: List[SomeipTpConnection]

    pdu_triggerings: Dict[Ref[PduTriggering], PduTriggering]
    """
    Actually belongs under ethernetcluster.ethernetphysicalchannel
    """

    isignal_triggerings: Dict[Ref[ISignalTriggering], ISignalTriggering]
    """
    Actually belongs under ethernetcluster.ethernetphysicalchannel
    """

    socket_connection_ipdu_identifiers: List[SocketConnectionIPduIdentifier]
    """
    Actually belongs here:

    ethernetcluster->
        ethernetphysicalchannel->
            soadconfig->
                socketconnectionbundle->
                    socketconnection
    """

    transformation_technologies: Dict[
        Ref[TransformationTechnology], TransformationTechnology
    ]

    sender_receiver_interfaces: Dict[str, SenderReceiverInterface]
    """Found inside <SENDER-RECEIVER-INTERFACE>.

    Contains VarDataPrototypes that point to ApplicationDataTypes.
    The individual VarDataPrototypes in this interface are pointed to by
    SENDER-RECEIVER-TO-SIGNAL-MAPPING.TARGET-DATA-PROTOTYPE-REF
    """

    ports: Dict[Ref[PortPrototype], Union[PPortPrototype, RPortPrototype]]

    mapping_sets: Dict[str, DataTypeMappingSet]
    implementation_data_types: Dict[str, ImplementationDataType]
    provided_service_instances: Dict[str, ProvidedServiceInstance]

    sender_receiver_to_signal_mapping: Dict[str, SenderReceiverToSignalMapping]
    """Keyed by event_handler fqn, contains a data ref to VariableDataPrototype."""

    sender_receiver_to_signal_mappings: List[SenderReceiverToSignalMapping]
    """Same as sender_receiver_to_signal_mapping above but not keyed.

    The event_handler_ref might be missing, but we still want to keep the mapping.
    """

    i_signals: Dict[str, ISignal]

    application_data_types_recursive: Dict[str, ApplicationDataTypes]
    """Carries both structs and their members at the top level for simple TLV lookup.

    TLV just points to members, not to application record types. To find the struct in a
    reliable way (i.e. no cutting of / of paths), you index into
    application_data_types_recursive with the application reference from TlvElement to
    get a ApplicationRecordElement.
    You then follow the "parent" to get the ApplicationRecordDataType. The
    ApplicationRecordDataType fqn can then be used to find the implementation type via
    the ClassicArxml.mapping_sets.
    """

    network_endpoints: Dict[str, NetworkEndpoint]
    component_types: Dict[Ref[CompositionSwComponentType], CompositionSwComponentType]
    root_sw_composition: Optional[RootSwCompositionPrototype] = None

    @staticmethod
    def make() -> ClassicArxml:
        """Factory method."""
        return ClassicArxml(
            location=common_model.ParseLocation.make(),
            tp_connections=[],
            pdu_triggerings={},
            isignal_triggerings={},
            socket_connection_ipdu_identifiers=[],
            soad_routing_groups={},
            transformation_technologies={},
            sender_receiver_interfaces={},
            ports={},
            mapping_sets={},
            implementation_data_types={},
            provided_service_instances={},
            sender_receiver_to_signal_mapping={},
            sender_receiver_to_signal_mappings=[],
            i_signals={},
            application_data_types_recursive={},
            network_endpoints={},
            component_types={},
        )


ApplicationDataTypeRef: TypeAlias = str
ImplementationDataTypeRef: TypeAlias = str


@dataclass
class ISignal(Absorbing):
    fqn: str = ""
    someip_transformation_isignal_props: List[SOMEIPTransformationSignalProps] = field(
        default_factory=list
    )

    e2e_transformation_isignal_props: List[EndToEndTransformationISignalProps] = field(
        default_factory=list
    )
    tlv_elements: Dict[ApplicationDataTypeRef, TlvEntry] = field(default_factory=dict)
    """Mapping from Application Type Ref to TLV id."""
    system_signal_ref: str = ""


class SOMEIPMessageTypeEnum(str, Enum):
    REQUEST = "request"
    REQUEST_NO_RETURN = "request-no-return"
    NOTIFICATION = "notification"
    RESPONSE = "response"
    ERROR = "error"


@dataclass
class SOMEIPTransformationSignalProps(Absorbing):
    interface_version: Optional[int] = None
    message_type: Optional[SOMEIPMessageTypeEnum] = None
    size_of_array_length_fields: Optional[int] = None
    size_of_struct_length_fields: Optional[int] = None
    size_of_union_length_fields: Optional[int] = None

    transformer_ref: Ref[TransformationTechnology] = None  # type: ignore


class E2EProfileName(str, Enum):
    """
    System Template CP Release 4.3.1, page 490, [TPS_SYST_02073]:

    EndToEndTransformationDescription.profileName can have the following
    values: PROFILE_01, PROFILE_02, PROFILE_04, PROFILE_05, PROFILE_06,
    PROFILE_07, PROFILE_11, PROFILE_22.
    """

    PROFILE_01 = "PROFILE_01"
    PROFILE_02 = "PROFILE_02"
    PROFILE_04 = "PROFILE_04"
    PROFILE_05 = "PROFILE_05"
    PROFILE_06 = "PROFILE_06"
    PROFILE_07 = "PROFILE_07"
    PROFILE_11 = "PROFILE_11"
    PROFILE_22 = "PROFILE_22"
    # invalid in classic
    PROFILE_08 = "PROFILE_08"


class ByteOrderEnum(str, Enum):
    MOST_SIGNIFICANT_BYTE_FIRST = "MOST-SIGNIFICANT-BYTE-FIRST"
    """Big endian"""

    MOST_SIGNIFICANT_BYTE_LAST = "MOST-SIGNIFICANT-BYTE-LAST"
    """Little endian"""

    OPAQUE = "OPAQUE"
    """Ignore, handled by another layer"""


@dataclass
class SOMEIPTransformationDescription(Absorbing):
    alignment: int = 0
    """
    Specifies the alignment of dynamic data in the serialized data stream. The alignment
    shall be specified in Bits.
    """
    interface_version: int = 0
    """
   The interface version the SOME/IP transformer shall use.
    """
    byte_order: ByteOrderEnum = None  # type: ignore
    """
    Defines which byte order shall be serialized by the SOME/IP transformer.
    """


@dataclass
class EndToEndTransformationDescription(Absorbing):
    """
    System Template CP Release 4.3.1, 7.3.4 E2E Transformer

    Not all attributes are mapped yet. See page 490ff on which profile uses which
    attributes. A summary for Profile 4 can be found on pages 500/501.
    """

    profile_name: E2EProfileName = None  # type: ignore
    """
    Definition of the E2E profile.
    """

    max_delta_counter: Optional[int] = None
    """
    Maximum allowed difference between two counter values of two consecutively received
    valid messages. For example, if the receiver gets data with counter 1 and
    MaxDeltaCounter is 3, then at the next reception the receiver can accept Counters
    with values 2, 3 or 4.

    If the EndToEndTransformationDescription.profileName attribute has a value of
    PROFILE_04 the value of maxDeltaCounter attribute shall be in the range 1-65535.
    """

    offset: Optional[int] = None
    """
    Offset of the E2E header in the Data[] array in bits.

    If the EndToEndTransformationDescription.profileName attribute is set to a value
    PROFILE_02, PROFILE_04, PROFILE_05, PROFILE_06, PROFILE_07, or PROFILE_22 then the
    multiplicity of the EndToEndTransformationDescription.offset attribute shall be 1.
    """


@dataclass
class TransformationTechnology(Absorbing):
    """
    System Template CP Release 4.3.1, table 7.3, page 450/451
    """

    short_name: str = ""

    fqn: str = ""

    header_length_bits: int = 0
    """
    Defines the length of the header (in bits) this transformer will add in front of the
    data.
    """

    protocol: str = ""
    """
    Specifies the protocol that is implemented by this transformer.
    """

    transformation_description: Optional[
        Union[SOMEIPTransformationDescription, EndToEndTransformationDescription]
    ] = None


@dataclass
class EndToEndTransformationISignalProps(Absorbing):
    """
    System Template CP Release 4.3.1 Table 7.26, page 489

    """

    location: common_model.ParseLocation
    data_ids: List[int] = field(default_factory=list)
    max_data_length_bits: Optional[int] = None
    min_data_length_bits: Optional[int] = None

    transformer_ref: Ref[TransformationTechnology] = None  # type: ignore


@dataclass
class PortPrototype(Absorbing):
    fqn: str = ""


@dataclass
class PPortPrototype(PortPrototype):
    """
    Component port providing a certain port interface.
    """

    provided_interface_ref: Ref[SenderReceiverInterface] = None  # type: ignore


@dataclass
class RPortPrototype(PortPrototype):
    """
    Component port requiring a certain port interface.
    """

    required_interface_ref: Ref[SenderReceiverInterface] = None  # type: ignore


@dataclass
class DataTypeMappingSet(Absorbing):
    fqn: str = ""
    mapping: Dict[ApplicationDataTypeRef, ImplementationDataTypeRef] = field(
        default_factory=dict
    )
    """Mapping from ApplicationDataType to ImplementationDataType."""


class CommunicationDirectionType(str, Enum):
    IN = "IN"
    OUT = "OUT"


@dataclass
class SenderReceiverToSignalMapping(Absorbing):
    service_instance_ref: str = ""
    event_handler_ref: str = ""
    """points to an EventHandler, i.e. an event."""
    system_signal_ref: str = ""
    target_data_prototype_ref: str = ""
    """Points to VariableDataPrototype"""
    direction: CommunicationDirectionType = None  # type: ignore

    context_port_ref: Ref[PortPrototype] = None  # type: ignore


class EventGroupControlTypeEnum(str, Enum):
    ACTIVATION_AND_TRIGGER_UNICAST = "ACTIVATION-AND-TRIGGER-UNICAST"
    """
    Activate the data path for unicast events and triggered unicast events that are sent
    out after a client got subscribed.
    """
    ACTIVATION_MULTICAST = "ACTIVATION-MULTICAST"
    """
    Activate the data path for multicast events of an EventGroup.
    """
    ACTIVATION_UNICAST = "ACTIVATION-UNICAST"
    """
    Activate the data path for unicast events of an EventGroup.
    """

    TRIGGER_UNICAST = "TRIGGER-UNICAST"
    """
    Activate the data path for triggered unicast events that are sent out after a client
    got subscribed.
    """


@dataclass
class SoAdRoutingGroup(Absorbing):
    fqn: str = ""
    event_group_control_type: Optional[EventGroupControlTypeEnum] = None
    """
    This attribute defines the type of a RoutingGroup.
    There are RoutingGroups that activate the data path for unicast or multicast events
    of an event group. And there are RoutingGroups that activate the data path for
    initial events that are triggered, namely events that are sent out on the server
    side after a client got subscribed.

    Please note that this attribute is only valid for event communication (Sender
    Receiver communication) and shall be omitted in MethodActivationRoutingGroups.
    """


@dataclass
class Event(Absorbing):
    location: common_model.ParseLocation
    fqn: str
    event_group_id: int
    event_name: str
    routing_group_ref: Ref[SoAdRoutingGroup]
    impl_type_ref: str = ""
    """Convenience attribute computed and set later during import."""


@dataclass
class ProvidedServiceInstance(Absorbing):
    location: common_model.ParseLocation
    fqn: str = ""
    short_name: str = ""
    service_id: int = 0
    instance_id: int = 0
    udp_port: int = 0
    network_endpoint_ref: str = ""
    events: Dict[str, Event] = field(default_factory=dict)
    version: Tuple[int, int] = None  # type: ignore


@dataclass
class VariableDataPrototype(Absorbing):
    """Representation of a Datatype used by a component."""

    fqn: str = ""
    short_name: str = ""
    application_type_ref: str = ""
    """Ref to an application type if dest == APPLICATION-PRIMITIVE-DATA-TYPE"""
    dest: str = ""


@dataclass
class SenderReceiverInterface(Absorbing):
    """Interface type representation."""

    fqn: str = ""
    data_elements: Dict[str, VariableDataPrototype] = field(default_factory=dict)


class ArraySizeSemantics(str, Enum):
    """
    https://www.autosar.org/fileadmin/standards/R4.3.1/CP/AUTOSAR_TPS_SoftwareComponentTemplate.pdf#page=259
    """

    fixedSize = "FIXED-SIZE"  # noqa: N815
    variableSize = "VARIABLE-SIZE"  # noqa: N815


class ArraySizeHandling(str, Enum):
    """
    https://www.autosar.org/fileadmin/standards/R4.3.1/CP/AUTOSAR_TPS_SoftwareComponentTemplate.pdf#page=260
    """

    allIndicesDifferentArraySize = "ALL-INDICES-DIFFERENT-ARRAY-SIZE"  # noqa: N815
    allIndicesSameArraySize = "ALL-INDICES-SAME-ARRAY-SIZE"  # noqa: N815
    inheritedFromArrayElementTypeSize = (  # noqa: N815
        "INHERITED-FROM-ARRAY-ELEMENT-TYPE-SIZE"
    )


@dataclass
class ImplementationDataTypeElement:
    """Sub element inside a ImplementationDataType."""

    location: common_model.ParseLocation
    short_name: str = ""
    category: Category = Category.UNKNOWN
    array_size: int = 0
    """
    [constr_1105] Value of arraySize
    The value of the attribute arraySize of an ImplementationDataTypeElement owned by an
    ImplementationDataType or ImplementationDataTypeElement of category ARRAY shall be
    greater than 0 unless attribute ImplementationDataTypeElement.arraySizeHandling
    exists and is set to the value inheritedFromArrayElementTypeSize.

    [TPS_SWCT_01478] Array size is defined as an attribute of the
    ImplementationDataTypeElement. Please note that the array size is not defined as an
    attribute of the ImplementationDataType which stands for the whole array. It is
    actually defined as an attribute of the ImplementationDataTypeElement which is
    describing the array element (note that the same pattern is used in
    ApplicationArrayDataType).

    Consequently, if a “struct” element represents an array this specific struct-element
    is given by an ImplementationDataTypeElement of category ARRAY which in turn
    aggregates another ImplementationDataTypeElement of e.g. category VALUE
    representing the array element and containing the size.

    - Software Component Template R4.3.1
    """
    array_size_semantics: Optional[ArraySizeSemantics] = None
    array_size_handling: Optional[ArraySizeHandling] = None
    sub_elements: List[ImplementationDataTypeElement] = field(default_factory=list)
    impl_data_type_ref: str = ""
    base_type_ref: str = ""
    is_optional = False

    def to_str(self, pad="") -> str:
        """create a string representation of this element."""
        elems = [self.category.value, self.short_name]
        new_pad = pad + "\t"
        if self.category == Category.TYPE_REFERENCE:
            elems.append(f"-> {self.impl_data_type_ref}")
        if self.category == Category.ARRAY:
            elems.append(
                f"size: {self.array_size} [{self.sub_elements[0].to_str(new_pad)}]"
            )

        return " ".join(elems)


class DynamicArraySizeProfile(str, Enum):
    """
    Profiles are defined in
    Software Component Template CP Release 4.3.1
    Chapter 2.8.1.2 "New-world" variable-size Arrays

    [TPS_SWCT_01636] Definition of profiles for the definition of Variable-Size
    Array Data Types

    https://www.autosar.org/fileadmin/standards/R4.3.1/CP/AUTOSAR_TPS_SoftwareComponentTemplate.pdf#page=62

    example:
    https://www.autosar.org/fileadmin/standards/R4.3.1/CP/AUTOSAR_TPS_SoftwareComponentTemplate.pdf#page=954
    """

    Linear = "VSA_LINEAR"
    Square = "VSA_SQUARE"
    Rectangular = "VSA_RECTANGULAR"
    FullyFlexible = "VSA_FULLY_FLEXIBLE"


@dataclass
class ImplementationDataType:
    """
    The ImplementationDataType is the root object for a type. It can have arbitrarily
    nested subtypes (e.g. member that is an array that contains an array, ...)

    This type also stores the current state for the SAX parser, i.e. which subelement we
    are currently parsing.
    """

    location: common_model.ParseLocation
    fqn: str = ""
    short_name: str = ""
    symbol_name: str = ""
    """
    [TPS_SWCT_01194] Symbolic name of an ImplementationDataType.

    The symbol to be used as (depending on the concrete case) either a complete
    replacement or a prefix.
    """
    category: Category = Category.UNKNOWN

    dynamic_array_size_profile: Optional[DynamicArraySizeProfile] = None

    sub_elements: List[ImplementationDataTypeElement] = field(default_factory=list)
    impl_data_type_ref: str = ""
    """In case this is a typedef, this is the referenced type"""
    base_type_ref: str = ""

    _active_elements: List[ImplementationDataTypeElement] = field(default_factory=list)
    is_optional = False
    _reading_optional = False
    """Flag marking we are inside a

            <SDG GID="StructureElementProperties">
                <SD GID="isOptional">true</SD>
     set when reading this--^         ^-- checked and reset when reading this
            </SDG>
    """

    def add_impl_element(self, element: ImplementationDataTypeElement) -> None:
        """Adds 'element' to the currently active sub-element."""
        if self._active_elements:
            # this belongs into some subelement of ours
            self._active_elements[-1].sub_elements.append(element)
        else:
            # we have no subelements yet, this is the first nesting level
            self.sub_elements.append(element)

        # in both cases, this is the "active" subelement now. Everything the parser sees
        # needs to go into this element now.
        self._active_elements.append(element)

    def leave_impl_element(self) -> None:
        """Pop the currently active sub-element."""
        self._active_elements.pop()

    def active_element(self) -> ImplementationDataTypeElement:
        """Return the currently active sub-element."""
        if not self._active_elements:
            raise IndexError("No active sub-element exists.")

        return self._active_elements[-1]

    def has_active_element(self) -> bool:
        return bool(self._active_elements)

    def mark_current_or_active_element_as_optional(self) -> None:
        """Marks the currently parsed element as optional.

        If no 'active_element' exists, it means the whole type is optional'.
        Called when

            <SDG GID="StructureElementProperties">
            <SD GID="isOptional">true</SD>
            </SDG>

        is parsed.
        """
        if self._active_elements:
            self._active_elements[-1].is_optional = True
        else:
            self.is_optional = True

    def to_str(self, pad: str = "\t") -> str:
        """Create a string representation of this element."""
        elems = [self.category.value, self.fqn]
        new_pad = pad + "\t"
        if self.category == Category.TYPE_REFERENCE:
            elems.append(f"-> {self.impl_data_type_ref}")
        elif self.category == Category.ARRAY:
            elems.append(
                f"size: {self.sub_elements[0].array_size} "
                f"[{self.sub_elements[0].to_str(new_pad)}]"
            )
        elif self.category == Category.STRUCTURE:
            for sub in self.sub_elements:
                elems.append(f"\n{pad}- {sub.to_str(new_pad)}")

        return " ".join(elems)


@dataclass
class TlvEntry(Absorbing):
    application_type_ref: str = ""
    tlv_id: int = 0


@dataclass
class _ISignal:
    fqn: str = ""
    someip_transformation_isignal_props: List[SOMEIPTransformationSignalProps] = field(
        default_factory=list
    )

    end_to_end_transformation_isignal_props: List[
        EndToEndTransformationISignalProps
    ] = field(default_factory=list)

    tlv_elements: List[TlvEntry] = field(default_factory=list)
    system_signal_ref: str = ""


@dataclass
class _EventHandler:
    """Part of a ProvidedService instance. Represents one event"""

    location: common_model.ParseLocation
    fqn: str = ""
    event_name: str = ""
    """Last part of the fqn"""
    consumed_event_group_ref: str = ""

    routing_group_ref: Ref[SoAdRoutingGroup] = None  # type: ignore


@dataclass
class _ProvidedServiceInstance:
    location: common_model.ParseLocation
    fqn: str = ""
    short_name: str = ""
    service_id: int = 0
    instance_id: int = 0
    udp_port: int = 0
    # todo: version
    event_handlers: List[_EventHandler] = field(default_factory=list)
    network_endpoint_ref: str = ""
    version_major: int = 0
    version_minor: int = 0


@dataclass
class _ConsumedEventGroup:
    """Part of a ConsumedServiceInstance. Carries the EventGroupId."""

    fqn: str = ""
    event_group_identifier: int = 0


@dataclass
class _ApplicationEndpoint:
    """Context helper to store port while collecting ProvidedServiceInstances."""

    fqn: str = ""
    udp_port: int = 0
    provided_service_instances: List[_ProvidedServiceInstance] = field(
        default_factory=list
    )
    network_endpoint_ref: str = ""


@dataclass
class SwComponentType(Absorbing):
    fqn: str = ""
    ports: List[PortPrototype] = field(default_factory=list)


@dataclass
class SwComponentPrototype(Absorbing):
    fqn: str = ""
    type_ref: Ref[CompositionSwComponentType] = None  # type: ignore


@dataclass
class CompositionSwComponentType(SwComponentType):
    components: List[SwComponentPrototype] = field(default_factory=list)
    data_type_mapping_refs: List[Ref[DataTypeMappingSet]] = field(default_factory=list)


@dataclass
class RootSwCompositionPrototype(Absorbing):
    software_composition_ref: Ref[CompositionSwComponentType] = None  # type: ignore


class Tag:
    """Collection of needed ARXML tags."""

    SHORT_NAME = "SHORT-NAME"
    SYMBOL = "SYMBOL"
    SYSTEM = "SYSTEM"
    ROOT_SW_COMPOSITION_PROTOTYPE = "ROOT-SW-COMPOSITION-PROTOTYPE"
    SOFTWARE_COMPOSITION_TREF = "SOFTWARE-COMPOSITION-TREF"
    COMPOSITION_SW_COMPONENT_TYPE = "COMPOSITION-SW-COMPONENT-TYPE"
    SW_COMPONENT_PROTOTYPE = "SW-COMPONENT-PROTOTYPE"
    SW_COMPONENT_TYPE = "SW-COMPONENT-TYPE"

    DATA_TYPE_MAPPING_REF = "DATA-TYPE-MAPPING-REF"

    # ports
    PORTS = "PORTS"
    P_PORT_PROTOTYPE = "P-PORT-PROTOTYPE"
    R_PORT_PROTOTYPE = "R-PORT-PROTOTYPE"
    PROVIDED_INTERFACE_TREF = "PROVIDED-INTERFACE-TREF"
    REQUIRED_INTERFACE_TREF = "REQUIRED-INTERFACE-TREF"

    # mapping
    SENDER_RECEIVER_TO_SIGNAL_MAPPING = "SENDER-RECEIVER-TO-SIGNAL-MAPPING"
    CONTEXT_PORT_REF = "CONTEXT-PORT-REF"

    COMMUNICATION_DIRECTION = "COMMUNICATION-DIRECTION"
    EVENT_HANDLER_REFS = "EVENT-HANDLER-REFS"
    EVENT_HANDLER_REF = "EVENT-HANDLER-REF"
    SERVICE_INSTANCE_REFS = "SERVICE-INSTANCE-REFS"
    SERVICE_INSTANCE_REF = "SERVICE-INSTANCE-REF"
    DATA_ELEMENT_IREF = "DATA-ELEMENT-IREF"
    TARGET_DATA_PROTOTYPE_REF = "TARGET-DATA-PROTOTYPE-REF"
    SYSTEM_SIGNAL_REF = "SYSTEM-SIGNAL-REF"

    # interface
    SENDER_RECEIVER_INTERFACE = "SENDER-RECEIVER-INTERFACE"
    VARIABLE_DATA_PROTOTYPE = "VARIABLE-DATA-PROTOTYPE"
    DATA_ELEMENTS = "DATA-ELEMENTS"
    TYPE_TREF = "TYPE-TREF"

    DATA_TYPE_MAPPING_SET = "DATA-TYPE-MAPPING-SET"  # only in package
    DATA_TYPE_MAPS = "DATA-TYPE-MAPS"  # only in DATA_TYPE_MAPPING_SET
    DATA_TYPE_MAP = "DATA-TYPE-MAP"  # only in DATA_TYPE_MAPS
    APPLICATION_DATA_TYPE_REF = "APPLICATION-DATA-TYPE-REF"  # used multiple times
    IMPLEMENTATION_DATA_TYPE_REF = "IMPLEMENTATION-DATA-TYPE-REF"  # used multiple times
    BASE_TYPE_REF = "BASE-TYPE-REF"

    # defining types
    IMPLEMENTATION_DATA_TYPE = "IMPLEMENTATION-DATA-TYPE"
    IMPLEMENTATION_DATA_TYPE_ELEMENT = "IMPLEMENTATION-DATA-TYPE-ELEMENT"
    CATEGORY = "CATEGORY"
    SW_DATA_DEF_PROPS = "SW-DATA-DEF-PROPS"
    SW_DATA_DEF_PROPS_VARIANTS = "SW-DATA-DEF-PROPS-VARIANTS"
    SW_DATA_DEF_PROPS_CONDITIONAL = "SW-DATA-DEF-PROPS-CONDITIONAL"

    SUB_ELEMENTS = "SUB-ELEMENTS"
    # Arrays
    ARRAY_SIZE_SEMANTICS = "ARRAY-SIZE-SEMANTICS"
    DYNAMIC_ARRAY_SIZE_PROFILE = "DYNAMIC-ARRAY-SIZE-PROFILE"
    ARRAY_SIZE_HANDLING = "ARRAY-SIZE-HANDLING"
    ARRAY_SIZE = "ARRAY-SIZE"

    APPLICATION_RECORD_DATA_TYPE = "APPLICATION-RECORD-DATA-TYPE"
    APPLICATION_RECORD_ELEMENT = "APPLICATION-RECORD-ELEMENT"
    APPLICATION_PRIMITIVE_DATA_TYPE = "APPLICATION-PRIMITIVE-DATA-TYPE"
    APPLICATION_ARRAY_DATA_TYPE = "APPLICATION-ARRAY-DATA-TYPE"
    ELEMENT = "ELEMENT"

    # defining endpoints
    ETHERNET_CLUSTER = "ETHERNET-CLUSTER"

    CONNECTION_BUNDLES = "CONNECTION-BUNDLES"
    SOCKET_CONNECTION_IPDU_IDENTIFIER = "SOCKET-CONNECTION-IPDU-IDENTIFIER"
    HEADER_ID = "HEADER-ID"
    PDU_TRIGGERING_REF = "PDU-TRIGGERING-REF"

    SOCKET_ADDRESSS = "SOCKET-ADDRESSS"
    SOCKET_ADDRESS = "SOCKET-ADDRESS"
    APPLICATION_ENDPOINT = (
        "APPLICATION-ENDPOINT"  # should always be in a SOCKET-ADDRESS
    )
    TP_CONFIGURATION = "TP-CONFIGURATION"
    UDP_TP = "UDP-TP"
    UDP_TP_PORT = "UDP-TP-PORT"
    PORT_NUMBER = "PORT-NUMBER"

    PDU_TRIGGERING = "PDU-TRIGGERING"
    ISIGNAL_TRIGGERING_REF = "I-SIGNAL-TRIGGERING-REF"

    ISIGNAL_TRIGGERING = "I-SIGNAL-TRIGGERING"
    ISIGNAL_REF = "I-SIGNAL-REF"

    PROVIDED_SERVICE_INSTANCES = "PROVIDED-SERVICE-INSTANCES"
    PROVIDED_SERVICE_INSTANCE = "PROVIDED-SERVICE-INSTANCE"

    SERVER_SERVICE_MAJOR_VERSION = "SERVER-SERVICE-MAJOR-VERSION"
    SERVER_SERVICE_MINOR_VERSION = "SERVER-SERVICE-MINOR-VERSION"
    # This is also a CLIENT-SERVICE-MAJOR-VERSION but we don't use that so far
    # I assume it's for the minimal required version

    CONSUMED_SERVICE_INSTANCES = "CONSUMED-SERVICE-INSTANCES"
    CONSUMED_SERVICE_INSTANCE = "CONSUMED-SERVICE-INSTANCE"

    ROUTING_GROUP_REFS = "ROUTING-GROUP-REFS"
    ROUTING_GROUP_REF = "ROUTING-GROUP-REF"

    EVENT_HANDLERS = "EVENT-HANDLERS"
    EVENT_HANDLER = "EVENT-HANDLER"
    CONSUMED_EVENT_GROUP_REFS = "CONSUMED-EVENT-GROUP-REFS"
    CONSUMED_EVENT_GROUP_REF = "CONSUMED-EVENT-GROUP-REF"

    CONSUMED_EVENT_GROUPS = "CONSUMED-EVENT-GROUPS"
    CONSUMED_EVENT_GROUP = "CONSUMED-EVENT-GROUP"
    EVENT_GROUP_IDENTIFIER = "EVENT-GROUP-IDENTIFIER"

    INSTANCE_IDENTIFIER = "INSTANCE-IDENTIFIER"
    SERVICE_IDENTIFIER = "SERVICE-IDENTIFIER"

    # ISignal
    ISIGNAL = "I-SIGNAL"
    ADMIN_DATA = "ADMIN-DATA"
    SDGS = "SDGS"
    SDG = "SDG"
    SD = "SD"
    SDX_REF = "SDX-REF"
    GID = "GID"

    SOMEIP_TRANSFORMATION_I_SIGNAL_PROPS = "SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS"
    SOMEIP_TRANSFORMATION_I_SIGNAL_PROPS_VARIANTS = (
        "SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS-VARIANTS"
    )
    SOMEIP_TRANSFORMATION_I_SIGNAL_PROPS_CONDITIONAL = (
        "SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS-CONDITIONAL"
    )
    TRANSFORMER_REF = "TRANSFORMER-REF"
    SOMEIP_TRANSFORMATION_DESCRIPTION = "SOMEIP-TRANSFORMATION-DESCRIPTION"
    ALIGNMENT = "ALIGNMENT"
    BYTE_ORDER = "BYTE-ORDER"

    # E2E
    END_TO_END_TRANSFORMATION_I_SIGNAL_PROPS_CONDITIONAL = (
        "END-TO-END-TRANSFORMATION-I-SIGNAL-PROPS-CONDITIONAL"
    )
    DATA_IDS = "DATA-IDS"
    DATA_ID = "DATA-ID"
    DATA_LENGTH = "DATA-LENGTH"
    MAX_DATA_LENGTH = "MAX-DATA-LENGTH"
    MIN_DATA_LENGTH = "MIN-DATA-LENGTH"

    # transformation technologies
    TRANSFORMATION_TECHNOLOGYS = "TRANSFORMATION-TECHNOLOGYS"
    TRANSFORMATION_TECHNOLOGY = "TRANSFORMATION-TECHNOLOGY"
    END_TO_END_TRANSFORMATION_DESCRIPTION = "END-TO-END-TRANSFORMATION-DESCRIPTION"
    PROTOCOL = "PROTOCOL"
    HEADER_LENGTH = "HEADER-LENGTH"
    PROFILE_NAME = "PROFILE-NAME"
    MAX_DELTA_COUNTER = "MAX-DELTA-COUNTER"
    OFFSET = "OFFSET"

    INTERFACE_VERSION = "INTERFACE-VERSION"
    MESSAGE_TYPE = "MESSAGE-TYPE"

    SIZE_OF_ARRAY_LENGTH_FIELDS = "SIZE-OF-ARRAY-LENGTH-FIELDS"
    SIZE_OF_STRUCT_LENGTH_FIELDS = "SIZE-OF-STRUCT-LENGTH-FIELDS"
    SIZE_OF_UNION_LENGTH_FIELDS = "SIZE-OF-UNION-LENGTH-FIELDS"

    DATA_MAPPINGS = "DATA-MAPPINGS"

    NETWORK_ENDPOINT_REF = "NETWORK-ENDPOINT-REF"
    NETWORK_ENDPOINTS = "NETWORK-ENDPOINTS"
    NETWORK_ENDPOINT = "NETWORK-ENDPOINT"
    IPV_6_ADDRESS = "IPV-6-ADDRESS"
    IPV_4_ADDRESS = "IPV-4-ADDRESS"

    SO_AD_ROUTING_GROUP = "SO-AD-ROUTING-GROUP"
    EVENT_GROUP_CONTROL_TYPE = "EVENT-GROUP-CONTROL-TYPE"

    TP_CONNECTIONS = "TP-CONNECTIONS"
    SOMEIP_TP_CONNECTION = "SOMEIP-TP-CONNECTION"
    TP_SDU_REF = "TP-SDU-REF"
    TRANSPORT_PDU_REF = "TRANSPORT-PDU-REF"


class ParserIntf:
    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        ...

    def chardata(self, data: str, parent: str, child: str) -> None:
        ...

    def end(self, name: str) -> None:
        ...

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        ...


class TpConnectionParser(ParserIntf):
    """
    Parses

    <TP-CONNECTIONS>
        <SOMEIP-TP-CONNECTION>
            <TP-SDU-REF DEST="PDU-TRIGGERING">/path/to/TpSdu</TP-SDU-REF>
            <TRANSPORT-PDU-REF DEST="PDU-TRIGGERING">/path/to/transportPdu</TRANSPORT-PDU-REF>
        </SOMEIP-TP-CONNECTION>
        ...

    </TP-CONNECTIONS>
    """

    trigger = Tag.TP_CONNECTIONS

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.someip_tp_connections: List[SomeipTpConnection] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.SOMEIP_TP_CONNECTION:
            self.someip_tp_connections.append(SomeipTpConnection())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.SOMEIP_TP_CONNECTION, Tag.TP_SDU_REF):
            self.someip_tp_connections[-1].tp_sdu_ref = Ref(data.strip(), PduTriggering)
        elif (parent, child) == (Tag.SOMEIP_TP_CONNECTION, Tag.TRANSPORT_PDU_REF):
            self.someip_tp_connections[-1].transport_pdu_ref = Ref(
                data.strip(), PduTriggering
            )

    def end(self, name: str) -> None:
        if name == TpConnectionParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.tp_connections = self.someip_tp_connections


class PortPrototypeParser(ParserIntf):
    """
    Parses

    <PORTS>
        <P-PORT-PROTOTYPE>
            <SHORT-NAME>{name}</SHORT-NAME>
            <PROVIDED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">
                {provided_interface_ref}
            </PROVIDED-INTERFACE-TREF>
        </P-PORT-PROTOTYPE>

        <R-PORT-PROTOTYPE>
            <SHORT-NAME>{name}</SHORT-NAME>
            <REQUIRED-INTERFACE-TREF DEST="SENDER-RECEIVER-INTERFACE">
                {required_intf_ref}
            </REQUIRED-INTERFACE-TREF>
        </R-PORT-PROTOTYPE>

        ...
    </PORTS>
    """

    trigger = Tag.PORTS

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.ports: List[PortPrototype] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.P_PORT_PROTOTYPE:
            self.ports.append(PPortPrototype())
        elif name == Tag.R_PORT_PROTOTYPE:
            self.ports.append(RPortPrototype())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if child == Tag.SHORT_NAME:
            self.ports[-1].fqn = self.dispatcher.get_current_fqn()
        elif child == Tag.PROVIDED_INTERFACE_TREF:
            cast(PPortPrototype, self.ports[-1]).provided_interface_ref = Ref(
                data.strip(), SenderReceiverInterface
            )
        elif child == Tag.REQUIRED_INTERFACE_TREF:
            cast(RPortPrototype, self.ports[-1]).required_interface_ref = Ref(
                data.strip(), SenderReceiverInterface
            )

    def end(self, name: str) -> None:
        if name == PortPrototypeParser.trigger:
            self.dispatcher.pop_active_parser()


class SoAdRoutingGroupParser(ParserIntf):
    """
    Parses

    <SO-AD-ROUTING-GROUP>
        <SHORT-NAME>{name}</SHORT-NAME>
        <EVENT-GROUP-CONTROL-TYPE>{event_group_control_type}</EVENT-GROUP-CONTROL-TYPE>
    </SO-AD-ROUTING-GROUP>
    """

    trigger = Tag.SO_AD_ROUTING_GROUP

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.routing_groups: List[SoAdRoutingGroup] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == SoAdRoutingGroupParser.trigger:
            self.routing_groups.append(SoAdRoutingGroup())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if child == Tag.SHORT_NAME:
            self.routing_groups[-1].fqn = self.dispatcher.get_current_fqn()
        elif child == Tag.EVENT_GROUP_CONTROL_TYPE:
            self.routing_groups[
                -1
            ].event_group_control_type = EventGroupControlTypeEnum(data.strip())

    def end(self, name: str) -> None:
        if name == SoAdRoutingGroupParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.soad_routing_groups = {
            Ref(group.fqn, SoAdRoutingGroup): group for group in self.routing_groups
        }


class NetworkEndpointParser(ParserIntf):
    """Parses

    <NETWORK-ENDPOINTS>
        <NETWORK-ENDPOINT>
            <SHORT-NAME>{name}</SHORT-NAME>
            <NETWORK-ENDPOINT-ADDRESSES>
                <IPV-6-CONFIGURATION>
                    <DEFAULT-ROUTER>{ignoree}</DEFAULT-ROUTER>
                    <IP-ADDRESS-PREFIX-LENGTH>{ignored}</IP-ADDRESS-PREFIX-LENGTH>
                    <IPV-6-ADDRESS>{ipv6-address}</IPV-6-ADDRESS>
                    <IPV-6-ADDRESS-SOURCE>{ignored}</IPV-6-ADDRESS-SOURCE>
                </IPV-6-CONFIGURATION>
            </NETWORK-ENDPOINT-ADDRESSES>
        </NETWORK-ENDPOINT>
        ...

    <NETWORK-ENDPOINTS>
    """

    trigger = Tag.NETWORK_ENDPOINTS

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.network_endpoints: List[NetworkEndpoint] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.NETWORK_ENDPOINT:
            self.network_endpoints.append(NetworkEndpoint())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.NETWORK_ENDPOINT, Tag.SHORT_NAME):
            self.network_endpoints[-1].fqn = self.dispatcher.get_current_fqn()
        elif child == Tag.IPV_6_ADDRESS:
            self.network_endpoints[-1].ipv6_addr = data.strip()
        elif child == Tag.IPV_4_ADDRESS:
            self.network_endpoints[-1].ipv4_addr = data.strip()

    def end(self, name: str) -> None:
        if name == self.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.network_endpoints = {n.fqn: n for n in self.network_endpoints}


class ApplicationPrimitiveDataTypeParser(ParserIntf):
    """Parses
    # TODO: test
    <APPLICATION-PRIMITIVE-DATA-TYPE>
        <SHORT-NAME>{name}</SHORT-NAME>
        <CATEGORY>VALUE</CATEGORY>
    </APPLICATION-PRIMITIVE-DATA-TYPE>

    """

    trigger = Tag.APPLICATION_PRIMITIVE_DATA_TYPE

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.primitive_types: List[ApplicationPrimitiveDataType] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.APPLICATION_PRIMITIVE_DATA_TYPE:
            self.primitive_types.append(ApplicationPrimitiveDataType())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if child == Tag.SHORT_NAME:
            self.primitive_types[-1].fqn = self.dispatcher.get_current_fqn()
        elif child == Tag.CATEGORY:
            self.primitive_types[-1].category = Category(data.strip())

    def end(self, name: str) -> None:
        if name == ApplicationPrimitiveDataTypeParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        for prim_type in self.primitive_types:
            classic_arxml.application_data_types_recursive[prim_type.fqn] = prim_type


class ApplicationArrayDataTypeParser(ParserIntf):
    """Parses

    <APPLICATION-ARRAY-DATA-TYPE>
        <SHORT-NAME>{name}</SHORT-NAME>
        <CATEGORY>ARRAY</CATEGORY>
        <DYNAMIC-ARRAY-SIZE-PROFILE>VSA_LINEAR</DYNAMIC-ARRAY-SIZE-PROFILE>
        <ELEMENT UUID="626a0273-0000-0000-08c4-00000125e13e">
            <SHORT-NAME>AT_TestStruct</SHORT-NAME>
            <CATEGORY>VALUE</CATEGORY>
            <TYPE-TREF DEST="APPLICATION-RECORD-DATA-TYPE">{type_ref}</TYPE-TREF>
            <ARRAY-SIZE-HANDLING>ALL-INDICES-SAME-ARRAY-SIZE</ARRAY-SIZE-HANDLING>
            <ARRAY-SIZE-SEMANTICS>VARIABLE-SIZE</ARRAY-SIZE-SEMANTICS>
            <MAX-NUMBER-OF-ELEMENTS>1141</MAX-NUMBER-OF-ELEMENTS>
        </ELEMENT>
    </APPLICATION-ARRAY-DATA-TYPE>
    """

    trigger = Tag.APPLICATION_ARRAY_DATA_TYPE

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.array_data_types: List[ApplicationArrayDataType] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.APPLICATION_ARRAY_DATA_TYPE:
            self.array_data_types.append(ApplicationArrayDataType())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.APPLICATION_ARRAY_DATA_TYPE, Tag.SHORT_NAME):
            self.array_data_types[-1].fqn = self.dispatcher.get_current_fqn()
            self.array_data_types[-1].short_name = data.strip()

        elif child == Tag.TYPE_TREF:
            self.array_data_types[-1].element_type_ref = data.strip()

    def end(self, name: str) -> None:
        if name == ApplicationArrayDataTypeParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        for array in self.array_data_types:
            classic_arxml.application_data_types_recursive[array.fqn] = array


class ApplicationRecordDataTypeParser(ParserIntf):
    """Parses

    <APPLICATION-RECORD-DATA-TYPE>
        <SHORT-NAME>{name}</SHORT-NAME>
        <CATEGORY>STRUCTURE</CATEGORY>
        <ELEMENTS>
        <APPLICATION-RECORD-ELEMENT>
            <SHORT-NAME>{name}</SHORT-NAME>
            <CATEGORY>{category}</CATEGORY>
            <TYPE-TREF DEST="APPLICATION-PRIMITIVE-DATA-TYPE">{type ref}</TYPE-TREF>
        </APPLICATION-RECORD-ELEMENT>
        </ELEMENTS>
    </APPLICATION-RECORD-DATA-TYPE>
    """

    trigger = Tag.APPLICATION_RECORD_DATA_TYPE

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.record_types: List[ApplicationRecordDataType] = []
        self.elements: List[ApplicationRecordElement] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.APPLICATION_RECORD_DATA_TYPE:
            self.record_types.append(ApplicationRecordDataType())
        elif name == Tag.APPLICATION_RECORD_ELEMENT:
            self.elements.append(ApplicationRecordElement())
            self.elements[-1].parent = self.record_types[-1]

    def chardata(self, data: str, parent: str, child: str) -> None:
        if parent == Tag.APPLICATION_RECORD_DATA_TYPE:
            if child == Tag.SHORT_NAME:
                self.record_types[-1].fqn = self.dispatcher.get_current_fqn()
                self.record_types[-1].short_name = data.strip()
            elif child == Tag.CATEGORY:
                self.record_types[-1].category = Category(data.strip())
        elif parent == Tag.APPLICATION_RECORD_ELEMENT:
            if child == Tag.SHORT_NAME:
                self.elements[-1].fqn = self.dispatcher.get_current_fqn()
                self.elements[-1].short_name = data.strip()
            elif child == Tag.CATEGORY:
                self.elements[-1].category = Category(data.strip())
            elif child == Tag.TYPE_TREF:
                self.elements[-1].type_ref = data.strip()

    def end(self, name: str) -> None:
        if name == ApplicationRecordDataTypeParser.trigger:
            self.dispatcher.pop_active_parser()
        elif name == Tag.APPLICATION_RECORD_ELEMENT:
            # TODO: is len(self.elements) always 1 here?
            for element in self.elements:
                element.idx = len(self.record_types[-1].elements)
                self.record_types[-1].elements[element.short_name] = element
            self.elements.clear()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        for record in self.record_types:
            classic_arxml.application_data_types_recursive[record.fqn] = record
            for element in record.elements.values():
                classic_arxml.application_data_types_recursive[element.fqn] = element


class SenderReceiverMappingParser(ParserIntf):
    """Parses

    <SYSTEM>
      <SHORT-NAME>System</SHORT-NAME>
      <MAPPINGS>
        <SYSTEM-MAPPING>
          <SHORT-NAME>mappings</SHORT-NAME>
          <DATA-MAPPINGS>
            <SENDER-RECEIVER-TO-SIGNAL-MAPPING>

              <COMMUNICATION-DIRECTION>{direction}</COMMUNICATION-DIRECTION>
              <EVENT-HANDLER-REFS>
                <EVENT-HANDLER-REF DEST="EVENT-HANDLER">{event ref}</EVENT-HANDLER-REF>
              </EVENT-HANDLER-REFS>

              <SERVICE-INSTANCE-REFS>
                <SERVICE-INSTANCE-REF DEST="PROVIDED-SERVICE-INSTANCE">
                    {instance ref}
                </SERVICE-INSTANCE-REF>
              </SERVICE-INSTANCE-REFS>

              <DATA-ELEMENT-IREF>
                <TARGET-DATA-PROTOTYPE-REF DEST="VARIABLE-DATA-PROTOTYPE">
                    {target data ref}
                </TARGET-DATA-PROTOTYPE-REF>

                <CONTEXT-PORT-REF DEST="{P/R}-PORT-PROTOTYPE">
                    {context_port}
                </CONTEXT-PORT-REF>
              </DATA-ELEMENT-IREF>

              <SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">
                {system signal ref}
              </SYSTEM-SIGNAL-REF>

            </SENDER-RECEIVER-TO-SIGNAL-MAPPING>
          </DATA-MAPPINGS>
        </SYSTEM-MAPPING>
      </MAPPINGS>
    </SYSTEM>
    """

    trigger = Tag.SENDER_RECEIVER_TO_SIGNAL_MAPPING

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.sender_receiver_mappings: List[SenderReceiverToSignalMapping] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.SENDER_RECEIVER_TO_SIGNAL_MAPPING:
            self.sender_receiver_mappings.append(SenderReceiverToSignalMapping())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if child == Tag.SERVICE_INSTANCE_REF:
            self.sender_receiver_mappings[-1].service_instance_ref = data.strip()
        elif child == Tag.EVENT_HANDLER_REF:
            self.sender_receiver_mappings[-1].event_handler_ref = data.strip()
        elif child == Tag.SYSTEM_SIGNAL_REF:
            self.sender_receiver_mappings[-1].system_signal_ref = data.strip()
        elif child == Tag.TARGET_DATA_PROTOTYPE_REF:
            self.sender_receiver_mappings[-1].target_data_prototype_ref = data.strip()
        elif child == Tag.COMMUNICATION_DIRECTION:
            self.sender_receiver_mappings[-1].direction = CommunicationDirectionType(
                data.strip()
            )
        elif child == Tag.CONTEXT_PORT_REF:
            self.sender_receiver_mappings[-1].context_port_ref = Ref(
                data.strip(), PortPrototype
            )

    def end(self, name: str) -> None:
        if name == SenderReceiverMappingParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.sender_receiver_to_signal_mapping = {
            m.event_handler_ref: m for m in self.sender_receiver_mappings
        }

        classic_arxml.sender_receiver_to_signal_mappings = self.sender_receiver_mappings


class TransformationTechnologyParser(ParserIntf):
    """Parses

    <TRANSFORMATION-TECHNOLOGYS>
        ...
        <TRANSFORMATION-TECHNOLOGY>
            <SHORT-NAME>E2E_Profile4_SOMEIP_Transformer</SHORT-NAME>
            <BUFFER-PROPERTIES>
                <BUFFER-COMPUTATION>
                    <COMPU-RATIONAL-COEFFS>
                    <COMPU-NUMERATOR>
                        <V>12</V>
                        <V>1</V>
                    </COMPU-NUMERATOR>
                    <COMPU-DENOMINATOR>
                        <V>1</V>
                    </COMPU-DENOMINATOR>
                    </COMPU-RATIONAL-COEFFS>
                </BUFFER-COMPUTATION>
                <HEADER-LENGTH>{header_length_bits}</HEADER-LENGTH>
                <IN-PLACE>true</IN-PLACE>
            </BUFFER-PROPERTIES>
            <HAS-INTERNAL-STATE>true</HAS-INTERNAL-STATE>
            <NEEDS-ORIGINAL-DATA>false</NEEDS-ORIGINAL-DATA>
            <PROTOCOL>E2E</PROTOCOL>
            <TRANSFORMATION-DESCRIPTIONS>
                <END-TO-END-TRANSFORMATION-DESCRIPTION>
                    <MAX-DELTA-COUNTER>{max_delta_counter}</MAX-DELTA-COUNTER>
                    <OFFSET>{offset}</OFFSET>
                    <PROFILE-NAME>{profile_name}</PROFILE-NAME>
                    <UPPER-HEADER-BITS-TO-SHIFT>64</UPPER-HEADER-BITS-TO-SHIFT>
                    <COUNTER-OFFSET>8</COUNTER-OFFSET>
                </END-TO-END-TRANSFORMATION-DESCRIPTION>
            </TRANSFORMATION-DESCRIPTIONS>
            <TRANSFORMER-CLASS>SAFETY</TRANSFORMER-CLASS>
            <VERSION>1.0.0</VERSION>
        </TRANSFORMATION-TECHNOLOGY>

        <TRANSFORMATION-TECHNOLOGY>
            <SHORT-NAME>TransformationTechnologySOMEIP_LittleEndian</SHORT-NAME>
            <BUFFER-PROPERTIES>
                <BUFFER-COMPUTATION>
                    <COMPU-RATIONAL-COEFFS>
                    <COMPU-NUMERATOR>
                        <V>8</V>
                        <V>1</V>
                    </COMPU-NUMERATOR>
                    <COMPU-DENOMINATOR>
                        <V>1</V>
                    </COMPU-DENOMINATOR>
                    </COMPU-RATIONAL-COEFFS>
                </BUFFER-COMPUTATION>
                <HEADER-LENGTH>64</HEADER-LENGTH>
                <IN-PLACE>false</IN-PLACE>
            </BUFFER-PROPERTIES>
            <NEEDS-ORIGINAL-DATA>false</NEEDS-ORIGINAL-DATA>
            <PROTOCOL>SOMEIP</PROTOCOL>
            <TRANSFORMATION-DESCRIPTIONS>
                <SOMEIP-TRANSFORMATION-DESCRIPTION>
                    <ALIGNMENT>8</ALIGNMENT>
                    <BYTE-ORDER>MOST-SIGNIFICANT-BYTE-LAST</BYTE-ORDER>
                    <INTERFACE-VERSION>1</INTERFACE-VERSION>
                </SOMEIP-TRANSFORMATION-DESCRIPTION>
            </TRANSFORMATION-DESCRIPTIONS>
            <TRANSFORMER-CLASS>SERIALIZER</TRANSFORMER-CLASS>
            <VERSION>1</VERSION>
        </TRANSFORMATION-TECHNOLOGY>

    </TRANSFORMATION-TECHNOLOGYS>
    """

    trigger = Tag.TRANSFORMATION_TECHNOLOGYS

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.transformation_technologies: List[TransformationTechnology] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.TRANSFORMATION_TECHNOLOGY:
            self.transformation_technologies.append(TransformationTechnology())
        elif name == Tag.END_TO_END_TRANSFORMATION_DESCRIPTION:
            self.transformation_technologies[
                -1
            ].transformation_description = EndToEndTransformationDescription()

        elif name == Tag.SOMEIP_TRANSFORMATION_DESCRIPTION:
            self.transformation_technologies[
                -1
            ].transformation_description = SOMEIPTransformationDescription()

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.TRANSFORMATION_TECHNOLOGY, Tag.SHORT_NAME):
            self.transformation_technologies[-1].fqn = self.dispatcher.get_current_fqn()
            self.transformation_technologies[-1].short_name = data.strip()
        elif child == Tag.PROTOCOL:
            self.transformation_technologies[-1].protocol = data.strip()
        elif child == Tag.HEADER_LENGTH:
            self.transformation_technologies[-1].header_length_bits = int(data.strip())
        elif (parent, child) == (
            Tag.END_TO_END_TRANSFORMATION_DESCRIPTION,
            Tag.MAX_DELTA_COUNTER,
        ):
            cast(
                EndToEndTransformationDescription,
                self.transformation_technologies[-1].transformation_description,
            ).max_delta_counter = int(data.strip())

        elif (parent, child) == (
            Tag.END_TO_END_TRANSFORMATION_DESCRIPTION,
            Tag.PROFILE_NAME,
        ):
            cast(
                EndToEndTransformationDescription,
                self.transformation_technologies[-1].transformation_description,
            ).profile_name = E2EProfileName(data.strip())

        elif (parent, child) == (Tag.END_TO_END_TRANSFORMATION_DESCRIPTION, Tag.OFFSET):
            cast(
                EndToEndTransformationDescription,
                self.transformation_technologies[-1].transformation_description,
            ).offset = int(data.strip())

        elif (parent, child) == (
            Tag.SOMEIP_TRANSFORMATION_DESCRIPTION,
            Tag.ALIGNMENT,
        ):
            cast(
                SOMEIPTransformationDescription,
                self.transformation_technologies[-1].transformation_description,
            ).alignment = int(data.strip())

        elif (parent, child) == (
            Tag.SOMEIP_TRANSFORMATION_DESCRIPTION,
            Tag.BYTE_ORDER,
        ):
            cast(
                SOMEIPTransformationDescription,
                self.transformation_technologies[-1].transformation_description,
            ).byte_order = ByteOrderEnum(data.strip())

        elif (parent, child) == (
            Tag.SOMEIP_TRANSFORMATION_DESCRIPTION,
            Tag.INTERFACE_VERSION,
        ):
            cast(
                SOMEIPTransformationDescription,
                self.transformation_technologies[-1].transformation_description,
            ).interface_version = int(data.strip())

    def end(self, name: str) -> None:
        if name == ISignalParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.transformation_technologies = {
            Ref(tt.fqn, TransformationTechnology): tt
            for tt in self.transformation_technologies
        }


class ISignalParser(ParserIntf):
    """Parses

    <I-SIGNAL>
      <SHORT-NAME>{name}</SHORT-NAME>
      <DATA-TYPE-POLICY>TRANSFORMING-I-SIGNAL</DATA-TYPE-POLICY>
      <I-SIGNAL-TYPE>{isignal type}</I-SIGNAL-TYPE>
      <LENGTH>{ignored}</LENGTH>
      <SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">{system signal ref}</SYSTEM-SIGNAL-REF>
      <TRANSFORMATION-I-SIGNAL-PROPSS>
        <SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS>
          <ADMIN-DATA>
            <SDGS>
              <SDG GID="TlvDataIdDefinition">
                <SDG GID="TlvRecordElement"> <!-- also <SDG GID="TlvArgument"> -->
                  <SDX-REF DEST="APPLICATION-RECORD-ELEMENT">
                    {ref to application record element}
                  </SDX-REF>
                  <SD GID="TLV-ID">{tlv id}</SD>
                </SDG>
              </SDG>
                ...
            </SDGS>
          </ADMIN-DATA>
          <SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS-VARIANTS>
            <SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS-CONDITIONAL>
              <TRANSFORMER-REF DEST="TRANSFORMATION-TECHNOLOGY">
                {ref to someip transformation technology}
                </TRANSFORMER-REF>
              <INTERFACE-VERSION>{interface version}</INTERFACE-VERSION>
              <MESSAGE-TYPE>{message type}</MESSAGE-TYPE>
              <SIZE-OF-ARRAY-LENGTH-FIELDS>{array lfs}</SIZE-OF-ARRAY-LENGTH-FIELDS>
              <SIZE-OF-STRUCT-LENGTH-FIELDS>{struct lfs}</SIZE-OF-STRUCT-LENGTH-FIELDS>
              <SIZE-OF-UNION-LENGTH-FIELDS>{union lfs}</SIZE-OF-UNION-LENGTH-FIELDS>
            </SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS-CONDITIONAL>
          </SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS-VARIANTS>
        </SOMEIP-TRANSFORMATION-I-SIGNAL-PROPS>
        <END-TO-END-TRANSFORMATION-I-SIGNAL-PROPS>
            <END-TO-END-TRANSFORMATION-I-SIGNAL-PROPS-VARIANTS>
                <END-TO-END-TRANSFORMATION-I-SIGNAL-PROPS-CONDITIONAL>
                    <TRANSFORMER-REF DEST="TRANSFORMATION-TECHNOLOGY">
                        {transformer-ref}
                    </TRANSFORMER-REF>
                    <DATA-IDS>
                        {data_ids}
                    </DATA-IDS>
                    <MAX-DATA-LENGTH>{max_data_length_bits}</MAX-DATA-LENGTH>
                    <MIN-DATA-LENGTH>{min_data_length_bits}</MIN-DATA-LENGTH>
                </END-TO-END-TRANSFORMATION-I-SIGNAL-PROPS-CONDITIONAL>
            </END-TO-END-TRANSFORMATION-I-SIGNAL-PROPS-VARIANTS>
        </END-TO-END-TRANSFORMATION-I-SIGNAL-PROPS>

      </TRANSFORMATION-I-SIGNAL-PROPSS>
    </I-SIGNAL>
    """

    trigger = Tag.ISIGNAL

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.i_signals: List[_ISignal] = []
        self._read_tlv = False

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == ISignalParser.trigger:
            self.i_signals.append(_ISignal())
        elif name == Tag.SDG and attributes.get("GID") == "TlvRecordElement":
            self.i_signals[-1].tlv_elements.append(TlvEntry())
            self._read_tlv = True
        elif name == Tag.SOMEIP_TRANSFORMATION_I_SIGNAL_PROPS_CONDITIONAL:
            self.i_signals[-1].someip_transformation_isignal_props.append(
                SOMEIPTransformationSignalProps()
            )
        elif name == Tag.END_TO_END_TRANSFORMATION_I_SIGNAL_PROPS_CONDITIONAL:
            self.i_signals[-1].end_to_end_transformation_isignal_props.append(
                EndToEndTransformationISignalProps(
                    location=self.dispatcher.current_location()
                )
            )

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.ISIGNAL, Tag.SHORT_NAME):
            self.i_signals[-1].fqn = self.dispatcher.get_current_fqn()
        elif child == Tag.SDX_REF and self._read_tlv:
            self.i_signals[-1].tlv_elements[-1].application_type_ref = data.strip()
        elif child == Tag.SD and self._read_tlv:
            self.i_signals[-1].tlv_elements[-1].tlv_id = int(data.strip())
        elif child == Tag.INTERFACE_VERSION:
            self.i_signals[-1].someip_transformation_isignal_props[
                -1
            ].interface_version = int(data.strip())
        elif child == Tag.MESSAGE_TYPE:
            self.i_signals[-1].someip_transformation_isignal_props[
                -1
            ].message_type = SOMEIPMessageTypeEnum(data.strip().lower())
        elif child == Tag.SIZE_OF_ARRAY_LENGTH_FIELDS:
            self.i_signals[-1].someip_transformation_isignal_props[
                -1
            ].size_of_array_length_fields = int(data.strip())
        elif child == Tag.SIZE_OF_STRUCT_LENGTH_FIELDS:
            self.i_signals[-1].someip_transformation_isignal_props[
                -1
            ].size_of_struct_length_fields = int(data.strip())
        elif child == Tag.SIZE_OF_UNION_LENGTH_FIELDS:
            self.i_signals[-1].someip_transformation_isignal_props[
                -1
            ].size_of_union_length_fields = int(data.strip())
        elif child == Tag.SYSTEM_SIGNAL_REF:
            self.i_signals[-1].system_signal_ref = data.strip()
        elif (parent, child) == (
            Tag.END_TO_END_TRANSFORMATION_I_SIGNAL_PROPS_CONDITIONAL,
            Tag.TRANSFORMER_REF,
        ):
            self.i_signals[-1].end_to_end_transformation_isignal_props[
                -1
            ].transformer_ref = Ref(data.strip(), TransformationTechnology)
        elif (parent, child) == (
            Tag.SOMEIP_TRANSFORMATION_I_SIGNAL_PROPS_CONDITIONAL,
            Tag.TRANSFORMER_REF,
        ):
            self.i_signals[-1].someip_transformation_isignal_props[
                -1
            ].transformer_ref = Ref(data.strip(), TransformationTechnology)
        elif (parent, child) == (Tag.DATA_IDS, Tag.DATA_ID):
            self.i_signals[-1].end_to_end_transformation_isignal_props[
                -1
            ].data_ids.append(int(data.strip()))
        elif child == Tag.MAX_DATA_LENGTH:
            self.i_signals[-1].end_to_end_transformation_isignal_props[
                -1
            ].max_data_length_bits = int(data.strip())
        elif child == Tag.MIN_DATA_LENGTH:
            self.i_signals[-1].end_to_end_transformation_isignal_props[
                -1
            ].min_data_length_bits = int(data.strip())

    def end(self, name: str) -> None:
        if name == ISignalParser.trigger:
            self.dispatcher.pop_active_parser()
        elif name == Tag.SDG:
            self._read_tlv = False

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.i_signals = {
            s.fqn: ISignal(
                fqn=s.fqn,
                someip_transformation_isignal_props=s.someip_transformation_isignal_props,
                e2e_transformation_isignal_props=s.end_to_end_transformation_isignal_props,
                tlv_elements={tlv.application_type_ref: tlv for tlv in s.tlv_elements},
                system_signal_ref=s.system_signal_ref,
            )
            for s in self.i_signals
        }


class ConsumedServiceParser(ParserIntf):
    """Parses

    </CONSUMED-SERVICE-INSTANCES>
        <CONSUMED-SERVICE-INSTANCE>
            <SHORT-NAME>{consumed_name}</SHORT-NAME>
            <CONSUMED-EVENT-GROUPS>
                <CONSUMED-EVENT-GROUP>
                    <SHORT-NAME>{consumed_group_name}</SHORT-NAME>
                    <APPLICATION-ENDPOINT-REF DEST="APPLICATION-ENDPOINT">
                        {application endpoint}
                    </APPLICATION-ENDPOINT-REF>
                    <EVENT-GROUP-IDENTIFIER>{event_group_id}</EVENT-GROUP-IDENTIFIER>
                </CONSUMED-EVENT-GROUP>
            </CONSUMED-EVENT-GROUPS>
        </CONSUMED-SERVICE-INSTANCE>
    </CONSUMED-SERVICE-INSTANCES>
    """

    trigger = Tag.CONSUMED_SERVICE_INSTANCES

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.consumed_event_groups: List[_ConsumedEventGroup] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.CONSUMED_EVENT_GROUP:
            self.consumed_event_groups.append(_ConsumedEventGroup())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.CONSUMED_EVENT_GROUP, Tag.SHORT_NAME):
            self.consumed_event_groups[-1].fqn = self.dispatcher.get_current_fqn()
        elif child == Tag.EVENT_GROUP_IDENTIFIER:
            self.consumed_event_groups[-1].event_group_identifier = int(data.strip())

    def end(self, name: str) -> None:
        if name == ConsumedServiceParser.trigger:
            self.dispatcher.pop_active_parser()


class ProvidedServiceParser(ParserIntf):
    """Parses

    <PROVIDED-SERVICE-INSTANCES>
        <PROVIDED-SERVICE-INSTANCE>
            <SHORT-NAME>{name}</SHORT-NAME>
            <INSTANCE-IDENTIFIER>{instance_id}</INSTANCE-IDENTIFIER>"
            <SERVICE-IDENTIFIER>{service_id}</SERVICE-IDENTIFIER>"
            <EVENT-HANDLERS>
                <EVENT-HANDLER>
                    <SHORT-NAME>{event_name}</SHORT-NAME>
                    <CONSUMED-EVENT-GROUP-REFS>
                        <CONSUMED-EVENT-GROUP-REF DEST="CONSUMED-EVENT-GROUP">
                            {consumed_event_group_ref}
                        </CONSUMED-EVENT-GROUP-REF>
                    </CONSUMED-EVENT-GROUP-REFS>
                    <ROUTING-GROUP-REFS>
                        <ROUTING-GROUP-REF DEST="SO-AD-ROUTING-GROUP">
                            {routing group ref}
                        </ROUTING-GROUP-REF>
                    </ROUTING-GROUP-REFS>
                </EVENT-HANDLER>
            </EVENT-HANDLERS>
        </PROVIDED-SERVICE-INSTANCE>

    </PROVIDED-SERVICE-INSTANCES>
    """

    trigger = Tag.PROVIDED_SERVICE_INSTANCES

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher

        self.provided_service_instances: List[_ProvidedServiceInstance] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.PROVIDED_SERVICE_INSTANCE:
            self.provided_service_instances.append(
                _ProvidedServiceInstance(location=self.dispatcher.current_location())
            )
        elif name == Tag.EVENT_HANDLER:
            self.provided_service_instances[-1].event_handlers.append(
                _EventHandler(location=self.dispatcher.current_location())
            )

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.PROVIDED_SERVICE_INSTANCE, Tag.SHORT_NAME):
            self.provided_service_instances[-1].fqn = self.dispatcher.get_current_fqn()
            self.provided_service_instances[-1].short_name = data.strip()
        elif (parent, child) == (Tag.EVENT_HANDLER, Tag.SHORT_NAME):
            self.provided_service_instances[-1].event_handlers[
                -1
            ].fqn = self.dispatcher.get_current_fqn()
            self.provided_service_instances[-1].event_handlers[
                -1
            ].event_name = data.strip()
        elif child == Tag.INSTANCE_IDENTIFIER:
            self.provided_service_instances[-1].instance_id = int(data.strip())
        elif child == Tag.SERVICE_IDENTIFIER:
            self.provided_service_instances[-1].service_id = int(data.strip())
        elif child == Tag.SERVER_SERVICE_MAJOR_VERSION:
            self.provided_service_instances[-1].version_major = int(data.strip())
        elif child == Tag.SERVER_SERVICE_MINOR_VERSION:
            self.provided_service_instances[-1].version_minor = int(data.strip())
        elif child == Tag.CONSUMED_EVENT_GROUP_REF:
            self.provided_service_instances[-1].event_handlers[
                -1
            ].consumed_event_group_ref = data.strip()

        elif (self.dispatcher._stack[-3], parent, child) == (
            Tag.EVENT_HANDLER,
            Tag.ROUTING_GROUP_REFS,
            Tag.ROUTING_GROUP_REF,
        ):
            self.provided_service_instances[-1].event_handlers[
                -1
            ].routing_group_ref = Ref(data.strip(), SoAdRoutingGroup)

    def end(self, name: str) -> None:
        if name == ProvidedServiceParser.trigger:
            self.dispatcher.pop_active_parser()

    def clear(self) -> None:
        """
        The SocketAddressParser calls this method to reuse its instance of this Parser.
        """
        self.provided_service_instances.clear()


class SocketAddressParser(ParserIntf):
    """Parses

    <SOCKET-ADDRESS>
        <SHORT-NAME>udp_sock_addr</SHORT-NAME>
        <APPLICATION-ENDPOINT>
            <SHORT-NAME>{name}</SHORT-NAME>
            <NETWORK-ENDPOINT-REF DEST="NETWORK-ENDPOINT">
                {endpoint}
            </NETWORK-ENDPOINT-REF>

            [ ProvidedServiceParser here ]

            [ ConsumedServiceParser here ]

            <TP-CONFIGURATION>
                <UDP-TP>
                    <UDP-TP-PORT>
                        <DYNAMICALLY-ASSIGNED>false</DYNAMICALLY-ASSIGNED>
                        <PORT-NUMBER>{udp_port}</PORT-NUMBER>
                    </UDP-TP-PORT>
                </UDP-TP>
            </TP-CONFIGURATION>
        </APPLICATION-ENDPOINT>
    </SOCKET-ADDRESS>
    """

    trigger = Tag.SOCKET_ADDRESSS

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.application_endpoints: List[_ApplicationEndpoint] = []
        self.provided_instances_parser = ProvidedServiceParser(self.dispatcher)
        self.consumed_instances_parser = ConsumedServiceParser(self.dispatcher)

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.APPLICATION_ENDPOINT:
            self.application_endpoints.append(_ApplicationEndpoint())
        elif name == ProvidedServiceParser.trigger:
            self.dispatcher.push_active_parser(self.provided_instances_parser)
        elif name == ConsumedServiceParser.trigger:
            self.dispatcher.push_active_parser(self.consumed_instances_parser)

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.UDP_TP_PORT, Tag.PORT_NUMBER):
            self.application_endpoints[-1].udp_port = int(data.strip())
        elif child == Tag.NETWORK_ENDPOINT_REF:
            self.application_endpoints[-1].network_endpoint_ref = data.strip()

    def end(self, name: str) -> None:
        if name == Tag.APPLICATION_ENDPOINT:
            for inst in self.provided_instances_parser.provided_service_instances:
                inst.udp_port = self.application_endpoints[-1].udp_port
                inst.network_endpoint_ref = self.application_endpoints[
                    -1
                ].network_endpoint_ref
                self.application_endpoints[-1].provided_service_instances.append(inst)
            # we clear the parser because the next time it is called, we are in another
            # endpoint with new provided services
            self.provided_instances_parser.clear()
        elif name == SocketAddressParser.trigger:
            self.dispatcher.pop_active_parser()


class SocketConnectionBundleParser(ParserIntf):
    """Parses

    <CONNECTION-BUNDLES>
        <SOCKET-CONNECTION-BUNDLE>
            <SHORT-NAME>ignored name</SHORT-NAME>
            <BUNDLED-CONNECTIONS>
                <SOCKET-CONNECTION>
                    <PDUS>
                        <SOCKET-CONNECTION-IPDU-IDENTIFIER>
                            <HEADER-ID>{header_id}</HEADER-ID>
                            <PDU-TRIGGERING-REF DEST="PDU-TRIGGERING">
                                {pdu_triggering}
                            </PDU-TRIGGERING-REF>
                            <ROUTING-GROUP-REFS>
                                <ROUTING-GROUP-REF DEST="SO-AD-ROUTING-GROUP">
                                    {routing_group_ref}
                                </ROUTING-GROUP-REF>
                            </ROUTING-GROUP-REFS>
                        </SOCKET-CONNECTION-IPDU-IDENTIFIER>
                    </PDUS>
                </SOCKET-CONNECTION>
            </BUNDLED-CONNECTIONS>
        </SOCKET-CONNECTION-BUNDLE>
    </CONNECTION-BUNDLES>
    """

    trigger = Tag.CONNECTION_BUNDLES

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.socket_connection_ipdu_idents: List[SocketConnectionIPduIdentifier] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == Tag.SOCKET_CONNECTION_IPDU_IDENTIFIER:
            self.socket_connection_ipdu_idents.append(SocketConnectionIPduIdentifier())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if child == Tag.PDU_TRIGGERING_REF:
            self.socket_connection_ipdu_idents[-1].pdu_triggering_ref = Ref(
                data.strip(), PduTriggering
            )
        elif child == Tag.ROUTING_GROUP_REF:
            self.socket_connection_ipdu_idents[-1].routing_group_refs.append(
                Ref(data.strip(), SoAdRoutingGroup)
            )
        elif child == Tag.HEADER_ID:
            self.socket_connection_ipdu_idents[-1].header_id = int(data.strip())

    def end(self, name: str) -> None:
        if name == SocketConnectionBundleParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        ...


class EthernetClusterParser(ParserIntf):
    """Parses

    <ETHERNET-CLUSTER>
        <SHORT-NAME>eth_cluster</SHORT-NAME>
        <ETHERNET-CLUSTER-VARIANTS>
            <ETHERNET-CLUSTER-CONDITIONAL>
                <PHYSICAL-CHANNELS>
                    <ETHERNET-PHYSICAL-CHANNEL>
                    <SHORT-NAME>eth_phy_channel</SHORT-NAME>
                        <SO-AD-CONFIG>
                            <SOCKET-ADDRESSS>

                                [ SocketAdressParser here ]

                            </SOCKET-ADDRESSS>
                            <CONNECTION-BUNDLES>

                                [ SocketConnectionBundleParser here ]

                            </CONNECTION-BUNDLES>
                        </SO-AD-CONFIG>

                        <PDU-TRIGGERINGS>
                            <PDU-TRIGGERING>
                                <SHORT-NAME>{name}</SHORT-NAME>
                                <I-SIGNAL-TRIGGERINGS>
                                    <I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                                    <I-SIGNAL-TRIGGERING-REF DEST="I-SIGNAL-TRIGGERING">
                                            {isignal-triggering ref}
                                    </I-SIGNAL-TRIGGERING-REF>
                                    </I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                                </I-SIGNAL-TRIGGERINGS>
                            </PDU-TRIGGERING>
                        </PDU-TRIGGERINGS>

                      <I-SIGNAL-TRIGGERINGS>
                        <I-SIGNAL-TRIGGERING>
                          <SHORT-NAME>{name}</SHORT-NAME>
                          <I-SIGNAL-REF DEST="I-SIGNAL">{isignal-ref}</I-SIGNAL-REF>
                        </I-SIGNAL-TRIGGERING>
                      </I-SIGNAL-TRIGGERINGS>

                    </ETHERNET-PHYSICAL-CHANNEL>
                </PHYSICAL-CHANNELS>
            </ETHERNET-CLUSTER-CONDITIONAL>
        </ETHERNET-CLUSTER-VARIANTS>
    </ETHERNET-CLUSTER>
    """

    trigger = Tag.ETHERNET_CLUSTER

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.pdu_triggerings: List[PduTriggering] = []
        self.isignal_triggerings: List[ISignalTriggering] = []
        self.address_parser = SocketAddressParser(dispatcher)
        self.socket_connection_bundle_parser = SocketConnectionBundleParser(dispatcher)

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == SocketAddressParser.trigger:
            self.dispatcher.push_active_parser(self.address_parser)
        elif name == SocketConnectionBundleParser.trigger:
            self.dispatcher.push_active_parser(self.socket_connection_bundle_parser)
        elif name == Tag.PDU_TRIGGERING:
            self.pdu_triggerings.append(PduTriggering())
        elif name == Tag.ISIGNAL_TRIGGERING:
            self.isignal_triggerings.append(ISignalTriggering())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.PDU_TRIGGERING, Tag.SHORT_NAME):
            self.pdu_triggerings[-1].fqn = self.dispatcher.get_current_fqn()
        elif (parent, child) == (Tag.ISIGNAL_TRIGGERING, Tag.SHORT_NAME):
            self.isignal_triggerings[-1].fqn = self.dispatcher.get_current_fqn()
        elif child == Tag.ISIGNAL_TRIGGERING_REF:
            self.pdu_triggerings[-1].isignal_triggering_refs.append(
                Ref(data.strip(), ISignalTriggering)
            )
        elif child == Tag.ISIGNAL_REF:
            self.isignal_triggerings[-1].isignal_ref = Ref(data.strip(), ISignal)

    def end(self, name: str) -> None:
        if name == SocketAddressParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        """This combines the consumed event_ids with the"""
        classic_arxml.pdu_triggerings = {
            Ref(pdu.fqn, PduTriggering): pdu for pdu in self.pdu_triggerings
        }
        classic_arxml.isignal_triggerings = {
            Ref(isig.fqn, ISignalTriggering): isig for isig in self.isignal_triggerings
        }
        classic_arxml.socket_connection_ipdu_identifiers = [
            s
            for s in self.socket_connection_bundle_parser.socket_connection_ipdu_idents
            if s.pdu_triggering_ref is not None and s.routing_group_refs
        ]
        consumed_fqn_to_event_id = {
            g.fqn: g.event_group_identifier
            for g in self.address_parser.consumed_instances_parser.consumed_event_groups
        }
        provided_instances: Dict[str, ProvidedServiceInstance] = {}

        for ae in self.address_parser.application_endpoints:
            for instance in ae.provided_service_instances:
                events = {}
                for event in instance.event_handlers:
                    event_id = consumed_fqn_to_event_id[event.consumed_event_group_ref]
                    assert event.routing_group_ref
                    events[event.fqn] = Event(
                        location=event.location,
                        fqn=event.fqn,
                        event_group_id=event_id,
                        event_name=event.event_name,
                        routing_group_ref=event.routing_group_ref,
                    )

                major = instance.version_major
                minor = instance.version_minor
                if major is None:
                    assert minor is None
                    major = 1
                    minor = 0
                provided_instances[instance.fqn] = ProvidedServiceInstance(
                    location=instance.location,
                    fqn=instance.fqn,
                    short_name=instance.short_name,
                    service_id=instance.service_id,
                    instance_id=instance.instance_id,
                    udp_port=ae.udp_port,
                    events=events,
                    network_endpoint_ref=instance.network_endpoint_ref,
                    version=(major, minor),
                )

        classic_arxml.provided_service_instances = provided_instances


class ImplementationDataTypeParser(ParserIntf):
    """Parses

    Category value:

    <IMPLEMENTATION-DATA-TYPE>
        <SHORT-NAME>{name}</SHORT-NAME>
        <CATEGORY>VALUE</CATEGORY>
        <SW-DATA-DEF-PROPS>
            <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <BASE-TYPE-REF DEST="SW-BASE-TYPE">{base ref here}</BASE-TYPE-REF>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
            </SW-DATA-DEF-PROPS-VARIANTS>
        </SW-DATA-DEF-PROPS>
        <SYMBOL-PROPS>
            <SHORT-NAME>SymbolProps</SHORT-NAME>
            <SYMBOL>{symbol_name}</SYMBOL>
        </SYMBOL-PROPS>
    </IMPLEMENTATION-DATA-TYPE>

    Category TypeReference:

    <IMPLEMENTATION-DATA-TYPE>
        <SHORT-NAME>{name}</SHORT-NAME>
        <CATEGORY>TYPE_REFERENCE</CATEGORY>
        <SW-DATA-DEF-PROPS>
            <SW-DATA-DEF-PROPS-VARIANTS>
                <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <IMPLEMENTATION-DATA-TYPE-REF DEST="IMPLEMENTATION-DATA-TYPE">
                        {implementation ref here}
                    </IMPLEMENTATION-DATA-TYPE-REF>
                </SW-DATA-DEF-PROPS-CONDITIONAL>
            </SW-DATA-DEF-PROPS-VARIANTS>
        </SW-DATA-DEF-PROPS>
        <SYMBOL-PROPS>
            <SHORT-NAME>SymbolProps</SHORT-NAME>
            <SYMBOL>{symbol_name}</SYMBOL>
        </SYMBOL-PROPS>
    </IMPLEMENTATION-DATA-TYPE>

    Category Struct:

    <IMPLEMENTATION-DATA-TYPE>
        <SHORT-NAME>{name}</SHORT-NAME>
        <CATEGORY>STRUCTURE</CATEGORY>
        <DYNAMIC-ARRAY-SIZE-PROFILE>{arraySizeProfile}</DYNAMIC-ARRAY-SIZE-PROFILE>
        <SUB-ELEMENTS>

            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>{member name}</SHORT-NAME>
                <CATEGORY>ARRAY</CATEGORY>
                <ARRAY-SIZE-SEMANTICS>{arraySizeSemantics}</ARRAY-SIZE-SEMANTICS>
                <SUB-ELEMENTS>
                    <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                        <SHORT-NAME>ignore</SHORT-NAME>
                        <CATEGORY>TYPE_REFERENCE</CATEGORY>
                        <ARRAY-SIZE>1</ARRAY-SIZE>
                        <ARRAY-SIZE-HANDLING>{arraySizeHandling}</ARRAY-SIZE-HANDLING>
                        <ARRAY-SIZE-SEMANTICS>{arraySizeSemantics}</ARRAY-SIZE-SEMANTICS>
                        <SW-DATA-DEF-PROPS>
                            <SW-DATA-DEF-PROPS-VARIANTS>
                                <SW-DATA-DEF-PROPS-CONDITIONAL>
                                    <IMPLEMENTATION-DATA-TYPE-REF
                                        DEST="IMPLEMENTATION-DATA-TYPE">
                                        {ref}
                                    </IMPLEMENTATION-DATA-TYPE-REF>
                                </SW-DATA-DEF-PROPS-CONDITIONAL>
                            </SW-DATA-DEF-PROPS-VARIANTS>
                        </SW-DATA-DEF-PROPS>
                    </IMPLEMENTATION-DATA-TYPE-ELEMENT>
                </SUB-ELEMENTS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>

            <IMPLEMENTATION-DATA-TYPE-ELEMENT>
                <SHORT-NAME>{member name}</SHORT-NAME>
                    <CATEGORY>TYPE_REFERENCE</CATEGORY>
                    <ADMIN-DATA>
                        <SDGS>
                            <SDG GID="StructureElementProperties">
                                <SD GID="isOptional">true</SD>
                            </SDG>
                        </SDGS>
                    </ADMIN-DATA>
                <SW-DATA-DEF-PROPS>
                    <SW-DATA-DEF-PROPS-VARIANTS>
                        <SW-DATA-DEF-PROPS-CONDITIONAL>
                            <IMPLEMENTATION-DATA-TYPE-REF
                                DEST="IMPLEMENTATION-DATA-TYPE">
                                    {impl ref}
                                </IMPLEMENTATION-DATA-TYPE-REF>
                        </SW-DATA-DEF-PROPS-CONDITIONAL>
                    </SW-DATA-DEF-PROPS-VARIANTS>
                </SW-DATA-DEF-PROPS>
            </IMPLEMENTATION-DATA-TYPE-ELEMENT>
        </SUB-ELEMENTS>
        <SYMBOL-PROPS>
            <SHORT-NAME>SymbolProps</SHORT-NAME>
            <SYMBOL>{symbol_name}</SYMBOL>
        </SYMBOL-PROPS>
    </IMPLEMENTATION-DATA-TYPE>
    """

    trigger = Tag.IMPLEMENTATION_DATA_TYPE

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.impl_data_types: List[ImplementationDataType] = []
        self._reading_optional = False

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == ImplementationDataTypeParser.trigger:
            self.impl_data_types.append(
                ImplementationDataType(location=self.dispatcher.current_location())
            )
        elif name == Tag.IMPLEMENTATION_DATA_TYPE_ELEMENT:
            sub_element = ImplementationDataTypeElement(
                location=self.dispatcher.current_location()
            )
            self.impl_data_types[-1].add_impl_element(sub_element)
        elif name == Tag.SD and attributes.get("GID") == "isOptional":
            self._reading_optional = True

    def chardata(self, data: str, parent: str, child: str) -> None:
        # root node
        if parent == Tag.IMPLEMENTATION_DATA_TYPE:
            if child == Tag.SHORT_NAME:
                self.impl_data_types[-1].fqn = self.dispatcher.get_current_fqn()
                self.impl_data_types[-1].short_name = data.strip()
            elif child == Tag.CATEGORY:
                self.impl_data_types[-1].category = Category(data.strip())
            elif child == Tag.DYNAMIC_ARRAY_SIZE_PROFILE:
                self.impl_data_types[
                    -1
                ].dynamic_array_size_profile = DynamicArraySizeProfile(data.strip())

        # SUB-ELEMENTS nodes
        elif parent == Tag.IMPLEMENTATION_DATA_TYPE_ELEMENT:
            if child == Tag.SHORT_NAME:
                self.impl_data_types[-1].active_element().short_name = data.strip()
            elif child == Tag.CATEGORY:
                self.impl_data_types[-1].active_element().category = Category(
                    data.strip()
                )
            elif child == Tag.ARRAY_SIZE:
                self.impl_data_types[-1].active_element().array_size = int(data.strip())
            elif child == Tag.ARRAY_SIZE_HANDLING:
                self.impl_data_types[
                    -1
                ].active_element().array_size_handling = ArraySizeHandling(data.strip())
            elif child == Tag.ARRAY_SIZE_SEMANTICS:
                self.impl_data_types[
                    -1
                ].active_element().array_size_semantics = ArraySizeSemantics(
                    data.strip()
                )

        # identical for both
        elif child == Tag.IMPLEMENTATION_DATA_TYPE_REF:
            if self.impl_data_types[-1].has_active_element():
                self.impl_data_types[
                    -1
                ].active_element().impl_data_type_ref = data.strip()
            else:
                self.impl_data_types[-1].impl_data_type_ref = data.strip()

        elif child == Tag.BASE_TYPE_REF:
            if self.impl_data_types[-1].has_active_element():
                self.impl_data_types[-1].active_element().base_type_ref = data.strip()
            else:
                self.impl_data_types[-1].base_type_ref = data.strip()
        elif child == Tag.SD and self._reading_optional:
            if data.strip() == "true":
                self.impl_data_types[-1].mark_current_or_active_element_as_optional()
            self._reading_optional = False

        elif child == Tag.SYMBOL:
            self.impl_data_types[-1].symbol_name = data.strip()

    def end(self, name: str) -> None:
        if name == Tag.IMPLEMENTATION_DATA_TYPE:
            self.dispatcher.pop_active_parser()

        elif name == Tag.IMPLEMENTATION_DATA_TYPE_ELEMENT:
            self.impl_data_types[-1].leave_impl_element()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.implementation_data_types = {
            t.fqn: t for t in self.impl_data_types
        }


class DataTypeMappingSetParser(ParserIntf):
    """Parses

    <DATA-TYPE-MAPPING-SET>
        <SHORT-NAME>{name}</SHORT-NAME>
        <DATA-TYPE-MAPS>
            <DATA-TYPE-MAP>
                <APPLICATION-DATA-TYPE-REF DEST={appl_dest}>
                    {appl_ref}
                </APPLICATION-DATA-TYPE-REF>
                <IMPLEMENTATION-DATA-TYPE-REF DEST={impl_dest}>
                    {impl_ref}
                </IMPLEMENTATION-DATA-TYPE-REF>
            </DATA-TYPE-MAP>
        </DATA-TYPE-MAPS>
    </DATA-TYPE-MAPPING-SET>
    """

    trigger = Tag.DATA_TYPE_MAPPING_SET

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.mapping_sets: List[DataTypeMappingSet] = []
        self.current_appl_ref = ""
        self.current_impl_ref = ""

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == DataTypeMappingSetParser.trigger:
            self.mapping_sets.append(DataTypeMappingSet())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.DATA_TYPE_MAPPING_SET, Tag.SHORT_NAME):
            self.mapping_sets[-1].fqn = self.dispatcher.get_current_fqn()
        elif child == Tag.APPLICATION_DATA_TYPE_REF:
            self.current_appl_ref = data.strip()
        elif child == Tag.IMPLEMENTATION_DATA_TYPE_REF:
            self.current_impl_ref = data.strip()

    def end(self, name: str) -> None:
        if name == Tag.DATA_TYPE_MAPPING_SET:
            self.dispatcher.pop_active_parser()
        elif name == Tag.DATA_TYPE_MAP:
            self.mapping_sets[-1].mapping[self.current_appl_ref] = self.current_impl_ref

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.mapping_sets = {m.fqn: m for m in self.mapping_sets}


class SenderReceiverInterfaceParser(ParserIntf):
    """Parses

    <SENDER-RECEIVER-INTERFACE>
        <SHORT-NAME>{name}</SHORT-NAME>
        <DATA-ELEMENTS>
            <VARIABLE-DATA-PROTOTYPE>
                <SHORT-NAME>{name}</SHORT-NAME>
                <TYPE-TREF DEST="{dest}">{ref}</TYPE-TREF>
            </VARIABLE-DATA-PROTOTYPE>
        </DATA-ELEMENTS>
    </SENDER-RECEIVER-INTERFACE>"

    """

    trigger = Tag.SENDER_RECEIVER_INTERFACE

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.interfaces: List[SenderReceiverInterface] = []
        self.current_prototype = VariableDataPrototype()

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == SenderReceiverInterfaceParser.trigger:
            self.interfaces.append(SenderReceiverInterface())
        elif name == Tag.VARIABLE_DATA_PROTOTYPE:
            self.current_prototype = VariableDataPrototype()
        elif name == Tag.TYPE_TREF:
            self.current_prototype.dest = attributes.get("DEST", "")

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.SENDER_RECEIVER_INTERFACE, Tag.SHORT_NAME):
            self.interfaces[-1].fqn = self.dispatcher.get_current_fqn()
        elif (parent, child) == (Tag.VARIABLE_DATA_PROTOTYPE, Tag.SHORT_NAME):
            self.current_prototype.fqn = self.dispatcher.get_current_fqn()
            self.current_prototype.short_name = data.strip()
        elif self.dispatcher._stack[-1] == Tag.TYPE_TREF:
            self.current_prototype.application_type_ref = data.strip()

    def end(self, name: str) -> None:
        if name == Tag.SENDER_RECEIVER_INTERFACE:
            self.dispatcher.pop_active_parser()
        elif name == Tag.VARIABLE_DATA_PROTOTYPE:
            self.interfaces[-1].data_elements[
                self.current_prototype.fqn
            ] = self.current_prototype

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.sender_receiver_interfaces = {i.fqn: i for i in self.interfaces}


class CompositionSwComponentTypeParser(ParserIntf):
    """
    Parses

    <COMPOSITION-SW-COMPONENT-TYPE>
        <SHORT-NAME>...</SHORT-NAME>
        <COMPONENTS>
            <SW-COMPONENT-PROTOTYPE>
                <SHORT-NAME>RAD_CPUInstance</SHORT-NAME>
                <TYPE-TREF DEST="COMPOSITION-SW-COMPONENT-TYPE">
                    {type_ref}
                </TYPE-TREF>
            </SW-COMPONENT-PROTOTYPE>
        </COMPONENTS>

    </COMPOSITION-SW-COMPONENT-TYPE>
    ...
    <COMPOSITION-SW-COMPONENT-TYPE>
        <PORTS>
          [ port parser here ]
        </PORTS>
    </COMPOSITION-SW-COMPONENT-TYPE>
    """

    trigger = Tag.COMPOSITION_SW_COMPONENT_TYPE

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.component_types: List[CompositionSwComponentType] = []
        self.port_parser = PortPrototypeParser(dispatcher)
        self._all_ports: List[PortPrototype] = []

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == PortPrototypeParser.trigger:
            self.dispatcher.enter_new_active_parser(self.port_parser, name, attributes)
        elif name == Tag.COMPOSITION_SW_COMPONENT_TYPE:
            self.component_types.append(CompositionSwComponentType())
        elif name == Tag.SW_COMPONENT_PROTOTYPE:
            self.component_types[-1].components.append(SwComponentPrototype())

    def chardata(self, data: str, parent: str, child: str) -> None:
        if (parent, child) == (Tag.COMPOSITION_SW_COMPONENT_TYPE, Tag.SHORT_NAME):
            self.component_types[-1].fqn = self.dispatcher.get_current_fqn()
        elif (parent, child) == (Tag.SW_COMPONENT_PROTOTYPE, Tag.SHORT_NAME):
            self.component_types[-1].components[
                -1
            ].fqn = self.dispatcher.get_current_fqn()
        elif (parent, child) == (Tag.SW_COMPONENT_PROTOTYPE, Tag.TYPE_TREF):
            self.component_types[-1].components[-1].type_ref = Ref(
                data.strip(), CompositionSwComponentType
            )
        elif child == Tag.DATA_TYPE_MAPPING_REF:
            self.component_types[-1].data_type_mapping_refs.append(
                Ref(data.strip(), DataTypeMappingSet)
            )

    def end(self, name: str) -> None:
        if name == CompositionSwComponentTypeParser.trigger:
            self.dispatcher.pop_active_parser()
            self.component_types[-1].ports = self.port_parser.ports
            self._all_ports.extend(self.port_parser.ports)
            self.port_parser.ports = []

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        classic_arxml.ports = {
            Ref(port.fqn, PortPrototype): port for port in self._all_ports
        }
        classic_arxml.component_types = {
            Ref(ct.fqn, CompositionSwComponentType): ct for ct in self.component_types
        }


class SystemParser(ParserIntf):
    """
    Parses:

    <SYSTEM>
        <MAPPINGS>
            <SYSTEM-MAPPING>
                <SHORT-NAME>...</SHORT-NAME>
                <DATA-MAPPINGS>

                    [ SenderReceiverMappingParser here ]

                </DATA-MAPPINGS>
            </SYSTEM-MAPPING>
        </MAPPINGS>
        <ROOT-SOFTWARE-COMPOSITIONS>
            <ROOT-SW-COMPOSITION-PROTOTYPE>
                <SHORT-NAME>{name}</SHORT-NAME>
                <SOFTWARE-COMPOSITION-TREF DEST="COMPOSITION-SW-COMPONENT-TYPE">
                    {root_sw_composition}
                </SOFTWARE-COMPOSITION-TREF>
            </ROOT-SW-COMPOSITION-PROTOTYPE>
        </ROOT-SOFTWARE-COMPOSITIONS>
    </SYSTEM>
    """

    trigger = Tag.SYSTEM

    def __init__(self, dispatcher: DispatchingParser) -> None:
        self.dispatcher = dispatcher
        self.root_sw_composition: Optional[RootSwCompositionPrototype] = None
        self.sender_receiver_to_signal_mapping_parser = SenderReceiverMappingParser(
            dispatcher
        )

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        if name == SenderReceiverMappingParser.trigger:
            self.dispatcher.enter_new_active_parser(
                self.sender_receiver_to_signal_mapping_parser, name, attributes
            )
        elif name == Tag.ROOT_SW_COMPOSITION_PROTOTYPE:
            assert (
                self.root_sw_composition is None
            ), "Cannot have multiple root sw compositions"
            self.root_sw_composition = RootSwCompositionPrototype()

    def chardata(self, data: str, parent: str, child: str) -> None:
        if child == Tag.SOFTWARE_COMPOSITION_TREF:
            assert self.root_sw_composition
            self.root_sw_composition.software_composition_ref = Ref(
                data.strip(), CompositionSwComponentType
            )

    def end(self, name: str) -> None:
        if name == SystemParser.trigger:
            self.dispatcher.pop_active_parser()

    def collect_data(self, classic_arxml: ClassicArxml) -> None:
        self.sender_receiver_to_signal_mapping_parser.collect_data(classic_arxml)
        if self.root_sw_composition:
            classic_arxml.root_sw_composition = self.root_sw_composition


class DispatchingParser:
    """Parser for AUTOSAR classic ARXMLs"""

    def __init__(self) -> None:
        self.expat_parser = xml.parsers.expat.ParserCreate()
        self.expat_parser.buffer_text = True
        self.current_depth = 0
        self._stack: List[str] = []
        self._namespace: List[Tuple[str, int]] = []
        self.expat_parser.StartElementHandler = self.enter
        self.expat_parser.CharacterDataHandler = self.chardata
        self.expat_parser.EndElementHandler = self.end
        self._current_file = Path()
        self.sub_parsers: Dict[str, ParserIntf] = {
            SystemParser.trigger: SystemParser(self),
            SenderReceiverInterfaceParser.trigger: SenderReceiverInterfaceParser(self),
            DataTypeMappingSetParser.trigger: DataTypeMappingSetParser(self),
            ImplementationDataTypeParser.trigger: ImplementationDataTypeParser(self),
            EthernetClusterParser.trigger: EthernetClusterParser(self),
            ISignalParser.trigger: ISignalParser(self),
            ApplicationRecordDataTypeParser.trigger: ApplicationRecordDataTypeParser(
                self
            ),
            ApplicationPrimitiveDataTypeParser.trigger: ApplicationPrimitiveDataTypeParser(
                self
            ),
            ApplicationArrayDataTypeParser.trigger: ApplicationArrayDataTypeParser(
                self
            ),
            NetworkEndpointParser.trigger: NetworkEndpointParser(self),
            TransformationTechnologyParser.trigger: TransformationTechnologyParser(
                self
            ),
            SoAdRoutingGroupParser.trigger: SoAdRoutingGroupParser(self),
            CompositionSwComponentTypeParser.trigger: CompositionSwComponentTypeParser(
                self
            ),
            TpConnectionParser.trigger: TpConnectionParser(self),
        }
        self._active_parsers: List[ParserIntf] = [ParserIntf()]  # noop parser

    def push_active_parser(self, parser: ParserIntf) -> None:
        self._active_parsers.append(parser)

    def enter_new_active_parser(
        self, parser: ParserIntf, name: str, attributes: Mapping[str, str]
    ) -> None:
        self.push_active_parser(parser)
        self._active_parsers[-1].enter(name, attributes)

    def pop_active_parser(self) -> None:
        self._active_parsers.pop()

    def current_location(self) -> common_model.ParseLocation:
        return common_model.ParseLocation.make(
            source_file=self._current_file,
            line=self.expat_parser.CurrentLineNumber,
            col=self.expat_parser.CurrentColumnNumber,
        )

    def get_current_fqn(self) -> str:
        return "/" + "/".join(n[0] for n in self._namespace)

    def _push_new_namespace(self, data: str) -> None:
        ns_parent_depth = self.current_depth - 1
        self._namespace.append((data, ns_parent_depth))

    def enter(self, name: str, attributes: Mapping[str, str]) -> None:
        self.current_depth += 1
        self._stack.append(name)

        if p := self.sub_parsers.get(name):
            self.push_active_parser(p)

        self._active_parsers[-1].enter(name, attributes)

    def chardata(self, data: str) -> None:
        if not data:
            return

        if self._stack[-1] == Tag.SHORT_NAME:
            self._push_new_namespace(data.strip())

        child = self._stack[-1]
        parent = self._stack[-2] if len(self._stack) > 1 else ""
        self._active_parsers[-1].chardata(data, parent=parent, child=child)

    def end(self, name: str) -> None:
        self.current_depth -= 1
        while self._namespace and self._namespace[-1][1] > self.current_depth:
            self._namespace.pop()

        self._active_parsers[-1].end(name)
        self._stack.pop()

    def parse(self, arxml: Path, pretty_path: Path) -> ClassicArxml:
        self._current_file = pretty_path
        try:
            with Path(arxml).open("rb") as f:
                self.expat_parser.ParseFile(f)
        except Exception as e:
            self._print_stack()
            _log.warning(
                "%s in file: %s:%d",
                e,
                self._current_file,
                self.expat_parser.CurrentLineNumber,
            )
            raise e from None

        classic_arxml = ClassicArxml.make()
        classic_arxml.location = self.current_location()

        for sub_parser in self.sub_parsers.values():
            sub_parser.collect_data(classic_arxml)

        return classic_arxml

    def _print_stack(self):
        print("__stack__")
        tab = "\t"
        for s in self._stack:
            print(f"|{tab}{s}{tab}|")
        print("-------")
        print("ns:", self.get_current_fqn())
