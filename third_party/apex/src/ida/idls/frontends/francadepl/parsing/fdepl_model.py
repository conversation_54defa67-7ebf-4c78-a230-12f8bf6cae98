# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

# ruff: noqa: TCH001, TCH002, TCH003


"""
This module contains python representations for the constructs appearing in franca
deployment files (fdepls). The lark parser is used to create these models. They are then
transformed into a SOME/IP model in a later stage.

Regarding naming:
Fdepl files are 'defining' deployments for types and interfaces, hence the 'defined_xyz'
names of attributes and classes.
"""

from __future__ import annotations

from pathlib import Path
from typing import Dict, List, Optional, Union

from dataclasses import dataclass, field
from enum import Enum, IntEnum

from idl_common.common_model import ParseLocation


FQN = str
"""Fully Qualified Name"""


@dataclass
class LocatedElement:
    location: ParseLocation


@dataclass
class Import:
    import_: Path


@dataclass
class InterfaceInstance(LocatedElement):
    fqn: FQN
    identifier: str
    someip_identifier: int
    domain: str = "local"
    """
    The domain part of the CommonAPI address. Defaults to 'local'.
    """
    unicast_address: str = ""
    reliable_unicast_port: int = 0
    unreliable_unicast_port: int = 0

    # From the someip fdepl spec:
    #
    # The following three arrays must be used together, meaning the configuration of
    # multicast address and port for an eventgroup is done by setting
    #
    #    multicast_event_groups[X] = <eventgroup identifier>
    #    SomeIpMulticastAddresses[X] = <multicast address for multicast_event_groups[X]>
    #    SomeIpMulticastPorts[X] = <multicast port for multicast_event_groups[X]>
    #    SomeIpMulticastThreshold[X] =
    # <specifies when to use multicast and when to use unicast to send a notification
    # event. Must be set to a non-negative number. If it is set to zero, all events of
    # the eventgroup will be sent by unicast. Otherwise, the events will be sent by
    # unicast as long as the number of subscribers is lower than the threshold and by
    # multicast if the number of subscribers is greater or equal. This means, a
    # threshold of 1 will lead to all events being sent by multicast. The default
    # value is 0.
    multicast_event_groups: List[int] = field(default_factory=list)
    multicast_addresses: List[str] = field(default_factory=list)
    multicast_ports: List[int] = field(default_factory=list)
    multicast_threshold: List[int] = field(default_factory=list)


class Endianess(Enum):
    le = 1
    be = 2


class CrcWidth(IntEnum):
    zero = 0
    one = 1
    four = 4


@dataclass
class Broadcast(LocatedElement):
    name: str
    someip_identifier: int
    event_groups: List[int]
    reliable: bool = False
    max_retention_time_ms: int = 5
    debounce_time_ms: int = 0
    segment_length: Optional[int] = None
    separation_time_ms: Optional[int] = None
    endianess: Endianess = Endianess.be
    crc_width: CrcWidth = CrcWidth.zero
    priority: Optional[int] = None


DefinitionValue = Union[str, int, List[str], List[int]]


@dataclass
class SingleDefinition(LocatedElement):
    key: str
    value: DefinitionValue


@dataclass
class NestedDefinition:
    key: str
    value: List[Definitions]


Definitions = Union[SingleDefinition, NestedDefinition]


@dataclass
class Method(LocatedElement):
    name: str
    someip_identifier: int
    timeout_ms: Optional[int]

    priority: Optional[int] = None
    segment_length: Optional[int] = None
    segment_length_response: Optional[int] = None

    separation_time_ms: Optional[int] = None
    separation_time_response_ms: Optional[int] = None

    reliable: bool = False
    max_retention_time_ms: int = 5
    response_max_retention_time_ms: int = 0
    request_debounce_time_ms: int = 0
    response_debounce_time_ms: int = 0

    endianess: Endianess = Endianess.be
    crc_width: CrcWidth = CrcWidth.zero

    inputs: Dict[str, NestedDefinition] = field(default_factory=dict)
    outputs: Dict[str, NestedDefinition] = field(default_factory=dict)


@dataclass
class Attribute(LocatedElement):
    name: str

    getter_id: Optional[int]
    setter_id: Optional[int]
    notifier_id: Optional[int]

    reliable: bool = False

    event_groups: List[int] = field(default_factory=list)
    """
    The attribute notifier is part of these groups.

    The fdepl keys 'SomeIpNotifierEventGroups' and 'SomeIpEventGroups' in attributes
    both map to this list, they seem to be treated as aliasses in the CommonAPI code,
    with 'SomeIpNotifierEventGroups' being checked first:
    https://github.com/COVESA/capicxx-someip-tools/blob/62f732f28200f7c533b6d1b8488a6cccae4af45d/org.genivi.commonapi.someip/src/org/genivi/commonapi/someip/deployment/PropertyAccessor.java#L284-L288
    """

    getter_max_retention_time_ms: int = 5
    setter_max_retention_time_ms: int = 5
    notifier_max_retention_time_ms: int = 5
    getter_response_max_retention_time_ms: int = 0
    setter_response_max_retention_time_ms: int = 0

    getter_request_debounce_time_ms: int = 0
    setter_request_debounce_time_ms: int = 0
    notifier_debounce_time_ms: int = 0
    getter_response_debounce_time_ms: int = 0
    setter_response_debounce_time_ms: int = 0

    setter_segment_length: Optional[int] = None
    setter_separation_time_ms: Optional[int] = None
    getter_segment_length_response: Optional[int] = None
    getter_separation_time_response_ms: Optional[int] = None
    notifier_segment_length: Optional[int] = None
    notifier_separation_time_ms: Optional[int] = None

    endianess: Endianess = Endianess.be
    crc_width: CrcWidth = CrcWidth.zero


@dataclass
class DefinedInterface(LocatedElement):
    fqn: FQN
    """The '.'-separated fully qualified name"""
    someip_identifier: int
    """The numeric identifier for this service interface type."""
    defined_types: List[NestedDefinition]
    """
    Types can be created inside interfaces (not just in typeCollections) in fidl files.
    These types can be defined in fdepl files inside the corresponding interface
    definition.
    """
    defined_methods: List[Method]
    defined_broadcasts: List[Broadcast]
    defined_attributes: List[Attribute]
    event_groups: List[int] = field(default_factory=list)
    """
    Each event (broadcast, attributes with notifier) must be at least in one event group
    """


@dataclass
class DefinedTypeCollection(LocatedElement):
    """Deployment for a typeCollection."""

    fqn: FQN
    """The fully quallified name of the typeCollection being defined."""
    defined_types: List[NestedDefinition]
    """All types defined in the deployment."""


@dataclass
class ProviderDeployment(LocatedElement):
    name: str
    interface_instances: List[InterfaceInstance]


@dataclass
class Deployment:
    """Franca deployment model."""

    name: str
    """Currently the file name of the deployment."""
    defined_interface: List[DefinedInterface]

    defined_typecollections: List[DefinedTypeCollection]

    provider_deployments: List[ProviderDeployment]

    imports: List[Import]
    fdepl_path: Path
    location: ParseLocation
