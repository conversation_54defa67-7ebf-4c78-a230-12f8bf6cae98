# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

from __future__ import annotations

from omgidl_frontend.parsing.model import (
    ArrayType,
    AstNode,
    AstNodePath,
    AstNodeRef,
    DecorHelpers,
    Member,
    SequenceType,
    Struct,
    TypeDef,
    TypeNodeVisitor,
    UnionMember,
    UnionType,
    ValueTypeInt,
)
from omgidl_frontend.parsing.utils import ValidationError


def lookup_node(name: AstNodePath, scope: AstNodePath, root: AstNode) -> AstNodeRef:
    """
    Look up node name in the tree, taking into account node scope.

    Terminate if type can not be resolved.

    :param name AstNodePath: name to search
    :param scope AstNodePath: scope of the node
    :param root AstNode: root node
    :rtype AstNodeRef: found node
    """
    assert scope[0] == "Root"
    cur_scope = scope.copy()
    while cur_scope:
        node = root.lookup_descendant(cur_scope + name)
        if node:
            return AstNodeRef(node=node)
        cur_scope.pop()
    raise ValidationError(f"unable to find node {name} in namespaces {cur_scope}")


class TypeResolver(TypeNodeVisitor):
    """Visitor to resolve types in ROSIDL model."""

    def visit_type_def(self, visited: TypeDef) -> bool:
        namespaces = visited.get_path()[:-1]
        if (
            isinstance(visited.ref_type, AstNodeRef)
            and not visited.ref_type.is_resolved()
        ):
            path = visited.ref_type.get_path()
            assert path
            visited.ref_type = lookup_node(
                name=path,
                scope=namespaces,
                root=visited.get_root(),
            )
        elif (
            isinstance(visited.ref_type, (ArrayType, SequenceType))
            and isinstance(visited.ref_type.type, AstNodeRef)
            and not visited.ref_type.type.is_resolved()
        ):
            path = visited.ref_type.type.get_path()
            assert path
            visited.ref_type.type = lookup_node(
                name=path,
                scope=namespaces,
                root=visited.get_root(),
            )
        else:
            pass
        return True

    def visit_member(self, visited: Member) -> bool:
        namespaces = visited.get_path()[:-2]
        # NOTE(roman.sokolkov): Workaround to support legacy constants
        namespaces.append(visited.get_path()[-2] + "_Enums")
        if isinstance(visited.type, AstNodeRef) and not visited.type.is_resolved():
            path = visited.type.get_path()
            assert path
            visited.type = lookup_node(
                name=path,
                scope=namespaces,
                root=visited.get_root(),
            )
        elif (
            isinstance(visited.type, (ArrayType, SequenceType))
            and isinstance(visited.type.type, AstNodeRef)
            and not visited.type.type.is_resolved()
        ):
            path = visited.type.type.get_path()
            assert path
            visited.type.type = lookup_node(
                name=path,
                scope=namespaces,
                root=visited.get_root(),
            )
        else:
            pass

        return True


class ConstantResolver(TypeNodeVisitor):
    """Resolve constant references in IDL."""

    def _get_constant_integer(self, const_path: AstNodePath, visited: Member) -> int:
        const_ref = lookup_node(
            name=const_path,
            scope=visited.get_path()[:-2],
            root=visited.get_root(),
        )
        if not isinstance(
            const_ref.node.value,
            ValueTypeInt,
        ):
            raise ValidationError(
                f"{const_ref.get_path()} referenced in {visited.get_path()}"
                " must be an integer"
            )
        return const_ref.node.value.value

    @staticmethod
    def _get_resolved_annotation(
        annt_name: str, annt_value: dict, namespaces: AstNodePath, root: AstNode
    ) -> dict:
        resolved_annotation = {}
        for k, v in annt_value.items():
            # resolve annotations with value e.g. @foo(BAR)
            if isinstance(k, AstNodeRef):
                assert v == ""
                # NOTE(roman.sokolkov): For compatibility w/ some IDL files,
                # skip resolving @transfer_mode(SHMEM_REF).
                # RTI ConnextDDS code generator supports this annotation,
                # but does not define SHMEM_REF anywhere.
                if annt_name == "transfer_mode":
                    key = k.get_path()[0]
                else:
                    const_ref = lookup_node(k.get_path(), namespaces, root)
                    key = str(const_ref.node.value.value)
                resolved_annotation[key] = v
            # resolve key-value annotations e.g. @foo(value=BAR)-
            elif isinstance(k, str) and isinstance(v, AstNodeRef):
                const_ref = lookup_node(v.get_path(), namespaces, root)
                resolved_annotation[k] = str(const_ref.node.value.value)
            else:
                resolved_annotation[k] = annt_value[k]
        return resolved_annotation

    def _resolve_constants_in_annotations(self, visited: Member):
        annotations = visited.decorators[DecorHelpers.DECOR_ANNOTATION]
        assert isinstance(annotations, dict)
        for annt_name, annt_value in annotations.items():
            assert isinstance(annt_value, dict)
            annotations[annt_name] = self._get_resolved_annotation(
                annt_name=annt_name,
                annt_value=annt_value,
                namespaces=visited.get_path()[:-2],
                root=visited.get_root(),
            )

    def _resolve_constants_in_arrays_and_sequences(self, visited: Member):
        if isinstance(visited.type, SequenceType) and isinstance(
            visited.type.capacity, AstNodeRef
        ):
            visited.type.capacity = self._get_constant_integer(
                visited.type.capacity.get_path(), visited
            )
        elif isinstance(visited.type, ArrayType) and isinstance(
            visited.type.size, AstNodeRef
        ):
            visited.type.size = self._get_constant_integer(
                visited.type.size.get_path(), visited
            )

    def visit_member(self, visited: Member) -> bool:
        if not isinstance(visited, UnionMember):
            # no annotations for union members (see #26514)
            self._resolve_constants_in_annotations(visited)
        self._resolve_constants_in_arrays_and_sequences(visited)
        return True

    def visit_struct(self, visited: Struct) -> bool:
        self._resolve_constants_in_annotations(visited)
        return True

    def visit_union_member(self, visited: UnionMember) -> bool:
        if isinstance(visited.discriminators[0], AstNodeRef):
            scope = visited.parent.node.cases_type.get_path()
            discriminators = []
            for discriminator in visited.discriminators:
                discriminators.append(
                    lookup_node(discriminator.get_path(), scope, visited.get_root())
                )
            visited.discriminators = discriminators
        return self.visit_member(visited)

    def visit_union(self, visited: UnionType) -> bool:
        if isinstance(visited.cases_type, AstNodeRef):
            visited.cases_type = lookup_node(
                visited.cases_type.get_path(), visited.get_path(), visited.get_root()
            )
        return True
