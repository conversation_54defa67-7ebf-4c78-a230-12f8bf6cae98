# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

# ruff: noqa: S324

import argparse
import logging
import hashlib
import json
from pathlib import Path
from pydantic import BaseModel
from typing import Sequence, Optional, List

from idl_common import common_model

from transformer import (
    CanDatabaseTransformer,
)


from ida.idls.frontends.dbc.e2e_importer import (
    add_e2e_profile,
)

from ida.idls.frontends.dbc.e2e_configuration import (
    End2EndConfiguration,
)

_logger = logging.getLogger(__name__)


class DbcFrontendError(Exception):
    def __init__(self, msg: str, file: Optional[Path]) -> None:
        super().__init__(f"In file {file}: {msg}" if file is not None else msg)


class JsonConfig(BaseModel):
    """Schema for the json file passed to the generator."""

    input_files: List[Path]
    type_model_path: Path
    can_model_path: Path
    e2e_configuration_path: Optional[Path]
    can_database_name: Optional[str]
    whitelist: List[str]


def main(args: Sequence[str]) -> int:
    """Reads DBC file and writes one serialized type model."""
    json_config = parse_arguments(args)

    assert json_config.input_files

    dbc_type_model = common_model.IdlModel()
    dbc_can_model = common_model.ConnectorModel()

    md5 = hashlib.md5()
    try:
        transformer = CanDatabaseTransformer(
            json_config.can_database_name,
            json_config.whitelist,
        )
        transformer.fill_from_can_database(
            json_config.input_files, dbc_type_model, dbc_can_model
        )

        # External E2E configuration takes preference over ARXML/DBC E2E configurations.
        if json_config.e2e_configuration_path is not None:
            for e2e_conf in End2EndConfiguration.parse_file(
                json_config.e2e_configuration_path
            ).configurations:
                add_e2e_profile(dbc_can_model.ast.root, e2e_conf)

        md5.update(str(json_config.input_files).encode())
    except Exception as e:  # noqa: BLE001
        import traceback

        traceback.print_exc()
        # TODO: (kevin.goez)
        # this is only needed for the exception text
        # figure out a better way
        dbc_file = json_config.input_files[0]
        raise DbcFrontendError(str(e), Path(dbc_file)) from None

    dbc_type_model.idl_md5 = md5.hexdigest()

    dbc_type_model.ast.unresolve()
    dbc_can_model.ast.unresolve()

    json_config.type_model_path.write_text(dbc_type_model.model_dump_json(indent=2))
    json_config.can_model_path.write_text(dbc_can_model.model_dump_json(indent=2))

    return 0


def parse_arguments(args: Sequence[str]) -> JsonConfig:
    """Create a JsonConfig from parsing the json config file provided via `args`."""
    argument_parser = argparse.ArgumentParser()
    argument_parser.add_argument("--config", type=Path)

    arguments = argument_parser.parse_args(args)
    return JsonConfig.model_validate(json.loads(Path(arguments.config).read_text()))
