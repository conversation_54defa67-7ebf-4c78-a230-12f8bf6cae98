# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

# ruff: noqa: E501

import argparse
import json
from pathlib import Path
from typing import Sequence, Tuple

from idl_common import common_model, type_model
from jinja2 import Environment, FileSystemLoader, StrictUndefined

from ida.idls.backends.someip_serialization.generator import (
    elements_collector,
    view_model_creator,
)
from ida.idls.backends.someip_serialization.generator import (
    serialization_configuration as config,
)
from ida.idls.backends.typesupport.view_models import TSViewModel, make_ts_view_model

_template_env = Environment(
    loader=FileSystemLoader(str(Path(__file__).parent / "templates")),
    undefined=StrictUndefined,
    trim_blocks=True,
    lstrip_blocks=True,
    keep_trailing_newline=True,
    autoescape=True,
)


def _make_view_models(
    config: config.SerializationConfig,
) -> Tuple[view_model_creator.ViewModels, TSViewModel]:
    """
    Create view_models from the someip_model and type model.

    :param config: Contains the command line options and the path to the serialized type
                   model.
    :returns: Viewmodel for each type.
    """

    tm = common_model.IdlModel.from_json(config.type_model_path.read_text())
    dep_type_models = [
        common_model.IdlModel.from_json(p.read_text()) for p in config.dep_typemodels
    ]
    type_model.merge_typemodels(tm, dep_type_models)
    tm.ast.resolve()

    someip_model_ = common_model.ConnectorModel.model_validate_json(
        config.someip_model_path.read_text()
    )
    someip_model_.ast.resolve(allow_failure=True)
    someip_model_.ast.resolve(tm.ast.root)
    vms = _viewmodels_from_root_nodes(
        type_model_root=tm.ast.root,
        someip_model_root=someip_model_.ast.root,
        apply_ros_transformations=config.apply_ros_transformations,
    )

    ts_vm = make_ts_view_model(
        dds_types_hpps=config.dds_type_hpps,
        root_node=tm.ast.root,
        apply_ros_transformations=config.apply_ros_transformations,
    )

    # we don't generate SOME/IP serialization for all types, hence we need to filter to avoid
    # generating type supports for types that don't have serialization configured
    configured_structs = {svm.fqn for svm in vms.struct_view_models}
    filtered_ts_message_types = []
    for m in ts_vm.ts_message_types:
        if m.fully_qualified_type in configured_structs:
            filtered_ts_message_types.append(m)
    ts_vm.ts_message_types = filtered_ts_message_types

    return vms, ts_vm


def _viewmodels_from_root_nodes(
    type_model_root: common_model.RootNode,
    someip_model_root: common_model.RootNode,
    apply_ros_transformations: bool,
) -> view_model_creator.ViewModels:
    (
        types_with_properties,
        entities,
    ) = elements_collector.collect_elements_with_properties(
        sm=someip_model_root, tm=type_model_root
    )

    views = view_model_creator.ViewModelCreator().get_view_models(
        entities,
        types_with_properties,
        apply_ros_transformations,
    )
    return views


def _render_templates(
    view_models: view_model_creator.ViewModels,
    type_support: TSViewModel,
    generated_cpp_file: Path,
    generated_ts_cpp_file: Path,
) -> None:
    """
    Writes header (filled out) templates to files.

    :param view_models: Header view_models to write to files.
    :dds_type_hpps: Paths to the generated DDS type headers.
    """

    header_template = _template_env.get_template("someip_serialization.cpp.j2")
    generated_cpp_file.write_text(
        header_template.render(
            {
                "vms": view_models.struct_view_models,
                "enums_view": view_models.enum_view_models,
                "union_views": view_models.union_view_models,
                "types_hpps": type_support.generated_dds_types_hpps,
            }
        )
    )

    ts_template = _template_env.get_template("someip_typesupport.cpp.j2")
    generated_ts_cpp_file.write_text(
        ts_template.render(
            {
                "message_types": type_support.ts_message_types,
                "types_hpps": type_support.generated_dds_types_hpps,
            }
        )
    )


def _parse_args(arguments: Sequence[str]) -> config.CliConfig:
    """Parse command line arguments into a config object.

    :param arguments: CLI arguments WITHOUT the initial binary name.
    :returns: parsed config.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config_path",
        type=Path,
        required=True,
        help="path to config json",
    )

    parsed_arguments = parser.parse_args(arguments)
    return config.CliConfig.model_validate(
        json.loads(parsed_arguments.config_path.read_text())
    )


def main(arguments: Sequence[str]) -> None:
    configuration = _parse_args(arguments)
    view_models, type_support = _make_view_models(configuration.serialization_config)
    _render_templates(
        view_models,
        type_support,
        configuration.serialization_config.generated_cpp_file,
        configuration.serialization_config.generated_ts_file,
    )
