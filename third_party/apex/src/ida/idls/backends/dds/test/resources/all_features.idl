

module foo {
  module bar {
    module baz {

      typedef long HorsePower;
      const short NUMBER_OF_WHEELS = 4;

      // idlc does not like this :/
      // typedef sequence<Nav, 10> Navigations;
      struct Nav;
      union Manufacturer;
      enum Brand {
        VW,
        BMW,
        MERCEDES
      };


      struct Car {
        HorsePower power;
        Nav nav;
        sequence<Nav, 10> nav_backups;
        Nav nav2[11];
        sequence<Nav> unbounded_navs;
        Brand brand;
        Manufacturer man;
      };

      @flat
      struct Nav {

        boolean a;
        octet b;
        char c;
        short d;
        unsigned short e;
        long f;
        unsigned long g;
        long long h;
        unsigned long long i;
        float j;
        double k;

        string supplier_name;
        string<16> bounded_supplier_name;

        uint8 o;
      };


      struct VW_Brand{};
      struct Other_Brand{};

      union Manufacturer switch (Brand){
        case VW:
          VW_Brand vw;
        case BMW: case MERCEDES:
          Other_Brand other;
      };
    };
  };
};
