load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//ida/bazel:detail/dds_backend.bzl", "dds_backend")
load("@apex//ida/bazel:detail/francaidl_frontend.bzl", "francaidl_frontend")
load("@apex//ida/bazel:detail/omgidl_frontend.bzl", "omgidl_frontend")
load("@apex//ida/idls/backends/dds/test/resources:custom_idl_ns.bzl", "cc_dds_library_from_frontend", "define_old_and_new_types")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

apex_py_test(
    name = "test_omgidl",
    srcs = [
        "test_dds_backend.py",
    ],
    data = [
        "//ida/idls/frontends/omgidl/test/resources",
    ],
    deps = [
        "//ida/idls/backends/dds:dds_backend_lib",
        "//ida/idls/frontends/omgidl:omgidl_frontend_lib",
        "//ida/idls/idl_common",
    ],
)

apex_py_test(
    name = "test_dds_backend",
    srcs = [
        "test_viewmodels.py",
        "test_vm_rendering.py",
    ],
    deps = [
        "//ida/idls/backends/dds:dds_backend_lib",
        "//ida/idls/idl_common",
        "//ida/idls/idl_common:idl_common_testing_helpers",
    ],
)

dds_backend(
    name = "test_backend",
    testonly = True,
    srcs = [
        "//ida/idls/frontends/omgidl/test:full_car_no_deps",
    ],
)

cc_library(
    name = "test_backend_cc",
    testonly = True,
    srcs = ["test_backend"],
    includes = ["."],
    deps = [
        "//common/cpputils",
        "//ida/thirdparty/cyclone_dds_vendor",
    ],
)

apex_cc_test(
    name = "test_cyclone_types",
    srcs = [
        "test_cyclone_types.cpp",
    ],
    deps = [
        ":test_backend_cc",
        "@googletest//:gtest_main",
    ],
)

omgidl_frontend(
    name = "test_idl_types",
    srcs = [
        ":test_types.idl",
    ],
)

dds_backend(
    name = "test_tm_dds_backend",
    testonly = True,
    srcs = [
        ":test_idl_types",
    ],
    use_idlc = False,
)

francaidl_frontend(
    name = "franca_enum_tm",
    srcs = [
        "//ida/idls/backends/dds/test/resources:enum_with_value.fidl",
    ],
)

cc_dds_library_from_frontend(
    name = "franca_enum",
    srcs = [
        ":franca_enum_tm",
    ],
)

define_old_and_new_types(
    "old_enum",
    "new_enum",
    idl_file = "//ida/idls/backends/dds/test/resources:enum.idl",
)

apex_cc_test(
    name = "test_old_and_new_backend",
    srcs = [
        "test_old_and_new_types.cpp",
    ],
    deps = [
        ":franca_enum",
        ":new_enum",
        ":old_enum",
        "@googletest//:gtest_main",
    ],
)
