# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

from pydantic import BaseModel
from typing import Mapping, List


class SerializationConfiguration(BaseModel):
    """Convenience class to read json arguments."""

    output_map: Mapping[str, Mapping[str, str]]
    """Map between DSL file and its generated file stem for the given DSL.
    Example: {"idlc": {"foo.dsl": "bar"}}
    """
    whitelist: List[str]


class CliConfig(BaseModel):
    serialization_config: SerializationConfiguration
