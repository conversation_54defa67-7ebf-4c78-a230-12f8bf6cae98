# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

import argparse
from pathlib import Path
from cantools.database.can.c_source import (
    generate,
)

from ida.idls.frontends.dbc.cantools.cantools_helpers import (
    build_codegen_database_name,
    cantools_database_from_file,
)

from ida.idls.backends.cantools_serialization import (
    serialization_configuration as config,
)


from typing import Sequence, Optional


class _Codegen:
    def generate_c_source(  # noqa: PLR0913
        self,
        input_file: Path,
        c_file: Path,
        h_file: Path,
        can_database_name: Optional[str] = None,
        whitelist: Optional[Sequence[str]] = None,
    ):
        # cantools will take the `Cantools` database directly and
        # parse the ApexDatabaseCollection object internally
        can_db, database_name = cantools_database_from_file(input_file)
        if can_database_name:
            database_name = can_database_name

        h_text, c_text, _, _ = generate(
            can_db,
            # NOTE(alivenets): the file name is added to the database name in cantools
            # codegen procedure to prevent symbol and macro duplication.
            # When there are two files with the same database and overlapping messages,
            # cantools will generate unique macro and message names.
            build_codegen_database_name(input_file, database_name),
            h_file.name,
            c_file.name,
            None,
            floating_point_numbers=True,
            bit_fields=False,
            use_float=False,
            node_name=None,
            whitelist=whitelist,
        )

        h_file.write_text(h_text)
        c_file.write_text(c_text)


def _parse_args(arguments: Sequence[str]) -> config.CliConfig:
    """Parse command line arguments into a config object.

    :param arguments: CLI arguments WITHOUT the initial binary name.
    :returns: parsed config.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config_path",
        type=Path,
        required=True,
        help="path to config json",
    )

    parsed_arguments = parser.parse_args(arguments)
    with Path.open(parsed_arguments.config_path, "r") as json_file:
        json_string = json_file.read()

    return config.CliConfig.model_validate_json(json_string)


def main(arguments: Sequence[str]):
    configuration = _parse_args(arguments)

    codegen = _Codegen()

    for file_info in configuration.serialization_config.output_map.values():
        codegen.generate_c_source(
            Path(file_info["dsl_path"]),
            Path(file_info["c_path"]),
            Path(file_info["h_path"]),
            file_info.get("database_name", None),
            whitelist=configuration.serialization_config.whitelist,
        )
