load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary")

py_binary(
    name = "cantools_codegen",
    srcs = [
        "cantools_codegen.py",
        "main.py",
        "serialization_configuration.py",
    ],
    main = "main.py",
    visibility = ["//visibility:public"],
    deps = [
        "//ida/idls/frontends/dbc/cantools:cantools_helpers",
        requirement("cantools"),
        requirement("pydantic"),
    ],
)
