# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

# ruff: noqa: T201

"""Test backend that prints provided type models."""

import json
from collections import defaultdict
from pathlib import Path
import sys
from typing import Dict, List

from idl_common import common_model, type_model


class _TestVisitor(type_model.TypeNodeVisitor):
    def __init__(self) -> None:
        self.contained_nodes: List[str] = []

    def visit_ast_node(self, at: common_model.AstNode) -> bool:
        self.contained_nodes.append(at.get_fqn())
        return True

    def visit_root_node(self, _: common_model.RootNode) -> bool:
        return True

    def visit_namespace(self, _: type_model.Namespace) -> bool:
        return True


if __name__ == "__main__":
    config = json.loads(Path(sys.argv[2]).read_text())

    type_model_path = Path(config["type_model"])
    dependency_tm_paths = config["dependencies"]
    direct_output_map = config["output_map"]
    lookup_map = config["import_lookup_map"]
    output_json = Path(config["output_json"])

    tm = common_model.IdlModel.from_json(type_model_path.read_text())
    dependencies = [
        common_model.IdlModel.from_json(Path(p).read_text())
        for p in dependency_tm_paths
    ]
    type_model.merge_typemodels(tm, dependencies)
    tm.ast.resolve()

    for generated in direct_output_map.values():
        Path(generated).write_text(".")

    output: Dict[str, Dict[str, List[str]]] = {
        "direct_nodes": defaultdict(list),
        "dependency_nodes": defaultdict(list),
    }

    visitor = _TestVisitor()
    tm.ast.root.traverse_dfs_from_top_only_nodes(visitor, tm.own_nodes)
    for fqn in visitor.contained_nodes:
        file_for_node = tm.node_to_dsl_file[fqn]
        output["direct_nodes"][file_for_node].append(fqn)

    for node_fqn, file in tm.node_to_dsl_file.items():
        if file in output["direct_nodes"]:
            continue
        output["dependency_nodes"][file].append(node_fqn)

    output_json.write_text(json.dumps(output))
