load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

apex_cc_test(
    name = "test",
    srcs = [
        "e2e_ids.hpp",
        "test_channel_reader.cpp",
        "test_channel_writer.cpp",
        "test_plugin.cpp",
        "type_support_mock.cpp",
        "type_support_mock.hpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    deps = [
        "//ida/base/core/tests/helpers",
        "//ida/common:test_util",
        "//ida/common/type_support_registry",
        "//ida/connectors/common/integration_testing_helpers:test_util",
        "//ida/plexus/connector_plugins:someip",
        "//ida/plexus/connector_plugins/someip/common",
        "//ida/plexus/connector_plugins/someip/common:channel_test_utils",
        "//ida/plexus/plugin_gateway_transport",
        "//ida/plexus/sample:sample_header",
        "//ida/serializers/someip:type_support",
        "//ida/verifiers/e2e",
        "//ida/verifiers/e2e:mock",
        "@googletest//:gtest_main",
    ],
)
