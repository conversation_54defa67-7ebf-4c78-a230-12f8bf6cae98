load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")

# TODO (Sumanth.Nirmal) #31041 the plugin gateway transport needs to be unified with the IPC
# abstraction in plexus, and should be used with all the connectors and the shared memory
# channels

# library containing the interface used in connector plugins and gateways.
# Note: this has an unresolved dependency that needs to be satisfied by linking _one_ of
# the link libraries defined below
apex_cc_library(
    name = "plugin_gateway_transport",
    srcs = [
        "common.cpp",
        "detail/ipc_reader.cpp",
        "detail/ipc_writer.cpp",
        "detail/reader.hpp",
        "detail/writer.hpp",
    ],
    hdrs = [
        "common.hpp",
        "transport_reader.hpp",
        "transport_writer.hpp",
    ],
    implementation_deps = [
        "//ida/ipc/core:ipc_core",
        "//ida/ipc/framework_layer",
        "//ida/plexus/ipc_transport",
        "//ida/plexus/qos:reconciliation",
    ],
    visibility = ["//ida:__subpackages__"],
    deps = [
        "//ida/base",
        "//ida/common",
        "//ida/ipc/core:ipc_core",
        "//ida/ipc/framework_layer",
        "//ida/plexus/condition:trigger_id_generators",
        "//ida/plexus/connector_interface",
        "//ida/plexus/resource_acquisition",
        "@iceoryx//iceoryx_hoofs",
    ],
)
