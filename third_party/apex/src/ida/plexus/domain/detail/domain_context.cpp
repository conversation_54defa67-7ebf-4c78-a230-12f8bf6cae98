/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Implementation of the plexus DomainContext

#include "ida/plexus/domain/detail/domain_context.hpp"

#include "ida/base/core/string_view.hpp"
#include "ida/plexus/common/discovery_config.hpp"
#include "ida/plexus/context/connectors_registry.hpp"

namespace
{
const char * const ERR_FAILED_TO_REMOVE_ENTITY =
  "Failed to remove entity even though it should be there";
const char * const ERR_CANT_REG_DUPLEX_ENDPOINT = "Can't register a duplex endpoint";
const char * const ERR_CANT_DEREG_DUPLEX_ENDPOINT = "Can't deregister a duplex endpoint";
}  // namespace

namespace apex
{
namespace plexus
{
namespace detail
{

DomainContext::DomainContext(base::badge<base::creator>,
                             base::optional<base::error> & maybe_error,
                             PlexusDiscovery & discovery,
                             const discovery::ProtocolSupport & protocol_support,
                             DomainId domain_id,
                             base::allocator & allocator)
: m_pinned_allocators(allocator),
  m_discovery(&discovery),
  m_protocol_support(&protocol_support),
  m_domain_id(domain_id),
  m_entity_store(allocator),
  m_allocator(allocator),
  m_topics(allocator),
  m_endpoint_id_to_topic(allocator),
  m_duplex_endpoint_map(allocator)
{
  auto inproc_queues_map_create_result = base::create_in_place(m_inproc_queues_map, m_allocator);
  if (inproc_queues_map_create_result.has_error()) {
    maybe_error = base::error("Could not create in-proc queues map",
                              base::move(inproc_queues_map_create_result.error()));
    return;
  }

  auto join_domain_result = discovery.join_domain(domain_id);
  if (join_domain_result.has_error()) {
    maybe_error = base::error("Could not join domain", base::move(join_domain_result.error()));
    return;
  }
}

Result<base::not_null<base::allocator *>> DomainContext::pin_allocator(
  base::unique_ptr<base::allocator> allocator)
{
  const auto g = m_mutex.take();
  const auto ptr_to_return = allocator.get();

  if (!m_pinned_allocators.emplace_back(base::move(allocator))) {
    return err("Failed to pin allocator", ErrorCode::BAD_ALLOC);
  }

  return base::ok(ptr_to_return);
}

Result<base::shared_ptr<detail::EntityGuard>> DomainContext::register_duplex_endpoint(
  const base::shared_not_null<InternedTopic> request_topic,
  const base::shared_not_null<InternedTopic> reply_topic,
  base::shared_not_null<AnyDuplexEntityContext> entity,
  const base::initializer_list<EndpointTag> & tags)
{
  const auto g = m_mutex.take();

  // base::not_null guarantees validity.
  if (auto result = m_entity_store.add(base::span<const base::shared_not_null<AnyEntityContext>>(
        // NB: qcc-7.1 fails to infer the element type of initializer_list otherwise.
        // Underlying reader and writer have to be registered too in order to handle WaitSet
        // attachment.
        base::initializer_list<const base::shared_not_null<AnyEntityContext>>{
          entity, entity->any_reader_context(), entity->any_writer_context()}));
      result.has_error()) {
    return err(base::move(result.error()));
  }

  const auto & reader_topic = entity->is_requester() ? reply_topic : request_topic;
  const auto & writer_topic = entity->is_requester() ? request_topic : reply_topic;

  const discovery::ReaderInfo reader =
    discovery::ReaderInfo::Builder(entity->any_reader_context()->id(), reader_topic->info())
      .set_qos(entity->reader_qos())
      .set_protocol_support(*m_protocol_support)
      .add_tags(tags)
      .set_unset_components_to_default()
      .build();
  const discovery::WriterInfo writer =
    discovery::WriterInfo::Builder(entity->any_writer_context()->id(), writer_topic->info())
      .set_qos(entity->writer_qos())
      .set_protocol_support(*m_protocol_support)
      .add_tags(tags)
      .set_unset_components_to_default()
      .build();

  if (auto result =
        entity->is_requester()
          ? m_discovery->signal_requester_available(discovery::RequesterInfo{reader, writer})
          : m_discovery->signal_replier_available(discovery::ReplierInfo{reader, writer});
      result.has_error()) {
    // Failed to register entity. Roll back the changes and bail
    if (const auto remove_result = m_entity_store.remove(entity->any_reader_context()->id());
        remove_result.has_error()) {
      base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(remove_result.error()));
    }
    if (const auto remove_result = m_entity_store.remove(entity->any_writer_context()->id());
        remove_result.has_error()) {
      base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(remove_result.error()));
    }
    if (const auto remove_result = m_entity_store.remove(entity->id()); remove_result.has_error()) {
      base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(remove_result.error()));
    }

    return err(base::move(result.error()));
  }

  if (auto insert_result =
        m_duplex_endpoint_map.insert(entity->any_writer_context()->id(), entity->id());
      insert_result.has_error()) {
    base::panic(ERR_CANT_REG_DUPLEX_ENDPOINT,
                base::err_fault(APEX_ERROR_FROM_ENUM(base::dict_error, insert_result.error())));
  }
  if (auto insert_result =
        m_endpoint_id_to_topic.insert(entity->any_reader_context()->id(), reader_topic);
      insert_result.has_error()) {
    base::panic(ERR_CANT_REG_DUPLEX_ENDPOINT,
                base::err_fault(APEX_ERROR_FROM_ENUM(base::dict_error, insert_result.error())));
  }
  if (auto insert_result =
        m_endpoint_id_to_topic.insert(entity->any_writer_context()->id(), writer_topic);
      insert_result.has_error()) {
    base::panic(ERR_CANT_REG_DUPLEX_ENDPOINT,
                base::err_fault(APEX_ERROR_FROM_ENUM(base::dict_error, insert_result.error())));
  }

  auto guard_result = apex::create_shared<detail::EntityGuard>(m_allocator, [entity, this]() {
    auto result = deregister_duplex_endpoint(entity);
    if (result.has_error()) {
      base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(base::move(result.error())));
    }
  });

  if (guard_result.has_error()) {
    return err(base::move(guard_result.error()));
  }

  // base::not_null guarantees validity.
  return base::ok(*guard_result);
}

Result<void> DomainContext::deregister_duplex_endpoint(
  base::shared_not_null<AnyDuplexEntityContext> entity)
{
  const auto g = m_mutex.take();

  // base::not_null guarantees validity.

  const auto & reader_id = entity->any_reader_context()->id();
  const auto maybe_reader_topic = m_endpoint_id_to_topic.get(reader_id);
  if (!maybe_reader_topic.has_value()) {
    base::panic(ERR_CANT_DEREG_DUPLEX_ENDPOINT,
                base::err_fault(base::error("Can't find reader topic of a Request/Reply entity",
                                            reader_id.as_string())));
  }
  const auto & reader_topic = maybe_reader_topic.value();

  const auto & writer_id = entity->any_writer_context()->id();
  const auto maybe_writer_topic = m_endpoint_id_to_topic.get(writer_id);
  if (!maybe_writer_topic.has_value()) {
    base::panic(ERR_CANT_DEREG_DUPLEX_ENDPOINT,
                base::err_fault(base::error("Can't find writer topic of a Request/Reply entity",
                                            reader_id.as_string())));
  }
  const auto & writer_topic = maybe_writer_topic.value();

  const discovery::ReaderInfo reader =
    discovery::ReaderInfo::Builder(entity->any_reader_context()->id(), reader_topic->info())
      .set_qos(entity->reader_qos())
      .set_protocol_support(*m_protocol_support)
      .set_unset_components_to_default()
      .build();
  const discovery::WriterInfo writer =
    discovery::WriterInfo::Builder(entity->any_writer_context()->id(), writer_topic->info())
      .set_qos(entity->writer_qos())
      .set_protocol_support(*m_protocol_support)
      .set_unset_components_to_default()
      .build();

  // Call Discovery to perform the de-registration
  auto result =
    entity->is_requester()
      ? m_discovery->signal_requester_unavailable(discovery::RequesterInfo{reader, writer})
      : m_discovery->signal_replier_unavailable(discovery::ReplierInfo{reader, writer});
  if (result.has_error()) {
    return err(base::move(result.error()));
  }

  if (!m_endpoint_id_to_topic.erase(entity->any_writer_context()->id())) {
    BASE_LOG(WARN,
             "Entity: " << entity->any_writer_context()->id()
                        << " not associated with any topic, domain context might be corrupted");
  }
  if (!m_endpoint_id_to_topic.erase(entity->any_reader_context()->id())) {
    BASE_LOG(WARN,
             "Entity: " << entity->any_reader_context()->id()
                        << " not associated with any topic, domain context might be corrupted");
  }
  if (!m_duplex_endpoint_map.erase(entity->any_writer_context()->id())) {
    BASE_LOG(WARN,
             "Entity: " << entity->any_writer_context()->id()
                        << " not associated with any topic, domain context might be corrupted");
  }

  if (const auto remove_result = m_entity_store.remove(entity->any_reader_context()->id());
      remove_result.has_error()) {
    base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(remove_result.error()));
  }
  if (const auto remove_result = m_entity_store.remove(entity->any_writer_context()->id());
      remove_result.has_error()) {
    base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(remove_result.error()));
  }
  if (const auto remove_result = m_entity_store.remove(entity->id()); remove_result.has_error()) {
    base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(remove_result.error()));
  }

  // base::not_null guarantees validity.

  return base::ok();
}

Result<base::shared_ptr<detail::EntityGuard>> DomainContext::register_data_writer(
  const base::shared_not_null<InternedTopic> topic,
  base::shared_not_null<AnyDataWriterContext> entity,
  const base::initializer_list<EndpointTag> & tags)
{
  const auto g = m_mutex.take();

  if (auto result = m_entity_store.add(entity); result.has_error()) {
    return err(base::move(result.error()));
  }

  // Note: If qos require different set of protocol support, copy the const injected protocol
  // support and remove non supported protocols.
  // Note: passing in a pointer to a local variable is
  // fine here because WriterInfo copies the data instead of retaining the pointer
  const auto info = discovery::WriterInfo::Builder(entity->id(), topic->info())
                      .set_qos(entity->qos())
                      .set_protocol_support(*m_protocol_support)
                      .add_tags(tags)
                      .set_unset_components_to_default()
                      .build();
  if (auto result = m_discovery->signal_writer_available(info); result.has_error()) {
    // Failed to register entity. Roll back the changes and bail
    if (auto remove_result = m_entity_store.remove(entity->id()); remove_result.has_error()) {
      base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(remove_result.error()));
    }

    return err(base::move(result.error()));
  }

  if (auto insert_result = m_endpoint_id_to_topic.insert(entity->id(), topic);
      insert_result.has_error()) {
    return err(APEX_ERROR_FROM_ENUM(base::dict_error, insert_result.error()));
  }

  auto deregister_cb = [entity, this]() {
    const auto res = deregister_data_writer(entity);
    if (res.has_error()) {
      BASE_LOG(WARN,
               "Failed to remove entity: " << entity->id().as_string() << " from the entity store");
    }
    if (!m_endpoint_id_to_topic.erase(entity->id())) {
      BASE_LOG(WARN,
               "Entity: " << entity->id().as_string()
                          << " not associated with any topic, domain context might be corrupted");
    }

    BASE_LOG(
      TRACE,
      "Successfuly removed entity: " << entity->id().as_string() << " from the entity store");
  };
  auto guard_result = apex::create_shared<detail::EntityGuard>(m_allocator, deregister_cb);

  if (guard_result.has_error()) {
    return err(base::move(guard_result.error()));
  }

  return base::ok(*guard_result);
}

Result<base::shared_ptr<detail::EntityGuard>> DomainContext::register_data_reader(
  const base::shared_not_null<InternedTopic> topic,
  base::shared_not_null<AnyDataReaderContext> entity,
  const base::initializer_list<EndpointTag> & tags)
{
  const auto g = m_mutex.take();

  if (auto result = m_entity_store.add(entity); result.has_error()) {
    return err(base::move(result.error()));
  }

  // Note: If qos require different set of protocol support, copy the const injected protocol
  // support and remove non supported protocols.
  // Note: passing in a pointer to a local variable is
  // fine here because ReaderInfo copies the data instead of retaining the pointer

  const auto info = discovery::ReaderInfo::Builder(entity->id(), topic->info())
                      .set_qos(entity->qos())
                      .set_protocol_support(*m_protocol_support)
                      .add_tags(tags)
                      .set_unset_components_to_default()
                      .build();
  if (auto result = m_discovery->signal_reader_available(info); result.has_error()) {
    // Failed to register entity. Roll back the changes and bail
    if (const auto remove_result = m_entity_store.remove(entity->id()); remove_result.has_error()) {
      base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(remove_result.error()));
    }

    return err(base::move(result.error()));
  }

  if (auto insert_result = m_endpoint_id_to_topic.insert(entity->id(), topic);
      insert_result.has_error()) {
    return err(APEX_ERROR_FROM_ENUM(base::dict_error, insert_result.error()));
  }

  auto deregister_cb = [entity, this]() {
    const auto res = deregister_data_reader(entity);
    if (res.has_error()) {
      BASE_LOG(WARN,
               "Failed to remove entity: " << entity->id().as_string() << " from the entity store");
    }
    if (!m_endpoint_id_to_topic.erase(entity->id())) {
      BASE_LOG(WARN,
               "Entity: " << entity->id().as_string()
                          << " not associated with any topic, domain context might be corrupted");
    }

    BASE_LOG(
      TRACE,
      "Successfully removed entity: " << entity->id().as_string() << " from the entity store");
  };

  auto guard_result = apex::create_shared<detail::EntityGuard>(m_allocator, deregister_cb);

  if (guard_result.has_error()) {
    return err(base::move(guard_result.error()));
  }

  return base::ok(*guard_result);
}

Result<void> DomainContext::deregister_data_reader(
  base::shared_not_null<AnyDataReaderContext> entity)
{
  const auto g = m_mutex.take();

  return deregister_data_reader_impl(*entity);
}

Result<void> DomainContext::deregister_data_writer(
  base::shared_not_null<AnyDataWriterContext> entity)
{
  const auto g = m_mutex.take();
  return deregister_data_writer_impl(*entity);
}

Result<void> DomainContext::deregister_data_reader_impl(AnyDataReaderContext & entity)
{
  if (!m_entity_store.has_entity(entity.id())) {
    return err("Entity is not registered in the entity store", ErrorCode::NO_SUCH_ENTITY);
  }

  const auto maybe_topic = m_endpoint_id_to_topic.get(entity.id());
  if (!maybe_topic.has_value()) {
    base::panic(
      "Endpoint ID is not in the topic map despite the entity being registered"
      "in the entity store");
  }
  auto topic = *maybe_topic;

  // Note: this does not set the correct protocol support, but discovery ignores it anyway.
  const auto info = discovery::ReaderInfo::Builder(entity.id(), topic->info())
                      .set_qos(entity.qos())
                      .set_unset_components_to_default()
                      .build();
  if (auto result = m_discovery->signal_reader_unavailable(info); result.has_error()) {
    return err(base::move(result.error()));
  }

  if (const auto result = m_entity_store.remove(entity.id()); result.has_error()) {
    base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(result.error()));
  }
  return base::ok();
}

Result<void> DomainContext::deregister_data_writer_impl(AnyDataWriterContext & entity)
{
  if (!m_entity_store.has_entity(entity.id())) {
    return err("Entity is not registered in the entity store", ErrorCode::NO_SUCH_ENTITY);
  }

  const auto maybe_topic = m_endpoint_id_to_topic.get(entity.id());
  if (!maybe_topic.has_value()) {
    base::panic(
      "Endpoint ID is not in the topic map despite the entity being registered"
      "in the entity store");
  }
  auto topic = *maybe_topic;

  // Note: this does not set the correct protocol support, but discovery ignores it anyway.
  const auto info = discovery::WriterInfo::Builder(entity.id(), topic->info())
                      .set_qos(entity.qos())
                      .set_unset_components_to_default()
                      .build();
  if (auto result = m_discovery->signal_writer_unavailable(info); result.has_error()) {
    return err(base::move(result.error()));
  }

  if (auto result = m_entity_store.remove(entity.id()); result.has_error()) {
    base::panic(ERR_FAILED_TO_REMOVE_ENTITY, base::err_fault(result.error()));
  }
  return base::ok();
}


Result<base::shared_ptr<InternedTopic>> DomainContext::find_topic(base::string_view topic)
{
  const auto g = m_mutex.take();

  if (auto get_result = m_topics.get(topic); get_result) {
    return base::ok(*get_result);
  }
  return base::ok(nullptr);
}

Result<base::shared_ptr<InternedTopic>> DomainContext::find_or_create_topic(
  base::string_view topic, base::string_view type, base::string_view type_hash)
{
  if (topic.size() > MAX_TOPIC_LENGTH) {
    base::err_message message{"Topic name too long: "};
    message.append(base::truncate_to_capacity, topic);
    return apex::err(base::move(message));
  }
  if (type.size() > MAX_TYPENAME_LENGTH) {
    base::err_message message{"Topic '"};
    message.append(base::truncate_to_capacity, topic);
    message.append(base::truncate_to_capacity, "' type name too long: ");
    message.append(base::truncate_to_capacity, type);
    return apex::err(base::move(message));
  }
  if (type_hash.size() > MAX_TYPEHASH_LENGTH) {
    base::err_message message{"Topic '"};
    message.append(base::truncate_to_capacity, topic);
    message.append(base::truncate_to_capacity, "' type hash too long: ");
    message.append(base::truncate_to_capacity, type_hash);
    return apex::err(base::move(message));
  }

  const auto g = m_mutex.take();

  if (auto get_result = m_topics.get(topic)) {
    auto && interned_topic = *get_result;
    // Check for other entities using the topic
    if (interned_topic.use_count() > 1) {
      // Both the type name and the type hash must match for the existing topic to be reused.
      // This will restrict the creation of topics with the same name, and same type but different
      // hashes.
      if (interned_topic->info().type() != type ||
          interned_topic->info().type_hash() != type_hash) {
        base::err_message message{"Topic '"};
        message.append(base::truncate_to_capacity, topic);
        message.append(base::truncate_to_capacity, "' type/hash mismatch. Expected: ");
        message.append(base::truncate_to_capacity, interned_topic->info().type());
        message.append(base::truncate_to_capacity, "/");
        message.append(base::truncate_to_capacity, interned_topic->info().type_hash());
        return apex::err(base::move(message), ErrorCode::INCONSISTENT_TOPIC);
      }
      return base::ok(interned_topic);
    }
    (void)m_topics.erase(interned_topic->info().topic_name());
  }

  if (m_topics.full()) {
    remove_unused_topics();
  }

  auto create_res = m_allocator.create_shared<InternedTopic>(topic, type, type_hash, m_domain_id);
  if (create_res.has_error()) {
    return err("Failed to create InternedTopic", ErrorCode::BAD_ALLOC);
  }
  auto interned_topic = *create_res;
  if (!m_topics.insert(interned_topic->info().topic_name(), interned_topic)) {
    return err("Failed to register topic in topic map");
  }
  return base::ok(interned_topic);
}

void DomainContext::remove_unused_topics()
{
  for (const auto & [topic_name, topic_ptr] : m_topics) {
    if (topic_ptr.use_count() == 1) {
      (void)m_topics.erase(topic_name);
    }
  }
}

Result<void> DomainContext::register_changed_waitset(const discovery::WaitSetInfo & ws_info,
                                                     EndpointId reader_id)
{
  // The discovery is unable to synchronize changes made by concurrent threads therefore we protect
  // the signaling with a mutex.
  auto g = m_mutex.take();
  return m_discovery->signal_waitset_changed(ws_info, reader_id);
}

Result<base::shared_not_null<InprocQueue>> DomainContext::find_or_create_inproc_queue(
  const ChannelId & channel_id, const base::uint32_t capacity)
{
  const auto g = m_mutex.take();

  auto maybe_queue_variant = m_inproc_queues_map->get(channel_id);
  if (!maybe_queue_variant) {
    auto create_result = apex::create_shared<InprocQueue>(m_allocator, capacity, m_allocator);
    if (create_result.has_error()) {
      return err(base::move(create_result.error()));
    }

    auto set_result = m_inproc_queues_map->insert(channel_id, *create_result);
    if (!set_result) {
      return err("Failed storing the inproc queue in a map.", base::move(set_result.error()));
    }
    return base::ok(*create_result);
  }

  auto maybe_queue_ptr_ref = base::get<base::shared_ptr<InprocQueue>>(*maybe_queue_variant);
  if (maybe_queue_ptr_ref.has_value()) {
    if ((*maybe_queue_ptr_ref)->buffer().capacity() != capacity) {
      return err("Queue exists but its capacity doesn't match the requested value.");
    }

    // `dict::erase` will invalidate `maybe_queue_ptr_ref`, so move the ptr into a local variable.
    auto queue_shared_ptr = base::move(*maybe_queue_ptr_ref);

    // Drop our reference the second time somebody asks for it. Typically this should mean that
    // both writer and reader got hold of the queue, and we may forget about it.
    //
    // TODO (max.breev) #25738: There's a possibility of endesly growing the `dict` in case one
    // side of the channel never claims the queue. One solution might be to follow the pattern of
    // `deregister_data_reader_if_last_reference` and `deregister_data_writer_if_last_reference`.
    (void)m_inproc_queues_map->erase(channel_id);

    return base::ok(base::move(queue_shared_ptr));
  }

  return err("Sample type mismatch for the given channel.");
}

Result<base::shared_ptr<AnyEntityContext>> DomainContext::lookup_duplex_entity(
  const EndpointId & writer_id)
{
  const auto g = m_mutex.take();

  auto maybe_duplex_id = m_duplex_endpoint_map.get(writer_id);
  if (!maybe_duplex_id.has_value()) {
    return err("Unknown writer Id", writer_id.as_string());
  }

  return base::ok(m_entity_store.get_entity(*maybe_duplex_id));
}

Result<bool> DomainContext::is_registered_entity(const AnyDataEntityContext & entity)
{
  const auto g = m_mutex.take();
  return base::ok(m_entity_store.has_entity(entity.id()));
}

void DomainContext::set_protocol_support(base::not_null<const discovery::ProtocolSupport *> support)
{
  // TODO(tobias.stark): #25802 consider if we still need this after a proper protocol support
  //                     solution has been developed
  m_protocol_support = support;
}

}  // namespace detail
}  // namespace plexus
}  // namespace apex
