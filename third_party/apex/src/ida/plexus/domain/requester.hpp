/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief The requester for request-reply communicatio.

#ifndef APEX_PLEXUS_REQUESTER_HPP
#define APEX_PLEXUS_REQUESTER_HPP

#include "ida/plexus/domain/detail/duplex.hpp"
#include "ida/plexus/entity_store/requester_context.hpp"

namespace apex::plexus
{

class AnyRequester
{
public:
  AnyRequester() = default;
  virtual ~AnyRequester() noexcept = default;
  /**
   * @brief Access the abstract reader context of the requester.
   */
  virtual AnyDataReaderContext & get_reader_context() = 0;

  /**
   * @brief Access the abstract writer context of the requester.
   */
  virtual DataWriterContext & get_writer_context() = 0;

protected:
  AnyRequester(const AnyRequester &) = default;
  AnyRequester(AnyRequester &&) noexcept = default;
  AnyRequester & operator=(const AnyRequester &) = default;
  AnyRequester & operator=(AnyRequester &&) noexcept = default;
};


/**
 * @brief Requester sends requests and can receive replies to those.
 * @tparam TRequest type of request samples.
 * @tparam TReply type of reply samples.
 */
template <typename TRequest,
          typename TReply,
          class Base = detail::Duplex<TRequest, TReply, RequesterContext>>
class Requester final : public AnyRequester, public Base
{
public:
  using Base::Base;

  /**
   * @brief Loan a request sample from the underlying data writer.
   * @details Loaning a sample is necessary in order to communicate with zero-copy. Once the loan
   *          is filled it can be submitted through the @c write_reply function.
   * @return A new request loan with a sample to be set.
   * @return Timeout error if the underlying mutex hasn't been locked in the specified deadline.
   * @return Allocation error if it wasn't possible to allocate the sample.
   */
  Result<Loan<TRequest>> loan_request()
  {
    return Base::loan();
  }

  /**
   * @brief Take ownership of the request sample and store it in the cache.
   * @note The function doesn't write the sample to the underlying channel but only stores the
   *       sample in the request cache. Call @c flush_requests to deliver the sample!
   * @param[in] request The request to be sent.
   * @see RawDataWriter::direct_write for possible return values.
   */
  Result<SampleIdentity> write_request(Loan<TRequest> && request)
  {
    return Base::write(base::move(request), SystemTime::clock::now());
  }

  /**
   * @brief Copy the provided request sample and store it in the request cache.
   * @note The function doesn't write the sample to the underlying channel but only stores the
   *       sample in the request cache. Call @c flush_requests to deliver the sample!
   * @param[in] request The request to be sent.
   * @see RawDataWriter::direct_write for possible return values.
   */
  Result<SampleIdentity> write_request(const TRequest & request)
  {
    return Base::loan_and_write(request, SystemTime::clock::now());
  }

  /**
   * @brief Take ownership of the request sample and store it in the cache.
   * @note The function doesn't write the sample to the underlying channel but only stores the
   *       sample in the request cache. Call @c flush_requests to deliver the sample!
   * @param[in] request The request to be sent.
   * @param[in] timestamp Timestamp to be attached to the request.
   * @param[in] timeout Deadline for the operation.
   * @see RawDataWriter::direct_write for possible return values.
   */
  Result<SampleIdentity> write_request(Loan<TRequest> && request, const SystemTime timestamp)
  {
    return Base::write(base::move(request), timestamp);
  }

  /**
   * @brief Copy the provided request sample and store it in the request cache.
   * @note The function doesn't write the sample to the underlying channel but only stores the
   *       sample in the request cache. Call @c flush_requests to deliver the sample!
   * @param[in] request The request to be sent.
   * @param[in] timestamp Timestamp to be attached to the request.
   * @see RawDataWriter::direct_write for possible return values.
   */
  Result<SampleIdentity> write_request(const TRequest & request, const SystemTime timestamp)
  {
    return Base::loan_and_write(request, timestamp);
  }

  /**
   * @copydoc DataWriter::flush
   */
  Result<void> flush_requests()
  {
    return Base::flush();
  }

  /**
   * @copydoc DataReader::sync_from_channels()
   */
  void sync_replies()
  {
    Base::sync_from_channels();
  }

  /**
   * @brief Read replies from the underlying reader without removing them
   *
   * See DataReader::read() for more info.
   */
  typename Base::ReceivedSamples read_replies(
    base::size_t max_samples = base::numeric_limits<base::size_t>::max(),
    const SampleMask & mask = SampleMask::all())
  {
    return Base::read(max_samples, mask);
  }

  /**
   * @brief Read replies from the underlying reader and remove them
   *
   * See DataReader::take() for more info.
   */
  typename Base::ReceivedSamples take_replies(
    base::size_t max_samples = base::numeric_limits<base::size_t>::max(),
    const SampleMask & mask = SampleMask::all())
  {
    return Base::take(max_samples, mask);
  }

  // AnyRequester:

  DataReaderContext & get_reader_context() final
  {
    return Base::context().reader_context();
  }

  DataWriterContext & get_writer_context() final
  {
    return Base::context().writer_context();
  }
};

}  // namespace apex::plexus

#endif
