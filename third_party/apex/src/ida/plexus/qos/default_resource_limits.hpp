/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef APEX_PLEXUS_QOS_DEFAULT_RESOURCE_LIMITS_HPP
#define APEX_PLEXUS_QOS_DEFAULT_RESOURCE_LIMITS_HPP

#include "ida/config/default_deployment.hpp"
#include "ida/plexus/publisher/data_writer_qos.hpp"
#include "ida/plexus/qos/qos.hpp"
#include "ida/plexus/sample/sample_traits.hpp"

namespace apex::plexus::qos
{
namespace
{
constexpr uint32_t derive_max_allocated_samples(base::size_t size_of_type)
{
  uint32_t max_allocated_samples{0};
  // Inital values are from an educated guess and many years of experience with an L4 ADAS
  // project
  if (size_of_type <= 128) {
    max_allocated_samples = config::MAX_ALLOCATED_SAMPLES_IN_ONE_SHARED_MEMORY_POOL;
  } else if (size_of_type <= 1024) {
    max_allocated_samples = config::MAX_ALLOCATED_SAMPLES_IN_ONE_SHARED_MEMORY_POOL / 2;
  } else if (size_of_type <= 16384) {
    max_allocated_samples = config::MAX_ALLOCATED_SAMPLES_IN_ONE_SHARED_MEMORY_POOL / 5;
  } else if (size_of_type <= 131072) {
    max_allocated_samples = config::MAX_ALLOCATED_SAMPLES_IN_ONE_SHARED_MEMORY_POOL / 20;
  } else if (size_of_type <= 524288) {
    max_allocated_samples = config::MAX_ALLOCATED_SAMPLES_IN_ONE_SHARED_MEMORY_POOL / 50;
  } else if (size_of_type <= 1048576) {
    max_allocated_samples = config::MAX_ALLOCATED_SAMPLES_IN_ONE_SHARED_MEMORY_POOL / 100;
  } else if (size_of_type <= 10485760) {
    max_allocated_samples = config::MAX_ALLOCATED_SAMPLES_IN_ONE_SHARED_MEMORY_POOL / 200;
  } else {
    max_allocated_samples = 3;
  }
  return max_allocated_samples;
}
}  // namespace
/**
 * @brief Derives the default of simultaneous loans for a specific messages size
 * @tparam SampleType type of the sample
 * @tparam QoSType type of the QoS
 * @param history_depth The user-configure history depth
 * @param max_non_self_contained_type_serialized_size The size of a chunk which contains serialized
 * data
 */
template <typename SampleType, typename QoSType>
base::uint32_t default_number_of_max_allocated_samples_for(
  base::uint32_t history_depth,
  [[maybe_unused]] base::uint32_t max_non_self_contained_type_serialized_size)
{
  if constexpr (base::is_same<QoSType, DataWriterQoS>::value) {
    if constexpr (SampleTraits<SampleType>::is_self_contained) {
      constexpr base::size_t SIZE_OF_TYPE = sizeof(SampleType);
      auto max_allocated_samples = derive_max_allocated_samples(SIZE_OF_TYPE);
      return base::max(max_allocated_samples, history_depth + 1);
    } else {
      // In case of the non-self-contained type, decide the max_allocated_samples based on the
      // max_non_self_contained_type_serialized_size.
      auto max_allocated_samples =
        derive_max_allocated_samples(max_non_self_contained_type_serialized_size);
      return base::max(max_allocated_samples, history_depth + 1);
    }
  } else {
    // The reader requires local memory for deserialization of samples from connectors. We allow
    // filling the reader cache + 1 extra for processing of the next sample. If the user knows
    // that there won't be any connector communication, they can change the resource limit to 0.
    // However, failing out of the box is a really bad user experience.
    return history_depth + 1;
  }
}
}  // namespace apex::plexus::qos
#endif  // APEX_PLEXUS_QOS_DEFAULT_RESOURCE_LIMITS_HPP
