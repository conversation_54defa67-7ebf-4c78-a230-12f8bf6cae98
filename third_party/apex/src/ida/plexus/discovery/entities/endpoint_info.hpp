/// \copyright Copyright 2023 - 2024 Apex.AI, Inc.
/// All rights reserved.

#ifndef APEX_PLEXUS_DISCOVERY_ENTITIES_ENDPOINT_INFO_HPP
#define APEX_PLEXUS_DISCOVERY_ENTITIES_ENDPOINT_INFO_HPP

#include "ida/base/core/def.hpp"
#include "ida/base/core/optional.hpp"
#include "ida/base/core/string_view.hpp"
#include "ida/plexus/common/domain_id.hpp"
#include "ida/plexus/common/endpoint_id.hpp"
#include "ida/plexus/common/runtime_id.hpp"
#include "ida/plexus/discovery/entities/endpoint_info_builder.hpp"
#include "ida/plexus/discovery/entities/metadata.hpp"
#include "ida/plexus/discovery/entities/protocol_support.hpp"
#include "ida/plexus/discovery/entities/topic_info.hpp"
#include "ida/plexus/discovery/entities/traits.hpp"
#include "ida/plexus/discovery/entities/waitset_info.hpp"
#include "ida/plexus/discovery/settings.hpp"
#include "ida/plexus/publisher/data_writer_qos.hpp"
#include "ida/plexus/subscriber/data_reader_qos.hpp"

namespace apex::plexus::discovery
{

//************************************************************************************************
// Endpoints
//************************************************************************************************

enum class CommunicationType : base::uint8_t
{
  PUBLISH_SUBSCRIBE,
  REQUEST,
  REPLY
};

/*
 AXIVION Next Codeline MisraC++2023-11.3.1: Defines a compile time constant, safe since it is always
 null-terminated.
 */
constexpr base::string_view CommunicationTypes[] = {"PUBLISH_SUBSCRIBE", "REQUEST", "REPLY"};

class ReaderInfo
{
public:
  using IdType = EndpointId;
  using Builder = EndpointInfoBuilder<ReaderInfo>;
  friend class EndpointInfoBuilder<ReaderInfo,
                                   QosIsSet,
                                   ProtocolIsSet,
                                   MetadataIsSet,
                                   WaitsetInfoIsSet,
                                   EndpointTagsIsSet,
                                   MirroredEndpointIsSet>;
  friend class EndpointInfoBuilder<ReaderInfo, DefaultsAreSet>;

public:
  ~ReaderInfo() noexcept = default;

  ReaderInfo(const ReaderInfo & other) = default;
  ReaderInfo & operator=(const ReaderInfo & other) = default;
  ReaderInfo(ReaderInfo && other) noexcept = default;
  ReaderInfo & operator=(ReaderInfo && other) noexcept = default;

  const EndpointId & id() const;
  DomainId domain_id() const;
  base::string_view topic() const;
  base::string_view type() const;
  base::string_view type_hash() const;
  const DataReaderQoS & qos() const;
  const base::optional<MetaData> & metadata() const;
  const ProtocolSupport & protocol_support() const;
  const EndpointTagStorage & all_tags() const;
  base::optional<EndpointTagValue> get_tag(const EndpointTagKey & key) const;
  const CommunicationType & communication_type() const;

  /**
   * @brief Returns endpoint ID of the writer mirrored by this reader.
   * @details If not null, the entity is mirroring the opposite entity with the set ID. This entity
   *          will be matched only with the entity it mirrors. The functionality is required by some
   *          connectors that are mirroring existing endpoints and then require the matches to be
   *          1:1. If the logic with mirroring didn't exist, multiple connections might be created,
   *          leading to delivering a sample through multiple channels, essentially duplicating it
   *          on the reader side. See #27779 for more details.
   * @return EndpointId::null() if the reader is not a mirror endpoint
   */
  const EndpointId & mirrored_endpoint_id() const noexcept;

  /**
   * @brief Checks if the endpoint is a mirror endpoint.
   * @return True if the entity is mirroring the opposite entity with the set ID. This entity
   *          will be matched only with the entity it mirrors. The functionality is required by some
   *          connectors that are mirroring existing endpoints and then require the matches to be
   *          1:1. If the logic with mirroring didn't exist, multiple connections might be created,
   *          leading to delivering a sample through multiple channels, essentially duplicating it
   *          on the reader side. See #27779 for more details.
   */
  bool is_mirror() const noexcept;

  const WaitSetInfo & waitset_info() const;

  // TODO(matthias.killat) #26149 We must agree on how to use the info objects before we consider
  // removing this function. If we decide it should be immutable then we can remove the function but
  // have to live with worse reconstruction. ReaderInfo is a special case here, as it can logically
  // change for an existing reader due to this.

  // This method must exist if ReaderInfo is supposed to be mutable for the purpose of changing and
  // attaching, detaching and changing waitsets. Otherwise it is completely immutable and a change
  // requires complete reconstruction. The general idea is that a reader holds on to its ReaderInfo
  // object for this purpose (this is currently not the case and leads to suboptimal API use and
  // performance).
  void set_waitset_info(const WaitSetInfo & info);

  friend bool operator==(const ReaderInfo & lhs, const ReaderInfo & rhs);
  friend bool operator!=(const ReaderInfo & lhs, const ReaderInfo & rhs);
  friend bool operator<(const ReaderInfo & lhs, const ReaderInfo & rhs);
  friend bool operator>(const ReaderInfo & lhs, const ReaderInfo & rhs);

  friend class RequesterInfo;
  friend class ReplierInfo;

private:
  ReaderInfo(const EndpointId & id,
             const TopicInfo & topic_info,
             const DataReaderQoS & qos,
             const base::optional<MetaData> & metadata,
             const ProtocolSupport & protocol,
             const EndpointTagStorage & tags,
             const EndpointId & mirrored_endpoint_id,
             const base::optional<WaitSetInfo> & waitset_info);

  EndpointId m_id;
  TopicInfo m_topic_info;
  DataReaderQoS m_qos;
  ProtocolSupport m_protocol;

  // TODO(matthias.killat) the optional is a problem while reading the state under seqlock.
  // Use something where reading cannot fail (i.e. cause panic) like with optional.
  base::optional<MetaData> m_metadata;
  EndpointTagStorage m_tags;
  WaitSetInfo m_waitset_info;

  // If not null, the entity is mirroring the opposite entity with the set ID. This entity will
  // be matched only with the entity it mirrors. The functionality is required by some connectors
  // that are mirroring existing endpoints and then require the matches to be 1:1. If the logic
  // with mirroring didn't exist, multiple connections might be created, leading to delivering a
  // sample through multiple channels, essentially duplicating it
  // on the reader side. See #27779 for more details.
  // NB: avoiding optional to save on memory and mainly to avoid a panic under seqlock.
  EndpointId m_mirrored_endpoint_id{EndpointId::null()};

  mutable CommunicationType m_communication_type{CommunicationType::PUBLISH_SUBSCRIBE};
};

class WriterInfo
{
public:
  using IdType = EndpointId;
  using Builder = EndpointInfoBuilder<WriterInfo>;
  friend class EndpointInfoBuilder<WriterInfo,
                                   QosIsSet,
                                   ProtocolIsSet,
                                   MetadataIsSet,
                                   EndpointTagsIsSet,
                                   MirroredEndpointIsSet>;
  friend class EndpointInfoBuilder<WriterInfo, DefaultsAreSet>;

  template <typename WriterInfo, typename...>
  friend class EndpointInfoBuilder;
  ~WriterInfo() noexcept = default;

  WriterInfo(const WriterInfo & other) = default;
  WriterInfo & operator=(const WriterInfo & other) = default;
  WriterInfo(WriterInfo && other) noexcept = default;
  WriterInfo & operator=(WriterInfo && other) noexcept = default;

  const EndpointId & id() const;
  DomainId domain_id() const;
  base::string_view topic() const;
  base::string_view type() const;
  base::string_view type_hash() const;
  const DataWriterQoS & qos() const;
  const base::optional<MetaData> & metadata() const;
  const ProtocolSupport & protocol_support() const;
  const EndpointTagStorage & all_tags() const;
  base::optional<EndpointTagValue> get_tag(const EndpointTagKey & key) const;
  const CommunicationType & communication_type() const;

  /**
   * @brief Returns endpoint ID of the reader mirrored by this writer.
   * @details If not null, the entity is mirroring the opposite entity with the set ID. This entity
   *          will be matched only with the entity it mirrors. The functionality is required by some
   *          connectors that are mirroring existing endpoints and then require the matches to be
   *          1:1. If the logic with mirroring didn't exist, multiple connections might be created,
   *          leading to delivering a sample through multiple channels, essentially duplicating it
   *          on the reader side. See #27779 for more details.
   * @return EndpointId::null() if this writer is not a mirror endpoint
   */
  const EndpointId & mirrored_endpoint_id() const noexcept;

  /**
   * @brief Checks if the endpoint is a mirror endpoint.
   * @return True if the entity is mirroring the opposite entity with the set ID. This entity
   *          will be matched only with the entity it mirrors. The functionality is required by some
   *          connectors that are mirroring existing endpoints and then require the matches to be
   *          1:1. If the logic with mirroring didn't exist, multiple connections might be created,
   *          leading to delivering a sample through multiple channels, essentially duplicating it
   *          on the reader side. See #27779 for more details.
   */
  bool is_mirror() const noexcept;

  friend bool operator==(const WriterInfo & lhs, const WriterInfo & rhs);
  friend bool operator!=(const WriterInfo & lhs, const WriterInfo & rhs);
  friend bool operator<(const WriterInfo & lhs, const WriterInfo & rhs);
  friend bool operator>(const WriterInfo & lhs, const WriterInfo & rhs);

  friend class RequesterInfo;
  friend class ReplierInfo;


private:
  WriterInfo(const EndpointId & id,
             const TopicInfo & topic_info,
             const DataWriterQoS & qos,
             const base::optional<MetaData> & metadata,
             const ProtocolSupport & protocol,
             const EndpointTagStorage & tags,
             const EndpointId & mirrored_endpoint_id);

  EndpointId m_id;
  TopicInfo m_topic_info;
  DataWriterQoS m_qos;
  ProtocolSupport m_protocol;

  // TODO(matthias.killat) the optional is a problem while reading the state under seqlock.
  // Use something where reading cannot fail (i.e. cause panic) like with optional.
  base::optional<MetaData> m_metadata;
  EndpointTagStorage m_tags;

  // If not null, the entity is mirroring the opposite entity with the set ID. This entity will
  // be matched only with the entity it mirrors. The functionality is required by some connectors
  // that are mirroring existing endpoints and then require the matches to be 1:1. If the logic
  // with mirroring didn't exist, multiple connections might be created, leading to delivering a
  // sample through multiple channels, essentially duplicating it
  // on the reader side. See #27779 for more details.
  // NB: avoiding optional to save on memory and mainly to avoid a panic under seqlock.
  EndpointId m_mirrored_endpoint_id{EndpointId::null()};

  mutable CommunicationType m_communication_type{CommunicationType::PUBLISH_SUBSCRIBE};
};

class RequesterInfo
{
public:
  RequesterInfo(const ReaderInfo & reader_info, const WriterInfo & writer_info);
  ~RequesterInfo() noexcept = default;
  RequesterInfo(const RequesterInfo & other) = default;
  RequesterInfo & operator=(const RequesterInfo & other) = default;
  RequesterInfo(RequesterInfo && other) noexcept = default;
  RequesterInfo & operator=(RequesterInfo && other) noexcept = default;

  DomainId domain_id() const
  {
    return get_info<WriterInfo>().domain_id();
  }

  template <typename InfoType = WriterInfo>
  EndpointId id() const
  {
    return get_info<InfoType>().id();
  }

  template <typename InfoType = WriterInfo>
  base::string_view topic() const
  {
    return get_info<InfoType>().topic();
  }

  template <typename InfoType = WriterInfo>
  base::string_view type() const
  {
    return get_info<InfoType>().type();
  }

  template <typename InfoType = WriterInfo>
  base::string_view type_hash() const
  {
    return get_info<InfoType>().type_hash();
  }

  template <typename InfoType = WriterInfo>
  const auto & qos() const
  {
    return get_info<InfoType>().qos();
  }

  const base::optional<MetaData> & metadata() const
  {
    return get_info<WriterInfo>().metadata();
  }

  template <typename InfoType = WriterInfo>
  const ProtocolSupport & protocol_support() const
  {
    return get_info<InfoType>().protocol_support();
  }

  const EndpointTagStorage & all_tags() const
  {
    return get_info<WriterInfo>().all_tags();
  }

  base::optional<EndpointTagValue> get_tag(const EndpointTagKey & key) const
  {
    return get_info<WriterInfo>().get_tag(key);
  }

  const WaitSetInfo & waitset_info() const
  {
    return m_reader_info.waitset_info();
  }

  void set_waitset_info(const WaitSetInfo & info)
  {
    m_reader_info.set_waitset_info(info);
  }

  template <typename T>
  friend const WriterInfo & extract_writer_info(const T & request_or_reply);
  template <typename T>
  friend const ReaderInfo & extract_reader_info(const T & request_or_reply);

  friend const WriterInfo & extract_request_channel_endpoint_info(const RequesterInfo & requester);
  friend const ReaderInfo & extract_reply_channel_endpoint_info(const RequesterInfo & requester);

private:
  template <typename InfoType>
  const InfoType & get_info() const
  {
    if constexpr (base::is_same_v<InfoType, WriterInfo>) {
      return m_writer_info;
    } else if constexpr (base::is_same_v<InfoType, ReaderInfo>) {
      return m_reader_info;
    } else {
      static_assert(base::dependent_false<InfoType>::value,
                    "Unknown type requested in 'RequesterInfo::get_info'!");
    }
  }

  static void establish_invariants(const ReaderInfo & reader, const WriterInfo & writer);

  ReaderInfo m_reader_info;
  WriterInfo m_writer_info;
};

class ReplierInfo
{
public:
  ReplierInfo(const ReaderInfo & reader_info, const WriterInfo & writer_info);
  ~ReplierInfo() noexcept = default;
  ReplierInfo(const ReplierInfo & other) = default;
  ReplierInfo & operator=(const ReplierInfo & other) = default;
  ReplierInfo(ReplierInfo && other) noexcept = default;
  ReplierInfo & operator=(ReplierInfo && other) noexcept = default;

  DomainId domain_id() const
  {
    return get_info<WriterInfo>().domain_id();
  }

  template <typename InfoType = ReaderInfo>
  EndpointId id() const
  {
    return get_info<InfoType>().id();
  }

  template <typename InfoType = WriterInfo>
  base::string_view topic() const
  {
    return get_info<InfoType>().topic();
  }

  template <typename InfoType = WriterInfo>
  base::string_view type() const
  {
    return get_info<InfoType>().type();
  }

  template <typename InfoType = WriterInfo>
  base::string_view type_hash() const
  {
    return get_info<InfoType>().type_hash();
  }

  template <typename InfoType = WriterInfo>
  const auto & qos() const
  {
    return get_info<InfoType>().qos();
  }

  const base::optional<MetaData> & metadata() const
  {
    return get_info<WriterInfo>().metadata();
  }

  template <typename InfoType = WriterInfo>
  const ProtocolSupport & protocol_support() const
  {
    return get_info<InfoType>().protocol_support();
  }

  const EndpointTagStorage & all_tags() const
  {
    return get_info<WriterInfo>().all_tags();
  }

  base::optional<EndpointTagValue> get_tag(const EndpointTagKey & key) const
  {
    return get_info<WriterInfo>().get_tag(key);
  }

  const WaitSetInfo & waitset_info() const
  {
    return m_reader_info.waitset_info();
  }

  void set_waitset_info(const WaitSetInfo & info)
  {
    m_reader_info.set_waitset_info(info);
  }

  template <typename T>
  friend const WriterInfo & extract_writer_info(const T & request_or_reply);
  template <typename T>
  friend const ReaderInfo & extract_reader_info(const T & request_or_reply);

  friend const ReaderInfo & extract_request_channel_endpoint_info(const ReplierInfo & replier);
  friend const WriterInfo & extract_reply_channel_endpoint_info(const ReplierInfo & replier);

private:
  template <typename InfoType>
  const InfoType & get_info() const
  {
    if constexpr (base::is_same_v<InfoType, WriterInfo>) {
      return m_writer_info;
    } else if constexpr (base::is_same_v<InfoType, ReaderInfo>) {
      return m_reader_info;
    } else {
      static_assert(base::dependent_false<InfoType>::value,
                    "Unknown type requested in 'ReplierInfo::get_info'!");
    }
  }

  static void establish_invariants(const ReaderInfo & reader, const WriterInfo & writer);

  ReaderInfo m_reader_info;
  WriterInfo m_writer_info;
};

// =================================== Variant ===================================

using EndpointInfo = base::variant<ReaderInfo, WriterInfo, RequesterInfo, ReplierInfo>;

//************************************************************************************************
// Helpers
//************************************************************************************************

// TODO(#32374): Remove this function
template <typename T>
const WriterInfo & extract_writer_info(const T & request_or_reply)
{
  return request_or_reply.m_writer_info;
}

// TODO(#32374): Remove this function
template <typename T>
const ReaderInfo & extract_reader_info(const T & request_or_reply)
{
  return request_or_reply.m_reader_info;
}

const WriterInfo & extract_request_channel_endpoint_info(const RequesterInfo & requester);
const ReaderInfo & extract_request_channel_endpoint_info(const ReplierInfo & replier);
const ReaderInfo & extract_reply_channel_endpoint_info(const RequesterInfo & requester);
const WriterInfo & extract_reply_channel_endpoint_info(const ReplierInfo & replier);

inline bool operator==(const RequesterInfo & lhs, const RequesterInfo & rhs)
{
  return (extract_request_channel_endpoint_info(lhs) ==
          extract_request_channel_endpoint_info(rhs)) &&
         (extract_reply_channel_endpoint_info(lhs) == extract_reply_channel_endpoint_info(rhs));
}

inline bool operator!=(const RequesterInfo & lhs, const RequesterInfo & rhs)
{
  return !(lhs == rhs);
}

inline bool operator==(const ReplierInfo & lhs, const ReplierInfo & rhs)
{
  return (extract_request_channel_endpoint_info(lhs) ==
          extract_request_channel_endpoint_info(rhs)) &&
         (extract_reply_channel_endpoint_info(lhs) == extract_reply_channel_endpoint_info(rhs));
}

inline bool operator!=(const ReplierInfo & lhs, const ReplierInfo & rhs)
{
  return !(lhs == rhs);
}

//************************************************************************************************
// Traits
//************************************************************************************************

template <>
struct IdType<ReaderInfo>
{
  using type = EndpointId;
};

template <>
struct IdType<WriterInfo>
{
  using type = EndpointId;
};

template <>
struct MaxStorable<ReaderInfo>
{
  static const base::size_t value = IDA_MAX_ENDPOINTS_PER_PROCESS;
};

template <>
struct MaxStorable<WriterInfo>
{
  static const base::size_t value = IDA_MAX_ENDPOINTS_PER_PROCESS;
};

template <>
struct OppositeInfoType<ReaderInfo>
{
  using type = WriterInfo;
};

template <>
struct OppositeInfoType<WriterInfo>
{
  using type = ReaderInfo;
};

template <>
struct QosType<ReaderInfo>
{
  using type = DataReaderQoS;
};

template <>
struct QosType<WriterInfo>
{
  using type = DataWriterQoS;
};

template <>
struct IdType<RequesterInfo>
{
  using type = EndpointId;
};

template <>
struct OppositeInfoType<RequesterInfo>
{
  using type = ReplierInfo;
};

template <>
struct IdType<ReplierInfo>
{
  using type = EndpointId;
};

template <>
struct OppositeInfoType<ReplierInfo>
{
  using type = RequesterInfo;
};

template <typename InfoType>
struct IsPubSubInfoType : base::false_type
{
};

template <>
struct IsPubSubInfoType<ReaderInfo> : base::true_type
{
};

template <>
struct IsPubSubInfoType<WriterInfo> : base::true_type
{
};

template <typename InfoType>
constexpr inline bool IS_PUB_SUB_INFO_TYPE = IsPubSubInfoType<InfoType>::value;

template <typename InfoType>
struct IsRequestReplyInfoType : base::false_type
{
};

template <>
struct IsRequestReplyInfoType<RequesterInfo> : base::true_type
{
};

template <>
struct IsRequestReplyInfoType<ReplierInfo> : base::true_type
{
};

template <typename InfoType>
constexpr inline bool IS_REQUEST_REPLY_INFO_TYPE = IsRequestReplyInfoType<InfoType>::value;


}  // namespace apex::plexus::discovery

namespace apex::plexus
{

using apex::plexus::discovery::CommunicationType;
using apex::plexus::discovery::ReaderInfo;
using apex::plexus::discovery::ReplierInfo;
using apex::plexus::discovery::RequesterInfo;
using apex::plexus::discovery::WriterInfo;

template <>
inline std::string pretty_string<ReaderInfo>(const ReaderInfo & info)
{
  std::stringstream ss;
  ss << pretty_string("ReaderInfo",
                      info.id(),
                      info.topic(),
                      info.type(),
                      info.type_hash(),
                      info.domain_id(),
                      info.waitset_info());
  std::string str = ss.str();

  return str;
}

template <>
inline std::string pretty_string<WriterInfo>(const WriterInfo & info)
{
  std::stringstream ss;
  ss << pretty_string(
    "WriterInfo", info.id(), info.topic(), info.type(), info.type_hash(), info.domain_id());
  std::string str = ss.str();

  return str;
}

template <>
inline std::string pretty_string<RequesterInfo>(const RequesterInfo & info)
{
  std::stringstream ss;
  ss << pretty_string("RequesterInfo",
                      discovery::extract_request_channel_endpoint_info(info),
                      discovery::extract_reply_channel_endpoint_info(info));
  std::string str = ss.str();

  return str;
}

template <>
inline std::string pretty_string<ReplierInfo>(const ReplierInfo & info)
{
  std::stringstream ss;
  ss << pretty_string("ReplierInfo",
                      discovery::extract_request_channel_endpoint_info(info),
                      discovery::extract_reply_channel_endpoint_info(info));
  std::string str = ss.str();

  return str;
}

template <>
inline std::string pretty_string<CommunicationType>(const CommunicationType & communication_type)
{
  if (communication_type == CommunicationType::PUBLISH_SUBSCRIBE) {
    return "Publish-Subscribe";
  } else if (communication_type == CommunicationType::REQUEST) {
    return "Request";
  } else if (communication_type == CommunicationType::REPLY) {
    return "Reply";
  } else {
    return "Unknown";
  }
}

}  // namespace apex::plexus

#endif
