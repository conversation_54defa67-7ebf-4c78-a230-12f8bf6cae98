/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.

#ifndef APEX_PLEXUS_TEST_UTILS__DISCOVERY_FILESYSTEM__HPP_
#define APEX_PLEXUS_TEST_UTILS__DISCOVERY_FILESYSTEM__HPP_

// TODO(matthias.killat) temporary, replace with something equivalent to search the filesystem
#include <filesystem>

#include "ida/base/core/heap_allocator.hpp"
#include "ida/base/core/memory.hpp"
#include "ida/common/allocator.hpp"
#include "ida/plexus/discovery/impl/decentralized/observer_registry.hpp"
#include "ida/plexus/discovery/impl/decentralized/shared_memory.hpp"
#include "ida/plexus/discovery/impl/decentralized/topic_registry.hpp"
#include "ida/plexus/resource_acquisition/resource_acquisition.hpp"


namespace apex
{
namespace plexus
{
namespace discovery
{

using namespace apex::base::literals;
using SharedMemory = apex::plexus::discovery::SharedMemory;

/**
 *
 * @brief Allows to list or remove discovery specific  discovery resources on the filesystem.
 * The goal is to protect against leftovers from other tests and leftovers caused by the test
 * itself. Deliberately not part of the interface for now as it is NOT supposed to be used by Plexus
 * user applications.
 */
class DiscoveryFileSystem
{
public:
  /**
   * @brief List all existing RuntimeStates
   * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
   *                                creator. The namespace might be used to separate resources used
   *                                by different tests. It's easy to use a process PID as namespace
   *                                in comparison to managing different domain IDs.
   *
   * @return List of Runtime IDs found
   */
  static apex::Result<std::vector<RuntimeId>> list_runtime_states(
    const ResourcesNamespace & resources_namespace);

  /**
   * @brief List all existing Topic Registries
   * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
   *                                creator. The namespace might be used to separate resources used
   *                                by different tests. It's easy to use a process PID as namespace
   *                                in comparison to managing different domain IDs.
   *
   * @return List of Topic Registries found
   */
  static apex::Result<std::vector<TopicRegistry::NameType>> list_topic_registries(
    const ResourcesNamespace & resources_namespace);

  /**
   * @brief List all existing Observer Registries
   * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
   *                                creator. The namespace might be used to separate resources used
   *                                by different tests. It's easy to use a process PID as namespace
   *                                in comparison to managing different domain IDs.
   *
   * @return List of Observer Registries found
   */
  static apex::Result<std::vector<ObserverRegistry::NameType>> list_observer_registries(
    const ResourcesNamespace & resources_namespace);


  /**
   * @brief Remove all RuntimeStates from filesystem
   * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
   *                                creator. The namespace might be used to separate resources used
   *                                by different tests. It's easy to use a process PID as namespace
   *                                in comparison to managing different domain IDs.
   */
  static void remove_all_runtime_states(const ResourcesNamespace & resources_namespace);

  /**
   * @brief Remove all Discovery Registries from filesystem
   * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
   *                                creator. The namespace might be used to separate resources used
   *                                by different tests. It's easy to use a process PID as namespace
   *                                in comparison to managing different domain IDs.
   */
  static void remove_all_topic_registries(const ResourcesNamespace & resources_namespace);

  /**
   * @brief Remove all Discovery Registries that belong to a certain Domain ID
   * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
   *                                creator. The namespace might be used to separate resources used
   *                                by different tests. It's easy to use a process PID as namespace
   *                                in comparison to managing different domain IDs.
   *
   * @param id Domain ID
   */
  static void remove_all_topic_registries(const ResourcesNamespace & resources_namespace,
                                          DomainId id);

  /**
   * @brief Remove all Discovery Registries that belong to a certain Topic
   * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
   *                                creator. The namespace might be used to separate resources used
   *                                by different tests. It's easy to use a process PID as namespace
   *                                in comparison to managing different domain IDs.
   *
   * @param filter Lambda function that returns a TopicInfo
   */
  static void remove_filtered_topic_registries(
    const ResourcesNamespace & resources_namespace,
    const base::function_ref<bool(const TopicInfo &)> & filter);

  /**
   * @brief Remove all Observer Registries that belong to a certain Domain ID
   */
  static void remove_all_observer_registries(const ResourcesNamespace & resources_namespace);
};

namespace detail
{

/**
 * @brief List all files from path set in DISCOVERY_ROOT_DIRECTORY
 * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
 *                                creator. The namespace might be used to separate resources used
 *                                by different tests. It's easy to use a process PID as namespace
 *                                in comparison to managing different domain IDs.
 *
 * @return list of filenames as string
 */
std::vector<std::string> list_discovery_files(const ResourcesNamespace & resources_namespace);

/**
 * @brief Filter list of strings if they contain the substring
 *        Partial Matches are allowed.
 *
 * @param values vector of strings
 * @param substring string to search
 *
 * @return list of strings that match with the substring
 */
std::vector<std::string> filter(const std::vector<std::string> & values,
                                const std::string & substring);

/**
 * @brief Remove prefix as defined in DISCOVERY_REGISTRY_EXTENSION from list of filenames
 *
 * @param values vector of filenames
 *
 * @return list strings without discovery prefix
 */
std::vector<std::string> extract_prefix(const std::vector<std::string> & values);

/**
 * @brief Extract Runtime ID from a list of RuntimeState file names
 *
 * @param values vector of filenames
 *
 * @return list of Runtime IDs from the the file names
 */
std::vector<RuntimeId> extract_id(const std::vector<std::string> & values);

/**
 * @brief Check if the corresponding RuntimeState to a RuntimeID exist
 *
 * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
 *                                creator. The namespace might be used to separate resources used
 *                                by different tests. It's easy to use a process PID as namespace
 *                                in comparison to managing different domain IDs.
 * @param runtime_id ID of runtime to check
 *
 * @return bool if State exist, false if not
 */
apex::Result<bool> state_exists_on_filesystem(const ResourcesNamespace & resources_namespace,
                                              const RuntimeId & runtime_id);

/**
 * @brief Check if the corresponding Registry to a name exist
 *
 * @param registry_name name of the registry to check
 * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
 *                                creator. The namespace might be used to separate resources used
 *                                by different tests. It's easy to use a process PID as namespace
 *                                in comparison to managing different domain IDs.
 *
 * @return bool if Registry exist, false if not
 */
apex::Result<bool> registry_exists_on_filesystem(const ResourcesNamespace & resources_namespace,
                                                 const base::string_view registry_name);

/**
 * @brief Check if the corresponding Registry to a name exist
 *
 * @tparam RegistryNameType Type for the name of the Registry
 * @param[in] resources_namespace Namespace to filter the files that's in sync with the resource
 *                                creator. The namespace might be used to separate resources used
 *                                by different tests. It's easy to use a process PID as namespace
 *                                in comparison to managing different domain IDs.
 * @param registry_name name of the registry to check
 *
 * @return bool if Registry exist, false if not
 */
template <typename RegistryNameType>
inline apex::Result<bool> registry_exists_on_filesystem(
  const ResourcesNamespace & resources_namespace, const RegistryNameType && registry_name)
{
  return registry_exists_on_filesystem(resources_namespace, registry_name.as_string());
}

}  // namespace detail

}  // namespace discovery
}  // namespace plexus
}  // namespace apex

#endif
