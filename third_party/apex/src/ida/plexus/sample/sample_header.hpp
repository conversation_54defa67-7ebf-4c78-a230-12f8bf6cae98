/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef APEX_PLEXUS_HEADER_HPP
#define APEX_PLEXUS_HEADER_HPP

#include "ida/plexus/common/def.hpp"
#include "ida/plexus/common/endpoint_id.hpp"

namespace apex
{
namespace plexus
{

/**
 * @brief This should uniquely identify any given sample.
 */
struct SampleIdentity final
{
  EndpointId writer_id{EndpointId::null()};
  SequenceNumber sequence_number{SEQUENCE_NUMBER_INITIAL};
};

/**
 * @brief Returns the hash code for a @c SampleIdentity.
 *
 * Enabled the use of @c SampleIdentity as keys in dictionaries.
 *
 * @param value Sample identity value.
 * @return Hash code of the input @c SampleIdentity.
 */
size_t hash_code(const SampleIdentity & value);

bool operator==(const SampleIdentity & left, const SampleIdentity & right);
bool operator!=(const SampleIdentity & left, const SampleIdentity & right);

struct SampleHeader
{
  /**
   * @brief This sample's identity.
   */
  SampleIdentity identity;

  /**
   * @brief Identity of another sample related to this one. The nature of relation is unspecified.
   *
   * One intended use case for this is to correlate responses to requests when implenting services.
   */
  base::optional<SampleIdentity> related_sample_id;

  /**
   * @brief Timepoint when the sample was created (as set by the client).
   */
  SystemTime source_timestamp;

  /**
   * @brief A bespoke tag that a user can attach when writing a sample.
   * @note This is serves as a stop-gap solution to allow pairing rpc requests and responses until
   * plexus supports RPC.
   */
  base::uint64_t tag;
};

bool operator==(const SampleHeader & left, const SampleHeader & right);
bool operator!=(const SampleHeader & left, const SampleHeader & right);

}  // namespace plexus
}  // namespace apex

#endif  // APEX_PLEXUS_HEADER_HPP
