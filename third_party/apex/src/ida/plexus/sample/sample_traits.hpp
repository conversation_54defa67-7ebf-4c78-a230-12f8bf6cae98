/// @copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// @file
/// @brief Type traits for sample type

#ifndef APEX_PLEXUS_SAMPLE_TRAITS_HPP
#define APEX_PLEXUS_SAMPLE_TRAITS_HPP

#include "ida/base/core/type_traits.hpp"
#include "org/eclipse/cyclonedds/topic/TopicTraits.hpp"

namespace apex
{
namespace plexus
{

/**
 * @brief Type traits for a sample type.
 * @details The structure is designed to be used across the plexus codebase to make compile time
 *          decisions. The structure hides the dependencies (such as cyclone DDS code generator) so
 *          when they change, only the structure needs to change.
 * @tparam TSample Sample the traits are for.
 */
template <typename TSample>
struct SampleTraits
{
  /**
   * @brief Is the type self-contained?
   * @details A self contained type doesn't contain any pointers (to external or internal memory)
   *          and is safe to transmit using shared memory transport layer without serialization.
   * @warning If TSample is not a type generated by cyclone DDS, the value is set to true by
   *          default.
   */
  static constexpr bool is_self_contained =
    org::eclipse::cyclonedds::topic::TopicTraits<TSample>::isSelfContained();

  /**
   * @brief The name of this type, for reference by the hub
   * @warning If TSample is not a type generated by cyclone DDS, the value is set to "" by default
   */
  static constexpr const char * const type_name =
    org::eclipse::cyclonedds::topic::TopicTraits<TSample>::getTypeName();

  /**
   * @brief The name of this type representation, for reference by the connectors using type
   * supports
   * @warning If TSample is not a type generated by cyclone DDS, the value is set to "" by default
   */
  static constexpr const char * const type_representation_name =
    org::eclipse::cyclonedds::topic::TopicTraits<TSample>::getTypeRepresentationName();

  // TODO(Sumanth.Nirmal) currently the type hash is same as the type representation name, replace
  // this with a proper typeid and hash
  static constexpr const char * const type_hash = type_representation_name;
};

/// @brief Helper SFINAE type to enable specialization of self-contained samples
template <typename TSample, typename T = void>
using ForSelfContained = base::enable_if_t<SampleTraits<TSample>::is_self_contained, T>;

/// @brief Helper SFINAE type to enable specialization of non self-contained samples
template <typename TSample, typename T = void>
using ForNonSelfContained = base::enable_if_t<!SampleTraits<TSample>::is_self_contained, T>;

}  // namespace plexus
}  // namespace apex

#endif  // APEX_PLEXUS_SAMPLE_TRAITS_HPP
