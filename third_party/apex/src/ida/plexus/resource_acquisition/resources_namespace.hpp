/// @copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#ifndef APEX_IDA_PLEXUS_RESOURCE_ACQUISITION_NAMESPACE_HPP
#define APEX_IDA_PLEXUS_RESOURCE_ACQUISITION_NAMESPACE_HPP

#include "iox/semantic_string.hpp"

namespace apex::plexus
{

namespace detail
{

/**
 * @brief Capacity of the namespace string.
 * @details The size of the namespace is limited to 33 characters. The expectation is that the
 *          namespace will be a md5 hash generated by baz<PERSON> to avoid interference between different
 *          tests. Base16 representation of md5 has 32 character. Plus extra character for the dot.
 */
constexpr base::size_t RESOURCES_NAMESPACE_CAPACITY{33U};

bool namespace_does_contain_invalid_content(
  const base::fixed_string<RESOURCES_NAMESPACE_CAPACITY> & value) noexcept;

bool namespace_does_contain_invalid_characters(
  const base::fixed_string<RESOURCES_NAMESPACE_CAPACITY> & value) noexcept;

}  // namespace detail

/**
 * @brief String for the resources namespace.
 * @details The namespace is used to avoid interference between different tests. The namespace
 *          has to start with a dot continued with characters, digits, underscores or dashes.
 *          The default constructed namespace is ".apex".
 */
class ResourcesNamespace
: public iox::SemanticString<ResourcesNamespace,
                             detail::RESOURCES_NAMESPACE_CAPACITY,
                             &detail::namespace_does_contain_invalid_content,
                             &detail::namespace_does_contain_invalid_characters>
{
  using Base = iox::SemanticString<ResourcesNamespace,
                                   detail::RESOURCES_NAMESPACE_CAPACITY,
                                   &detail::namespace_does_contain_invalid_content,
                                   &detail::namespace_does_contain_invalid_characters>;

  using Base::Base;

public:
  /**
   * @brief Construct a new Resources Namespace object
   * @details The default constructed namespace is ".apex".
   */
  ResourcesNamespace() noexcept : Base(base::fixed_string<5>(".apex")) {}
};

}  // namespace apex::plexus

#endif  // APEX_IDA_PLEXUS_RESOURCE_ACQUISITION_NAMESPACE_HPP
