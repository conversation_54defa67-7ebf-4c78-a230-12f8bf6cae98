/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.

#include "ida/common/type_support_registry/type_support_registry.hpp"

#include "ida/base/core/bits/unfair_spinlock.hpp"
#include "ida/os/bits/synchronization/mutex.hpp"
#include "ida/os/bits/time/steady_clock.hpp"

namespace apex::type_supports
{

TypeSupportRegistry::TypeSupportRegistry(base::allocator & allocator)
: m_allocator(&allocator), m_type_supports(*m_allocator)
{
  invoke_late_joined_initializers();
}

[[nodiscard]] TypeSupportBase * TypeSupportRegistry::lookup_from_type(base::string_view type_name,
                                                                      TypeSupportKind kind)
{
  // handle dlopen()'ed message support libraries
  invoke_late_joined_initializers();

  auto maybe_ts = m_type_supports.get({{base::truncate_to_capacity, type_name}, kind});
  if (!maybe_ts) {
    return nullptr;
  }

  return *maybe_ts;
}

Result<void> TypeSupportRegistry::register_type_support(
  base::string_view type_name, base::not_null<TypeSupportBase *> type_support)
{
  if (type_name.size() > plexus::MAX_TYPE_LENGTH) {
    auto error_msg = base::err_message{"Type name exceeds max length: "};
    error_msg.append(base::truncate_to_capacity,
                     base::err_message(base::truncate_to_capacity, type_name));
    return err(error_msg);
  }

  TypeName name{base::panic_on_overflow, type_name};
  if (m_type_supports.get({name, type_support->kind()})) {
    auto error_msg = base::err_message{"Typesupport already registered for "};
    error_msg.append(base::truncate_to_capacity,
                     base::err_message(base::truncate_to_capacity, type_name));
    error_msg.append(base::truncate_to_capacity, " of kind ");
    error_msg.append(base::truncate_to_capacity,
                     base::err_message(base::truncate_to_capacity,
                                       type_support_kind_to_string(type_support->kind())));
    return err(error_msg);
  }

  if (const auto res = m_type_supports.insert({name, type_support->kind()}, type_support);
      res.has_error()) {
    return err("Failed to insert typesupport", res.error());
  }

  return base::ok();
}

void TypeSupportRegistry::invoke_late_joined_initializers()
{
  m_latest_observed_initializer =
    internal::visit_initializers(*this, m_latest_observed_initializer);
}

namespace internal
{

namespace
{

// This mutex ONLY guards initializers, not concurrent usage of the
// instance.
/*
 AXIVION Next Construct MisraC++2023-6.7.2: Reason: Code Quality (Functional suitability),
 Justification: Allow non-const global variables to use mutex.
 */

base::_::unfair_spinlock & get_initializer_mutex()
{
  /*
   AXIVION Next Construct MisraC++2023-6.7.1: Reason: Code Quality (Functional suitability),
   Justification: static required, this is a singleton that avoids
   static-init-order-fiasco because it's initialized lazily upon first execution.
   */
  static base::_::unfair_spinlock initializer_mutex{base::default_unfair_contention_patience};
  return initializer_mutex;
}

auto make_yield_with_backoff() -> auto
{
  using namespace apex::base::literals;

  size_t count = 0;
  return [count]() mutable {
    if (++count < 100) {
      (void)base::timeout<os::steady_clock>{1us}.wait();
    } else {
      (void)base::timeout<os::steady_clock>{50us}.wait();
    }
  };
}

}  // namespace

static TypeSupportInitializer *& get_initializer_head()
{
  /*
   AXIVION Next Construct MisraC++2023-6.7.1: Reason: Code Quality (Functional suitability),
   Justification: static required, this is a singleton that avoids
   static-init-order-fiasco because it's initialized lazily upon first execution.
   */
  static TypeSupportInitializer * initializer{nullptr};
  return initializer;
}

void add_initializer(base::not_null<TypeSupportInitializer *> initializer)
{
  const auto g = get_initializer_mutex().lock(make_yield_with_backoff());

  auto & head = get_initializer_head();
  initializer->next = head;
  head = initializer;
}

void remove_initializer(base::not_null<TypeSupportInitializer *> initializer)
{
  const auto g = get_initializer_mutex().lock(make_yield_with_backoff());

  auto & head = get_initializer_head();
  if (head == initializer) {
    head = head->next;
    return;
  }

  auto current = head;
  while (current) {
    if (current->next == initializer) {
      current->next = initializer->next;
      return;
    }
    current = current->next;
  }
}

TypeSupportInitializer * visit_initializers(TypeSupportRegistry & registry,
                                            TypeSupportInitializer * latest_observed)
{
  const auto g = get_initializer_mutex().lock(make_yield_with_backoff());

  TypeSupportInitializer * current = get_initializer_head();
  auto latest = current;

  while (current != latest_observed) {
    current->init(registry);
    current = current->next;
  }

  return latest;
}

}  // namespace internal

}  // namespace apex::type_supports
