/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief The logging utilities for Apex.Ida

#ifndef APEX_COMMON_LOGGING_HPP
#define APEX_COMMON_LOGGING_HPP

// TODO(simon.hoinkis): #24161 Use quill as the backend instead of the console logger
#include "ida/base/core/error.hpp"
#include "ida/base/core/logging.hpp"

namespace apex::base
{

inline base::log::LogStream & operator<<(base::log::LogStream & ostream,
                                         const base::error & err) noexcept
{
  // Walk through the cause chain and print the errors. Axivion doesn't like recursion, so we have
  // to implement the recursion by hand
  const base::error * cur = &err;

  while (cur != nullptr) {
    ostream << cur->message().c_str() << ", at " << cur->origin().function_name() << " ("
            << cur->origin().file_name() << ":" << cur->origin().line() << ")\n";
    if (auto cause = cur->cause()) {
      cur = &*cause;
      ostream << "  caused by: ";
    } else {
      cur = nullptr;
    }
  }
  return ostream;
}
}  // namespace apex::base

#endif  // APEX_COMMON_LOGGING_HPP
