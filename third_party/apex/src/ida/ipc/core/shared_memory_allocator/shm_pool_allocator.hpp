// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#ifndef CONCEPT_ABSTRACTION_LAYER_SHARED_MEMORY_ALLOCATOR_SHM_POOL_ALLOCATOR_HPP
#define CONCEPT_ABSTRACTION_LAYER_SHARED_MEMORY_ALLOCATOR_SHM_POOL_ALLOCATOR_HPP

#include "iceoryx_hoofs/internal/concurrent/loffli.hpp"
#include "ida/base/core/bits/memory/layout.hpp"
#include "ida/base/core/def.hpp"
#include "ida/base/core/expected.hpp"
#include "ida/base/core/memory.hpp"
#include "ida/ipc/core/settings.hpp"
#include "ida/ipc/core/shared_memory_allocator/concept.hpp"
#include "ida/ipc/core/shm_pointer.hpp"
#include "iox/not_null.hpp"

namespace apex
{
namespace ipc
{
namespace core
{
namespace shared_memory_allocator
{
class ShmPoolAllocator : public SharedMemoryAllocatorConcept<ShmPoolAllocator>
{
public:
  struct ShmPoolAllocatorConfig
  {
    ShmPoolAllocatorConfig() noexcept : m_layout(base::layout::from<base::uint64_t>()) {}

    explicit ShmPoolAllocatorConfig(const base::layout layout) noexcept : m_layout(layout) {}

    const base::layout layout() const noexcept
    {
      return m_layout;
    }

  private:
    base::layout m_layout;
  };
  using Configuration = ShmPoolAllocatorConfig;

  static constexpr base::uint8_t allocator_id{2};

  /// @brief c'tor
  /// @param[in] start_address of the memory this allocator manages
  /// @param[in] length of the memory this allocator manages, i.e. the pool size
  /// @param[in] config is the ShmPoolAllocatorConfig which contains the chunk size and the chunk
  /// alignment
  ShmPoolAllocator(iox::not_null<void * const> start_address,
                   const base::uint64_t length,
                   const Configuration & config) noexcept;

  ShmPoolAllocator(const ShmPoolAllocator &) = delete;
  ShmPoolAllocator(ShmPoolAllocator && other) noexcept = delete;
  ShmPoolAllocator & operator=(const ShmPoolAllocator &) noexcept = delete;
  ShmPoolAllocator & operator=(ShmPoolAllocator && other) noexcept = delete;
  ~ShmPoolAllocator() noexcept = default;

  /// @brief Returns the aligned size of the block
  base::uint64_t block_size() const noexcept;

  friend class SharedMemoryAllocatorConcept<ShmPoolAllocator>;

private:
  /// @brief allocates on the memory supplied with the ctor
  /// @param[in] layout containing size and alignment of the memory to allocate
  /// @return an expected containing a distance to the memory if allocation was successful,
  /// otherwise SharedMemoryAllocationError
  base::expected<PointerDetails, SharedMemoryAllocationError> allocate_impl(
    const base::layout layout) noexcept;

  /// @brief deallocates memory at given PointerOffset
  /// @param[in] offset where the memory shall be deallocated
  /// @param[in] layout containing size and alignment of the memory to deallocate
  void deallocate_impl(const PointerOffset offset, const base::layout) noexcept;

  /// @brief Returns the total number of blocks
  base::uint64_t max_samples_impl() noexcept;

  /// @copydoc SharedMemoryAllocatorConcept::get_allocator_instance_size
  static base::uint64_t get_instance_size_impl(const Configuration & config) noexcept;

  const base::layout m_layout;

  const base::uint64_t m_start_address{0};
  const base::uint64_t m_length{0};

  iox::concurrent::FixedSizeLoFFLi<
    SHARED_MEMORY_ALLOCATOR_CONCEPT__SHM_POOL_ALLOCATOR__LOFFLI_MAX_CAPACITY>
    m_free_chunks;
};

}  // namespace shared_memory_allocator
}  // namespace core
}  // namespace ipc
}  // namespace apex

#endif
