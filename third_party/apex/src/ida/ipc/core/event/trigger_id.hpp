// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#ifndef IDA_IPC_CORE_EVENT__TRIGGER_ID_HPP
#define IDA_IPC_CORE_EVENT__TRIGGER_ID_HPP

#ifdef QNX
  #include <sys/neutrino.h>
#endif

#include "ida/base/core/bits/memory/create.hpp"
#include "ida/base/core/def.hpp"
#include "ida/base/core/error.hpp"
#include "ida/base/core/limits.hpp"
#include "ida/base/core/logging.hpp"


namespace apex::ipc::core::event
{

// Trigger IDs have 32 bits of value + 8 bits of code. They are assigned as follows:
// - Code 1: value is a file descriptor
// - Code 2: value is a process-local number. These can be acquired by anyone from the process-local
//           global variable
// - Code 3: value is an IPC code for inter-process notification.
//           For now it is the last 4 bytes of the match ID (which are random),
//           but in the long term this should be communicated through discovery
// - Code 4: value is a runtime ID. This is used for discovery notifications.
//
// When adding a new code, 'check_code()' must also be updated

/// @note The trigger codes are sorted from high to low prio and are used for sorting when
///       returned from a reactor
constexpr base::int8_t TRIGGER_CODE_DEFAULT = 0;
constexpr base::int8_t TRIGGER_CODE_DISCOVERY = 1;
constexpr base::int8_t TRIGGER_CODE_INTERNAL = 2;
constexpr base::int8_t TRIGGER_CODE_IPC = 3;
constexpr base::int8_t TRIGGER_CODE_FD = 4;

// These codes are the absolute value of codes defined in neutrino.h
constexpr base::int8_t PULSE_CODE_UNBLOCK = 32;
constexpr base::int8_t PULSE_CODE_DISCONNECT = 33;
constexpr base::int8_t PULSE_CODE_THREADDEATH = 34;
constexpr base::int8_t PULSE_CODE_COIDDEATH = 35;
constexpr base::int8_t PULSE_CODE_NET_ACK = 36;
constexpr base::int8_t PULSE_CODE_NET_UNBLOCK = 37;
constexpr base::int8_t PULSE_CODE_NET_DETACH = 38;
constexpr base::int8_t PULSE_CODE_RESTART = 39;
constexpr base::int8_t PULSE_CODE_NORESTART = 40;
constexpr base::int8_t PULSE_CODE_UNBLOCK_RESTART = 41;
constexpr base::int8_t PULSE_CODE_UNBLOCK_TIMER = 42;


/** @brief Identifies a Notifier in a non-unique way. It is used to identify
 *        the origin of a trigger on the Listener side.
 */
struct TriggerId
{
  using CodeType = base::int8_t;
  using ValueType = base::uint32_t;
  static_assert(base::numeric_limits<ValueType>::max() <=
                base::numeric_limits<base::uintptr_t>::max());

public:
  /**
   * @brief TriggerId constructor
   * @param[in] code   The trigger code that identifies the type of the trigger. Must be positive
   * @param[in] value  The trigger value that contains the actual identifier
   */
  template <typename T>
  explicit TriggerId(
    base::badge<base::creator>,
    base::optional<base::error> & maybe_error,
    CodeType code,
    T value,
    typename base::enable_if<base::is_same<T, ValueType>::value, bool>::type = 0) noexcept
  : m_code{code}, m_value{value}
  {
    check_code(m_code, maybe_error);
  }

  /**
   * @brief TriggerId constructor
   * The passed id will be split into a code (8 bits) and a value (32 bits)
   * --- layout ---
   * 0b00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000'
   *  |  code  |            -             |           value                   |
   * @param[in] id   The encoded id
   */
  template <typename T>
  explicit TriggerId(
    base::badge<base::creator>,
    base::optional<base::error> & maybe_error,
    T id,
    typename base::enable_if<base::is_same<T, base::uint64_t>::value, bool>::type = 0) noexcept
  /*
   AXIVION DISABLE STYLE MisraC++2023-4.1.3, MisraC++2023-7.0.5: Reason: Code Quality
   (Functional suitability), Justification: we want to truncate the code to i8. Type
   conversion is okay.
   */
  : m_code(static_cast<CodeType>(id >> CODE_OFFSET)),
    m_value(static_cast<ValueType>(id & VALUE_MASK))
  // AXIVION ENABLE STYLE MisraC++2023-4.1.3
  {
    check_code(m_code, maybe_error);
    if ((id & 0x00FF'FFFF'FFFF'FFFFU) > base::numeric_limits<ValueType>::max()) {
      BASE_LOG(WARN,
               "Passed Trigger id value ["
                 << (id & 0x00FF'FFFF'FFFF'FFFFU) << "] is larger than max ValueType ["
                 << base::numeric_limits<ValueType>::max() << "] , it will be truncated.");
    }
  }
  TriggerId & operator=(const TriggerId &) noexcept = default;
  TriggerId & operator=(TriggerId &&) noexcept = default;
  TriggerId(const TriggerId &) noexcept = default;
  TriggerId(TriggerId &&) noexcept = default;

  CodeType code() const noexcept
  {
    return m_code;
  }

  ValueType value() const noexcept
  {
    return m_value;
  }

  base::uint64_t as_u64() const noexcept
  {
    /*
     AXIVION DISABLE STYLE MisraC++2023-7.0.5: Reason: Code Quality
     (Functional suitability), Justification: we want to truncate the code to i8. Type
     conversion is okay.
     */
    return (static_cast<base::uint64_t>(m_code) << CODE_OFFSET) |
           (static_cast<base::uint64_t>(m_value));
    // AXIVION ENABLE STYLE MisraC++2023-7.0.5
  }


  bool operator==(const TriggerId & rhs) const noexcept
  {
    return ((this->m_code == rhs.m_code) && (this->m_value == rhs.m_value));
  }

  bool operator<(const TriggerId & rhs) const noexcept
  {
    if (this->m_code != rhs.m_code) {
      return this->m_code < rhs.m_code;
    }
    return this->m_value < rhs.m_value;
  }


  bool operator!=(const TriggerId & rhs) const noexcept
  {
    return !(*this == rhs);
  }

  static bool is_qnx_reserved_code(const CodeType code)
  {
    return ((code == PULSE_CODE_UNBLOCK) || (code == PULSE_CODE_DISCONNECT) ||
            (code == PULSE_CODE_THREADDEATH) || (code == PULSE_CODE_COIDDEATH) ||
            (code == PULSE_CODE_NET_ACK) || (code == PULSE_CODE_NET_UNBLOCK) ||
            (code == PULSE_CODE_NET_DETACH) || (code == PULSE_CODE_RESTART) ||
            (code == PULSE_CODE_NORESTART) || (code == PULSE_CODE_UNBLOCK_RESTART) ||
            (code == PULSE_CODE_UNBLOCK_TIMER));
  }

private:
  void check_code(CodeType code, base::optional<base::error> & maybe_error) noexcept
  {
    if (code < 0) {
      maybe_error = base::error("Trigger id code must be positive");
      return;
    }
#ifdef QNX
    if (code < _PULSE_CODE_MINAVAIL) {
      maybe_error = base::error("Trigger id code must be larger than PULSE_CODE_MINAVAIL");
      return;
    }
#endif
    if ((code != TRIGGER_CODE_DEFAULT) && (code != TRIGGER_CODE_DISCOVERY) &&
        (code != TRIGGER_CODE_INTERNAL) && (code != TRIGGER_CODE_IPC) &&
        (code != TRIGGER_CODE_FD) && (code != PULSE_CODE_UNBLOCK) &&
        (code != PULSE_CODE_DISCONNECT) && (code != PULSE_CODE_THREADDEATH) &&
        (code != PULSE_CODE_COIDDEATH) && (code != PULSE_CODE_NET_ACK) &&
        (code != PULSE_CODE_NET_UNBLOCK) && (code != PULSE_CODE_NET_DETACH) &&
        (code != PULSE_CODE_RESTART) && (code != PULSE_CODE_NORESTART) &&
        (code != PULSE_CODE_UNBLOCK_RESTART) && (code != PULSE_CODE_UNBLOCK_TIMER)) {
      maybe_error = base::error("Trigger id code must be one of the defined code");
      return;
    };
  }

  static constexpr base::uint8_t CODE_OFFSET{56};
  static constexpr base::uint64_t VALUE_MASK{0x0000'0000'FFFF'FFFFUL};
  CodeType m_code;
  ValueType m_value;
};

[[nodiscard]] size_t hash_code(const TriggerId & trigger_id) noexcept;
std::ostream & operator<<(std::ostream & os, const TriggerId & trigger_id);
base::log::LogStream & operator<<(base::log::LogStream & stream, const TriggerId & trigger_id);
}  // namespace apex::ipc::core::event
#endif
