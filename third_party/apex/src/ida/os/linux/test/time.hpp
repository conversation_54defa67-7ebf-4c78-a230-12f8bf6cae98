// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#ifndef APEX_OS_LINUX_TESTING_TIME_HPP
#define APEX_OS_LINUX_TESTING_TIME_HPP

#include <gmock/gmock.h>

#include "ida/os/linux/time.hpp"

namespace apex::os::linux::testing
{

struct TimeMock
{
  MOCK_METHOD2(clock_gettime, int(clockid_t clk_id, struct timespec * tp));
  MOCK_METHOD4(
    clock_nanosleep,
    int(clockid_t clk_id, int flags, const struct timespec * request, struct timespec * remain));
};

TimeMock & time_mock();

}  // namespace apex::os::linux::testing

#endif
