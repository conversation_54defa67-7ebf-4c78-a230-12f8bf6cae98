# TCP (C99)

## Purpose

Nearly all TCP transactions require multiple calls to the networking libraries:

1. Create a client/server socket, [`socket`](https://linux.die.net/man/7/socket)
1. Set the required socket options, [`setsockopt`](https://linux.die.net/man/2/setsockopt)
1. Bind the socket to an internal interface, [`bind`](https://linux.die.net/man/2/bind)
1. Wait for and receive data, [`recv`](https://linux.die.net/man/2/recv),
with the added possibility of retrying until required number of bytes is received
1. Send data between sockets, [`sendto`](https://linux.die.net/man/2/sendto),
1. Connect a client socket, [`connect`](https://linux.die.net/man/3/connect)
1. Listen for a new client, [`listen`](https://linux.die.net/man/7/listen)
1. Accept a new TCP client, [`accept`](https://linux.die.net/man/3/accept)

Each of these calls must be handled in a MISRA-compliant implementation, by
checking all return values and reacting to possible error conditions.

The TCP API packs all required functionality into a safe, MISRA C:2012 compliant
library, with a simple interface.

## Design

### How does it work

The TCP API safely wraps the following POSIX.1 calls:

- [`socket`](https://linux.die.net/man/7/socket)
- [`setsockopt`](https://linux.die.net/man/2/setsockopt)
- [`bind`](https://linux.die.net/man/2/bind)
- [`recv`](https://linux.die.net/man/2/recv)
- [`connect`](https://linux.die.net/man/3/connect)
- [`listen`](https://linux.die.net/man/7/listen)
- [`accept`](https://linux.die.net/man/3/accept)

### Literature used in this implementation

- Linux Man pages linked in the [how does it work](#how-does-it-work) section
above
- Windows uses the same BSD network stack as Linux

### Inputs and outputs for the API

See the Apex.Grace API documentation in `apex_tcp.h`.

## Usage

An example usage is provided  in the below CPP wrapper classes: [](apex::networking::tcp::TcpServer),
[](apex::networking::tcp::TcpClient), [](apex::networking::tcp::TcpConnection)

### Special conditions

- Certain operating systems such as Windows require escalated privileges to
bind to a socket
- If the  receive timeout is zero, the underlying socket will be switched to
[O_NONBLOCK mode](https://www.gnu.org/software/libc/manual/html_node/Open_002dtime-Flags.html)
- The Windows implementation limits the maximum timeout to `UINT_MAX` milliseconds,
 ~50 days
