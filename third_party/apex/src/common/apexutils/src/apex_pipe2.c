// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include "apexutils/apex_pipe2.h"

#ifdef QNX
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>

#include <share.h>
#include <sys/iomsg.h>
#include <sys/ftype.h>


int pipe2(int fildes[2], int flags)
{
  fildes[0] = _connect(_lowest_fd, "/dev/pipe", 0,
      (unsigned int) flags | O_RDONLY | O_NOCTTY | O_ANON, SH_DENYNO,
      _IO_CONNECT_OPEN, 0, _IO_FLAG_RD | _IO_FLAG_WR, _FTYPE_PIPE,
      0, 0, NULL, 0, NULL, NULL);

  if (fildes[0] == -1) {
    if (errno == ENOENT) {
      errno = ENOSYS;
    }
    return -1;
  }

  fildes[1] = _sopenfd(fildes[0], flags | O_WRONLY, SH_DENYNO, _IO_OPENFD_PIPE);
  if (fildes[1] != -1) {
    return 0;
  }

  int const saved_errno = errno;
  int ret;

  io_close_t const msg = {
    .i.type = _IO_CLOSE,
  };

  do {
    ret = (int)MsgSendnc(fildes[0], &msg, sizeof(msg), NULL, 0);
  } while ((ret == -1) && (errno == EINTR) && __libc_close_retry);

  (void)ConnectDetach_r(fildes[0]);

  errno = saved_errno;

  return -1;
}
#endif

int apex_pipe2(int fildes[2], int flags)
{
  return pipe2(fildes, flags);
}
