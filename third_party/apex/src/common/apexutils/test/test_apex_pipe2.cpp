// Copyright 2023 Apex.AI, Inc.
// All rights reserved.
#include <gtest/gtest.h>

#include <fcntl.h>

#include "apexutils/apex_pipe2.h"


TEST(test_apex_pipe2, no_flags)
{
  int fds[2];
  EXPECT_EQ(apex_pipe2(fds, 0), APEX_RET_OK);
  ::close(fds[0]);
  ::close(fds[1]);
}

TEST(test_apex_pipe2, close_flag)
{
  int fds[2];
  EXPECT_EQ(apex_pipe2(fds, O_CLOEXEC), APEX_RET_OK);
  ::close(fds[0]);
  ::close(fds[1]);
}
