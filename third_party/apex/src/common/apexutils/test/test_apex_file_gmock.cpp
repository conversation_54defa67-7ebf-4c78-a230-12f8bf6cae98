// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <apexutils/apexutils.h>
#include <fstream>
#include <string>
#include <memory>

using ::testing::Return;
using ::testing::_;
using ::testing::NiceMock;

// read function become the stub function
class apexutilsMock
{
public:
  apexutilsMock() {}
  MOCK_METHOD3(read, ssize_t(int __fd, void * __buf, size_t __nbytes));
};
std::unique_ptr<apexutilsMock> apexutils;

class test_apex_file_gmock : public testing::Test
{
public:
  void SetUp()
  {
    apexutils = std::make_unique<NiceMock<apexutilsMock>>();
  }
  void TearDown()
  {
    apexutils.reset();
  }
};

ssize_t read(int __fd, void * __buf, size_t __nbytes)
{
  return apexutils->read(__fd, __buf, __nbytes);
}

/**
 * This gtest code tests the retrying logic to handle case with partial read
 * when read API returns less size than the request (read the file partially).
 *
 * @test{
"req": ["SWRQ_APEXUTILS_226}
 */
TEST_F(test_apex_file_gmock, test_file_retry) {
  static constexpr size_t kPARTIAL_READ_SIZE = 1;
  static constexpr size_t kREAD_SIZE = 10;
  static constexpr size_t retry_parameter = 5;
  char buffer[kREAD_SIZE];

  // Dummy file descriptor
  int32_t fd = 1;

  EXPECT_CALL(*apexutils,
    read(_, _, _)).WillRepeatedly(Return(kPARTIAL_READ_SIZE));

  int64_t bytes_read = apex_read_bytes(
    fd,
    buffer,
    kREAD_SIZE,
    retry_parameter);
  /*
   * the stubbed read function returns kPARTIAL_READ_SIZE
   * and re-tried to read retry_parameter times.
   * So, the final read byte size should be retry_parameter*kPARTIAL_READ_SIZE.
   */
  EXPECT_EQ((size_t)bytes_read, retry_parameter * kPARTIAL_READ_SIZE);
}
