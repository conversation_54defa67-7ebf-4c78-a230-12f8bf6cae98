// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.
#include "gtest/gtest.h"
#include "apexutils/apexutils.h"
#ifdef APEX_LINUX
#include <unistd.h>
#include <sys/prctl.h>
#include <sys/resource.h>
#include <sys/time.h>
#include <sys/types.h>
#include <linux/capability.h>
#include <limits>

#include "apexutils/apex_rt_detail.h"
#endif

#define ONE_KB (1 * 1024)
#define ONE_MB (ONE_KB * ONE_KB)

#if defined(APEX_LINUX)
namespace apex
{
/*
 * The memory lock limitation affects mlockall function with an unprivileged process.
 * Refer to https://linux.die.net/man/2/setrlimit
 */
class MemLockLimitResizer
{
public:
  explicit MemLockLimitResizer(rlim_t memory_lock_limit)
  : mem_lock_limit_changed_(false)
  {
    int res;
    struct rlimit newlimit;
    res = getrlimit(RLIMIT_MEMLOCK, &oldlimit_);
    EXPECT_EQ(res, 0);
    auto current_limit = oldlimit_.rlim_cur;
    if (current_limit != memory_lock_limit) {
      newlimit.rlim_max = oldlimit_.rlim_max;
      newlimit.rlim_cur = memory_lock_limit;
      /* Set the memory lock limitation */
      res = setrlimit(RLIMIT_MEMLOCK, &newlimit);
      EXPECT_EQ(res, 0);
      mem_lock_limit_changed_ = true;
    }
  }
  virtual ~MemLockLimitResizer()
  {
    int res;
    if (mem_lock_limit_changed_) {
      /* Restore the memory lock limitation */
      res = setrlimit(RLIMIT_MEMLOCK, &oldlimit_);
      EXPECT_EQ(res, 0);
    }
  }

private:
  rlim_t memory_lock_limit_;
  struct rlimit oldlimit_;
  bool mem_lock_limit_changed_;
};
}  // namespace apex
#endif  // APEX_LINUX

/// @test{
/// "req" : ["SWRQ_APEXUTILS_442"]
/// }
TEST(test_apex_rt, test_apex_mem_rt) {
#if defined(APEX_LINUX) || defined(APEX_QNX)
  const uid_t my_uid = getuid();
  int res;
  if (0U == my_uid) {
#if defined(APEX_LINUX)
    // I have sudo privileges. Check if I have CAP_IPC_LOCK capability to lock memory
    res = prctl(PR_CAPBSET_READ, CAP_IPC_LOCK, 0, 0, 0);
    if (res > 0) {
#endif
    // I have IPC_LOCK capability
    EXPECT_EQ(apex_proc_mem_rt_init(ONE_KB), APEX_RET_OK);
    EXPECT_EQ(apex_proc_mem_rt_init(0), APEX_RET_OK);
    EXPECT_EQ(apex_proc_mem_rt_init(std::numeric_limits<size_t>::max()),
      APEX_RET_MEM_ALIGN_ERR);
#if defined(APEX_LINUX)
  }  // res > 0
#endif  // prctl in Linux
  } else {
    #if defined(APEX_LINUX)
    auto resizer = apex::MemLockLimitResizer(ONE_MB);
    #endif
    EXPECT_NE(apex_proc_mem_rt_init(ONE_KB), APEX_RET_OK);
  }
#else
  EXPECT_EQ(apex_proc_mem_rt_init(0U), APEX_RET_OK);
#endif
}

/*
 * Test apex_proc_mem_rt_init with mlockall failure by
 * 1. setting the user id to a non root user if the current user is root
 * 2. setting the memory lock limitation configuration
 */
#ifdef APEX_LINUX
TEST(test_apex_rt, apex_proc_mem_rt_init_returns_error_with_mlockall_failure) {
  uid_t my_euid = geteuid();
  uid_t my_uid = getuid();
  int res;
  if (0U == my_uid) {
    GTEST_SKIP() << "Test should only run when user is not root";
  }
  {
    auto resizer = apex::MemLockLimitResizer(ONE_MB);
    EXPECT_EQ(apex_proc_mem_rt_init(ONE_KB), APEX_RET_MEM_LOCK_ERR);
  }
  if (0U == my_uid && (uid_t)-1 != my_euid) {
    /* Restore root effective uid again */
    res = seteuid(my_euid);
    ASSERT_EQ(res, 0);
  }
}
#endif

/// @test{
/// "req" : ["SWRQ_APEXUTILS_443"]
/// }
TEST(test_apex_rt, test_apex_cpu_rt) {
#if defined(APEX_LINUX) || defined(APEX_QNX)
  EXPECT_EQ(apex_proc_cpu_rt_init(0), APEX_RET_ERROR);
  EXPECT_EQ(apex_proc_cpu_rt_init(1), APEX_RET_OK);
  EXPECT_EQ(apex_proc_cpu_rt_init(10), APEX_RET_OK);
#else
  EXPECT_NE(apex_proc_cpu_rt_init(0U), APEX_RET_OK);
#endif
}

#if defined(APEX_LINUX)
TEST(test_apex_rt, apex_set_cpu_affinity_mask_returns_OK_on_success) {
  cpu_set_t set;
  CPU_ZERO(&set);
  CPU_SET(0, &set);
  EXPECT_EQ(apex_set_cpu_affinity_mask(getpid(), &set), APEX_RET_OK);
}

TEST(test_apex_rt, apex_set_cpu_affinity_mask_returns_error_on_failure) {
  cpu_set_t set;
  CPU_ZERO(&set);
  CPU_SET(0, &set);
  EXPECT_EQ(apex_set_cpu_affinity_mask(getpid() - 999, &set), APEX_RET_SET_CPU_AFFINITY_ERR);
}

TEST(test_apex_rt, apex_cpu_set_adds_cpu_to_cpu_set) {
  cpu_set_t set;
  CPU_ZERO(&set);
  bool is_set_before = CPU_ISSET(0, &set);
  EXPECT_FALSE(is_set_before);
  apex_cpu_set_macro_wrapper(0, &set);
  bool is_set_after = CPU_ISSET(0, &set);
  EXPECT_TRUE(is_set_after);
}

TEST(test_apex_rt, apex_cpu_set_does_not_add_cpu_to_cpu_set_when_cpu_number_is_too_high) {
  cpu_set_t set;
  CPU_ZERO(&set);
  size_t cpu_too_big = sizeof(cpu_set_t) * 8;
  bool is_set_before = CPU_ISSET(cpu_too_big, &set);
  EXPECT_FALSE(is_set_before);
  apex_cpu_set_macro_wrapper(cpu_too_big, &set);
  bool is_set_after = CPU_ISSET(cpu_too_big, &set);
  EXPECT_FALSE(is_set_after);
}
#endif  // defined(APEX_LINUX)

TEST(test_apex_rt, apex_set_cpu_affinity) {
  {
    // empty cpu set
    apex_cpuset_t cpu_set;
    apex_cpu_zero(&cpu_set);
    EXPECT_EQ(apex_set_cpu_affinity(cpu_set), APEX_RET_SET_CPU_AFFINITY_ERR);
  }

  {
    // too large cpu set
    apex_cpuset_t cpu_set;
    apex_cpu_zero(&cpu_set);
    apex_cpu_set(APEX_CPUSET_SIZE, &cpu_set);
    EXPECT_EQ(apex_set_cpu_affinity(cpu_set), APEX_RET_SET_CPU_AFFINITY_ERR);
  }

  {
    apex_cpuset_t cpu_set;
    apex_cpu_zero(&cpu_set);
    apex_cpu_set(1, &cpu_set);
    EXPECT_EQ(apex_set_cpu_affinity(cpu_set), APEX_RET_OK);
  }

  {
    apex_cpuset_t cpu_set;
    apex_cpu_zero(&cpu_set);
    apex_cpu_set(1, &cpu_set);
    apex_cpu_set(3, &cpu_set);
    EXPECT_EQ(apex_set_cpu_affinity(cpu_set), APEX_RET_OK);
  }
}
