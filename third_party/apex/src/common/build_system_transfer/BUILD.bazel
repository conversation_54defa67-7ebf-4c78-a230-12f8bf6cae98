load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_VALUES")
load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_INVALID")
load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_CODEGEN")
load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_COLCON_UBUNTU")
load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_COLCON_YOCTO")
load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_CONAN_CENTER")
load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_CONAN_UBUNTU")
load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_BITBAKE_YOCTO_CODEGEN_CONTAINER")
load("@apex//common/build_system_transfer/backend/bazel:defs.bzl", "BST_MODE_BITBAKE_YOCTO_CODEGEN_INCLUDED")
load("@bazel_skylib//lib:selects.bzl", "selects")
load("@bazel_skylib//rules:common_settings.bzl", "string_flag")

string_flag(
    name = "mode",
    build_setting_default = BST_MODE_INVALID,
    values = BST_MODE_VALUES,
    visibility = ["//visibility:public"],
)

[
    config_setting(
        name = bst_mode,
        flag_values = {
            ":mode": bst_mode,
        },
    )
    for bst_mode in BST_MODE_VALUES
]

selects.config_setting_group(
    name = "colcon",
    match_any = [
        ":" + BST_MODE_COLCON_UBUNTU,
        ":" + BST_MODE_COLCON_YOCTO,
    ],
)

selects.config_setting_group(
    name = "conan",
    match_any = [
        ":" + BST_MODE_CONAN_CENTER,
        ":" + BST_MODE_CONAN_UBUNTU,
    ],
)

selects.config_setting_group(
    name = "yocto_sdk",
    match_any = [
        ":" + BST_MODE_COLCON_YOCTO,
        ":" + BST_MODE_BITBAKE_YOCTO_CODEGEN_CONTAINER,
        ":" + BST_MODE_BITBAKE_YOCTO_CODEGEN_INCLUDED,
    ],
)

selects.config_setting_group(
    name = "use_cmake_find_package",
    match_any = [
        ":" + BST_MODE_CONAN_CENTER,
        ":" + BST_MODE_COLCON_YOCTO,
        ":" + BST_MODE_BITBAKE_YOCTO_CODEGEN_CONTAINER,
        ":" + BST_MODE_BITBAKE_YOCTO_CODEGEN_INCLUDED,
    ],
)

selects.config_setting_group(
    name = "bitbake_mode",
    match_any = [
        ":" + BST_MODE_BITBAKE_YOCTO_CODEGEN_CONTAINER,
        ":" + BST_MODE_BITBAKE_YOCTO_CODEGEN_INCLUDED,
    ],
)

selects.config_setting_group(
    name = "enabled",
    match_any = [
        ":" + bst_mode
        for bst_mode in BST_MODE_VALUES
        if bst_mode != BST_MODE_CODEGEN and bst_mode != BST_MODE_INVALID
    ],
)
