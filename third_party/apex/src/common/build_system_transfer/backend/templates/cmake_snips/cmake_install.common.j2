{% for target in targets -%}
{% for hdr in target.hdrs -%}
{% if colcon_mode %}
if(NOT IS_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/{{hdr}}")
install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/{{hdr}}" DESTINATION "include/${PROJECT_NAME}/{{hdr|dirname}}")
else()
install(DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/{{hdr}}" DESTINATION "include/${PROJECT_NAME}/{{hdr|dirname}}")
endif()
{% else %}
if(NOT IS_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/{{hdr}}")
install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/{{hdr}}" DESTINATION "include/{{hdr|dirname}}")
else()
install(DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/{{hdr}}" DESTINATION "include/{{hdr|dirname}}")
endif()
{% endif -%}
{% endfor -%} 

{% if target.kind == "EXECUTABLE" -%}
{% if target.runfiles_wrapper %}
install(PROGRAMS {{target.runfiles_wrapper}} DESTINATION bin)
install(DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/{{target.runfiles_dir}} DESTINATION share/{{package_name}})
install(TARGETS {{target.name}}
    EXPORT {{package_name}}Targets
    RUNTIME DESTINATION share/{{package_name}}/{{target.runfiles_dir}}/{{target.runfiles_workspace_name}}/{{target.runfiles_dest_for_self}}
)
{% else %}
install(TARGETS {{target.name}})
{% endif %}
{% elif target.kind == "LIBRARY" or target.kind == "INTERFACE_LIBRARY" or target.kind == "FILES_LIBRARY" or target.kind == "MODULE" -%}
install(TARGETS {{target.name}}
    EXPORT {{package_name}}Targets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
{% if target.hdrs %}
    INCLUDES DESTINATION include
{% endif %}
)
{% elif target.kind == "PYMODULE" -%}
install(TARGETS {{target.name}}
    EXPORT {{package_name}}Targets
    LIBRARY DESTINATION lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/site-packages/{{package.name}}
    ARCHIVE DESTINATION lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/site-packages/{{package.name}}
    RUNTIME DESTINATION lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/site-packages/{{package.name}}
{% if target.hdrs %}
    INCLUDES DESTINATION include
{% endif %}
)
{% elif target.kind == "PYBINARY" or target.kind == "PYLIBRARY" -%}

{% if not colcon_mode and target.name == "pkg" -%}{# "pkg" is used as target name for all python requirements #}
{% for file in target.srcs   -%} {# + target.data #}
install(FILES "{{file}}" DESTINATION "lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/{{file|dirname}}")
{% if file|basename == "entry_points.txt" -%}
{% include 'cmake_snips/create_entry_point_script.include.j2' %}
file(READ "{{file}}" __file_lines)
string(REGEX REPLACE ";" "\\\\;" __file_lines "${__file_lines}")
string(REGEX REPLACE "\n" ";" __file_lines "${__file_lines}")
list(REMOVE_ITEM __file_lines "")
set(__inside_console_scripts FALSE)
foreach(__line IN LISTS __file_lines)
    if (__line MATCHES "^\\[")
        if (__line STREQUAL "[console_scripts]")
            set(__inside_console_scripts TRUE)
        else()
            set(__inside_console_scripts FALSE)
        endif()
    else()
        if (__inside_console_scripts)
            _create_entry_point("${__line}")
        endif()
    endif()
endforeach()
{% endif -%}
{% endfor -%}
{% else %}
{% for file in target.srcs -%}
install(FILES "{{file}}" DESTINATION "lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/site-packages/{{file|dirname}}")
{% endfor -%}
{% endif %}

{% if not colcon_mode and target.data is defined -%}
{% for file in target.data -%}
if(NOT IS_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/{{file}}")
install(FILES "${CMAKE_CURRENT_SOURCE_DIR}/{{file}}" DESTINATION "{{file|dirname}}")
else()
install(DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/{{file}}" DESTINATION "{{file|dirname}}")
endif()
{% endfor -%}
{% endif -%}

{% if target.kind == "PYBINARY" %}
set(__script_file_name "${CMAKE_CURRENT_BINARY_DIR}/entry_point_{{target.name}}")
file(WRITE "${__script_file_name}" "#!/bin/sh\n")
file(APPEND "${__script_file_name}" "\"exec\" \"python3\" \"\$(dirname \"\$(realpath -- \"\$0\")\")/../lib/python${Python_VERSION_MAJOR}.${Python_VERSION_MINOR}/{{target.main}}\" \"\$@\"\n")
install(PROGRAMS "${CMAKE_CURRENT_BINARY_DIR}/entry_point_{{target.name}}" DESTINATION "bin" RENAME "{{target.name}}")
{% endif -%}

{% else -%}
{{fail("Unexpected target-kind " + target.kind + " used for target "+ target.name)}}
{% endif -%}
{% endfor -%}

{% for subdirectory in subdirectories %}
add_subdirectory({{subdirectory}})
{% endfor %}

{% if package is not none -%}
{% if package.entry_points -%}
{% if "console_scripts" in package.entry_points %}
#
# Python entry points
#
{% for console_script in package.entry_points["console_scripts"] %}
{% include 'cmake_snips/create_entry_point_script.include.j2' %}
_create_entry_point("{{console_script}}")
{% endfor -%}
{% endif -%}
{% endif -%}
{% endif -%}

{% if not colcon_mode and package is not none -%}
{% if all_py_targets|length > 0 -%}
#
# Python pkg info
#
{% include 'cmake_snips/create_python_egg_info.include.j2' %}
create_python_egg_info()
{% endif -%}
{% endif -%}