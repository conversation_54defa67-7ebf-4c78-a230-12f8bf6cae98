{% if package is not none and all_targets -%}
{% include 'cmake_snips/cmake_set_and_check.cmake.j2' %}

{% include 'cmake_snips/system_dependencies.include.j2' %}

{% if colcon_mode and package.name == "zstd_vendor" %}
enable_language(ASM)
set_source_files_properties(__repo_root__/lib/decompress/huf_decompress_amd64.S PROPERTIES LANGUAGE ASM)
{% endif -%}

{% for build_dependency in package.transitive_dependencies %}
{% if build_dependency.name == "python_vendor" %}
{# Skip python vendor #}
{% elif build_dependency.bst_substitute_with and build_dependency.bst_substitute_with.cmake_find_package %}
{{build_dependency.bst_substitute_with.cmake_find_package | replace('{pkg_name}', build_dependency.name) }}
{% endif %}
{% endfor %}

{% for build_dependency in package.build_dependencies %}
{% if build_dependency.name == "python_vendor" %}
{# Skip python vendor #}
{% elif build_dependency.bst_substitute_with and build_dependency.bst_substitute_with.cmake_find_package %}
{# Already in transitive dependencies #}
{% else %}
{# Avoid running find_package over and over again by using IF-NOT-FOUND check #}
if (NOT {{build_dependency.name}}_FOUND)
    find_package({{build_dependency.name}} CONFIG REQUIRED)
endif()
{% endif %}
{% endfor %}

{% if package.requires_python %}
if (NOT QNX)
    find_package(Python REQUIRED COMPONENTS Interpreter Development)
endif()
{% endif %}

{% if package.requires_system_openssl %}
find_package(OpenSSL REQUIRED COMPONENTS Crypto SSL)
{% endif %}

{% endif %}