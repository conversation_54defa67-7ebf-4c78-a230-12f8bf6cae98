load("@apex//common/bazel/aspects/dependencies:locator.bzl", "locators_for_label_list")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "DynamicLinkingHint")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//common/bazel/helpers:defs.bzl", "merge_default_info")

def _merge_cc_infos_impl(ctx):
    cc_info = cc_common.merge_cc_infos([dep[CcInfo] for dep in ctx.attr.deps])
    default_info = merge_default_info([dep[DefaultInfo] for dep in ctx.attr.deps])
    rm_info = RuleMetaInfo(
        deps = locators_for_label_list(ctx.attr, "deps"),
        dynamic_linking_hint = DynamicLinkingHint(),
    )
    return [cc_info, default_info, rm_info]

merge_cc_infos = rule(
    implementation = _merge_cc_infos_impl,
    attrs = {
        "deps": attr.label_list(
            providers = [CcInfo],
            doc = "Libraries to be merged",
        ),
    },
    provides = [CcInfo, RuleMetaInfo],
    doc = "Merge multiple C/C++ libraries into one.",
)
