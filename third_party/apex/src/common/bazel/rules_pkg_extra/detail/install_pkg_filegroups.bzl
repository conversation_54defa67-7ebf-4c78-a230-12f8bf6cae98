load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@rules_pkg//pkg:providers.bzl", "PackageFilegroupInfo")
load(":detail/utils.bzl", "prepare_pkg_filegroups_for_python")

SCRIPT_TEMPLATE = """
#! /bin/bash
set -e
if [ $# -ne 1 ]; then
    >&2 echo "ERROR: Provide the destination directory as an argument"
    exit 1
fi

if [ -z "$BUILD_WORKING_DIRECTORY" ]; then
  >&2 echo "ERROR: install_pkg_filegroups must be called through 'bazel run'"
fi

destination=$1
case "$destination" in
  /*)
    # looks like an absolute path, good
    ;;
  *)
    # in case of execution through `bazel run` $BUILD_WORKING_DIRECTORY is set to the path of the calling context.
    destination="$BUILD_WORKING_DIRECTORY/$destination"
    ;;
esac

if [ ! -d "$destination" ]; then
    >&2 echo "ERROR: Destination directory $destination does not exist"
    exit 1
fi
{copy_files} --mapping-json {mapping_json} {force_flag} --dest $1
echo "Installed {srcs} to $1"
"""

def _install_pkg_filegroups_impl(ctx):
    json_file, inputs = prepare_pkg_filegroups_for_python(
        actions = ctx.actions,
        pkg_filegroups = [tgt[PackageFilegroupInfo] for tgt in ctx.attr.srcs],
        json_filename = "{}_files.json".format(ctx.label.name),
        short_paths = True,
    )

    script_file = ctx.actions.declare_file(ctx.label.name)
    ctx.actions.write(
        output = script_file,
        content = SCRIPT_TEMPLATE.format(
            copy_files = ctx.executable._script.short_path,
            mapping_json = json_file.short_path,
            force_flag = "--force" if ctx.attr.force else "--no-force",
            srcs = [str(tgt.label) for tgt in ctx.attr.srcs],
        ),
    )

    runfiles = ctx.runfiles(files = [ctx.executable._script] + inputs).merge(ctx.attr._script[DefaultInfo].default_runfiles)
    return [
        DefaultInfo(executable = script_file, runfiles = runfiles),
        RuleMetaInfo(),  # Not interesting for aspects at the moment
    ]

install_pkg_filegroups = rule(
    implementation = _install_pkg_filegroups_impl,
    doc = """Creates a directory according to a `PackageFilegroupInfo`""",
    executable = True,
    attrs = {
        "srcs": attr.label_list(
            providers = [PackageFilegroupInfo],
            default = [],
        ),
        "_script": attr.label(
            default = ":copy_files",
            executable = True,
            cfg = "exec",
        ),
        "force": attr.bool(
            default = False,
            doc = "Overwrite any existing destination files instead of failing with an error",
        ),
    },
    provides = [DefaultInfo],
)
