load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary", "py_library")
load("//tools/bazel/rules_docs:defs.bzl", "bzl_docs")

py_library(
    name = "jinja_helpers",
    srcs = glob(["jinja_helpers/**/*.py"]),
    imports = ["."],
)

py_binary(
    name = "expand_jinja_template",
    srcs = ["expand_jinja_template.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":jinja_helpers",
        requirement("click"),
        requirement("jinja2"),
    ],
)

bzl_docs(
    name = "bzl_docs",
    srcs = ["expand_jinja_template.bzl"],
    bzl_docs_deps = [
        "//common/bazel/aspects/dependencies:bzl_docs",
        "//common/bazel/aspects/rule_meta_aspect:bzl_docs",
        "//common/bazel/reflection:bzl_docs",
    ],
    deps = [
        "@aspect_bazel_lib//lib:stamping",
    ],
)
