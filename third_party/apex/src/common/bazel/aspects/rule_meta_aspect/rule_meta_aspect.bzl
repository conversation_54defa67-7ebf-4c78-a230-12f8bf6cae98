load("@apex//common/bazel/aspects/dependencies:locator.bzl", "locator_for_label")
load("@apex//common/bazel/aspects/dependencies:locator.bzl", "locators_for_label_list")
load("@apex//common/bazel/rules_cc:constants.bzl", "SHARED_LIBRARY_NAME_TAG_PREFIX")
load("@apex_config//:defs.bzl", "RULE_META_INFO_CONFIG")
load(":providers.bzl", "DynamicLinkingHint")
load(":providers.bzl", "InstallSpaceHint")
load(":providers.bzl", "PrecompiledLibrary")
load(":providers.bzl", "RuleMetaInfo")

def _get_short_basename(label, is_executable):
    """Computes the basename for the final library/executable."""
    name_parts = label.name.rsplit("/", 1)
    if len(name_parts) == 1:
        basename = name_parts[0]
    else:
        basename = name_parts[1]
    if basename.endswith(".so") or is_executable:
        return basename
    else:
        return "lib{}.so".format(basename)

def _handle_cc_binary(target, ctx):
    is_executable = not ctx.rule.attr.linkshared
    is_py_extension_module = "Python extension module" in (ctx.rule.attr.tags or [])
    use_short_name = is_py_extension_module or is_executable
    dynamic_linking_hint = DynamicLinkingHint(
        pic_objects = target[OutputGroupInfo].compilation_outputs.to_list(),
        is_executable = is_executable,
        basename = _get_short_basename(label = target.label, is_executable = is_executable) if use_short_name else None,
        user_link_flags = ctx.rule.attr.linkopts,
    )
    return RuleMetaInfo(
        deps = locators_for_label_list(ctx.rule.attr, "deps"),
        data_deps = locators_for_label_list(ctx.rule.attr, "data"),
        dynamic_linking_hint = dynamic_linking_hint,
    )

def _handle_cc_import(target, ctx):
    deps = locators_for_label_list(ctx.rule.attr, "deps")
    srcs = []
    cc_import = None
    if ctx.rule.attr.shared_library != None:
        # deps is for targets, srcs for (precompiled) files. A cc_import on a cc_binary(linkshared = True)
        # target is not a real import from the perspective of the dynamic_linking_aspect, because
        # the LinkerInput for that library (and its dependencies) has already been created in the cc_binary.
        # Also, the 'cc_import' field causes the 'library' field in DynamicLinkingInfo to be set to the same
        # file as the 'library' field in the cc_binary's DynamicLinkingInfo, even though that field is
        # supposed to be unique.
        # Summary: If a cc_binary is imported, the cc_import rule acts as an empty forwarding rule and
        # the special cc_import behavior is not used.
        if CcInfo in ctx.rule.attr.shared_library or CcSharedLibraryInfo in ctx.rule.attr.shared_library:
            deps.append(locator_for_label(ctx.rule.attr, "shared_library"))
        else:
            cc_import = PrecompiledLibrary(
                shared_library = ctx.rule.file.shared_library,
                interface_library = ctx.rule.file.interface_library,
            )
            srcs.append(locator_for_label(ctx.rule.attr, "shared_library"))
    elif ctx.rule.attr.static_library != None:
        if "unsafe_pic_static_library" in (ctx.rule.attr.tags or []):
            cc_import = PrecompiledLibrary(pic_static_library = ctx.rule.file.static_library)
        elif "unsafe_nopic_static_library" in (ctx.rule.attr.tags or []):
            cc_import = PrecompiledLibrary(nopic_static_library = ctx.rule.file.static_library)
        else:
            fail(
                "The dynamic linking aspect, which re-links libraries into shared objects, " +
                "cannot process a static library without further information. " +
                "Static libraries in cc_import can only be processed if they are tagged either " +
                "with 'unsafe_pic_static_library' or with 'unsafe_nopic_static_library'.\n" +
                "If the static library was built with -fPIC, then it can be re-linked into a " +
                "shared library - this needs to be opted into with the 'unsafe_pic_static_library' " +
                "tag, which has an 'unsafe_' prefix to signal that problems will occur if the " +
                "library was not indeed built with -fPIC.",
                "An alternative is the 'unsafe_nopic_static_library' tag, which can be used no " +
                "whether the library was built with -fPIC. This will not link the static library " +
                "into any shared object, and instead only into any executables depending on it. " +
                "This results in incomplete libraries that cannot be loaded with dlopen(), which " +
                "can be surprising to users, hence this tag is also marked with 'unsafe_'.\n" +
                "The best solution, if possible, would be to import a shared library instead.",
            )
        srcs = [locator_for_label(ctx.rule.attr, "static_library")]
    else:
        fail("cc_import without shared_library or static_library")
    return RuleMetaInfo(
        dynamic_linking_hint = DynamicLinkingHint(
            cc_import = cc_import,
        ),
        deps = deps,
        srcs = srcs,
    )

def _handle_cc_library(target, ctx):
    tags = ctx.rule.attr.tags or []
    short_name = None

    # Highest precedence: If this library comes from an apex_cc_library, the name is determined by the
    # 'shared_lib_name' attribute of the macro. This information is encoded in a tag.
    for tag in (ctx.rule.attr.tags or []):
        if tag.startswith(SHARED_LIBRARY_NAME_TAG_PREFIX):
            short_name = tag[len(SHARED_LIBRARY_NAME_TAG_PREFIX):]

    # Fallback to the tags that indicate a Python extension module or a plugin
    if short_name == None:
        for tag in (ctx.rule.attr.tags or []):
            if tag in ["Python extension module", "Use short library name in install space"]:
                short_name = _get_short_basename(label = target.label, is_executable = False)

    dynamic_linking_hint = DynamicLinkingHint(
        pic_objects = target[OutputGroupInfo].compilation_outputs.to_list(),
        basename = short_name,
        user_link_flags = ctx.rule.attr.linkopts,
        implementation_deps = [tgt.label for tgt in (ctx.rule.attr.implementation_deps or [])],
    )
    install_space_hint = InstallSpaceHint(
        includes = ctx.rule.attr.includes,
        strip_include_prefix = ctx.rule.attr.strip_include_prefix,
        include_prefix = ctx.rule.attr.include_prefix,
    )
    return RuleMetaInfo(
        deps = locators_for_label_list(ctx.rule.attr, "deps") + locators_for_label_list(ctx.rule.attr, "implementation_deps"),
        data_deps = locators_for_label_list(ctx.rule.attr, "data"),
        dynamic_linking_hint = dynamic_linking_hint,
        install_space_hint = install_space_hint,
    )

def _handle_cc_shared_library(target, ctx):
    use_short_name = "Use short library name in install space" in (ctx.rule.attr.tags or [])
    if ctx.rule.attr.shared_lib_name:
        basename = ctx.rule.attr.shared_lib_name
    else:
        basename = _get_short_basename(label = target.label, is_executable = False) if use_short_name else None
    return RuleMetaInfo(
        deps = locators_for_label_list(ctx.rule.attr, "deps"),
        dynamic_linking_hint = DynamicLinkingHint(
            basename = basename,
        ),
    )

def _handle_filegroup(target, ctx):
    # Theoretically we could do a nested label locator if ctx.rule.attr.output_group != ""
    return RuleMetaInfo(
        deps = locators_for_label_list(ctx.rule.attr, "srcs"),
    )

def _handle_native_binary(target, ctx):
    return RuleMetaInfo(
        srcs = [locator_for_label(ctx.rule.attr, "src")],
        data_deps = locators_for_label_list(ctx.rule.attr, "data"),
    )

def _handle_proto_library(target, ctx):
    return RuleMetaInfo(
        srcs = locators_for_label_list(ctx.rule.attr, "srcs"),
        deps = locators_for_label_list(ctx.rule.attr, "deps"),
    )

def _handle_py_binary(target, ctx):
    return _handle_py_library(target, ctx)

def _handle_py_library(target, ctx):
    kwargs = {}
    if "py_need_no_wheel" in ctx.rule.attr.tags:
        kwargs["install_space_hint"] = InstallSpaceHint(py_need_no_wheel = True)
    return RuleMetaInfo(
        deps = locators_for_label_list(ctx.rule.attr, "deps"),
        data_deps = locators_for_label_list(ctx.rule.attr, "data"),
        **kwargs
    )

def _handle_py_wheel(target, ctx):
    return RuleMetaInfo(
        deps = locators_for_label_list(ctx.rule.attr, "deps"),
    )

def _handle_sh_binary(target, ctx):
    return RuleMetaInfo(
        deps = locators_for_label_list(ctx.rule.attr, "deps"),
    )

def _irrelevant(target, ctx):
    return RuleMetaInfo()

def _not_implemented(target, ctx):
    return RuleMetaInfo(not_implemented = True)

RULE_IMPLS = {
    "cc_binary": _handle_cc_binary,
    "cc_import": _handle_cc_import,
    "cc_library": _handle_cc_library,
    "cc_shared_library": _handle_cc_shared_library,
    "filegroup": _handle_filegroup,
    "native_binary": _handle_native_binary,
    "proto_library": _handle_proto_library,
    "py_binary": _handle_py_binary,
    "py_library": _handle_py_library,
    "py_wheel": _handle_py_wheel,
    "sh_binary": _handle_sh_binary,
    "toolchain_type": None,
    "cc_toolchain_type": None,
    "cc_toolchain_alias": None,
    "merge_dynamic_deps": None,
    "config_setting": None,
    "string_flag": None,
    "bool_flag": None,
    "_keyed_bool_setting": None,
    "pkg_files": None,
    "_license_kind": None,
    "_license": None,
    "_configure_file": None,
    "_copy_file": None,
    "genrule": None,
    "cmake_like_configure_file": None,
    "launcher_flag_alias": None,
    "java_import": None,
    "java_toolchain_alias": None,
    "java_plugins_flag_alias": None,
    "java_binary": None,
} | RULE_META_INFO_CONFIG.rule_handler_extension_dict

def _rule_meta_aspect_impl(target, ctx):
    if RuleMetaInfo in target:  # already defined explicitly in the rule itself
        return []

    rule_specific_impl = RULE_IMPLS.get(ctx.rule.kind, "not found") or _irrelevant

    if rule_specific_impl != "not found":  # if handler function found, use it
        return [rule_specific_impl(target, ctx)]

    elif RULE_META_INFO_CONFIG.fail_on_missing and AnalysisTestResultInfo not in target:
        # no handler found, then fail when used as a build dependency

        # Defer failure to execution phase (instead of analysis phase)
        validation_output = ctx.actions.declare_file(target.label.name + ".rule-meta-aspect-validation")
        ctx.actions.run_shell(
            outputs = [validation_output],
            command = 'echo "RuleMetaInfo is required for $2" > $1; exit 1',
            arguments = [validation_output.path, ctx.rule.kind],
        )

        return [
            OutputGroupInfo(_validation = depset([validation_output])),
            _not_implemented(target, ctx),
        ]
    else:  # no handler found, no build failure
        return [_not_implemented(target, ctx)]

rule_meta_aspect = aspect(
    attr_aspects = ["*"],
    implementation = _rule_meta_aspect_impl,
    apply_to_generating_rules = True,
)
