"""
A rule that generates the required files (entry_points.txt, METADATA) to register
entry points with a py_library target.
"""

load("@bazel_skylib//rules:expand_template.bzl", "expand_template")
load("@rules_python//python:defs.bzl", "py_binary")

TEMPLATE = "@apex//common/bazel/rules_python_extra:py_entry_points_binary_template"

def py_entry_points_binary(
        *,
        name,
        group = "console_scripts",
        entry_point_name = "",
        deps = [],
        py_entry_points_library,
        **kwargs):
    """
    Define an executable script that calls a given entry_point.

    Args:
        name: The name of the target - but also the name of the entry_point to extract from the py_entry_points_library
        group: The entry_points group that this entry_point is in
        entry_point_name: Optional argument in case the Bazel target `name` is not the actual entry point name
        py_entry_points_library: The py_library that contains the entry_point to extract
        **kwargs: Additional arguments passed along to the py_library
    """
    if "main" in kwargs:
        fail("Cannot specify a main file with this rule since it is generated automatically")

    entry_points_filename = "%s_launching_script.py" % name
    entry_point_name_str = entry_point_name if entry_point_name else name
    expand_template(
        name = "generate_{name}_main".format(name = name),
        out = entry_points_filename,
        substitutions = {
            "{{entry_point}}": entry_point_name_str,
            "{{group}}": group,
        },
        template = TEMPLATE,
        tags = ["manual"],
        visibility = ["//visibility:private"],
    )

    py_binary(
        name = name,
        srcs = [entry_points_filename],
        main = entry_points_filename,
        deps = deps + [py_entry_points_library],
        **kwargs
    )
