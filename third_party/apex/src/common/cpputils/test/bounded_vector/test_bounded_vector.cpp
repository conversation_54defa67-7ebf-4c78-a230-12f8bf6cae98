// Copyright (c) 2022 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include <limits>
#include <memory>
#include <numeric>
#include <utility>
#include <vector>

#include <bounded_vector/bounded_vector.hpp>

namespace
{
template <class T>
struct invalid_alloc_tmp : public std::allocator<T>
{
  static size_t MAX_ERR_COUNT;
  template <typename _Up, typename... _Args>
  void construct(_Up * __p, _Args &&... __args)
  {
    static size_t count = 0;
    /* throw the exception in the 2nd call */
    if (count < MAX_ERR_COUNT) {
      ::new (reinterpret_cast<void *>(__p)) _Up(std::forward<_Args>(__args)...);
      count++;
    } else {
      throw std::runtime_error("invalid_construct");
    }
  }
};

template <class T>
size_t invalid_alloc_tmp<T>::MAX_ERR_COUNT = 0;

using invalid_alloc = invalid_alloc_tmp<int32_t>;

struct TestType
{
  static int constructor_call_cnt;
  TestType() : m_count(constructor_call_cnt++)
  {
    if (constructor_call_cnt > 5) {
      throw apex::runtime_error("Failed in constructor!");
    }
  }
  int m_count;
};
}  // namespace

int TestType::constructor_call_cnt = 0;

TEST(bounded_vector, basic_test)
{
  // test static capacity() function
  using IntegerVector = apex::BoundedVector<int, 1024>;
  EXPECT_EQ(IntegerVector::capacity(), 1024);

  // test copy assignment from std::vector
  IntegerVector int_bounded_vec;
  std::vector<int> int_vec;
  for (int i = 0; i < 10; i++) {
    int_vec.push_back(i);
    int_bounded_vec.push_back(i);
  }
  IntegerVector int_bounded_vec2;
  int_bounded_vec2 = int_vec;
  EXPECT_EQ(int_bounded_vec, int_bounded_vec2);

  // test out of bound assignment from std::vector object
  std::vector<int> int_big_vec(2000, 0);
  EXPECT_THROW(int_bounded_vec2 = int_big_vec, apex::invalid_argument);
}

TEST(bounded_vector, basic_test_stack_storage)
{
  // test static capacity() function
  using IntegerVector = apex::StackVector<int, 1024>;
  EXPECT_EQ(IntegerVector::capacity(), 1024);

  // test copy assignment from std::vector
  IntegerVector int_bounded_vec;
  std::vector<int> int_vec;
  for (int i = 0; i < 10; i++) {
    int_vec.push_back(i);
    int_bounded_vec.push_back(i);
  }
  IntegerVector int_bounded_vec2;
  int_bounded_vec2 = int_vec;
  EXPECT_EQ(int_bounded_vec, int_bounded_vec2);

  // test out of bound assignment from std::vector object
  std::vector<int> int_big_vec(2000, 0);
  EXPECT_THROW(int_bounded_vec2 = int_big_vec, apex::invalid_argument);
}

TEST(bounded_vector, test_pop_back)
{
  using IntegerVector = apex::BoundedVector<int, 1024>;
  EXPECT_EQ(IntegerVector::capacity(), 1024);

  IntegerVector int_bounded_vec;
  for (int i = 0; i < 10; ++i) {
    int_bounded_vec.push_back(i);
  }

  // test pop_back()
  int_bounded_vec.pop_back();
  auto size = int_bounded_vec.size();
  EXPECT_EQ(size, 9);
  for (size_t i = 0; i < size; ++i) {
    EXPECT_EQ(int_bounded_vec[i], i);
  }

  // test pop_back() for empty vector
  int_bounded_vec.clear();
  ASSERT_EQ(int_bounded_vec.size(), 0);
  EXPECT_THROW(int_bounded_vec.pop_back(), apex::logic_error);
}

TEST(bounded_vector, test_pop_back_stack_storage)
{
  using IntegerVector = apex::StackVector<int, 1024>;
  EXPECT_EQ(IntegerVector::capacity(), 1024);

  IntegerVector int_bounded_vec;
  for (int i = 0; i < 10; ++i) {
    int_bounded_vec.push_back(i);
  }

  // test pop_back()
  int_bounded_vec.pop_back();
  auto size = int_bounded_vec.size();
  EXPECT_EQ(size, 9);
  for (size_t i = 0; i < size; ++i) {
    EXPECT_EQ(int_bounded_vec[i], i);
  }

  // test pop_back() for empty vector
  int_bounded_vec.clear();
  ASSERT_EQ(int_bounded_vec.size(), 0);
  EXPECT_THROW(int_bounded_vec.pop_back(), apex::logic_error);
}

TEST(bounded_vector, iterator_conversion)
{
  apex::BoundedVector<int, 16U> vector;
  vector.push_back(1);
  vector.push_back(2);
  vector.push_back(3);

  ASSERT_EQ(vector.size(), 3U);
  // Allow iterator to be converted to const_iterator, but not the other way
  apex::BoundedVector<int, 16U>::iterator it = vector.begin() + 1U;
  apex::BoundedVector<int, 16U>::const_iterator cit = it;
  // apex::BoundedVector<int, 16U>::const_iterator it2 = cit;  // Should not compile
  ASSERT_EQ(*it, 2);
  ASSERT_EQ(*cit, 2);
  ASSERT_EQ(*it, *cit);
}

TEST(bounded_vector, iterator_conversion_stack_storage)
{
  apex::StackVector<int, 16U> vector;
  vector.push_back(1);
  vector.push_back(2);
  vector.push_back(3);

  ASSERT_EQ(vector.size(), 3U);
  // Allow iterator to be converted to const_iterator, but not the other way
  apex::StackVector<int, 16U>::iterator it = vector.begin() + 1U;
  apex::StackVector<int, 16U>::const_iterator cit = it;
  // apex::BoundedVector<int, 16U>::const_iterator it2 = cit;  // Should not compile
  ASSERT_EQ(*it, 2);
  ASSERT_EQ(*cit, 2);
  ASSERT_EQ(*it, *cit);
}

TEST(bounded_vector, reverse_iteration)
{
  // test static capacity() function
  using IntegerVector = apex::BoundedVector<int, 1024>;
  EXPECT_EQ(IntegerVector::capacity(), 1024);

  // test copy assignment from std::vector
  const int num = 10;
  IntegerVector int_bounded_vec;
  for (int i = 0; i < num; i++) {
    int_bounded_vec.push_back(i);
  }
  int i = num - 1;
  for (auto iter = int_bounded_vec.rbegin(); iter != int_bounded_vec.rend(); ++iter, --i) {
    EXPECT_EQ(*iter, i);
  }
  i = num - 1;
  for (auto iter = int_bounded_vec.crbegin(); iter != int_bounded_vec.crend(); ++iter, --i) {
    EXPECT_EQ(*iter, i);
  }
}

TEST(bounded_vector, resize_test)
{
  const auto vec_capacity = 1024;
  apex::BoundedVector<int, vec_capacity> int_bounded_vec;
  EXPECT_EQ(int_bounded_vec.size(), 0);

  using sizeT = decltype(int_bounded_vec.size());
  sizeT size_S1 = 512;
  int_bounded_vec.resize(size_S1);
  EXPECT_EQ(int_bounded_vec.size(), size_S1);

  sizeT size_S2 = 256;
  int_bounded_vec.resize(size_S2);
  EXPECT_EQ(int_bounded_vec.size(), size_S2);

  sizeT size_S3 = size_S2;
  int_bounded_vec.resize(size_S3);
  EXPECT_EQ(int_bounded_vec.size(), size_S3);

  sizeT size_S4 = vec_capacity + 1;
  EXPECT_THROW(int_bounded_vec.resize(size_S4), apex::invalid_argument);
}

TEST(bounded_vector, capacity_test)
{
#ifdef APEX_LIMITED_MEM_ALLOC
  // Some targets can't allocate max(uint32_t) and may cause std::bad_alloc exception
  const uint16_t max = std::numeric_limits<std::uint16_t>::max();
#else
  const uint32_t max = std::numeric_limits<std::uint32_t>::max();
#endif

  // testing creating integer bounded vector with different capacity values
  {
    apex::BoundedVector<int8_t, max> int_bounded_vec_max;  // MAX
    EXPECT_EQ(int_bounded_vec_max.capacity(), max);
  }
  {
    apex::BoundedVector<int8_t, max - 1> int_bounded_vec_max_1;  // MAX - 1
    EXPECT_EQ(int_bounded_vec_max_1.capacity(), max - 1);
  }
  {
    apex::BoundedVector<int8_t, max / 2> int_bounded_vec_max_2;  // MID
    EXPECT_EQ(int_bounded_vec_max_2.capacity(), max / 2);
  }
  apex::BoundedVector<int8_t, 1> int_bounded_vec_min_1;  // MIN + 1
  EXPECT_EQ(int_bounded_vec_min_1.capacity(), 1);
  apex::BoundedVector<int8_t,
                      std::numeric_limits<std::uint32_t>::min()>
    int_bounded_vec_min;  // MIN
  EXPECT_EQ(int_bounded_vec_min.capacity(), std::numeric_limits<std::uint32_t>::min());
}

TEST(bounded_vector, assignment)
{
  apex::BoundedVector<int, 50> points1;
  apex::BoundedVector<int, 50> points2;
  std::vector<int> points3(50);

  std::iota(points3.begin(), points3.end(), 100);

  points1 = points3;
  points2 = points3;

  ASSERT_EQ(points1.size(), points3.size());
  ASSERT_EQ(points2.size(), points3.size());
  ASSERT_EQ(points1.front(), points3.front());
  ASSERT_EQ(points1.back(), points3.back());
  ASSERT_EQ(points2.front(), points3.front());
  ASSERT_EQ(points2.back(), points3.back());
}

TEST(bounded_vector, assignment_stack_storage)
{
  apex::StackVector<int, 50> points1;
  apex::StackVector<int, 50> points2;
  std::vector<int> points3(50);

  std::iota(points3.begin(), points3.end(), 100);

  points1 = points3;
  points2 = points3;

  ASSERT_EQ(points1.size(), points3.size());
  ASSERT_EQ(points2.size(), points3.size());
  ASSERT_EQ(points1.front(), points3.front());
  ASSERT_EQ(points1.back(), points3.back());
  ASSERT_EQ(points2.front(), points3.front());
  ASSERT_EQ(points2.back(), points3.back());
}

TEST(bounded_vector, assign)
{
  std::vector<int> fill(100);
  std::iota(fill.begin(), fill.end(), 1);
  apex::BoundedVector<int, 50> test;
  test.assign(fill.begin(), fill.begin() + 50);
  for (auto i = 0U; i < test.capacity(); ++i) {
    ASSERT_EQ(fill[i], test[i]);
  }

  ASSERT_NO_THROW(test.assign(fill.begin(), fill.begin() + 50));
  ASSERT_THROW(test.assign(fill.begin(), fill.begin() + 51), apex::invalid_argument);
}

TEST(bounded_vector, erase)
{
  std::vector<int> fill(10U);
  std::iota(fill.begin(), fill.end(), 1);
  apex::BoundedVector<int, 10U> test;
  test = fill;
  ASSERT_THROW(test.erase(test.begin() - 1U), apex::out_of_range);
  ASSERT_THROW(test.erase(test.end()), apex::out_of_range);
  ASSERT_THROW(test.erase(test.end() + 1U), apex::out_of_range);
  ASSERT_NO_THROW(test.erase(test.begin()));
  ASSERT_EQ(test.size(), 9U);
  EXPECT_EQ(test[0], 2);
  EXPECT_EQ(test[1], 3);
  EXPECT_EQ(test[2], 4);
  EXPECT_EQ(test[3], 5);
  EXPECT_EQ(test[4], 6);
  EXPECT_EQ(test[5], 7);
  EXPECT_EQ(test[6], 8);
  EXPECT_EQ(test[7], 9);
  EXPECT_EQ(test[8], 10);

  ASSERT_THROW(test.erase(test.begin(), test.end() + 1U), apex::out_of_range);
  ASSERT_THROW(test.erase(test.begin() - 1U, test.end()), apex::out_of_range);
  ASSERT_THROW(test.erase(test.begin(), test.begin()), apex::invalid_argument);
  ASSERT_THROW(test.erase(test.end(), test.end()), apex::invalid_argument);
  ASSERT_THROW(test.erase(test.end(), test.begin()), apex::invalid_argument);

  ASSERT_NO_THROW(test.erase(test.begin() + 2U, test.begin() + 5U));
  ASSERT_EQ(test.size(), 6U);
  EXPECT_EQ(test[0], 2);
  EXPECT_EQ(test[1], 3);
  EXPECT_EQ(test[2], 7);
  EXPECT_EQ(test[3], 8);
  EXPECT_EQ(test[4], 9);
  EXPECT_EQ(test[5], 10);

  ASSERT_NO_THROW(test.erase(test.begin(), test.end()));
  ASSERT_EQ(test.size(), 0U);
}

TEST(bounded_vector, erase_stack_vector)
{
  std::vector<int> fill(10U);
  std::iota(fill.begin(), fill.end(), 1);
  apex::StackVector<int, 10U> test;
  test = fill;
  ASSERT_THROW(test.erase(test.begin() - 1U), apex::out_of_range);
  ASSERT_THROW(test.erase(test.end()), apex::out_of_range);
  ASSERT_THROW(test.erase(test.end() + 1U), apex::out_of_range);
  ASSERT_NO_THROW(test.erase(test.begin()));
  ASSERT_EQ(test.size(), 9U);
  EXPECT_EQ(test[0], 2);
  EXPECT_EQ(test[1], 3);
  EXPECT_EQ(test[2], 4);
  EXPECT_EQ(test[3], 5);
  EXPECT_EQ(test[4], 6);
  EXPECT_EQ(test[5], 7);
  EXPECT_EQ(test[6], 8);
  EXPECT_EQ(test[7], 9);
  EXPECT_EQ(test[8], 10);

  ASSERT_THROW(test.erase(test.begin(), test.end() + 1U), apex::out_of_range);
  ASSERT_THROW(test.erase(test.begin() - 1U, test.end()), apex::out_of_range);
  ASSERT_THROW(test.erase(test.begin(), test.begin()), apex::invalid_argument);
  ASSERT_THROW(test.erase(test.end(), test.end()), apex::invalid_argument);
  ASSERT_THROW(test.erase(test.end(), test.begin()), apex::invalid_argument);

  ASSERT_NO_THROW(test.erase(test.begin() + 2U, test.begin() + 5U));
  ASSERT_EQ(test.size(), 6U);
  EXPECT_EQ(test[0], 2);
  EXPECT_EQ(test[1], 3);
  EXPECT_EQ(test[2], 7);
  EXPECT_EQ(test[3], 8);
  EXPECT_EQ(test[4], 9);
  EXPECT_EQ(test[5], 10);

  ASSERT_NO_THROW(test.erase(test.begin(), test.end()));
  ASSERT_EQ(test.size(), 0U);
}

TEST(bounded_vector, test_operators)
{
  apex::BoundedVector<int, 5> obj_1;
  EXPECT_TRUE(obj_1.empty());
  obj_1.push_back(101);

// self-assignment is deliberate
#ifdef __clang__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wself-assign-overloaded"
#endif
  obj_1 = obj_1;
#ifdef __clang__
  #pragma GCC diagnostic pop
#endif


  EXPECT_EQ(obj_1.at(0), 101);

  EXPECT_FALSE(obj_1.empty());

  apex::BoundedVector<int, 5> temp1;
  apex::BoundedVector<int, 5> obj_2(std::move(temp1));

  apex::BoundedVector<int, 5> temp2;
  obj_2 = std::move(temp2);

  std::vector<int> vec2 = {10, 20, 30, 40, 50};
  obj_2 = vec2;
  obj_1 = obj_2;
  EXPECT_EQ(obj_1.at(0), 10);

  apex::swap(obj_2, obj_1);
  EXPECT_EQ(obj_2.at(0), 10);

  std::vector<int> vec1 = {1, 2, 3, 4, 5};
  obj_1 = vec1;
  obj_2 = vec2;
  obj_1.swap(obj_2);
  EXPECT_EQ(obj_1.at(0), 10);

  apex::BoundedVector<int, 5> obj_3(obj_1);

  auto it_1 = obj_3.rbegin();
  EXPECT_EQ(*it_1, 50);
  it_1++;

  it_1 = obj_3.rend();
  it_1--;
  EXPECT_EQ(*it_1, 10);

  EXPECT_EQ(obj_3.front(), 10);
  EXPECT_EQ(obj_3.back(), 50);

  std::vector<int> vec3 = {100, 200, 300, 400, 500, 600, 700};
  apex::BoundedVector<int, 7> obj_4;
  obj_4 = vec3;
  EXPECT_TRUE(obj_1 == obj_3);
  EXPECT_FALSE(obj_1 == obj_4);
  EXPECT_FALSE(obj_1 == obj_2);

  EXPECT_FALSE(obj_1 != obj_3);
  EXPECT_TRUE(obj_1 != obj_4);
  EXPECT_TRUE(obj_1 != obj_2);

  EXPECT_FALSE(obj_1 < obj_2);
  EXPECT_TRUE(obj_2 < obj_1);

  EXPECT_FALSE(obj_1 <= obj_2);
  EXPECT_TRUE(obj_2 <= obj_1);
  EXPECT_TRUE(obj_3 <= obj_1);

  EXPECT_TRUE(obj_1 > obj_2);
  EXPECT_FALSE(obj_2 > obj_1);

  EXPECT_TRUE(obj_1 >= obj_2);
  EXPECT_FALSE(obj_2 >= obj_1);
  EXPECT_TRUE(obj_3 >= obj_1);

  obj_1.reserve(2);
  EXPECT_THROW(obj_1.reserve(10), apex::logic_error);

  obj_2.assign_all(5U, 100);
  EXPECT_EQ(obj_2.at(0), 100);
  EXPECT_THROW(obj_2.at(10), apex::out_of_range);

  apex::BoundedVector<int, 100> nonconst_vec;
  nonconst_vec.push_back(11);
  nonconst_vec.push_back(12);

  const auto & const_ref = nonconst_vec;
  auto const_front = const_ref.front();
  EXPECT_EQ(const_front, 11);

  auto const_back = const_ref.back();
  EXPECT_EQ(const_back, 12);
  EXPECT_EQ(const_ref.at(0), 11);

  auto x1 = const_ref.data();
  EXPECT_EQ(x1[0], 11);
  EXPECT_EQ(x1[1], 12);

  const auto x2 = const_ref.rbegin();
  EXPECT_EQ(x2[0], 12);

  const auto x3 = const_ref.rend();
  EXPECT_EQ(x3[-1], 11);
}

TEST(bounded_vector, self_assignment_rvalue)
{
  apex::BoundedVector<int, 5> temp;
  temp.push_back(1);
  EXPECT_NO_THROW(std::move(temp) = std::move(temp));
  EXPECT_EQ(temp.capacity(), 5);
  EXPECT_EQ(temp.at(0), 1);
}

TEST(bounded_vector_error, invalid_allocator)
{
  struct test_type
  {
    test_type() = default;
    test_type(const test_type &) = default;
    test_type & operator=(const test_type &) = default;
    test_type(int32_t) {}  // NOLINT Converting ctor
    virtual ~test_type() = default;
  };

  using invalidBoundVector = apex::BoundedVector<test_type, 5, invalid_alloc_tmp<test_type>>;

  invalid_alloc_tmp<test_type>::MAX_ERR_COUNT = 1;
  invalidBoundVector invalidVector;
  invalidBoundVector invalidVector1;
  EXPECT_NO_THROW(invalidVector.emplace_back(1));
  EXPECT_THROW(invalidVector.emplace_back(1), std::runtime_error);

  invalid_alloc_tmp<test_type>::MAX_ERR_COUNT = 1;
  EXPECT_THROW(invalidVector.resize(5), std::runtime_error);

  invalid_alloc_tmp<test_type>::MAX_ERR_COUNT = 0;
  EXPECT_THROW(invalidVector1 = invalidVector, std::runtime_error);
  EXPECT_THROW(invalidBoundVector invalidVector2(invalidVector), std::runtime_error);

  std::vector<test_type> int_vec(5);
  EXPECT_THROW(invalidVector1 = int_vec, std::runtime_error);
}

TEST(BoundedVector, throw_on_resize)
{
  apex::BoundedVector<TestType, 10> bv;
  EXPECT_NO_THROW(bv.resize(5));
  EXPECT_EQ(bv.size(), 5);

  for (size_t i = 0; i < bv.size(); i++) {
    EXPECT_EQ(bv[i].m_count, i);
  }

  EXPECT_THROW(bv.resize(10), apex::runtime_error);
  EXPECT_EQ(bv.size(), 5);
  for (size_t i = 0; i < bv.size(); i++) {
    EXPECT_EQ(bv[i].m_count, i);
  }
}

TEST(BoundedVector, comparison_operators)
{
  apex::BoundedVector<int, 1> bv1, bv2;
  bv1.push_back(1);
  bv2.push_back(2);

  EXPECT_NE(bv1, bv2);  // 1 != 2
  EXPECT_LT(bv1, bv2);  // 1 < 2
  EXPECT_LE(bv1, bv2);  // 1 <= 2

  EXPECT_GT(bv2, bv1);  // 2 > 1
  EXPECT_GE(bv2, bv1);  // 2 >= 1

  bv1[0] = 2;
  EXPECT_EQ(bv1, bv2);  // 2 == 2
  EXPECT_GE(bv1, bv2);  // 2 >= 2
  EXPECT_LE(bv1, bv1);  // 2 <= 2
}

TEST(bounded_vector_error, implicit_conversion)
{
  apex::BoundedVector<int, 2> bv1;
  bv1.push_back(1);
  bv1.push_back(2);
  std::vector<int> vector{bv1};
  EXPECT_EQ(vector[0], 1);
  EXPECT_EQ(vector[1], 2);

  std::vector<int> vector2 = apex::BoundedVector<int, 2>();
  EXPECT_EQ(vector2.size(), 0U);
}

TEST(bounded_vector, assign_all_true)
{
  apex::BoundedVector<int, 5> temp1;
  apex::BoundedVector<int, 5> obj_2(std::move(temp1));

  apex::BoundedVector<int, 5> temp2;
  obj_2 = std::move(temp2);

  std::vector<int> vec2 = {10, 20, 30, 40, 50};
  obj_2 = vec2;

  obj_2.assign_all(3U, 100);
  EXPECT_EQ(obj_2.at(0), 100);
  EXPECT_THROW(obj_2.at(10), apex::out_of_range);

  apex::BoundedVector<int, 100> nonconst_vec;
  nonconst_vec.push_back(11);
  nonconst_vec.push_back(12);

  auto & ref = nonconst_vec;
  auto x1 = ref.data();
  EXPECT_EQ(x1[0], 11);
  EXPECT_EQ(x1[1], 12);
}

TEST(bounded_vector, test_operator)
{
  apex::BoundedVector<int, 5> obj1;
  apex::BoundedVector<int, 5> obj2;
  apex::BoundedVector<int, 5>::iterator obj;
  std::vector<int> vec1 = {10, 20, 30, 40, 50};
  obj1 = vec1;

  auto it_m1 = obj1.begin();
  it_m1++;
  EXPECT_EQ(*it_m1, 20);
  it_m1--;
  EXPECT_EQ(*it_m1, 10);

  std::vector<int> vec2 = {1, 2, 3, 4, 5};
  obj2 = vec2;

  auto it_m2 = obj2.begin();
  it_m2 += 1;
  EXPECT_EQ(*it_m2, 2);

  EXPECT_EQ(it_m2[0], 2);

  it_m2 -= 1;
  EXPECT_EQ(*it_m2, 1);

  const auto it_m3 = obj2.begin();
  const auto it_m4 = obj1.begin();
  EXPECT_TRUE(*it_m3 < *it_m4);
  EXPECT_FALSE(*it_m4 < *it_m3);

  EXPECT_TRUE(*it_m3 <= *it_m4);
  EXPECT_FALSE(*it_m4 <= *it_m3);

  EXPECT_TRUE(*it_m4 > *it_m3);
  EXPECT_FALSE(*it_m3 > *it_m4);

  EXPECT_TRUE(*it_m4 >= *it_m3);
  EXPECT_FALSE(*it_m3 >= *it_m4);
}

TEST(bounded_vector, test_initializer_list_construction)
{
  apex::BoundedVector<int, 5> apex_ints = {1, 2, 3, 4, 5};
  apex::BoundedVector<int, 5> apex_ints_2{1, 2, 3, 4, 5};
  std::vector<int> std_ints = {1, 2, 3, 4, 5};
  EXPECT_EQ(apex_ints.size(), 5);
  for (size_t x = 0; x < std_ints.size(); ++x) {
    EXPECT_EQ(apex_ints[x], std_ints[x]);
    EXPECT_EQ(apex_ints_2[x], std_ints[x]);
  }
}

TEST(bounded_vector, test_initializer_list_construction_stack_storage)
{
  apex::StackVector<int, 5> apex_ints = {1, 2, 3, 4, 5};
  apex::StackVector<int, 5> apex_ints_2{1, 2, 3, 4, 5};
  std::vector<int> std_ints = {1, 2, 3, 4, 5};
  EXPECT_EQ(apex_ints.size(), 5);
  for (size_t x = 0; x < std_ints.size(); ++x) {
    EXPECT_EQ(apex_ints[x], std_ints[x]);
    EXPECT_EQ(apex_ints_2[x], std_ints[x]);
  }
}

TEST(bounded_vector, test_bounded_vector_of_strings)
{
  apex::BoundedVector<apex::string_strict256_t, 5> apex_strings = {
    "one", "two", "three", "four", "five"};
  auto other = apex_strings;
  EXPECT_EQ(apex_strings, other);
}

TEST(bounded_vector, test_bounded_vector_of_strings_stack_storage)
{
  apex::StackVector<apex::string_strict256_t, 5> apex_strings = {
    "one", "two", "three", "four", "five"};
  auto other = apex_strings;
  EXPECT_EQ(apex_strings, other);
}

struct Msg
{
  explicit Msg(int initialization = 2)
  {
    if (2 == initialization) {
      foo = false;
      bar = 0;
    }
  }

  bool foo;
  int bar;
};

TEST(bounded_vector, test_flat_bounded_vector_of_structs)
{
  apex::StackVector<Msg, 5> test;
  for (const auto & t : test) {
    EXPECT_FALSE(t.foo);
    EXPECT_EQ(t.bar, 0);
  }
}
