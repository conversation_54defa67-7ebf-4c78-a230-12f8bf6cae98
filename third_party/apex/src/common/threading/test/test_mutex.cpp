// Copyright 2017-2018 Apex.AI, Inc.
// All rights reserved.
#include <gtest/gtest.h>

#include <cpputils/safe_cast.hpp>
#include <threading/mutex.hpp>
#include <threading/shared_mutex.hpp>
#include <threading/thread.hpp>
#include <threading/unlock_guard.hpp>

#include <chrono>
#include <condition_variable>
#include <utility>
#include <mutex>
#include <thread>
#include <vector>
#include <memory>
#include <algorithm>
#include <atomic>
#include <shared_mutex> //NOLINT

using apex::threading::mutex;
using apex::threading::timed_mutex;
using apex::threading::recursive_mutex;
using apex::threading::recursive_timed_mutex;
using apex::threading::shared_mutex;
using apex::threading::shared_timed_mutex;
using apex::threading::mutex_attributes;
using apex::threading::pmutex_attributes;
using apex::threading::mutex_protocol;

using apex::threading::thread;
using apex::threading::thread_attributes;
using apex::threading::base_thread;
using apex::threading::thread_error;

using apex::threading::details::call_posix_api;

using apex::cast::safe_cast;

using namespace std::chrono_literals;
using std::chrono::steady_clock;
using std::chrono::system_clock;

namespace
{

template<class Mutex>
class tester_thread
{
private:
  enum class states
  {
    done,
    failed,
    stop,
    lock,
    try_lock,
    unlock,
    lock_shared,
    try_lock_shared,
    unlock_shared
  };

public:
  explicit tester_thread(Mutex & mtx)
  : m_mutex{mtx}
  {
    m_thread.issue();
  }

  ~tester_thread()
  {
    set_state(states::stop);
  }

  bool lock()
  {
    return set_state(states::lock);
  }

  bool lock_shared()
  {
    return set_state(states::lock_shared);
  }

  bool unlock()
  {
    return set_state(states::unlock);
  }

  bool try_lock()
  {
    return set_state(states::try_lock);
  }

  bool try_lock_shared()
  {
    return set_state(states::try_lock_shared);
  }

  bool unlock_shared()
  {
    return set_state(states::unlock_shared);
  }

private:
  void run()
  {
    auto stop = false;
    while (!stop) {
      std::unique_lock<std::mutex> lock(m_state_mutex);
      m_cv_state_changed.wait(lock, [&] {return m_state != states::done;});
      switch (m_state) {
        case states::lock:
          m_mutex.lock();
          m_state = states::done;
          break;
        case states::lock_shared:
          m_state = lock_shared_internal();
          break;
        case states::try_lock:
          m_state = m_mutex.try_lock() ? states::done : states::failed;
          break;
        case states::try_lock_shared:
          m_state = try_lock_shared_internal();
          break;
        case states::unlock:
          m_mutex.unlock();
          m_state = states::done;
          break;
        case states::unlock_shared:
          m_state = unlock_shared_internal();
          break;
        case states::stop:
          m_state = states::done;
          stop = true;
          break;
        default:
          m_state = states::failed;
          break;
      }

      lock.unlock();
      m_cv_state_changed.notify_one();
    }
  }

  template<class M = Mutex,
    std::enable_if_t<std::is_base_of<shared_mutex, M>::value> * = nullptr>
  states try_lock_shared_internal()
  {
    return m_mutex.try_lock_shared() ? states::done : states::failed;
  }

  template<class M = Mutex,
    std::enable_if_t<!std::is_base_of<shared_mutex, M>::value> * = nullptr>
  states try_lock_shared_internal()
  {
    return states::failed;
  }

  template<class M = Mutex,
    std::enable_if_t<std::is_base_of<shared_mutex, M>::value> * = nullptr>
  states lock_shared_internal()
  {
    m_mutex.lock_shared();
    return states::done;
  }

  template<class M = Mutex,
    std::enable_if_t<!std::is_base_of<shared_mutex, M>::value> * = nullptr>
  states lock_shared_internal()
  {
    return states::failed;
  }

  template<class M = Mutex,
    std::enable_if_t<std::is_base_of<shared_mutex, M>::value> * = nullptr>
  states unlock_shared_internal()
  {
    m_mutex.unlock_shared();
    return states::done;
  }

  template<class M = Mutex,
    std::enable_if_t<!std::is_base_of<shared_mutex, M>::value> * = nullptr>
  states unlock_shared_internal()
  {
    return states::failed;
  }

  bool set_state(states state)
  {
    {
      std::lock_guard<std::mutex> lock(m_state_mutex);
      m_state = state;
    }

    m_cv_state_changed.notify_one();

    auto retval = false;
    {
      std::unique_lock<std::mutex> lock(m_state_mutex);
      m_cv_state_changed.wait(lock,
        [&] {
          return m_state == states::done || m_state == states::failed;
        }
      );

      retval = m_state == states::done;
      m_state = states::done;
    }

    return retval;
  }

  Mutex & m_mutex;
  std::mutex m_state_mutex;
  std::condition_variable m_cv_state_changed;
  states m_state {states::done};
  thread m_thread{[this] {run();}};
};

template<class Mutex>
inline void run_basic_mutex_tests(Mutex & m)
{
  tester_thread<Mutex> tester(m);

  tester.lock();
  ASSERT_FALSE(m.try_lock());
  tester.unlock();
  ASSERT_TRUE(m.try_lock());
  ASSERT_NO_THROW(m.unlock());
  ASSERT_NO_THROW(m.native_handle());
}

// NOTE: the test measures time and therefore can be influenced by cpu loads
template<class Mutex>
inline void run_timed_mutex_tests(Mutex & m)
{
  tester_thread<Mutex> tester(m);

  {
    tester.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_for(100ms));
    ASSERT_GE(system_clock::now() - start, 100ms);

    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_for(100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }

  {
    tester.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_until(start + 100ms));
    ASSERT_GE(system_clock::now() - start, 100ms);
    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_until(start + 100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }

  {
    tester.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_until(start - 100ms));
    ASSERT_LE(system_clock::now() - start, 100ms);
    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_until(start + 100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }
}

// NOTE: the test measures time and therefore can be influenced by cpu loads
template<class Mutex>
inline void run_timed_mutex_tests_monotonic(Mutex & m)
{
  tester_thread<Mutex> tester(m);

  {
// TODO(lander.usategui): On QNX710/QEMU there exists timing issues due to
// usage of steady_clock. When #9137 is fixed to use steady_clock, remove this block
#if defined(QNX710) && (defined(_M_AMD64) || defined(__amd64__))
    tester.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_for(100ms));
    ASSERT_GE(system_clock::now() - start, 100ms);

    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_for(100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }

  {
    tester.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_until(start + 100ms));
    ASSERT_GE(system_clock::now() - start, 100ms);
    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_until(start + 100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }

  {
    tester.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_until(start - 100ms));
    ASSERT_LE(system_clock::now() - start, 100ms);
    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_until(start + 100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }
#else
    tester.lock();
    auto start = steady_clock::now();
    ASSERT_FALSE(m.try_lock_for(100ms));
    ASSERT_GE(steady_clock::now() - start, 100ms);

    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = steady_clock::now();
    ASSERT_TRUE(m.try_lock_for(100ms));
    auto elapsed = steady_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }

  {
    tester.lock();
    auto start = steady_clock::now();
    ASSERT_FALSE(m.try_lock_until(start + 100ms));
    ASSERT_GE(steady_clock::now() - start, 100ms);
    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = steady_clock::now();
    ASSERT_TRUE(m.try_lock_until(start + 100ms));
    auto elapsed = steady_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }

  {
    tester.lock();
    auto start = steady_clock::now();
    ASSERT_FALSE(m.try_lock_until(start - 100ms));
    ASSERT_LE(steady_clock::now() - start, 100ms);
    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        tester.unlock();
      }
    };

    t.issue();
    start = steady_clock::now();
    ASSERT_TRUE(m.try_lock_until(start + 100ms));
    auto elapsed = steady_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }
#endif  // QNX710 x86_64
}

template<class Mutex>
inline void run_recursive_mutex_tests(Mutex & m)
{
  tester_thread<recursive_mutex> tester{m};

  ASSERT_NO_THROW(m.lock());
  ASSERT_FALSE(tester.try_lock());
  ASSERT_NO_THROW(m.lock());
  ASSERT_FALSE(tester.try_lock());
  ASSERT_NO_THROW(m.unlock());
  ASSERT_FALSE(tester.try_lock());
  ASSERT_NO_THROW(m.unlock());
  ASSERT_TRUE(tester.try_lock());
  tester.unlock();
}

template<class Mutex>
inline void run_shared_mutex_tests(Mutex & m)
{
  tester_thread<Mutex> t1{m};
  tester_thread<Mutex> t2{m};

  m.lock();
  ASSERT_FALSE(t1.try_lock_shared());
  m.unlock();
  ASSERT_TRUE(t1.lock_shared());
  ASSERT_TRUE(t2.lock_shared());
  ASSERT_FALSE(m.try_lock());
  ASSERT_TRUE(t1.unlock_shared());
  ASSERT_TRUE(t2.unlock_shared());

  ASSERT_TRUE(t1.try_lock_shared());
  ASSERT_FALSE(t2.try_lock());
  ASSERT_TRUE(t2.try_lock_shared());
  ASSERT_FALSE(m.try_lock());
  ASSERT_TRUE(m.try_lock_shared());
  ASSERT_NO_THROW(m.unlock_shared());
  ASSERT_FALSE(m.try_lock());
  ASSERT_TRUE(t2.unlock_shared());
  ASSERT_FALSE(m.try_lock());
  ASSERT_TRUE(t1.unlock_shared());
  ASSERT_TRUE(m.try_lock());
  ASSERT_NO_THROW(m.unlock());
}

// NOTE: the test measures time and therefore can be influenced by cpu loads
template<class Mutex>
inline void run_shared_timed_mutex_tests(Mutex & m)
{
  tester_thread<Mutex> t1{m};

  {
    t1.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_shared_for(100ms));
    ASSERT_GE(system_clock::now() - start, 100ms);

    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        t1.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_shared_for(100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }

  {
    t1.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_shared_until(start + 100ms));
    ASSERT_GE(system_clock::now() - start, 100ms);

    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        t1.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_shared_until(start + 100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }

  {
    t1.lock();
    auto start = system_clock::now();
    ASSERT_FALSE(m.try_lock_shared_until(start - 100ms));
    ASSERT_LE(system_clock::now() - start, 100ms);

    thread t{[&] {
        std::this_thread::sleep_for(1ms);
        t1.unlock();
      }
    };

    t.issue();
    start = system_clock::now();
    ASSERT_TRUE(m.try_lock_shared_until(start + 100ms));
    auto elapsed = system_clock::now() - start;
    ASSERT_LE(elapsed, 100ms);
    ASSERT_GE(elapsed, 1ms);
    ASSERT_NO_THROW(m.unlock());
  }
}

template<class Mutex>
inline void run_movability_test()
{
  Mutex m;
  ASSERT_NO_THROW(m.lock());
  ASSERT_TRUE(m.valid());
  Mutex moved = std::move(m);
  ASSERT_FALSE(m.valid());
  ASSERT_TRUE(moved.valid());
  ASSERT_NO_THROW(moved.unlock());
  Mutex another;
  another = std::move(moved);
  ASSERT_FALSE(moved.valid());
  ASSERT_TRUE(another.valid());
  ASSERT_NO_THROW(another.lock());
  ASSERT_NO_THROW(another.unlock());
}

template<class Mutex>
void check_moved_throws_basic()
{
  Mutex m;
  auto m1 = std::move(m);
  ASSERT_THROW(m.lock(), thread_error);
  ASSERT_THROW(m.unlock(), thread_error);
  ASSERT_THROW(m.native_handle(), thread_error);
  ASSERT_THROW(m.try_lock(), thread_error);
}

template<class Mutex>
void check_moved_throws_timed()
{
  Mutex m;
  auto m1 = std::move(m);
  ASSERT_THROW(m.try_lock_until(std::chrono::system_clock::now()), thread_error);
  ASSERT_THROW(m.try_lock_for(1ms), thread_error);
}

template<class Mutex>
void check_moved_throws_shared()
{
  Mutex m;
  auto m1 = std::move(m);
  ASSERT_THROW(m.lock_shared(), thread_error);
  ASSERT_THROW(m.unlock_shared(), thread_error);
  ASSERT_THROW(m.try_lock_shared(), thread_error);
}

template<class Mutex>
void check_moved_throws_shared_timed()
{
  Mutex m;
  auto m1 = std::move(m);
  ASSERT_THROW(m.try_lock_shared_until(std::chrono::system_clock::now()), thread_error);
  ASSERT_THROW(m.try_lock_shared_for(1ms), thread_error);
}

template<class Mutex>
inline void compile_std_not_shared_constructs()
{
  Mutex m;
  {
    std::lock_guard<Mutex> lock{m};
  }
  {
    std::condition_variable_any cv;
    std::unique_lock<Mutex> lock{m};
    cv.wait(lock, [] {return true;});
  }

  {
    Mutex m1, m2, m3;
    std::lock(m1, m2, m3);
  }

  {
    Mutex m1, m2, m3;
    ASSERT_EQ(std::try_lock(m1, m2, m3), -1);
  }
}

template<class Mutex>
inline void compile_std_shared_constructs()
{
  Mutex m;
  {
    std::shared_lock<Mutex> lock{m};
  }
}

template<class Mutex>
inline void compile_with_apex_thread()
{
  using rt_thread = base_thread<Mutex>;

  auto x = 0;
  rt_thread t{[&] {++x;}};
  t.issue();
  ASSERT_NO_THROW(t.join());
  ASSERT_EQ(x, 1);
  ASSERT_NO_THROW(t.native_handle());
}

template<class Mutex>
inline void compile_with_apex_thread_forward_mutex_attr()
{
  using rt_thread = base_thread<Mutex>;

  auto x = 0;
  rt_thread t{[&] {++x;}, thread_attributes::build(), mutex_attributes::build()};
  t.issue();
  ASSERT_NO_THROW(t.join());
  ASSERT_EQ(x, 1);
}
}  // namespace

/// @test{
/// "req" : ["SWRQ_THREADING_43", "SWRQ_THREADING_44", "SWRQ_THREADING_411"]
/// }
TEST(test_mutex, mutex) {
  mutex m;
  run_basic_mutex_tests(m);
  run_movability_test<mutex>();
  check_moved_throws_basic<mutex>();
  compile_std_not_shared_constructs<mutex>();
  compile_with_apex_thread<mutex>();
  compile_with_apex_thread_forward_mutex_attr<mutex>();
}

/// @test{
/// "req" : ["SWRQ_THREADING_43", "SWRQ_THREADING_44", "SWRQ_THREADING_413"]
/// }
TEST(test_mutex, timed_mutex) {
  timed_mutex m;
  run_basic_mutex_tests(m);
#ifdef APEX_QNX
  run_timed_mutex_tests_monotonic(m);
#else
  run_timed_mutex_tests(m);
#endif
  run_movability_test<timed_mutex>();
  check_moved_throws_basic<timed_mutex>();
  check_moved_throws_timed<timed_mutex>();
  compile_std_not_shared_constructs<timed_mutex>();
  compile_with_apex_thread<timed_mutex>();
  compile_with_apex_thread_forward_mutex_attr<mutex>();
}

/// @test{
/// "req" : ["SWRQ_THREADING_43", "SWRQ_THREADING_44", "SWRQ_THREADING_412"]
/// }
TEST(test_mutex, recursive_mutex) {
  recursive_mutex m;
  run_basic_mutex_tests(m);
  run_recursive_mutex_tests(m);
  run_movability_test<recursive_mutex>();
  check_moved_throws_basic<recursive_mutex>();
  compile_std_not_shared_constructs<recursive_mutex>();
  compile_with_apex_thread<recursive_mutex>();
  compile_with_apex_thread_forward_mutex_attr<mutex>();
}

/// @test{
/// "req" : ["SWRQ_THREADING_43", "SWRQ_THREADING_44", "SWRQ_THREADING_414"]
/// }
TEST(test_mutex, recursive_timed_mutex) {
  recursive_timed_mutex m;
  run_basic_mutex_tests(m);
#ifdef APEX_QNX
  run_timed_mutex_tests_monotonic(m);
#else
  run_timed_mutex_tests(m);
#endif
  run_recursive_mutex_tests(m);
  run_movability_test<recursive_timed_mutex>();
  check_moved_throws_basic<recursive_timed_mutex>();
  check_moved_throws_timed<recursive_timed_mutex>();
  compile_std_not_shared_constructs<recursive_timed_mutex>();
  compile_with_apex_thread<recursive_timed_mutex>();
  compile_with_apex_thread_forward_mutex_attr<mutex>();
}

/// @test{
/// "req" : ["SWRQ_THREADING_43", "SWRQ_THREADING_44", "SWRQ_THREADING_415"]
/// }
TEST(test_mutex, shared_mutex) {
  shared_mutex m;
  run_basic_mutex_tests(m);
  run_shared_mutex_tests(m);
  run_movability_test<shared_mutex>();
  check_moved_throws_basic<shared_mutex>();
  check_moved_throws_shared<shared_mutex>();
  compile_std_not_shared_constructs<shared_mutex>();
  compile_std_shared_constructs<shared_mutex>();
  compile_with_apex_thread<shared_mutex>();
}

/// @test{
/// "req" : ["SWRQ_THREADING_43", "SWRQ_THREADING_44", "SWRQ_THREADING_416"]
/// }
TEST(test_mutex, shared_timed_mutex) {
  shared_timed_mutex m;
  run_basic_mutex_tests(m);
  run_timed_mutex_tests(m);
  run_shared_mutex_tests(m);
  run_shared_timed_mutex_tests(m);
  run_movability_test<shared_timed_mutex>();
  check_moved_throws_basic<shared_timed_mutex>();
  check_moved_throws_timed<shared_timed_mutex>();
  check_moved_throws_shared<shared_timed_mutex>();
  check_moved_throws_shared_timed<shared_timed_mutex>();
  compile_std_not_shared_constructs<shared_timed_mutex>();
  compile_std_shared_constructs<shared_timed_mutex>();
  compile_with_apex_thread<shared_timed_mutex>();
}

/// @test{
/// "req" : ["SWRQ_THREADING_45", "SWRQ_THREADING_417"]
/// }
TEST(test_mutex, attributes_sanity) {
  auto attr = mutex_attributes::build();

  ASSERT_FALSE(attr.has_priority_ceiling());
  ASSERT_FALSE(attr.has_protocol());
}

/// @test{
/// "req" : ["SWRQ_THREADING_45", SWRQ_THREADING_411", SWRQ_THREADING_417"]
/// }
TEST(test_mutex, mutex_attributes) {
  {
    auto attr = mutex_attributes::build().protocol(mutex_protocol::inherit);
    ASSERT_TRUE(attr.has_protocol());

    pmutex_attributes pa{attr};

    int protocol = 0;
    ASSERT_NO_THROW(call_posix_api(pthread_mutexattr_getprotocol, pa, &protocol));
    ASSERT_EQ(protocol, PTHREAD_PRIO_INHERIT);
  }
  {
    auto attr = mutex_attributes::build().protocol(mutex_protocol::none);
    pmutex_attributes pa{attr};

    int protocol = 0;
    ASSERT_NO_THROW(call_posix_api(pthread_mutexattr_getprotocol, pa, &protocol));
    ASSERT_EQ(protocol, PTHREAD_PRIO_NONE);
  }

  {
    auto attr = mutex_attributes::build().priority_ceiling(42);
    ASSERT_TRUE(attr.has_priority_ceiling());

    pmutex_attributes pa{attr};

    int priority_ceiling = 0;
    ASSERT_NO_THROW(call_posix_api(pthread_mutexattr_getprioceiling, pa, &priority_ceiling));
    ASSERT_EQ(priority_ceiling, 42);
  }

  // Test conversion to  const pthread_mutexattr_t *()
  {
    auto attr = mutex_attributes::build().protocol(mutex_protocol::inherit);
    ASSERT_TRUE(attr.has_protocol());

    pmutex_attributes pa{attr};
    const pmutex_attributes & const_ref = pa;

    int protocol = 0;
    ASSERT_NO_THROW(call_posix_api(pthread_mutexattr_getprotocol, const_ref, &protocol));
    ASSERT_EQ(protocol, PTHREAD_PRIO_INHERIT);
  }
}

TEST(test_mutex, attributes_inequality) {
  auto attr1 = mutex_attributes::build()
    .protocol(mutex_protocol::inherit).priority_ceiling(20);

  auto attr2 = mutex_attributes::build()
    .protocol(mutex_protocol::none).priority_ceiling(25);
  ASSERT_TRUE(attr1 != attr2);
}

TEST(test_mutex, attributes_equality) {
  auto attr1 = mutex_attributes::build()
    .protocol(mutex_protocol::none).priority_ceiling(20);

  auto attr2 = mutex_attributes::build()
    .protocol(mutex_protocol::none).priority_ceiling(20);
  ASSERT_FALSE(attr1 != attr2);
}

TEST(test_mutex, mutex_attributes_protocol_inequality) {
  auto attr1 = mutex_attributes::build();
  attr1.protocol(mutex_protocol::inherit);
  auto attr2 = mutex_attributes::build();
  attr2.protocol(mutex_protocol::none);
  EXPECT_NE(attr1, attr2);
}

TEST(test_mutex, mutex_attributes_priority_ceiling) {
  auto attr1 = mutex_attributes::build();
  attr1.priority_ceiling(40);
  auto attr2 = mutex_attributes::build();
  attr2.priority_ceiling(42);
  auto attr3 = mutex_attributes::build();
  attr3.priority_ceiling(42);
  EXPECT_NE(attr1, attr2);
  EXPECT_EQ(attr2, attr3);
}

// TODO(lander.usategui): Enable this after 16642 is fixed
#ifndef QNX
TEST(test_unlock_guard, unlock_guard) {
  auto res = false;
  std::mutex m;
  m.lock();
  {
    apex::threading::unlock_guard<std::mutex> l{m};
    std::thread t{[&] {res = m.try_lock();}};
    t.join();
    ASSERT_TRUE(res);
    m.unlock();
  }

  std::thread t{[&] {res = m.try_lock();}};
  t.join();
  ASSERT_FALSE(res);
  m.unlock();
}
#endif  // QNX
