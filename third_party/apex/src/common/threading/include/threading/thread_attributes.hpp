/// \copyright Copyright 2017-2019 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file declares apex::threading thread_attributes

#ifndef THREADING__THREAD_ATTRIBUTES_HPP_
#define THREADING__THREAD_ATTRIBUTES_HPP_

#include <cpputils/optional.hpp>

#include <cstdint>
#include <unordered_set>
#include <utility>

/// \namespace apex
namespace apex
{
/// \namespace apex::threading
namespace threading
{

/// \class scheduler
/// Scheduler types
/// \cert
enum class scheduler : std::int32_t
{
  normal,
  other = normal,
  fifo,
  round_robin
};

/// \class destructor_action
/// What to do on thread destruction
/// \cert
enum class destructor_action : std::int32_t
{
  join, throw_if_joinable
};

/// \class thread_attributes
/// Abstract thread attributes defined by apex::threading API
/// \cert
class thread_attributes
{
public:
  using affinity_mask_type = std::unordered_set<std::size_t>;

  /// \brief A builder for thread attributes
  /// \return A new instance of thread_attributes
  /// \cert
  /// \deterministic
  static thread_attributes build()
  {
    return {};
  }

  /// \brief The setter for thread scheduling policy
  /// \param[in] scheduling_policy a scheduling policy
  /// \return The self instance
  /// \cert
  /// \deterministic
  thread_attributes & scheduling_policy(scheduler scheduling_policy) noexcept
  {
    m_scheduling_policy = scheduling_policy;
    return *this;
  }

  /// \brief The getter for thread scheduling policy
  /// \return The thread's scheduling policy
  /// \cert
  /// \deterministic
  scheduler scheduling_policy() const
  {
    return *m_scheduling_policy;
  }

  /// \brief Tests whether the attributes have a scheduling policy
  /// \return Whether a scheduling policy is set
  /// \cert
  /// \deterministic
  bool has_scheduling_policy() const noexcept
  {
    return m_scheduling_policy != apex::nullopt;
  }

  /// \brief The setter for thread's priority
  /// \param[in] priority thread's priority
  /// \return The self instance
  /// \cert
  /// \deterministic
  thread_attributes & priority(std::int32_t priority) noexcept
  {
    m_priority = priority;
    return *this;
  }

  /// \brief The getter for thread's priority
  /// \return Thread's priority
  /// \cert
  /// \deterministic
  std::int32_t priority() const
  {
    return *m_priority;
  }

  /// \brief Tests whether the attributes have a priority
  /// \return Whether a priority is set
  /// \cert
  /// \deterministic
  bool has_priority() const noexcept
  {
    return m_priority != apex::nullopt;
  }

  /// \brief The setter for thread's stack size
  /// \param[in] stack_size thread's stack size
  /// \return The self instance
  /// \cert
  /// \deterministic
  thread_attributes & stack_size(size_t stack_size) noexcept
  {
    m_stack_size = stack_size;
    return *this;
  }

  /// \brief The getter for thread's stack size
  /// \return Thread's stack size
  /// \cert
  /// \deterministic
  size_t stack_size() const
  {
    return *m_stack_size;
  }

  /// \brief Tests whether the attributes have a stack size
  /// \return Whether a stack size is set
  /// \cert
  /// \deterministic
  bool has_stack_size() const noexcept
  {
    return m_stack_size != apex::nullopt;
  }

  /// \brief The setter for thread's stack address
  /// \param[in] stack_address thread's stack address
  /// \return The self instance
  /// \cert
  /// \deterministic
  thread_attributes & stack_address(void * stack_address) noexcept
  {
    m_stack_address = stack_address;
    return *this;
  }

  /// \brief The getter for thread's stack address
  /// \return Thread's stack address
  /// \cert
  /// \deterministic
  void * stack_address() const
  {
    return *m_stack_address;
  }

  /// \brief Tests whether the attributes have a stack address
  /// \return Whether a stack address is set
  /// \cert
  /// \deterministic
  bool has_stack_address() const noexcept
  {
    return m_stack_address != apex::nullopt;
  }

  /// \brief The setter for thread's affinity mask
  /// \param[in] affinity_mask thread's affinity mask
  /// \return The self instance
  /// \cert
  /// \deterministic
  thread_attributes & affinity_mask(affinity_mask_type affinity_mask) noexcept
  {
    m_affinity_mask = std::move(affinity_mask);
    return *this;
  }

  /// \brief The getter for thread's affinity mask
  /// \return Thread's affinity mask
  /// \cert
  /// \deterministic
  const affinity_mask_type & affinity_mask() const
  {
    return *m_affinity_mask;
  }

  /// \brief Tests whether the attributes have an affinity_mask
  /// \return Whether an affinity_mask is set
  /// \cert
  /// \deterministic
  bool has_affinity_mask() const noexcept
  {
    return m_affinity_mask != apex::nullopt;
  }

  /// \brief The setter for thread's destruction policy
  /// \param[in] destruction_policy thread's destruction policy
  /// \return The self instance
  /// \cert
  /// \deterministic
  thread_attributes & destruction_policy(destructor_action destruction_policy) noexcept
  {
    m_destruction_policy = destruction_policy;
    return *this;
  }

  /// \brief The getter for thread's destruction policy
  /// \return Thread's destruction policy
  /// \cert
  /// \deterministic
  destructor_action destruction_policy() const
  {
    return m_destruction_policy;
  }

  /// \brief Equality operator
  /// \param other An object to compare to
  /// \return true if objects are equal
  /// \cert
  /// \deterministic
  bool operator==(const thread_attributes & other) const noexcept
  {
    return (m_scheduling_policy == other.m_scheduling_policy) &&
           (m_priority == other.m_priority) &&
           (m_stack_size == other.m_stack_size) &&
           (m_stack_address == other.m_stack_address) &&
           (m_affinity_mask == other.m_affinity_mask) &&
           (m_destruction_policy == other.m_destruction_policy);
  }

  /// \brief Inequality operator
  /// \param other An object to compare to
  /// \return true if objects are not equal
  /// \cert
  /// \deterministic
  bool operator!=(const thread_attributes & other) const noexcept
  {
    return !operator==(other);
  }

private:
  /// \brief The build() function should be used to create an instance
  thread_attributes() = default;

  apex::optional<scheduler> m_scheduling_policy;
  apex::optional<std::int32_t> m_priority;
  apex::optional<size_t> m_stack_size;
  apex::optional<void *> m_stack_address;
  apex::optional<affinity_mask_type> m_affinity_mask;
  destructor_action m_destruction_policy{destructor_action::join};
};

}  // namespace threading
}  // namespace apex

#endif  // THREADING__THREAD_ATTRIBUTES_HPP_
