/// \copyright Copyright 2017-2018 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains Apex.OS settings repository construction API for entity types

#ifndef SETTINGS__CONSTRUCT__TYPES_HPP_
#define SETTINGS__CONSTRUCT__TYPES_HPP_

#include <algorithm>
#include <map>
#include <memory>
#include <string>
#include <tuple>
#include <type_traits>
#include <vector>

#include <cpputils/string_view.hpp>
#include <cpputils/tuple.hpp>
#include <cpputils/variant.hpp>
#include <cpputils/variant_traits.hpp>
#include <settings/construct/tracking_ptr.hpp>
#include <settings/visibility.hpp>

/// \namespace apex
namespace apex
{
/// \namespace apex::settings
namespace settings
{
/// \namespace apex::settings::construct
namespace construct
{

/// A forward declare for node type
class node;

/// \brief The node pointer type
/// \cert
using node_ptr = details::tracking_ptr<node>;

/// \namespace apex::settings::construct::details
namespace details
{
/// \brief The node owning pointer type
/// \cert
using owning_node_ptr = details::owning_unique_ptr<node>;
}  // namespace details

/// \brief The integer value type
using integer = int32_t;
/// \brief The floating value type
using floating = double;
/// \brief The boolean value type
using boolean = bool;
/// \brief The string value type
using string = std::string;

/// \namespace apex::settings::construct::details
namespace details
{
/// \class dictionary_comparator
/// A transparent comparator which will allow the dictionary entries to be looked up not only
/// by the real key type (which is a dynamically allocated type)
/// but also by a couple of compatible statically allocated types
/// \cert
/// \deterministic
struct dictionary_comparator
{
  using is_transparent = std::true_type;

  /// \cert
  /// \deterministic
  bool operator()(const std::string & lhs, const std::string & rhs) const noexcept
  {
    return lhs < rhs;
  }

  /// \cert
  /// \deterministic
  bool operator()(const std::string & lhs, const apex::string_view & rhs) const noexcept
  {
    return rhs.compare(lhs.c_str()) > 0;
  }

  /// \cert
  /// \deterministic
  bool operator()(const apex::string_view & lhs, const std::string & rhs) const noexcept
  {
    return lhs.compare(rhs.c_str()) < 0;
  }

  /// \cert
  /// \deterministic
  bool operator()(const std::string & lhs, const char * rhs) const noexcept
  {
    return lhs.compare(rhs) < 0;
  }

  /// \cert
  /// \deterministic
  bool operator()(const char * lhs, const std::string & rhs) const noexcept
  {
    return rhs.compare(lhs) > 0;
  }
};

/// \brief The supported static value types
/// \cert
using non_allocating_value_types = std::tuple<integer, floating, boolean>;

/// \brief The supported dynamically allocating value types
/// \cert
using allocating_value_types = std::tuple<string>;

/// \brief All the supported value types
/// \cert
using all_value_types = cat_tuples_t<non_allocating_value_types, allocating_value_types>;

/// \brief Variant of all the supported value types
/// \cert
using value_type = variant_from_tuple_t<all_value_types>;

}  // namespace details

/// \brief The dictionary container type
/// \cert
// unordered_map cannot support transparent comparators, using map instead
using dictionary = std::map<string, node_ptr, details::dictionary_comparator>;

/// \brief The array container type
/// \cert
using array = std::vector<node_ptr>;

/// \brief Compares two dictionaries for equality
/// \param lhs The first dictionary
/// \param rhs The second dictionary
/// \return Whether two dictionaries are equal
/// \cert
/// \deterministic
SETTINGS_PUBLIC
bool operator==(const dictionary & lhs, const dictionary & rhs) noexcept;

/// \brief Compares two dictionaries for inequality
/// \param lhs The first dictionary
/// \param rhs The second dictionary
/// \return Whether two dictionaries are not equal
/// \cert
/// \deterministic
inline bool operator!=(const dictionary & lhs, const dictionary & rhs) noexcept
{
  return !operator==(lhs, rhs); /*lint !e9153*/  // false-positive
}

/// \brief Compares two arrays for equality
/// \param lhs The first array
/// \param rhs The second array
/// \return Whether two arrays are equal
/// \cert
/// \deterministic
SETTINGS_PUBLIC
bool operator==(const array & lhs, const array & rhs) noexcept;

/// \brief Compares two arrays for inequality
/// \param lhs The first array
/// \param rhs The second array
/// \return Whether two arrays are not equal
/// \cert
/// \deterministic
inline bool operator!=(const array & lhs, const array & rhs) noexcept
{
  return !operator==(lhs, rhs); /*lint !e9153*/  // false-positive
}

/// \namespace apex::settings::construct::details
namespace details
{
/// \brief All the supported value types human readable names
/*
 AXIVION Next CodeLine MisraC++2023-11.3.1: C style array is safe here
 */
static constexpr const char * value_type_names[] = {
  "integer", "floating", "boolean", "string", "dictionary", "array"};

/// class type_to_string
/// Converts a type to a human readable string
/// \tparam T The type to convert
template <class T>
struct type_to_string
{
  static constexpr const char * value = value_type_names
    [tuple_type_index<T, cat_tuples_t<all_value_types, std::tuple<dictionary, array>>>::value];
};

// lint -e{9107} NOLINT - False positive: Required for linker
template <class T>
constexpr const char * type_to_string<T>::value;

}  // namespace details

}  // namespace construct
}  // namespace settings
}  // namespace apex


#endif  // SETTINGS__CONSTRUCT__TYPES_HPP_
