/// \copyright Copyright 2017-2018 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains Apex.OS settings repository construction API for tracking pointer

#ifndef SETTINGS__CONSTRUCT__TRACKING_PTR_HPP_
#define SETTINGS__CONSTRUCT__TRACKING_PTR_HPP_

#include <cassert>
#include <cstdint>
#include <exception>
#include <limits>
#include <memory>
#include <type_traits>
#include <utility>

#include <settings/error.hpp>

/// \namespace apex
namespace apex
{
/// \namespace apex::settings
namespace settings
{
/// \namespace apex::settings::construct
namespace construct
{
/// \namespace apex::settings::construct::details
namespace details
{

template <class T>
class owning_unique_ptr;

template <class T>
class tracking_ptr;

/// \class tracked_data_block
/// \tparam T the type of the tracked object
/// The class holds the tracked object pointer along with the reference count
/// \cert
/// \deterministic
template <class T>
struct tracked_data_block
{
private:
  T * object{nullptr};
  std::int32_t ref_count{0};

  friend class owning_unique_ptr<T>;
  friend class tracking_ptr<T>;
};

/// \class tracking_ptr
/// Tracking pointer which behaves like shared_ptr counting the references.
/// However it does not delete the object when reference count drops to zero
/// since it is not an owning pointer.
/// This semantics (along with some additional logic) better suits the pointers used in data types
/// which can trigger cascade deletion causing recursive destructors calls, like lists and trees.
/// \cert
/// \deterministic
template <class T>
class tracking_ptr final
{
public:
  friend class owning_unique_ptr<T>;

  /// \brief Default tracking pointer points to nullptr
  /// \cert
  /// \deterministic
  /*
   AXIVION Next CodeLine MisraC++2023-15.1.4: member variable m_data{nullptr} initialized
   during definition right before the constructor call.
   */
  tracking_ptr() = default;

  /// \brief Copy constructor increases reference count
  /// \param other Another tracking pointer to copy from
  /// \cert
  tracking_ptr(const tracking_ptr & other) noexcept : m_data{other.m_data}
  {
    inc_ref();
  }

  /// \brief Assignment operator decreases reference count of the already tracked object and
  /// increases the reference count of the new one
  /// \param other Another tracking pointer to copy from
  /// \return Self-reference
  /// \cert
  /// \deterministic
  tracking_ptr & operator=(const tracking_ptr & other) & noexcept
  {
    if (this != &other) {
      dec_ref();
      /*lint -e{1555}*/  // Shallow copy is desired behavior here.
      m_data = other.m_data;
      inc_ref();
    }

    return *this;
  }

  /// \brief Assignment operator from nullptr decreases reference count and removes association
  /// with the tracked object
  /// \return Self-reference
  /// \cert
  /// \deterministic
  tracking_ptr & operator=(std::nullptr_t) & noexcept
  {
    dec_ref();
    m_data = nullptr;
    return *this;
  }

  /// \brief Move constructor
  /// \param other Another tracking pointer to move from
  /// \cert
  /// \deterministic
  tracking_ptr(tracking_ptr && other) noexcept : m_data{other.m_data}
  {
    other.m_data = nullptr;
  }

  /// \brief Move assignment operator
  /// \param other Another tracking pointer to move from
  /// \return Self-reference
  /// \cert
  /// \deterministic
  tracking_ptr & operator=(tracking_ptr && other) & noexcept
  {
    if (this != &other) {
      dec_ref();
      /*lint -e{1555}*/  // Shallow copy is desired behavior here.
      m_data = other.m_data;
      other.m_data = nullptr;
    }

    return *this;
  }

  /// \brief Destructor
  /// May call to std::terminate() if memory ownership is compromised
  /// \cert
  ~tracking_ptr()
  {
    dec_ref();
    m_data = nullptr;  // to shut up the linter
  }

  /// \brief Gets the pointer to T
  /// \return The pointer to T
  /// \cert
  /// \deterministic
  T * get() const
  {
    if (m_data == nullptr) {
      throw settings_error{"the tracking pointer is nullptr"};
    }
    return m_data->object;
  }

  /// \brief Gets the pointer to T
  /// \return The pointer to T
  /// \cert
  /// \deterministic
  T * operator->() const
  {
    if (m_data == nullptr) {
      throw settings_error{"the tracking pointer is nullptr"};
    }
    return m_data->object;
  }

  /// \brief Gets the reference to T
  /// \return The reference to T
  /// \cert
  /// \deterministic
  T & operator*() const
  {
    if (m_data == nullptr) {
      throw settings_error{"the tracking pointer is nullptr"};
    }
    return *m_data->object;
  }

  /// \brief Checks whether the tracking pointer points to an object
  /// \return Whether the tracking pointer points to an object
  /// \cert
  /// \deterministic
  explicit operator bool() const noexcept
  {
    return m_data != nullptr;
  }

  /// \brief Compares two tracking pointers for equality
  /// Two tracking pointers considered equal only if they share the same data block
  /// \param other Another tracking pointer to compare to
  /// \return Whether two tracking pointers are equal
  /// \cert
  /// \deterministic
  bool operator==(const tracking_ptr & other) const noexcept
  {
    return m_data == other.m_data;
  }

  /// \brief Compares two tracking pointers for inequality
  /// Two tracking pointers considered equal only if they share the same data block
  /// \param other Another tracking pointer to compare to
  /// \return Whether two tracking pointers are not equal
  /// \cert
  /// \deterministic
  bool operator!=(const tracking_ptr & other) const noexcept
  {
    return !operator==(other);
  }

  /// \brief Compares a tracking pointer for equality with nullptr
  /// \return Whether the tracking pointer is equal to nullptr
  /// \cert
  /// \deterministic
  bool operator==(std::nullptr_t) const noexcept
  {
    return m_data == nullptr;
  }

  /// \brief Compares a tracking pointer for inequality with nullptr
  /// \return Whether the tracking pointer is not equal to nullptr
  /// \cert
  /// \deterministic
  bool operator!=(std::nullptr_t) const noexcept
  {
    return !operator==(nullptr);
  }

private:
  /// \brief Creates a new tracking pointer tracking the value in the data block
  /// Can be called by owning_unique_ptr only
  /// \param data The data block to track
  explicit tracking_ptr(tracked_data_block<T> * const data) noexcept : m_data{data}
  {
    inc_ref();
  }

  /// \brief Increases the reference count of the tracked data block
  void inc_ref() noexcept
  {
    if (m_data != nullptr) {
      auto & count = m_data->ref_count;  // two lines to shut up the linter
      ++count;
      if (count > (std::numeric_limits<std::remove_reference_t<decltype(count)>>::max() - 1)) {
        // tracked data block count overflows
        std::terminate();
      }
    }
  }

  /// \brief Decreases the reference count of the tracked data block
  void dec_ref() noexcept
  {
    if (m_data != nullptr) {
      /*
       AXIVION DISABLE STYLE CodeLine MisraC++2023-4.1.3: Reason: Code Quality (Functional
       suitability), Justification: Underflow and dereference cannot happen since it is checked
       */
      auto & count = m_data->ref_count;  // two lines to shut up the linter
      --count;
      if (m_data->ref_count <= 0) {
        // Memory ownership is compromised -- no owner left so a leak is inevitable.
        std::terminate();
      }
    }
  }

  tracked_data_block<T> * m_data{nullptr};
};

/// \class owning_unique_ptr
/// Owning pointer which behaves like unqiue_ptr but also has the reference count which it
/// shares with any tracking pointers created from it.
/// \cert
template <class T>
class owning_unique_ptr final
{
private:
  using deleter = void (*)(T *);

public:
  /// \brief Creates the data block including the data pointer and the reference count
  /// \tparam U the auto deducted type of the pointer (delays instantiation until U is known)
  /// \param ptr The pointer to the object to track
  /// \param del The deleter
  /// \cert
  /*lint -e{9173}*/  // a smart pointer type needs to delete things explicitly
  template <class U, std::enable_if_t<std::is_same<U, T>::value> * = nullptr>
  explicit owning_unique_ptr(
    U * const ptr, deleter del = [](T * const obj) { delete obj; })
  : m_data{std::make_unique<tracked_data_block<T>>()}, m_deleter{std::move(del)}
  {
    m_data->object = ptr;

    auto & count = m_data->ref_count;  // two lines to shut up the linter
    ++count;
  }

  owning_unique_ptr(const owning_unique_ptr &) = delete;
  owning_unique_ptr & operator=(const owning_unique_ptr &) = delete;

  /// \brief Move constructor
  /// \cert
  /// \deterministic
  owning_unique_ptr(owning_unique_ptr &&) noexcept = default;

  /// \brief Move assignment operator
  /// \cert
  /// \deterministic
  owning_unique_ptr & operator=(owning_unique_ptr &&) noexcept = default;

  /// \brief Destructs the owning pointer and the pointed object using the deleter
  /// May call to std::terminate() if memory ownership is compromised
  /// \cert
  ~owning_unique_ptr() noexcept
  {
    if (m_data != nullptr) {
      auto & count = m_data->ref_count;  // two lines to shut up the linter
      --count;

      if (m_data->ref_count != 0) {
        // Memory ownership is compromised -- there are dangling tracking pointers out there.
        std::terminate();
      }

      m_deleter(m_data->object);
    }

    m_deleter = nullptr;  // to shut up the linter
  }

  /// \brief Gets the current use count
  /// \return The current use count
  /// \cert
  /// \deterministic
  std::int32_t use_count() const noexcept
  {
    return m_data->ref_count;
  }
  // AXIVION ENABLE STYLE CodeLine MisraC++2023-4.1.3

  /// \brief Creates a tracking pointer for the held data block
  /// \return A tracking pointer for the held data block
  /// \cert
  /// \deterministic
  tracking_ptr<T> get_tracking_ptr() const noexcept
  {
    return tracking_ptr<T>{m_data.get()};
  }

private:
  std::unique_ptr<tracked_data_block<T>> m_data;
  deleter m_deleter;
};

}  // namespace details
}  // namespace construct
}  // namespace settings
}  // namespace apex


#endif  // SETTINGS__CONSTRUCT__TRACKING_PTR_HPP_
