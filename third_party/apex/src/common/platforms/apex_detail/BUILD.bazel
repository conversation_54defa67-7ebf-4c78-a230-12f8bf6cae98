package(default_visibility = ["//visibility:public"])

config_setting(
    name = "ubuntu-focal-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:x86_64",
        "@apex//common/platforms/os_distro:ubuntu_focal",
    ],
)

config_setting(
    name = "ubuntu-focal-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/os_distro:ubuntu_focal",
    ],
)

config_setting(
    name = "ubuntu-jammy-x86_64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:x86_64",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
    ],
)

config_setting(
    name = "ubuntu-jammy-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/os_distro:ubuntu_jammy",
    ],
)

config_setting(
    name = "yocto-kirkstone-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/os_distro:poky_kirkstone",
    ],
)

config_setting(
    name = "yocto-dunfell-aarch64",
    constraint_values = [
        "@platforms//os:linux",
        "@platforms//cpu:aarch64",
        "@apex//common/platforms/os_distro:poky_dunfell",
    ],
)
