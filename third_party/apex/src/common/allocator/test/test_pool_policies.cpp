// Copyright 2017-2020 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include <allocator/details/static_memory_pool.hpp>

#include <memory>

#include "mem_pool_buckets_impl.hpp"
#include "tracker.hpp"
#include "static_pool_helpers.hpp"

namespace memory_pool = apex::allocator::memory_pool;

using test::max_count;
using test::free_count;
using test::setup_all_block_sizes_for_pools;
using test::reset_all_pools;

namespace
{
class memory_pool_policies : public ::testing::Test
{
public:
  void SetUp() override
  {
    setup_all_block_sizes_for_pools(std::make_index_sequence<99>{});
  }

  void TearDown() override
  {
    reset_all_pools(std::make_index_sequence<99>{});
  }
};
}  // namespace

/// @test{
/// "req" : ["SWRQ_ALLOCATOR_303", "SWRQ_ALLOCATOR_312", "SWRQ_ALLOCATOR_314"]
/// }
TEST_F(memory_pool_policies, first_fit) {
  for (auto i = 0U; i < max_count<4>(8); ++i) {
    memory_pool::allocate<4>(8, 1);
  }

  ASSERT_EQ(free_count<4>(8), 0U);

  ASSERT_THROW(memory_pool::allocate<4>(8, 1), apex::bad_alloc);

  /* Test SWRQ_ALLOCATOR_312 */
  memory_pool::discard_all<4>();
  ASSERT_EQ(free_count<4>(2), max_count<4>(2));
  ASSERT_EQ(free_count<4>(8), max_count<4>(8));
  ASSERT_EQ(free_count<4>(16), max_count<4>(16));
  ASSERT_EQ(free_count<4>(32), max_count<4>(32));
}

/// @test{
/// "req" : ["SWRQ_ALLOCATOR_303", "SWRQ_ALLOCATOR_312", "SWRQ_ALLOCATOR_315"]
/// }
TEST_F(memory_pool_policies, first_fit_or_smaller) {
  for (auto i = 0U; i < max_count<1>(64); ++i) {
    memory_pool::allocate<1>(64, 1);
  }

  ASSERT_EQ(free_count<1>(64), 0U);
  ASSERT_NO_THROW(memory_pool::allocate<1>(64, 1));
  ASSERT_EQ(max_count<1>(32) - 2, free_count<1>(32));

  for (auto i = 2U; i < max_count<1>(32); i += 2) {
    memory_pool::allocate<1>(64, 1);
  }

  ASSERT_THROW(memory_pool::allocate<1>(64, 1), apex::bad_alloc);

  /* Test SWRQ_ALLOCATOR_312 */
  memory_pool::discard_all<1>();
  ASSERT_EQ(free_count<1>(32), max_count<1>(32));
  ASSERT_EQ(free_count<1>(64), max_count<1>(64));
  ASSERT_EQ(free_count<1>(128), max_count<1>(128));
  ASSERT_EQ(free_count<1>(256), max_count<1>(256));
}

/// @test{
/// "req" : ["SWRQ_ALLOCATOR_303", "SWRQ_ALLOCATOR_312", "SWRQ_ALLOCATOR_317"]
/// }
TEST_F(memory_pool_policies, any) {
  for (auto i = 0U; i < max_count<5>(64); ++i) {
    memory_pool::allocate<5>(64, 1);
  }

  ASSERT_EQ(free_count<5>(64), 0U);

  ASSERT_NO_THROW(memory_pool::allocate<5>(64, 1));
  ASSERT_EQ(max_count<5>(128) - 1, free_count<5>(128));

  for (auto i = 1U; i < max_count<5>(128); ++i) {
    memory_pool::allocate<5>(64, 1);
  }

  ASSERT_NO_THROW(memory_pool::allocate<5>(64, 1));
  ASSERT_EQ(max_count<5>(256) - 1, free_count<5>(256));

  for (auto i = 1U; i < max_count<5>(256); ++i) {
    memory_pool::allocate<5>(64, 1);
  }

  ASSERT_NO_THROW(memory_pool::allocate<5>(64, 1));
  ASSERT_EQ(max_count<5>(32) - 2, free_count<5>(32));

  for (auto i = 2U; i < max_count<5>(32); i += 2) {
    memory_pool::allocate<5>(64, 1);
  }

  ASSERT_THROW(memory_pool::allocate<5>(64, 1), apex::bad_alloc);

  /* Test SWRQ_ALLOCATOR_312 */
  memory_pool::discard_all<5>();
  ASSERT_EQ(free_count<5>(32), max_count<5>(32));
  ASSERT_EQ(free_count<5>(64), max_count<5>(64));
  ASSERT_EQ(free_count<5>(128), max_count<5>(128));
  ASSERT_EQ(free_count<5>(256), max_count<5>(256));
}

/// @test{
/// "req" : [
///   "SWRQ_ALLOCATOR_302",
///   "SWRQ_ALLOCATOR_303",
///   "SWRQ_ALLOCATOR_312",
///   "SWRQ_ALLOCATOR_318"
/// ]
/// }
TEST_F(memory_pool_policies, frugal) {
  memory_pool::allocate<6>(161, 1);
  ASSERT_EQ(free_count<6>(32), max_count<6>(32) - 6);
  memory_pool::allocate<6>(254, 1);
  ASSERT_EQ(free_count<6>(256), max_count<6>(256) - 1);
  memory_pool::allocate<6>(600, 1);
  ASSERT_EQ(free_count<6>(256), max_count<6>(256) - 4);

  for (auto i = 0U; i < max_count<6>(256) - 4; ++i) {
    memory_pool::allocate<6>(255, 1);
    ASSERT_EQ(free_count<6>(256), max_count<6>(256) - 5 - i);
  }
  ASSERT_EQ(free_count<6>(256), 0U);

  memory_pool::allocate<6>(255, 1);
  ASSERT_EQ(free_count<6>(32), max_count<6>(32) - 14);

  memory_pool::allocate<6>(769, 1);
  ASSERT_EQ(free_count<6>(1024), max_count<6>(1024) - 1);

  memory_pool::allocate<6>(1024, 1);
  ASSERT_EQ(free_count<6>(1024), max_count<6>(1024) - 2);

  /* Test SWRQ_ALLOCATOR_312 */
  memory_pool::discard_all<6>();
  ASSERT_EQ(free_count<6>(32), max_count<6>(32));
  ASSERT_EQ(free_count<6>(64), max_count<6>(64));
  ASSERT_EQ(free_count<6>(1024), max_count<6>(1024));
}

/// @test{
/// "req" : ["SWRQ_ALLOCATOR_303", "SWRQ_ALLOCATOR_312", "SWRQ_ALLOCATOR_316"]
/// }
TEST_F(memory_pool_policies, first_fit_or_bigger) {
  for (auto i = 0U; i < max_count<7>(161); ++i) {
    memory_pool::allocate<7>(161, 1);
  }

  ASSERT_EQ(free_count<7>(161), 0U);

  for (auto i = 0U; i < max_count<7>(1024); ++i) {
    memory_pool::allocate<7>(161, 1);
  }

  ASSERT_EQ(free_count<7>(1024), 0U);

  ASSERT_THROW(memory_pool::allocate<7>(161, 1), apex::bad_alloc);

  /* Test SWRQ_ALLOCATOR_312 */
  memory_pool::discard_all<7>();
  ASSERT_EQ(free_count<7>(32), max_count<7>(32));
  ASSERT_EQ(free_count<7>(64), max_count<7>(64));
  ASSERT_EQ(free_count<7>(1024), max_count<7>(1024));
}

/**
 * @test{
 * "req" : ["SWRQ_ALLOCATOR_312"]
 * }
 * The purpose of this is to test `allocate` function,
 * When the alignment of the bucket is smaller than alignment parameter of `allocate`
 * `allocate` function should throw `apex::bad_alloc` exception.
 */
TEST_F(memory_pool_policies, frugal_bad_alloc_with_bigger_alignment) {
  ASSERT_THROW(memory_pool::allocate<6>(32, 128), apex::bad_alloc);
  ASSERT_THROW(memory_pool::allocate<1>(32, 128), apex::bad_alloc);
}

/// @test{
/// "req" : ["SWRQ_ALLOCATOR_312"]
/// }
TEST_F(memory_pool_policies, frugal_coverage) {
  memory_pool::allocate<8>(11, 32);  // 1 block in bucket_cfg1024
  memory_pool::allocate<8>(16, 32);  // 1 block in bucket_cfg1024
  memory_pool::allocate<8>(20, 32);  // 1 block in bucket_cfg1024
  memory_pool::allocate<8>(60, 32);  // 1 block in bucket_cfg1024
  memory_pool::allocate<8>(1024, 32);  // 1 block in bucket_cfg1024
  memory_pool::allocate<8>(1025, 32);  // 2 block (1024 + 1) in bucket_cfg1024
  memory_pool::allocate<8>(2050, 32);  // 3 block (1024*2 + 2) in bucket_cfg1024
  static constexpr auto used_block_cfg1024 = 10;  // 10 blocks are used in bucket_cfg1024.

  ASSERT_EQ(free_count<8>(32), max_count<6>(32));
  ASSERT_EQ(free_count<8>(1024), max_count<8>(1024) - used_block_cfg1024);

  /* Test SWRQ_ALLOCATOR_312 */
  memory_pool::discard_all<8>();
  ASSERT_EQ(free_count<8>(32), max_count<8>(32));
  ASSERT_EQ(free_count<8>(1024), max_count<8>(1024));
}
