/// \copyright Copyright 2017-2020 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief The flavours of std::queue and std::priority_queue which use pmr::polymorphic_allocator
/// enabled containers as their defaults

#ifndef ALLOCATOR__PMR__QUEUE_HPP_
#define ALLOCATOR__PMR__QUEUE_HPP_

#include <allocator/details/polymorphic_allocator.hpp>
#include <allocator/pmr/vector.hpp>
#include <allocator/pmr/deque.hpp>

#include <functional>
#include <queue>

/// \namespace apex
namespace apex
{
/// \namespace apex::allocator
namespace allocator
{
/// \namespace apex::allocator::pmr
namespace pmr
{

/// \brief A specialization of std::queue which uses a polymorphic_allocator enabled container as
/// its default
/// \tparam T The value type
/// \tparam Key The key type
/// \tparam Container The underlying container type
template<class T, class Container = pmr::deque<T>>
using queue = std::queue<T, Container>;

/// \brief A specialization of std::priority_queue which uses a polymorphic_allocator enabled
/// container as its default
/// \tparam T The value type
/// \tparam Key The key type
/// \tparam Container The underlying container type
/// \tparam Compare The comparator type
template<class T,
  class Container = pmr::vector<T>, class Compare = std::less<typename Container::value_type>>
using priority_queue = std::priority_queue<T, Container, Compare>;

}  // namespace pmr
}  // namespace allocator
}  // namespace apex

#endif  // ALLOCATOR__PMR__QUEUE_HPP_
