cmake_minimum_required(VERSION 3.5)

project(containers)

find_package(ament_cmake_auto REQUIRED)
find_package(cpputils)
ament_auto_find_build_dependencies()
include_directories(include)

# dummy.cpp forces PCLint run on the header
set(containers_sources
    src/dummy.cpp
    include/containers/static_queue.hpp
    include/containers/static_vector.hpp)

set_source_files_properties(${containers_sources} PROPERTIES language "CXX")
add_library(${PROJECT_NAME} INTERFACE)

ament_export_include_directories(include)

target_include_directories(${PROJECT_NAME} INTERFACE
    include
    ${cpputils_INCLUDE_DIRS})

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  find_package(apex_test_tools REQUIRED)
  ament_lint_auto_find_test_dependencies()

  list(APPEND AMENT_LINT_AUTO_EXCLUDE ament_cmake_uncrustify ament_cmake_cpplint)

  apex_test_tools_add_gtest(test_containers
      test/containers/test_shared_ptr.cpp
      test/containers/test_static_allocators_vectors.cpp
      test/containers/test_static_allocators_basic.cpp
      test/containers/test_static_vector_snippets.cpp
      test/containers/test_static_queue.cpp)
  if(VECTORCAST_BUILD)
    vcast_set_io_cover_cxx(test_containers)
  endif()
  target_link_libraries(test_containers ${PROJECT_NAME})
  ament_target_dependencies(test_containers cpputils)

endif()

ament_auto_package()
