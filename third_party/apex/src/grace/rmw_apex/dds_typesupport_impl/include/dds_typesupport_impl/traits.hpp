/// \copyright Copyright 2017-2019 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Header containing base template definition for apex_middleware message traits.
/// Each message will define a template specialization for the message type

#ifndef DDS_TYPESUPPORT_IMPL__TRAITS_HPP_
#define DDS_TYPESUPPORT_IMPL__TRAITS_HPP_

#include <dds_typesupport_impl/sample_info.hpp>
#include <dds_typesupport/traits.hpp>
#include <cyclone_dds_vendor/dds.hpp>

namespace apex::apex_middleware
{
/// \brief Provides middleware-specific operations and types required by dds_typesupport
///
/// The MiddlewareTraits will be specialized to contain the following typedefs:
/// * DDSLoanedSamplesType<MWType>: The middleware's LoanedSamples type
/// * DDSSampleInfoType: The middlware's SampleInfo type
/// * TypeTraits<ROSType>: DDS_ROS_TypeTraits for this middleware (see above)
/// * ServiceTypeTraits<ServiceType>: Service_TypeTraits for this middleware (see above)
/// * get_sample_data<MWType>(LoanedSamples, index): a function retrieving sample data
///   from a given LoanedSamples object
/// * get_sample_info(LoanedSamples, index): a function retrieving sample metadata
///   from a given LoanedSamples object
struct MiddlewareTraits
{
  template<typename MWType>
  using LoanedSamplesType = dds::sub::LoanedSamples<MWType>;
  using SampleInfoType = dds::sub::SampleInfo;

  template<typename ROSType>
  using TypeTraits = DDS_ROS_TypeTraits<ROSType>;
  template<typename ServiceType>
  using ServiceTypeTraits = Service_TypeTraits<ServiceType>;

  template<typename MWType>
  static const MWType & get_sample_data(
    const LoanedSamplesType<MWType> & loaned_samples,
    const size_t idx)
  {
    return loaned_samples.delegate()->get_buffer()[idx]->data();
  }
  template<typename MWType>
  static const SampleInfoType & get_sample_info(
    const LoanedSamplesType<MWType> & loaned_samples,
    const size_t idx)
  {
    return loaned_samples.delegate()->get_buffer()[idx]->info();
  }
};
}  // namespace apex::apex_middleware

#endif  // DDS_TYPESUPPORT_IMPL__TRAITS_HPP_
