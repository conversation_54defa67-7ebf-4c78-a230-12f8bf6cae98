/// \copyright Copyright 2020 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file defines rmw_apex_middleware type support interface for services.
//
// This file contains modified code from the following open source projects
// published under the licenses listed below:
//
//
// # Copyright 2016 Open Source Robotics Foundation, Inc.
// # Licensed under the Apache License, Version 2.0 (the "License");
// # you may not use this file except in compliance with the License.
// # You may obtain a copy of the License at
// #     http://www.apache.org/licenses/LICENSE-2.0
// # Unless required by applicable law or agreed to in writing, software
// # distributed under the License is distributed on an "AS IS" BASIS,
// # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// # See the License for the specific language governing permissions and
// # limitations under the License.

#ifndef ROSIDL_TYPESUPPORT_APEX_MIDDLEWARE_CPP__SERVICE_TYPE_SUPPORT_HPP_
#define ROSIDL_TYPESUPPORT_APEX_MIDDLEWARE_CPP__SERVICE_TYPE_SUPPORT_HPP_

#include <memory>
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wsuggest-override"
#include "rosidl_typesupport_apex_middleware_cpp/service_requester.hpp"
#include "rosidl_typesupport_apex_middleware_cpp/service_replier.hpp"

#ifdef APEX_CERT
//lint -e{9141} NOLINT This type is required to be in the global namespace
/*
 AXIVION Construct MisraC++2023-6.0.3: This type is required to be in the global namespace
 */
extern "C" struct rosidl_service_type_support_t;

namespace rosidl_typesupport_cpp
{
// This template was taken from service_type_support.hpp in rosidl_generator_cpp.
// It was moved here since rosidl_typesupport_cpp and rosidl_generator_cpp are not being certified
// Since Apex.OS Cert only supports rosidl_typesupport_apex_middleware_cpp, the additional functionality
// provided by these packages is not required
template<typename T>
const rosidl_service_type_support_t * get_service_type_support_handle();

}  // namespace rosidl_typesupport_cpp
#endif  // APEX_CERT

namespace rosidl_typesupport_apex_middleware_cpp
{
/// \brief Type support interface for Services to handle interacting with and converting
/// between the ROS and DDS types used by the Service
class ServiceTypeSupportInterface
{
public:
  /// \brief Gets the name of the package containing the Service message types
  /// \returns String containing the package name
  virtual const char * package_name() const = 0;

  /// \brief Gets the namespace of the package containing the Service message types
  /// \returns String containing the message namespace
  virtual const char * message_namespace_name() const = 0;

  /// \brief Gets the name of the Service
  /// \returns String containing the name of the service
  virtual const char * service_name() const = 0;

  /// \brief Gets the singleton instance of the class derived from this interface, for the
  /// given type.
  /// \tparam ServiceType The type of the Service to get the interface for
  /// \returns The type support interface for the provided Service type
  template<typename ServiceType>
  static
  const ServiceTypeSupportInterface &
  get_type_support_interface();

  virtual
  std::unique_ptr<ServiceRequesterInterface>
  create_requester(
    apex::ApexMiddlewareContext & mw_context,
    const char * request_topic_name,
    const char * response_topic_name,
    const dds::sub::qos::DataReaderQos & response_datareader_qos,
    const dds::pub::qos::DataWriterQos & request_datawriter_qos,
    dds::sub::Subscriber & subscriber,
    dds::pub::Publisher & publisher) const = 0;

  virtual
  std::unique_ptr<ServiceReplierInterface>
  create_replier(
    apex::ApexMiddlewareContext & mw_context,
    const char * request_topic_name,
    const char * response_topic_name,
    const dds::sub::qos::DataReaderQos & request_datareader_qos,
    const dds::pub::qos::DataWriterQos & response_datawriter_qos,
    dds::sub::Subscriber & subscriber,
    dds::pub::Publisher & publisher) const = 0;

  virtual
  ~ServiceTypeSupportInterface() = default;
};

/// \brief Implementation of the ServiceTypeSupportInterface for a particular Service type
/// \tparam ServiceTypeTraits The type traits structure generated by
/// rosidl_typesupport_apex_middleware_c/cpp
template<typename ServiceTypeTraits>
class ServiceTypeSupport : public ServiceTypeSupportInterface
{
  const char * package_name() const final
  {
    return ServiceTypeTraits::service_type_package_name;
  }

  const char * message_namespace_name() const final
  {
    return ServiceTypeTraits::service_type_namespace_name;
  }

  const char * service_name() const final
  {
    return ServiceTypeTraits::service_type_name;
  }

  /// \brief Create a requester for this Service type
  /// \param mw_context The middleware context containing the DDS DomainParticipant that
  /// the service should be created with
  /// \param[in] request_topic_name Name to use for the request topic
  /// \param[in] response_topic_name Name to use for the response topic
  /// \param response_datareader_qos The DDS QOS to use when creating the response DataReader
  /// \param request_datawriter_qos The DDS QOS to use when creating the request DataWriter
  /// \param subscriber The DDS Subscriber that should be used to create the response DataReader
  /// \param publisher The DDS Subscriber that should be used to create the request DataWriter
  /// \returns Unique_ptr holding the new service requester
  /// \throws RMWException if any parameter is invalid or if a DDS error occurs
  std::unique_ptr<ServiceRequesterInterface>
  create_requester(
    apex::ApexMiddlewareContext & mw_context,
    const char * request_topic_name,
    const char * response_topic_name,
    const dds::sub::qos::DataReaderQos & response_datareader_qos,
    const dds::pub::qos::DataWriterQos & request_datawriter_qos,
    dds::sub::Subscriber & subscriber,
    dds::pub::Publisher & publisher) const final
  {
    return std::make_unique<ServiceRequester<ServiceTypeTraits>>(
      mw_context,
      request_topic_name,
      response_topic_name,
      request_datawriter_qos,
      response_datareader_qos,
      subscriber,
      publisher);
  }

  /// \brief Create a replier for this Service type
  /// \param mw_context The middleware context containing the DDS DomainParticipant that
  /// the service should be created with
  /// \param[in] request_topic_name Name to use for the request topic
  /// \param[in] response_topic_name Name to use for the response topic
  /// \param request_datareader_qos The DDS QOS to use when creating the request DataReader
  /// \param response_datawriter_qos The DDS QOS to use when creating the response DataWriter
  /// \param subscriber The DDS Subscriber that should be used to create the request DataReader
  /// \param publisher The DDS Subscriber that should be used to create the response DataWriter
  /// \returns Unique_ptr holding the new service replier
  /// \throws RMWException if any parameter is invalid or if a DDS error occurs
  std::unique_ptr<ServiceReplierInterface>
  create_replier(
    apex::ApexMiddlewareContext & mw_context,
    const char * request_topic_name,
    const char * response_topic_name,
    const dds::sub::qos::DataReaderQos & request_datareader_qos,
    const dds::pub::qos::DataWriterQos & response_datawriter_qos,
    dds::sub::Subscriber & subscriber,
    dds::pub::Publisher & publisher) const final
  {
    return std::make_unique<ServiceReplier<ServiceTypeTraits>>(
      mw_context,
      request_topic_name,
      response_topic_name,
      request_datareader_qos,
      response_datawriter_qos,
      subscriber,
      publisher);
  }
#pragma GCC diagnostic pop
};

}  // namespace rosidl_typesupport_apex_middleware_cpp
#endif  // ROSIDL_TYPESUPPORT_APEX_MIDDLEWARE_CPP__SERVICE_TYPE_SUPPORT_HPP_
