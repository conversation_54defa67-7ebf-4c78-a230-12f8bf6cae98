// Copyright 2020 Apex.AI, Inc.
// All rights reserved.

#include "rmw_apex_middleware/utils/prefix.hpp"

namespace apex
{
namespace rmw_apex_middleware
{
ROSPrefix::ROSPrefix()
: m_ros_topic_prefix("rt"),
  m_ros_service_request_prefix("rq"),
  m_ros_service_response_prefix("rr"),
  m_ros_service_prefix("rs"),
  m_ros_parameter_prefix("rp"),
  m_ros_action_prefix("ra"),
  m_ros_prefix_list{{
        m_ros_topic_prefix,
        m_ros_service_request_prefix,
        m_ros_service_response_prefix,
        m_ros_service_prefix,
        m_ros_parameter_prefix,
        m_ros_action_prefix
      }} {}


std::array<apex::string_strict8_t,
  ROSPrefix::NUM_PREFIXES> ROSPrefix::get_ros_prefix_list() noexcept
{
  return get().m_ros_prefix_list;
}

apex::string_strict8_t ROSPrefix::get_ros_topic_prefix() noexcept
{
  return get().m_ros_topic_prefix;
}

apex::string_strict8_t ROSPrefix::get_ros_service_request_prefix() noexcept
{
  return get().m_ros_service_request_prefix;
}

apex::string_strict8_t ROSPrefix::get_ros_service_response_prefix() noexcept
{
  return get().m_ros_service_response_prefix;
}

apex::string_strict8_t ROSPrefix::get_ros_service_prefix() noexcept
{
  return get().m_ros_service_prefix;
}

apex::string_strict8_t ROSPrefix::get_ros_parameter_prefix() noexcept
{
  return get().m_ros_parameter_prefix;
}

apex::string_strict8_t ROSPrefix::get_ros_action_prefix() noexcept
{
  return get().m_ros_action_prefix;
}

apex::string_strict8_t
ROSPrefix::get_ros_prefix_if_exists(const apex::string_strict256_t & topic_name) noexcept
{
  apex::string_strict8_t prefix_ret = "";
  for (size_t i = 0U; i < get_ros_prefix_list().size(); i++) {
    const apex::string_strict8_t st = get_ros_prefix_list()[i];
    if (0 == topic_name.compare(0U, st.length(), st.c_str(), st.length())) {
      prefix_ret = get_ros_prefix_list()[i];
    }
  }
  return prefix_ret;
}

apex::string_strict256_t make_fqtopic(
  const apex::string_strict8_t & prefix, const apex::string_strict256_t & topic_name,
  const apex::string_strict8_t & suffix,
  bool avoid_ros_namespace_conventions)
{
  if (avoid_ros_namespace_conventions) {
    return topic_name + suffix;
  } else {
    return apex::string_strict256_t(prefix) + topic_name + suffix;
  }
}

}  // namespace rmw_apex_middleware
}  // namespace apex
