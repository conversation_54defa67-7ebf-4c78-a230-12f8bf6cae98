// Copyright 2019 Apex.AI, Inc.
// All rights reserved.

#include <cyclone_dds_vendor/dds.hpp>
#include <rmw_apex_middleware/detail/qos.hpp>
#include <cpputils/safe_cast.hpp>
#include <limits>
#include "rmw_apex_middleware/rmw_apex_middleware.hpp"
#include "rmw_apex_middleware/utils/error_handling.hpp"
#include "rmw_apex_middleware/detail/publisher_payload.hpp"

namespace apex
{
namespace rmw_apex_middleware
{
using dds::core::policy::ResourceLimits;
using dds::core::policy::History;
using dds::core::policy::HistoryKind;
using dds::core::policy::Durability;
using dds::core::policy::DurabilityKind;
using dds::core::policy::Reliability;
using dds::core::policy::ReliabilityKind;
using dds::core::policy::Lifespan;
using dds::core::policy::Liveliness;
using dds::core::policy::LivelinessKind;
using dds::core::policy::Deadline;

static void apply_resource_limits(
  dds::pub::qos::DataWriterQos & dw_qos,
  const rmw_qos_profile_t & rmw_qos)
{
  //! [Default QOS resource limits for DataWriters]
  // There can only be one instance, but use 2 to for a bit of extra wiggle room
  (void)dw_qos.policy<ResourceLimits>().max_instances(2);

  if (rmw_qos.depth > 0U) {
    // There should never be more samples than the history depth, as there is just one instance
    (void)
    dw_qos.policy<ResourceLimits>().max_samples(apex::cast::safe_cast<int32_t>(rmw_qos.depth));
  } else {
    // Fallback to predefined safe value
    (void)dw_qos.policy<ResourceLimits>().max_samples(
      rmw_apex_middleware::WRITERS_MAX_SAMPLES_DEFAULT_VAL);
  }

  (void)dw_qos.policy<ResourceLimits>().max_samples_per_instance(
    dw_qos.policy<ResourceLimits>().max_samples());
}

static void apply_resource_limits(
  dds::sub::qos::DataReaderQos & dr_qos,
  const rmw_qos_profile_t & rmw_qos)
{
  //! [QOS default DataReader]
  // There can only be one instance, but use 2 to for a bit of extra wiggle room
  (void)dr_qos.policy<ResourceLimits>().max_instances(2);

  if (rmw_qos.depth > 0U) {
    // Max_samples never exceed the history depth, however the difference between
    // history and resource limits can be used to receive data even if all samples are
    // being loaned out. Therefore set max samples to twice the history depth, so there
    // is always enough space to receive data.
    size_t max_samples = 2U * rmw_qos.depth;

    /*
     AXIVION Next Line MisraC++2023-7.0.5: Reason: Code Quality (Functional suitability),
     Justification: Converting to unsigned is ok here as negative values will not occur
     */
    /*lint -e{9117} Converting to unsigned is ok here as negative values will not occur */
    // TODO(apex.os): Check and fix sign-compare compilation warning 25491
    if (max_samples > static_cast<size_t>(std::numeric_limits<int32_t>::max())) {
      max_samples = static_cast<size_t>(std::numeric_limits<int32_t>::max());
    }
    (void)dr_qos.policy<ResourceLimits>().max_samples(apex::cast::safe_cast<int32_t>(max_samples));
  } else {
    // Fallback to predefined safe value
    (void)dr_qos.policy<ResourceLimits>().max_samples(
      rmw_apex_middleware::READERS_MAX_SAMPLES_DEFAULT_VAL);
  }

  (void)dr_qos.policy<ResourceLimits>().max_samples_per_instance(
    dr_qos.policy<ResourceLimits>().max_samples());
}

static
bool is_time_default(const rmw_time_t & time_rmw)
{
  static constexpr rmw_time_t kduration_unspecified = RMW_DURATION_UNSPECIFIED;
  return (time_rmw.sec == kduration_unspecified.sec) &&
         (time_rmw.nsec == kduration_unspecified.nsec);
}

static dds::core::Duration rmw_time_to_dds(const rmw_time_t & rmw_duration)
{
  static constexpr rmw_time_t kduration_infinite = RMW_DURATION_INFINITE;
  if (rmw_duration.sec == kduration_infinite.sec && rmw_duration.nsec == kduration_infinite.nsec) {
    return dds::core::Duration::infinite();
  } else {
    const dds::core::Duration dds_duration(
      apex::cast::safe_cast<int32_t>(rmw_duration.sec),
      apex::cast::safe_cast<uint32_t>(rmw_duration.nsec));
    return dds_duration;
  }
}

static rmw_time_t dds_time_to_rmw(const dds::core::Duration & dds_duration)
{
  if (dds_duration == dds::core::Duration::infinite()) {
    return RMW_DURATION_INFINITE;
  } else {
    rmw_time_t time_rmw{};
    time_rmw.sec = apex::cast::safe_cast<uint64_t>(dds_duration.sec());
    time_rmw.nsec = apex::cast::safe_cast<uint64_t>(dds_duration.nanosec());
    return time_rmw;
  }
}

template<class DDSQoSType>
void update_dds_lifespan_duration(DDSQoSType &, const rmw_time_t &);

// This is to match the template specialized API
template<>
void update_dds_lifespan_duration(dds::sub::qos::DataReaderQos &, const rmw_time_t &)
{
  // intentionally a no-op, lifespan is not set for the reader
}

// Template specialization is deliberately hidden lifespan QOS is only valid for DataWriter
// lint -e{9073} NOLINT seems false positive on template specialization
template<>
void update_dds_lifespan_duration(
  dds::pub::qos::DataWriterQos & dds_qos,
  const rmw_time_t & duration)
{
  if (!is_time_default(duration)) {
    (void)dds_qos.policy(Lifespan{rmw_time_to_dds(duration)});
  }
}

template<class DDSQoSType>
void update_rmw_lifespan_duration(rmw_qos_profile_t &, const DDSQoSType &);

// This is to match the template specialized API
template<>
void update_rmw_lifespan_duration(rmw_qos_profile_t &, const dds::sub::qos::DataReaderQos &)
{
  // intentionally a no-op, lifespan is not set for the reader
}

// Template specialization is deliberately hidden lifespan QOS is only valid for DataWriter
//lint -e{9073} NOLINT seems false positive on template specialization
template<>
void update_rmw_lifespan_duration(
  rmw_qos_profile_t & rmw_qos,
  const dds::pub::qos::DataWriterQos & dds_qos)
{
  rmw_qos.lifespan = dds_time_to_rmw(dds_qos.policy<Lifespan>().duration());
}

template<class DDSQoSType>
void rmw_qos_to_dds_qos(
  const rmw_qos_profile_t & rmw_qos,
  const detail::QoSPayload<DDSQoSType> & payload,
  DDSQoSType & dds_qos)
{
  const auto qos_profile_is_system_default = [](const rmw_qos_profile_t & qos) -> bool {
      return (qos.durability == rmw_qos_profile_system_default.durability) &&
             (qos.history == rmw_qos_profile_system_default.history) &&
             (qos.reliability == rmw_qos_profile_system_default.reliability) &&
             (qos.liveliness == rmw_qos_profile_system_default.liveliness);
    };

  // if the payload provides a custom DDS QoS, use that only, and ignore the ROS qos
  if (payload.has_qos()) {
    // ensure that a custom QoS provided by the payload is not used with anything
    // other than the "system default" settings for a ROS qos
    if (!qos_profile_is_system_default(rmw_qos)) {
      throw RMWException(RMW_RET_ERROR,
              "Cannot use custom QOS via payload unless system default QOS profile is used.");
    }
    dds_qos = payload.get_qos();
    return;
  }

  // default dds QoS
  DDSQoSType default_qos{};
  dds_qos = default_qos;  // Init output parameter with default qos profile first

  // If not using the default QOS, do some sanity checking:
  // ensure the history depth is at least the requested queue size
  // expected depth' type size_t to be non-negative for all cases among HISTORY_KEEP_ALL
  if ((rmw_qos.depth == 0U) && (rmw_qos.history == RMW_QOS_POLICY_HISTORY_KEEP_LAST)) {
    throw RMWException(RMW_RET_INVALID_ARGUMENT,
            "History depth must be greater than 0 if history is set to KEEP_LAST");
  }
  /*
   AXIVION Next Line MisraC++2023-7.0.5: Reason: Code Quality (Functional suitability),
   Justification: Converting to unsigned is ok here as negative
   values will not occur
   */
  /*lint -e{9117} Converting to unsigned is ok here as negative values will not occur */
  if (rmw_qos.depth >= static_cast<size_t>(std::numeric_limits<int32_t>::max())) {
    throw RMWException(RMW_RET_INVALID_ARGUMENT,
            "Requested queue size exceeds maximum for the DDS type");
  }

  apply_resource_limits(dds_qos, rmw_qos);

  // history depth QOS policy
  if (rmw_qos.depth != RMW_QOS_POLICY_DEPTH_SYSTEM_DEFAULT) {
    (void)dds_qos.template policy<History>().depth(apex::cast::safe_cast<int32_t>(rmw_qos.depth));
  }

  // history QOS policy
  switch (rmw_qos.history) {
    case RMW_QOS_POLICY_HISTORY_KEEP_LAST:
      (void)dds_qos.template policy<History>().kind(dds::core::policy::HistoryKind::KEEP_LAST);
      break;
    case RMW_QOS_POLICY_HISTORY_KEEP_ALL:
      (void)dds_qos.template policy<History>().kind(dds::core::policy::HistoryKind::KEEP_ALL);
      break;
    case RMW_QOS_POLICY_HISTORY_SYSTEM_DEFAULT:
      (void)dds_qos.template policy<History>().kind(default_qos.template policy<History>().kind());
      break;
    case RMW_QOS_POLICY_HISTORY_UNKNOWN:
    default:
      throw RMWException("Unknown QOS history policy");
  }

  // reliability QOS policy
  switch (rmw_qos.reliability) {
    case RMW_QOS_POLICY_RELIABILITY_BEST_EFFORT:
      (void)dds_qos.template policy<Reliability>().kind(ReliabilityKind::BEST_EFFORT);
      break;
    case RMW_QOS_POLICY_RELIABILITY_RELIABLE:
      (void)dds_qos.template policy<Reliability>().kind(ReliabilityKind::RELIABLE);
      break;
    case RMW_QOS_POLICY_RELIABILITY_SYSTEM_DEFAULT:
      // default
      break;
    case RMW_QOS_POLICY_RELIABILITY_UNKNOWN:
    default:
      throw RMWException("Unknown QOS reliability policy");
  }

  // durability QOS policy
  switch (rmw_qos.durability) {
    case RMW_QOS_POLICY_DURABILITY_TRANSIENT_LOCAL:
      (void)dds_qos.template policy<Durability>(Durability::TransientLocal());
      break;
    case RMW_QOS_POLICY_DURABILITY_VOLATILE:
      (void)dds_qos.template policy<Durability>(Durability::Volatile());
      break;
    case RMW_QOS_POLICY_DURABILITY_SYSTEM_DEFAULT:
      // default
      break;
    case RMW_QOS_POLICY_DURABILITY_UNKNOWN:
    default:
      throw RMWException("Unknown QOS durability policy");
  }

  // deadline QOS
  if (!is_time_default(rmw_qos.deadline)) {
    (void)dds_qos.template policy<Deadline>(Deadline(rmw_time_to_dds(rmw_qos.deadline)));
  }

  // lifespan QOS
  update_dds_lifespan_duration(dds_qos, rmw_qos.lifespan);

  // liveliness QOS policy
  switch (rmw_qos.liveliness) {
    case RMW_QOS_POLICY_LIVELINESS_AUTOMATIC:
      (void)dds_qos.template policy<Liveliness>(Liveliness::Automatic());
      break;
    case RMW_QOS_POLICY_LIVELINESS_MANUAL_BY_TOPIC:
      (void)dds_qos.template policy<Liveliness>(Liveliness::ManualByTopic());
      break;
    case RMW_QOS_POLICY_LIVELINESS_SYSTEM_DEFAULT:
      // default
      break;
    case RMW_QOS_POLICY_LIVELINESS_UNKNOWN:
    default:
      throw RMWException("Unknown QoS liveliness policy");
  }

  if (!is_time_default(rmw_qos.liveliness_lease_duration)) {
    (void)dds_qos.template policy<Liveliness>().lease_duration(
      rmw_time_to_dds(rmw_qos.liveliness_lease_duration));
  }
}

template
RMW_APEX_MIDDLEWARE_EXPORT
void rmw_qos_to_dds_qos<dds::pub::qos::DataWriterQos>(
  const rmw_qos_profile_t & rmw_qos,
  const detail::QoSPayload<dds::pub::qos::DataWriterQos> & payload,
  dds::pub::qos::DataWriterQos & dds_qos);

template
RMW_APEX_MIDDLEWARE_EXPORT
void rmw_qos_to_dds_qos<dds::sub::qos::DataReaderQos>(
  const rmw_qos_profile_t & rmw_qos,
  const detail::QoSPayload<dds::sub::qos::DataReaderQos> & payload,
  dds::sub::qos::DataReaderQos & dds_qos);

template<typename DDSQoSType>
void dds_qos_to_rmw_qos(const DDSQoSType & dds_qos, rmw_qos_profile_t & rmw_qos)
{
  rmw_qos = rmw_qos_profile_unknown;
  // history QOS policy
  switch (dds_qos.template policy<History>().kind()) {
    case dds::core::policy::HistoryKind::KEEP_LAST:
      rmw_qos.history = RMW_QOS_POLICY_HISTORY_KEEP_LAST;
      break;
    case dds::core::policy::HistoryKind::KEEP_ALL:
      rmw_qos.history = RMW_QOS_POLICY_HISTORY_KEEP_ALL;
      break;
    default:
      rmw_qos.history = RMW_QOS_POLICY_HISTORY_UNKNOWN;
      break;
  }
  rmw_qos.depth = apex::cast::safe_cast<size_t>(dds_qos.template policy<History>().depth());

  // reliability QOS policy
  switch (dds_qos.template policy<Reliability>().kind()) {
    case ReliabilityKind::BEST_EFFORT:
      rmw_qos.reliability = RMW_QOS_POLICY_RELIABILITY_BEST_EFFORT;
      break;
    case ReliabilityKind::RELIABLE:
      rmw_qos.reliability = RMW_QOS_POLICY_RELIABILITY_RELIABLE;
      break;
    default:
      rmw_qos.reliability = RMW_QOS_POLICY_RELIABILITY_UNKNOWN;
      break;
  }

  // durability QOS policy
  switch (dds_qos.template policy<Durability>().kind()) {
    case dds::core::policy::DurabilityKind::TRANSIENT_LOCAL:
      rmw_qos.durability = RMW_QOS_POLICY_DURABILITY_TRANSIENT_LOCAL;
      break;
    case dds::core::policy::DurabilityKind::VOLATILE:
      rmw_qos.durability = RMW_QOS_POLICY_DURABILITY_VOLATILE;
      break;
    case dds::core::policy::DurabilityKind::TRANSIENT:
    case dds::core::policy::DurabilityKind::PERSISTENT:
    default:
      rmw_qos.durability = RMW_QOS_POLICY_DURABILITY_UNKNOWN;
      break;
  }

  // deadline QOS
  rmw_qos.deadline = dds_time_to_rmw(dds_qos.template policy<Deadline>().period());

  // lifespan QOS
  update_rmw_lifespan_duration(rmw_qos, dds_qos);

  // liveliness QOS policy
  switch (dds_qos.template policy<Liveliness>().kind()) {
    case dds::core::policy::LivelinessKind::AUTOMATIC:
      rmw_qos.liveliness = RMW_QOS_POLICY_LIVELINESS_AUTOMATIC;
      break;
    case dds::core::policy::LivelinessKind::MANUAL_BY_TOPIC:
      rmw_qos.liveliness = RMW_QOS_POLICY_LIVELINESS_MANUAL_BY_TOPIC;
      break;
    default:
      rmw_qos.liveliness = RMW_QOS_POLICY_LIVELINESS_UNKNOWN;
      break;
  }
  rmw_qos.liveliness_lease_duration =
    dds_time_to_rmw(dds_qos.template policy<Liveliness>().lease_duration());
}

template
void dds_qos_to_rmw_qos<dds::pub::qos::DataWriterQos>(
  const dds::pub::qos::DataWriterQos & dds_qos,
  rmw_qos_profile_t & qos);

template
void dds_qos_to_rmw_qos<dds::sub::qos::DataReaderQos>(
  const dds::sub::qos::DataReaderQos & dds_qos,
  rmw_qos_profile_t & qos);

}  // namespace rmw_apex_middleware
}  // namespace apex
