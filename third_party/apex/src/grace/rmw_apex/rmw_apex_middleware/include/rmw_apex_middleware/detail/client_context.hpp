/// \copyright Copyright 2020 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file provides definition of client context class for rmw_apex_middleware

#ifndef RMW_APEX_MIDDLEWARE__DETAIL__CLIENT_CONTEXT_HPP_
#define RMW_APEX_MIDDLEWARE__DETAIL__CLIENT_CONTEXT_HPP_

#include <rmw/types.h>
#include <string/string.hpp>
#include <rosidl_typesupport_apex_middleware_cpp/service_type_support.hpp>
#include <rmw_apex_middleware/detail/node_context.hpp>
#include <memory>

namespace apex
{
namespace rmw_apex_middleware
{
namespace detail
{
/// \brief Class to contain the ServiceRequester and associated metadata for a Service Client
/// \cert
class ClientContext
{
public:
  using ServiceTypeSupportInterface =
    rosidl_typesupport_apex_middleware_cpp::ServiceTypeSupportInterface;

  using ServiceRequesterInterface =
    rosidl_typesupport_apex_middleware_cpp::ServiceRequesterInterface;

  /// \brief Creates a new ClientContext object for a given service type using a given service name
  /// \param node_context NodeContext for the node that owns this client
  /// \param rmw_qos QOS configuration for the client to use
  /// \param type_support_interface Type support for the service message type
  /// \param service_name Name of the service
  /// \cert
  ClientContext(
    NodeContext & node_context,
    const rmw_qos_profile_t rmw_qos,
    const ServiceTypeSupportInterface & type_support_interface,
    const apex::string_strict256_t & service_name);

  /// \cert
  ~ClientContext();

  /// \brief Extracts a valid ClientContext from an opaque handle
  /// \param[in] pointer Opaque handle holding a ClientContext
  /// \returns Valid ClientContext
  /// \throws std::invalid_argument if pointer is invalid
  /// \cert
  static
  ClientContext &
  from_void(void * pointer);

  /// \brief Gets the ReadCondition associated with the DataReader of the service response
  /// \return return ReadCondition
  /// \cert
  /// \deterministic
  dds::sub::cond::ReadCondition & read_condition()
  {
    auto & req = *rmw_apex_middleware::RMWPointerChecker::expect_not_null(
      m_requester,
      "Requester interface is null"
    );
    return req.get_readcondition();
  }

  /// \brief Gets the StatusCondition associated with the DataReader of the service response
  /// \return return StatusCondition
  /// \cert
  /// \deterministic
  dds::core::cond::StatusCondition & status_condition()
  {
    auto & req = *rmw_apex_middleware::RMWPointerChecker::expect_not_null(
      m_requester,
      "Requester interface is null"
    );
    return req.get_statuscondition();
  }

  /// \brief Gets the name of the Service associated with this Client
  /// \returns String containing the Service name
  /// \cert
  const apex::string_strict256_t &
  get_service_name() const;

  /// \brief Gets the internal ServiceTypeSupportInterface for the Service message types
  /// \cert
  const ServiceTypeSupportInterface &
  get_service_type_support_interface() const;

  /// \brief Gets the ServiceRequesterInterface used by this Client for making requests to the
  /// Service
  /// \cert
  ServiceRequesterInterface *
  get_requester_interface() const;

private:
  void init_guid();
  void init_guid_impl(dds_entity_t dds_entity, rmw_gid_t & gid);

  /// The NodeContext to be used by this Client
  NodeContext & m_node_context;
  /// The type specific interface for handling the user defined request and response objects.
  const ServiceTypeSupportInterface & m_type_support_interface;
  /// The name to be used for the service.
  const apex::string_strict256_t m_service_name;
  /// Fully qualified type name
  apex::string_strict256_t m_full_type_name;
  /// GUID used for graph info updates
  rmw_gid_t m_reader_gid;
  rmw_gid_t m_writer_gid;

  /// Reference to the interface base class for an instance of the type-specific replier.
  std::unique_ptr<ServiceRequesterInterface> m_requester;
};

}  // namespace detail
}  // namespace rmw_apex_middleware
}  // namespace apex

#endif  // RMW_APEX_MIDDLEWARE__DETAIL__CLIENT_CONTEXT_HPP_
