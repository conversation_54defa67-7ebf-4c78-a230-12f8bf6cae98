/// \copyright Copyright 2020 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file provides definition of node context class for rmw_apex_middleware

#ifndef RMW_APEX_MIDDLEWARE__DETAIL__NODE_CONTEXT_HPP_
#define RMW_APEX_MIDDLEWARE__DETAIL__NODE_CONTEXT_HPP_

#include <rmw_apex_middleware/rmw_apex_middleware.hpp>
#include <rmw_apex_middleware/utils/error_handling.hpp>
#include <rmw_apex_middleware/detail/rmw_apex_middleware_context.hpp>
#include <rmw_apex_middleware/detail/discovery_info_manager.hpp>
#include <cyclone_dds_vendor/dds.hpp>
#include <apex_middleware_support/apex_middleware_context.hpp>
#include <containers/static_vector.hpp>
#include <cpputils/common_exceptions.hpp>
#include <cpputils/safe_cast.hpp>
#include <cpputils/optional.hpp>

#include <limits>
#include <random>
#include <vector>
#include <mutex>

namespace apex
{
namespace rmw_apex_middleware
{
namespace detail
{
/// \brief Class for context keeper for a node
/// \cert
class NodeContext final
{
public:
  /// \brief Creates node context object for given node name and node name space
  /// Also responsible for writing the graph info sample
  /// \param[in] node_name name of the node
  /// \param[in] namespace_name node namespace
  /// \cert
  NodeContext(
    const apex::string_strict256_t node_name,
    const apex::string_strict256_t namespace_name,
    rmw_context_impl_t & rmw_context_impl)
  : m_node_name(node_name),
    m_namespace_name(namespace_name),
    m_rmw_context_impl(&rmw_context_impl)
  {
    using rmw_apex_middleware::get_implementation_identifier;

    m_guard_condition.implementation_identifier = get_implementation_identifier();
    m_guard_condition.data =
      &(m_rmw_context_impl->discovery_info_manager().get_graph_guard_condition());
    m_rmw_context_impl->discovery_info_manager().add_node(node_name, namespace_name);
    m_rmw_context_impl->incref();
    // TODO(Sumanth): replace with a proper GUID generator
    init_guid();
  }

  /// \brief Gets the discovery info manager
  /// \return returns DiscoveryInfoManager
  /// \cert
  DiscoveryInfoManager & discovery_info_manager() const
  {
    return m_rmw_context_impl->discovery_info_manager();
  }

  /// \brief Gets the middleware context
  /// \return returns ApexMiddlewareContext
  /// \cert
  ApexMiddlewareContext & mw_context() const
  {
    return m_rmw_context_impl->mw_context();
  }

  /// \brief Gets the domain participant
  /// \return returns DDSDomainParticipant
  /// \cert
  dds::domain::DomainParticipant & participant()
  {
    return m_rmw_context_impl->mw_context().get_domain_participant();
  }

  /// \brief Gets the name of the node associated with this node
  /// \return returns node name
  /// \cert
  /// \deterministic
  const apex::string_strict256_t & node_name() const noexcept
  {
    return m_node_name;
  }

  /// \brief Gets the node namespace associated with this node context
  /// \return returns node namespace
  /// \cert
  /// \deterministic
  const apex::string_strict256_t & namespace_name() const noexcept
  {
    return m_namespace_name;
  }

  /// \brief Gets the global identifier associated with this node
  /// \return returns the gid
  /// \cert
  /// \deterministic
  rmw_gid_t gid() const noexcept
  {
    return m_gid;
  }

  /// \brief Add the requested publisher instance handle to the list of the publisher of
  ///        this node
  /// This uses lock to protect shared data, list of publisher instance handle.
  /// \param[in] instance_handle instance handle requested publisher
  /// \cert
  void add_publisher_instance_handle(const dds_instance_handle_t instance_handle)
  {
    const std::lock_guard<std::mutex> lock(m_publisher_guid_mtx);
    m_publisher_instance_handle_list.push_back(instance_handle);
    // mutex is automatically released when lock goes out of scope
  }

  /// \brief Delete the requested instance handle from list of the publisher of this
  ///        node
  /// This uses lock to protect shared data, list of publisher instance handle.
  /// \param[in] instance_handle instance handle requested publisher
  /// \cert
  void remove_publisher_instance_handle(const dds_instance_handle_t instance_handle)
  {
    const std::lock_guard<std::mutex> lock(m_publisher_guid_mtx);
    (void)m_publisher_instance_handle_list.erase(
      std::remove(
        m_publisher_instance_handle_list.begin(),
        m_publisher_instance_handle_list.end(),
        instance_handle),
      m_publisher_instance_handle_list.end());
    // mutex is automatically released when lock goes out of scope
  }

  /// \brief This compares the given instance handle to determine if the publisher is from
  ///        this node
  /// This uses lock to protect shared data, list of publisher instance handle.
  /// \param[in] instance_handle instance handle requested publisher
  /// \return returns true if the publisher if from this node, False otherwise
  /// \cert
  bool is_publisher_from_node(const dds_instance_handle_t instance_handle)
  {
    const std::lock_guard<std::mutex> lock(m_publisher_guid_mtx);
    const auto iterator = std::find(
      m_publisher_instance_handle_list.begin(),
      m_publisher_instance_handle_list.end(),
      instance_handle
    );
    return iterator != m_publisher_instance_handle_list.end();
    // mutex is automatically released when lock goes out of scope
  }

  /// \brief  The destructor responsible for disposing the graph info sample corresponding to this
  /// node context.
  /// \cert
  ~NodeContext()
  {
    discovery_info_manager().remove_node(m_node_name, m_namespace_name);
    if (m_rmw_context_impl->decref()) {
      m_rmw_context_impl->~rmw_context_impl_t();
      rmw_free(m_rmw_context_impl);
    }
  }

  /// \brief Extracts a valid NodeContext from an opaque handle
  /// \param[in] ptr Opaque handle holding a NodeContext
  /// \return Valid NodeContext
  /// \throws std::invalid_argument if pointer is invalid
  /// \cert
  /// \deterministic
  static NodeContext & from_void(void * ptr)
  {
    if (ptr == nullptr) {
      throw apex::invalid_argument("NodeContext pointer is null.");
    }
    /*
     AXIVION Next Line MisraC++2023-8.2.6: Reason: Code Quality (Functional suitability),
     Justification: The caller ensures this is a valid cast
     */
    return *static_cast<NodeContext *>(ptr);
  }


  /// \brief Gets the rmw guard condition
  /// \return returns the rmw guard condition
  /// \cert
  rmw_guard_condition_t & rmw_guard_condition()
  {
    //lint -e{1536} NOLINT - Intended behavior is to expose private member
    return m_guard_condition;
  }

  /// \brief Gets the rmw guard condition
  /// \return returns the rmw guard condition
  /// \cert
  const rmw_guard_condition_t & rmw_guard_condition() const
  {
    return m_guard_condition;
  }

private:
  /// \brief Initialises the global unique identifier for the node
  void init_guid()
  {
    std::random_device rd;
    std::mt19937_64 rng(rd());

    /*
     AXIVION Next Line MisraC++2023-11.3.1: C-style arrays used for direct memory copy
     */
    uint64_t rnd[3];
    rnd[0] = rng();
    rnd[1] = rng();
    rnd[2] = rng();

    static_assert(sizeof(m_gid.data) >= sizeof(rnd), "GUID is not expected size");
    /*lint -e{857} Copying between different types is intended and allowed through memcpy */
    // this will copy 24 bytes of rnd to the 24 byte field in m_gid.data
    (void)std::memcpy(&m_gid.data, &rnd, sizeof(m_gid.data));

    using rmw_apex_middleware::get_implementation_identifier;
    m_gid.implementation_identifier = get_implementation_identifier();
  }

  const apex::string_strict256_t m_node_name;  ///< node name
  const apex::string_strict256_t m_namespace_name;  ///< node name space
  rmw_context_impl_t * const m_rmw_context_impl{nullptr};  ///< context impl, shared ownership
  rmw_gid_t m_gid{};  ///< unique global identifier for node

  ///< list of publisher instance handle belonging to this node
  /// TODO (Misha): Replace with a static one when Cyclone is also static
  apex::static_vector<dds_instance_handle_t> m_publisher_instance_handle_list{512U};
  ///< mutex for accessing the publisher GUIDS
  std::mutex m_publisher_guid_mtx;

  rmw_guard_condition_t m_guard_condition;  ///< guard condition
};
}  // namespace detail
}  // namespace rmw_apex_middleware
}  // namespace apex

#endif  // RMW_APEX_MIDDLEWARE__DETAIL__NODE_CONTEXT_HPP_
