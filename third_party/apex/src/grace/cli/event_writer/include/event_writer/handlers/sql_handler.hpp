/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef EVENT_WRITER__SQL_HPP
#define EVENT_WRITER__SQL_HPP

#include <string>

#include "event_writer/event_handler.hpp"
#include "event_writer/types.hpp"
#include "event_writer/visibility.hpp"
#include "sqlite3.h"  // NOLINT

namespace apex::event::dump
{

/// \brief An event handler that saves events to an SQLite database
class EVENT_WRITER_PUBLIC sql_handler : public event_handler
{
public:
  /// \brief Constructs the handler
  /// \param filename The name of the SQLite file to create the table in
  /// (if not exists yet)
  /// \param table_name The name of the table in the database
  sql_handler(const std::string & filename, std::string table_name);

  /// \brief Closes the database file
  // if an exception is thrown, we want to terminate
  ~sql_handler() override;

  /// \brief The event handler function
  /// \param ev The event to handle
  void operator()(types::event_cref_t ev) override;

private:
  /// \brief Executes an SQL command
  void execute(const char * command);

  sqlite3 * m_db{nullptr};
  std::string m_table_name;
};
}  // namespace apex::event::dump

#endif  // EVENT_WRITER__SQL_HPP
