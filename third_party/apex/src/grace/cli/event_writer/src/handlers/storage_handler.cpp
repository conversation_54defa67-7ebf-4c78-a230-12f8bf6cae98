/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include "event_writer/handlers/storage_handler.hpp"

#include <utility>

#include "rclcpp/rclcpp.hpp"

namespace apex::event::dump
{
storage_handler::storage_handler(rclcpp::Node::SharedPtr node,
                                 const std::string & instance_name,
                                 const std::string & database_name,
                                 std::uint32_t history_size)
: m_node{std::move(node)}
{
  assert(m_node != nullptr);
  const auto com = apex::storage::communication::create(m_node, instance_name);
  const auto db = com->get_database(database_name);
  m_store = db->get_store<types::event_storage_t>(0U, history_size);
}

void storage_handler::operator()(types::event_cref_t ev)
{
  types::event_storage_t e;
  e.data = ev;
  m_store->save(e);
}
}  // namespace apex::event::dump
