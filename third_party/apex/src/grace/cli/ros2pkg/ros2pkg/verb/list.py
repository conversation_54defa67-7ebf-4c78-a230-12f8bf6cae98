# Copyright 2017 Open Source Robotics Foundation, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from ros2pkg.api import get_package_names
from ros2pkg.verb import VerbExtension


class ListVerb(VerbExtension):
    """Output a list of available packages."""

    def main(self, *, args):
        for pkg_name in sorted(get_package_names()):
            print(pkg_name)
