load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "ros2lifecycle_test_fixtures_pkg",
    description = "Package containing fixture nodes for ros2lifecycle tests.",
    lib_executables = [":simple_lifecycle_node"],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.13.0",
    visibility = ["//visibility:public"],
)

ament_pkg_resources(
    name = "ament_resources",
    package = "ros2lifecycle_test_fixtures",
    resources = {
        ":simple_lifecycle_node": "executable",
    },
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "simple_lifecycle_node",
    srcs = ["src/simple_lifecycle_node.cpp"],
    tags = ["exclude_sca"],
    deps = [
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_lifecycle",
    ],
)
