// Copyright 2022 Apex.AI, Inc.
// All rights reserved.


#include "test_executor_common.hpp"

#include <gtest/gtest.h>

#include <rclcpp/rclcpp.hpp>
#include <rclcpp/dynamic_waitset/file_monitor.hpp>
#include <executor2/executor_factory.hpp>
#include <executor2/executor_runner.hpp>
#include <executor2/executable_item.hpp>
#include <executor2/graph.hpp>
#include <executor2/execution_monitor.hpp>
#include <execution_monitor/communication_stub.hpp>
#include <execution_monitor/communication.hpp>
#include <std_msgs/msg/int32.hpp>

#include <string>
#include <memory>
#include <atomic>
#include <utility>
#include <vector>
#include <algorithm>
#include <thread>


using namespace std::chrono_literals;
using namespace test_common;  // NOLINT
using apex::executor::task_id_t;
using apex::executor::task_id_cref_t;

namespace
{
class test_executor_with_execution_monitor : public ::testing::Test
{
protected:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
    node = std::make_shared<rclcpp::Node>("test_executor");
    pub = node->create_publisher<MsgType>(TriggeringTopic, qos());
    another_pub = node->create_publisher<MsgType>(AnotherTriggeringTopic, qos());
    m_executor = apex::executor::executor_factory::create();
    m_monitor = std::make_unique<apex::executor::execution_monitor>(monitor_name, m_comm);
    m_executor->set_execution_monitor(*m_monitor);
  }

  void TearDown() override
  {
    rclcpp::shutdown();
  }

  /// \brief Destroys the old executor and creates a new one
  void reset_executor()
  {
    // Change the monitor name after the reset
    monitor_name += "_";
    m_executor = apex::executor::executor_factory::create();
    m_monitor = std::make_unique<apex::executor::execution_monitor>(monitor_name, m_comm);
    m_executor->set_execution_monitor(*m_monitor);
  }

  /// Publish `num_of_msgs` messages, separated by `pause` milliseconds.
  /// `another_topic` decides whether to publish to `TriggeringTopic` (false) or
  /// `AnotherTriggeringTopic` (true).
  void publish(
    std::size_t num_of_msgs = 10U,
    std::chrono::milliseconds pause = 10ms,
    bool another_topic = false) const
  {
    for (auto i = 0U; i < num_of_msgs; ++i) {
      MsgType m;
      if (another_topic) {
        another_pub->publish(m);
      } else {
        pub->publish(m);
      }
      std::this_thread::sleep_for(pause);
    }
  }

  static std::chrono::nanoseconds now_ns()
  {
    return std::chrono::nanoseconds{std::chrono::steady_clock::now().time_since_epoch()};
  }

  std::size_t num_infractions(
    task_id_cref_t task_id,
    std::uint64_t expectation,
    apex::optional<std::chrono::nanoseconds> timestamp = {})
  {
    return m_comm.num_infractions(monitor_name,
             task_id,
             expectation,
             timestamp ? *timestamp : now_ns());
  }

  rclcpp::Node::SharedPtr node;
  rclcpp::Publisher<MsgType>::SharedPtr pub;
  rclcpp::Publisher<MsgType>::SharedPtr another_pub;
  apex::execution_monitor::communication_stub m_comm;
  std::unique_ptr<apex::executor::execution_monitor> m_monitor;
  apex::executor::live_executor_ptr m_executor;
  std::string monitor_name {"test_monitor"};
};

class test_execution_monitor_doc : public ::testing::Test
{
protected:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
  }

  void TearDown() override
  {
    rclcpp::shutdown();
  }
};

std::chrono::nanoseconds get_current_cpu_clock_time()
{
  ::timespec time{0, 0};
  if (clock_gettime(CLOCK_THREAD_CPUTIME_ID, &time) < 0) {
    throw apex::system_error(errno, "Failed to get per-clock CPU time");
  }
  return std::chrono::nanoseconds(
    (apex::cast::safe_cast<uint64_t>(time.tv_sec) * 1'000'000'000) +
    apex::cast::safe_cast<uint64_t>(time.tv_nsec));
}
void waste_cpu_time(std::chrono::nanoseconds cpu_time_to_waste)
{
  const auto start = get_current_cpu_clock_time();
  while (get_current_cpu_clock_time() < start + cpu_time_to_waste) {
    // do nothing
  }
}

namespace expect = apex::execution_monitor::expectations;

// cppcheck-suppress syntaxError
TEST_F(test_executor_with_execution_monitor, expectations_are_tied_to_correct_task_id) {
  auto node1 = std::make_shared<node_that_has_a_sub>();
  auto node2 = std::make_shared<node_that_has_a_sub>(false, AnotherTriggeringTopic);

  // The expectation for task1 will be fulfilled by node_that_has_a_sub, the one for task2 not
  // By checking when the monitor signals an error, we can observe which task is which
  auto task1 = m_executor->add({expect::wall_clock_runtime(100ms)}, node1);
  auto task2 = m_executor->add({expect::wall_clock_runtime(100ms, 100ms)}, node2);
  m_monitor->registrations_complete();

  publish(1);
  ASSERT_NO_THROW(m_executor->run(1s, 1));
  ASSERT_EQ(num_infractions(task1, 0), 0);
  ASSERT_EQ(num_infractions(task2, 0), 0);

  publish(1, 10ms, true);
  ASSERT_NO_THROW(m_executor->run(1s, 1));
  ASSERT_EQ(num_infractions(task1, 0), 0);
  ASSERT_EQ(num_infractions(task2, 0), 1);
}

TEST_F(test_executor_with_execution_monitor, executor_checks_that_all_tasks_have_an_expectation) {
  auto node1 = std::make_shared<node_that_has_a_sub>();
  auto node2 = std::make_shared<node_that_has_a_sub>(false, AnotherTriggeringTopic);

  // Assign a monitor that has two tasks but only has expectations for the first task.
  (void) m_executor->add({expect::wall_clock_runtime(100ms)}, node1);
  m_monitor->registrations_complete();
  m_executor->add(node2);
  // Running the executor should throw as no expectation is configured for the task.
  ASSERT_THROW(m_executor->run(apex::executor::InfiniteWait, 0U), apex::logic_error);
}

TEST_F(test_executor_with_execution_monitor, execution_monitor_monitors_sub_tasks) {
  for (auto i = 0U; i < 2U; ++i) {
    const auto node = std::make_shared<node_that_has_a_sub>();
    apex::executor::task_id_t task;
    if ((i % 2) != 0) {
      task = m_executor->add(apex::executor::vertex{node});
    } else {
      task = m_executor->add(node);
    }

    const apex::execution_monitor::expectation_list
      node_never_triggers = {expect::activations_per_window(0, 0, 2s)};
    m_monitor->register_task(task, {node_never_triggers});
    m_monitor->registrations_complete();
    ASSERT_EQ(num_infractions(task, 0), 0);
    publish(1);
    ASSERT_NO_THROW(m_executor->run(1s, 1));
    ASSERT_EQ(num_infractions(task, 0), 1);

    reset_executor();
  }
}

TEST_F(test_executor_with_execution_monitor, execution_monitor_monitors_file_tasks) {
  auto pipe = create_pipe();
  auto guard = create_pipe_guard(pipe);
  auto node = std::make_shared<node_with_files>(std::vector<std::int32_t>{pipe[PipeIn]});
  rclcpp::dynamic_waitset::file_monitor file_monitor;
  m_executor->set_file_monitor(file_monitor);

  auto task = m_executor->add(node);
  const apex::execution_monitor::expectation_list
    node_never_triggers = {expect::activations_per_window(0, 0, 2s)};
  m_monitor->register_task(task, {node_never_triggers});
  m_monitor->registrations_complete();
  ASSERT_EQ(num_infractions(task, 0), 0);
  write_pipe(pipe, 1);
  ASSERT_NO_THROW(m_executor->run(1s, 1));
  ASSERT_EQ(num_infractions(task, 0), 1);
}

TEST_F(test_executor_with_execution_monitor, execution_monitor_exceeded_expectations) {
  // Run an execution condition that blocks/spins for a given time, check that the two runtime
  // monitors include it in their observation
  auto node_sub = std::make_shared<node_that_has_a_sub>(false, TriggeringTopic,
      [](const auto &) {
        waste_cpu_time(500us);
        return true;
      }
  );
  auto node_other_sub = std::make_shared<node_that_has_a_sub>(false, AnotherTriggeringTopic,
      [](const auto &) {
        std::this_thread::sleep_for(5ms);
        return true;
      }
  );

  auto cpu_waster_task = m_executor->add(node_sub);
  m_monitor->register_task(cpu_waster_task, {expect::cpu_clock_runtime(450us)});

  auto sleeper_task = m_executor->add(node_other_sub);
  m_monitor->register_task(sleeper_task, {expect::wall_clock_runtime(2ms)});
  m_monitor->registrations_complete();

  publish(1U);
  ASSERT_NO_THROW(m_executor->run(1s, 1));
  // The execution condition exceeded the CPU-time budget
  ASSERT_EQ(num_infractions(cpu_waster_task, 0), 1);
  ASSERT_EQ(num_infractions(sleeper_task, 0), 0);

  publish(1U, 10ms, true);
  ASSERT_NO_THROW(m_executor->run(1s, 1));
  ASSERT_EQ(num_infractions(cpu_waster_task, 0), 1);
  ASSERT_EQ(num_infractions(sleeper_task, 0), 1);
}

TEST_F(test_executor_with_execution_monitor,
  activation_monitor_counts_executions_prevented_by_execution_condition) {
  // Run a task with an always-false execution condition. Check that the two activation
  // monitors count the blocked activation as "activation".
  // This test can be easily flipped if we change the behavior
  auto node = std::make_shared<node_that_has_a_sub>(false, TriggeringTopic,
      [](const auto &) {return false;});

  auto task = m_executor->add(node);
  m_monitor->register_task(task, {expect::activations_per_window(
        1, expect::activations_per_window::infinity, 5ms)});
  m_monitor->registrations_complete();
  // Trigger the node and run one iteration of the executor.
  publish(1);
  // Note that limiting the executor to one iteration prevents a busy-loop here: the execution
  // condition prevents `node` from running, so the waiting sample will keep triggering the waitset
  ASSERT_NO_THROW(m_executor->run(1s, 1));
  ASSERT_EQ(num_infractions(task, 0), 0);
}

}  // namespace
