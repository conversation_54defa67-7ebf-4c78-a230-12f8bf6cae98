/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTOR2__CONNECTOR__SLOT_NODE_HPP_
#define EXECUTOR2__CONNECTOR__SLOT_NODE_HPP_

#include <executor2/details/slot_executable_item.hpp>
#include <executor2/executable_item.hpp>
#include <executor2/connector/common.hpp>

#include <type_traits>
#include <functional>
#include <string>
#include <string_view>  // NOLINT
#include <memory>
#include <utility>

namespace apex
{
namespace executor
{
namespace connector
{
namespace details
{
/// \brief Helper for validating and selecting a slot-compatible callable type
/// \tparam T Message type
/// \tparam F User-provided callable type
/// \cert
/// \deterministic
template<class T, class F, class = void>
struct valid_slot_function : std::false_type {};

/// \brief Helper for validating and selecting a slot-compatible callable type
/// A specialization for bool() callables
/// \tparam T Message type
/// \tparam F User-provided callable type
/// \cert
/// \deterministic
template<class T, class F>
struct valid_slot_function<T, F,
  std::enable_if_t<std::is_same_v<std::invoke_result_t<F>, bool>>>
  : std::true_type
{
  using type = std::function<bool ()>;
};

/// \brief Helper for validating and selecting a slot-compatible callable type
/// A specialization for void() callables
/// \tparam T Message type
/// \tparam F User-provided callable type
/// \cert
/// \deterministic
template<class T, class F>
struct valid_slot_function<T, F,
  std::enable_if_t<std::is_same_v<std::invoke_result_t<F>, void>>>
  : std::true_type
{
  using type = std::function<void ()>;
};

/// \brief Helper for validating and selecting a slot-compatible callable type
/// A specialization for bool(T::BorrowedType &) callables
/// \tparam T Message type
/// \tparam F User-provided callable type
/// \cert
/// \deterministic
template<class T, class F>
struct valid_slot_function<T, F,
  std::enable_if_t<std::is_same_v<std::invoke_result_t<F, const typename T::BorrowedType &>, bool>>>
  : std::true_type
{
  using type = std::function<bool (const typename T::BorrowedType &)>;
};

/// \brief Helper for validating and selecting a slot-compatible callable type
/// A specialization for void(T::BorrowedType &) callables
/// \tparam T Message type
/// \tparam F User-provided callable type
/// \cert
/// \deterministic
template<class T, class F>
struct valid_slot_function<T, F,
  std::enable_if_t<std::is_same_v<std::invoke_result_t<F, const typename T::BorrowedType &>, void>>>
  : std::true_type
{
  using type = std::function<void (const typename T::BorrowedType &)>;
};

/// \brief A concept for enabling signature only for valid callables
/// \tparam T Message type
/// \tparam F User-provided callable type
/// \cert
/// \deterministic
template<class T, class F>
using enable_for_valid_slot_function = std::enable_if_t<
  valid_slot_function<T, F>::value,
  executable_item_ptr
>;

/// \brief A trait for selecting a compatible concrete callable type
/// \tparam T Message type
/// \tparam F User-provided callable type
/// \cert
/// \deterministic
template<class T, class F>
using select_slot_function_type = typename valid_slot_function<T, F>::type;
}  // namespace details

/// \brief Create a slot executable item
/// \tparam T Message type
/// \tparam Policy A policy for taking the messages (one or all at single execution)
/// \param topic_name The topic name
/// \param func A callable to call after receiving a slot message
/// \param qos QoS settings for the subscriber of the slot
/// \param app_id Application id
/// \param instance_id The application instance id
/// \param namespace_ An explicit namespace for the slot node
/// if not provided, the namespace will be taken from the topic_name
/// \return An executable item implementing the slot
/// \cert
template<class T = default_message_type, class Policy = take_one, class U>
details::enable_for_valid_slot_function<T, U>
create_slot(
  const std::string & topic_name,
  U && func,
  rclcpp::QoS qos = rclcpp::DefaultQoS(),
  std::string_view app_id = "",
  apex::event::types::instance_id_t instance_id = 0U,
  const std::string & namespace_ = ""
)
{
  return std::make_shared<details::slot_executable_item<T, Policy,
           details::select_slot_function_type<T, U>>>(
    topic_name,
    std::forward<U>(func),
    qos,
    app_id,
    instance_id,
    namespace_);
}

/// \brief Create a slot executable item
/// \tparam T Message type
/// \tparam Policy A policy for taking the messages (one or all at single execution)
/// \param topic_name The topic name
/// \param qos QoS settings for the subscriber of the slot
/// \param app_id Application id
/// \param instance_id The application instance id
/// \param namespace_ An explicit namespace for the slot node
/// if not provided, the namespace will be taken from the topic_name
/// \return An executable item implementing the slot
/// \cert
template<class T = default_message_type, class Policy = take_one>
executable_item_ptr create_slot(
  const std::string & topic_name,
  rclcpp::QoS qos = rclcpp::DefaultQoS(),
  std::string_view app_id = "",
  apex::event::types::instance_id_t instance_id = 0U,
  const std::string & namespace_ = ""
)
{
  return std::make_shared<details::slot_executable_item<T, Policy, void>>(
    topic_name,
    qos,
    app_id,
    instance_id,
    namespace_
  );
}

/// \brief Create a signal executable item
/// \tparam T Message type
/// \param node The parent node
/// \param topic_name A custom topic name
/// \param func A callable to call after receiving a slot message
/// \param qos QoS settings for the subscriber of the slot
/// \param app_id Application id
/// \param instance_id The application instance id
/// \return An executable item implementing the signal
/// \cert
template<class T = default_message_type, class Policy = take_one, class U>
details::enable_for_valid_slot_function<T, U>
create_slot(
  rclcpp::Node::SharedPtr node,
  const std::string & topic_name,
  U && func,
  rclcpp::QoS qos = rclcpp::DefaultQoS(),
  std::string_view app_id = "",
  apex::event::types::instance_id_t instance_id = 0U
)
{
  return std::make_shared<details::slot_executable_item<T, Policy,
           details::select_slot_function_type<T, U>>>(
    std::move(node),
    topic_name,
    std::forward<U>(func),
    qos,
    app_id,
    instance_id);
}

/// \brief Create a signal executable item
/// \tparam T Message type
/// \param node The parent node
/// \param topic_name The topic name
/// \param qos QoS settings for the subscriber of the slot
/// \param app_id Application id
/// \param instance_id The application instance id
/// \return An executable item implementing the signal
/// \cert
template<class T = default_message_type, class Policy = take_one>
executable_item_ptr create_slot(
  rclcpp::Node::SharedPtr node,
  const std::string & topic_name,
  rclcpp::QoS qos = rclcpp::DefaultQoS(),
  std::string_view app_id = "",
  apex::event::types::instance_id_t instance_id = 0U
)
{
  return std::make_shared<details::slot_executable_item<T, Policy, void>>(
    std::move(node),
    topic_name,
    qos,
    app_id,
    instance_id
  );
}

/// \brief Create a slot executable item cloning from an existing subscription
/// \tparam Policy A policy for taking the messages (one or all at single execution)
/// \param sub The subscription to clone into the slot
/// \param func A callable to call after receiving a slot message
/// \param app_id Application id
/// \param instance_id The application instance id
/// \param namespace_ An explicit namespace for the slot node
/// if not provided, the namespace will be taken from the topic_name
/// \return An executable item implementing the slot
/// \cert
template<class Policy = take_one, class T, class U>
details::enable_for_valid_slot_function<T, U>
create_slot_from_subscription(
  const rclcpp::PollingSubscription<T> & sub,
  U && func,
  std::string_view app_id = "",
  apex::event::types::instance_id_t instance_id = 0U,
  const std::string & namespace_ = ""
)
{
  return create_slot<T, Policy>(sub.get_topic_name(),
           std::forward<U>(func),
           sub.get_actual_qos(),
           app_id,
           instance_id,
           namespace_);
}

/// \brief Create a slot executable item cloning from an existing subscription
/// \tparam Policy A policy for taking the messages (one or all at single execution)
/// \param sub The subscription to clone into the slot
/// \param app_id Application id
/// \param instance_id The application instance id
/// \param namespace_ An explicit namespace for the slot node
/// if not provided, the namespace will be taken from the topic_name
/// \return An executable item implementing the slot
/// \cert
template<class Policy = take_one, class T>
executable_item_ptr
create_slot_from_subscription(
  const rclcpp::PollingSubscription<T> & sub,
  std::string_view app_id = "",
  apex::event::types::instance_id_t instance_id = 0U,
  const std::string & namespace_ = ""
)
{
  return create_slot<T, Policy>(sub.get_topic_name(),
           sub.get_actual_qos(),
           app_id,
           instance_id,
           namespace_);
}

/// \brief Create a slot executable item cloning from an existing subscription
/// \tparam Policy A policy for taking the messages (one or all at single execution)
/// \param node The parent node
/// \param sub The subscription to clone into the slot
/// \param func A callable to call after receiving a slot message
/// \param app_id Application id
/// \param instance_id The application instance id
/// \return An executable item implementing the slot
/// \cert
template<class Policy = take_one, class T, class U>
details::enable_for_valid_slot_function<T, U>
create_slot_from_subscription(
  rclcpp::Node::SharedPtr node,
  const rclcpp::PollingSubscription<T> & sub,
  U && func,
  std::string_view app_id = "",
  apex::event::types::instance_id_t instance_id = 0U
)
{
  return create_slot<T, Policy>(
    std::move(node),
    sub.get_topic_name(),
    std::forward<U>(func),
    sub.get_actual_qos(),
    app_id,
    instance_id);
}

/// \brief Create a slot executable item cloning from an existing subscription
/// \tparam Policy A policy for taking the messages (one or all at single execution)
/// \param node The parent node
/// \param sub The subscription to clone into the slot
/// \param app_id Application id
/// \param instance_id The application instance id
/// \return An executable item implementing the slot
/// \cert
template<class Policy = take_one, class T>
executable_item_ptr
create_slot_from_subscription(
  rclcpp::Node::SharedPtr node,
  const rclcpp::PollingSubscription<T> & sub,
  std::string_view app_id = "",
  apex::event::types::instance_id_t instance_id = 0U
)
{
  return create_slot<T, Policy>(
    std::move(node),
    sub.get_topic_name(),
    sub.get_actual_qos(),
    app_id,
    instance_id);
}
}  // namespace connector
}  // namespace executor
}  // namespace apex

#endif  // EXECUTOR2__CONNECTOR__SLOT_NODE_HPP_
