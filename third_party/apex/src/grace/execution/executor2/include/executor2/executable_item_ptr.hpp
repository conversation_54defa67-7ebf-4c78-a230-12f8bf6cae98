/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTOR2__EXCUTABLE_ITEM_PTR_HPP_
#define EXECUTOR2__EXCUTABLE_ITEM_PTR_HPP_

#include <memory>

namespace apex
{
namespace executor
{
/// \brief A forward declaration of a shared pointer to executable_item
/// \cert
using executable_item_ptr = std::shared_ptr<class executable_item>;
}  // namespace executor
}  // namespace apex

#endif  // EXECUTOR2__EXCUTABLE_ITEM_PTR_HPP_
