/// \copyright Copyright 2017-2021 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTOR2__EXECUTOR_RUNNER_HPP_
#define EXECUTOR2__EXECUTOR_RUNNER_HPP_

#include <executor2/executor_base.hpp>
#include <executor2/visibility.hpp>
#include <threading/thread.hpp>
#include <threading/thread_attributes.hpp>
#include <interrupt/interrupt_handler.hpp>
#include <cpputils/common_exceptions.hpp>

#include <utility>
#include <optional>  // NOLINT

namespace apex
{
namespace executor
{

/// \class executor_runner
/// A RAII for running an executor on a separate thread
/// \cert
class EXECUTOR_PUBLIC executor_runner final
{
public:
  /// A flag type
  /// \cert
  using flag_t = std::uint32_t;

  /// \class no_flags
  /// \cert
  static constexpr flag_t no_flags = 0x00;

  /// \class deferred
  /// A flag for creating a runner without actually running it
  /// \cert
  static constexpr flag_t deferred = 0x01;

  /// \class interrupt
  /// A flag for creating a runner that triggers interrupt on an error
  /// \cert
  static constexpr flag_t interrupt = 0x02;

  /// \brief Creates a runner
  /// \param flags Execution flags
  /// \param exec An executor to run
  /// \param attr Attributes of the worker thread
  /// \param timeout Maximum of how long to wait for a next event
  /// \param max_iterations How many iterations to run
  ///
  /// Note: the caller is responsible for ensuring the exec object remains valid for
  /// the lifetime of the runner
  /// \cert
  executor_runner(
    flag_t flags,
    executor_base & exec,
    threading::thread_attributes attr,
    std::chrono::nanoseconds timeout = InfiniteWait,
    std::size_t max_iterations = InfiniteIterations)
  : m_exec{&exec}
  {
    if ((flags & interrupt) != no_flags) {
      if (!apex::interrupt_handler::is_inited()) {
        throw apex::runtime_error{
                "interrupt handler must be initialized before "
                "executor_runner::interrupt can be used"
        };
      }
      m_thread.emplace([exec = m_exec, timeout, max_iterations] {
          run_with_interrupt(exec, timeout, max_iterations);
        }, std::move(attr));
    } else {
      m_thread.emplace([exec = m_exec, timeout, max_iterations] {
          run(exec, timeout, max_iterations);
        }, std::move(attr));
    }
    if ((flags & deferred) == no_flags) {
      m_thread->issue();
    }
  }

  /// \brief Creates a runner that triggers an interrupt on error
  /// \param flags Execution flags
  /// \param exec An executor to run
  /// \param timeout Maximum of how long to wait for a next event
  /// \param max_iterations How many iterations to run
  ///
  /// Note: the caller is responsible for ensuring the exec object remains valid for
  /// the lifetime of the runner
  /// \cert
  executor_runner(
    flag_t flags,
    executor_base & exec,
    std::chrono::nanoseconds timeout = InfiniteWait,
    std::size_t max_iterations = InfiniteIterations)
  : executor_runner{flags, exec, threading::thread_attributes::build(), timeout,
      max_iterations}
  {}

  /// \brief Creates a runner and runs the executor
  /// \param exec An executor to run
  /// \param attr Attributes of the worker thread
  /// \param timeout Maximum of how long to wait for a next event
  /// \param max_iterations How many iterations to run
  ///
  /// Note: the caller is responsible for ensuring the exec object remains valid for
  /// the lifetime of the runner
  /// \cert
  executor_runner(
    executor_base & exec,
    threading::thread_attributes attr,
    std::chrono::nanoseconds timeout = InfiniteWait,
    std::size_t max_iterations = InfiniteIterations)
  : executor_runner{flag_t{no_flags}, exec, std::move(attr), timeout, max_iterations}
  {}

  /// \brief Creates a runner and runs the executor
  /// \param exec An executor to run
  /// \param timeout Maximum of how long to wait for a next event
  /// \param max_iterations How many iterations to run
  ///
  /// Note: the caller is responsible for ensuring the exec object remains valid for
  /// the lifetime of the runner
  /// \cert
  explicit executor_runner(
    executor_base & exec,
    std::chrono::nanoseconds timeout = InfiniteWait,
    std::size_t max_iterations = InfiniteIterations)
  : executor_runner{flag_t{no_flags}, exec, threading::thread_attributes::build(), timeout,
      max_iterations}
  {}

  executor_runner(executor_runner &&) noexcept = default;
  executor_runner & operator=(executor_runner &&) noexcept = default;

  /// \brief Stops the executor
  /// \cert
  ~executor_runner()
  {
    stop();
  }

  /// \brief Starts the execution for an executor created with "deferred" flag
  /// \cert
  /// \deterministic
  void issue() const
  {
    m_thread->issue();
  }

  /// \brief Stops the executor
  /// \cert
  void stop() const
  {
    if (m_thread->valid() && m_thread->joinable()) {
      m_exec->stop();
      m_thread->join();
    }
  }

private:
  /// \brief Runs the executor
  /// \param exec The executor to run
  /// \param timeout Maximum of how long to wait for a next event
  /// \param max_iterations How many iterations to run
  /// \cert
  /// \deterministic
  static void run(
    executor_base * exec,
    std::chrono::nanoseconds timeout,
    std::size_t max_iterations)
  {
    exec->run(timeout, max_iterations);
  }

  /// \brief Runs the executor and triggers an interrupt on error
  /// \param exec The executor to run
  /// \param timeout Maximum of how long to wait for a next event
  /// \param max_iterations How many iterations to run
  /// \cert
  /// \deterministic
  static void run_with_interrupt(
    executor_base * exec,
    std::chrono::nanoseconds timeout,
    std::size_t max_iterations)
  {
    try {
      exec->run(timeout, max_iterations);
      // Intended catch-all and re-throw
    } catch (...) {
      try {
        apex::interrupt_handler::trigger();
        // Intended catch-all to prevent exception
      } catch (...) {}
      throw;
    }
  }

  executor_base * m_exec;
  std::optional<apex::threading::thread> m_thread;
};

}  // namespace executor
}  // namespace apex

#endif  // EXECUTOR2__EXECUTOR_RUNNER_HPP_
