/// \copyright Copyright 2017-2021 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTOR2__NODE_HPP_
#define EXECUTOR2__NODE_HPP_

#include <executor2/visibility.hpp>
#include <cpputils/callable_traits.hpp>
#include <cpputils/noncopyable.hpp>

#include <utility>
#include <memory>
#include <vector>
#include <type_traits>
#include <algorithm>

namespace apex
{
namespace executor
{
namespace details
{

/// \class callee_base
/// The base class for the node functor
/// \cert
class callee_base : private apex::NonCopyable
{
public:
  callee_base() = default;
  virtual ~callee_base() = default;

  /// \brief The node functor method
  /// \return False, if run should be interrupted
  /// \cert
  /// \deterministic if implementation is deterministic
  virtual bool operator()() = 0;
};

/// \class callee
/// The implementation class for the node functor
/// \tparam F The functor type
/// \cert
template<typename F>
class callee : public callee_base
{
public:
  /// \brief Creates a node functor
  /// \param u A callable
  /// \cert
  template<class U>
  explicit callee(U && u)
  : f{std::forward<U>(u)} {}

  /// \brief The node functor method
  /// \cert
  /// \return False, if run should be interrupted
  /// \deterministic if f() is deterministic.
  /// \note Intended to skip the determinism verification.
  /// Because the determinism was reviewed through code inspection.
  /*
   AXIVION Next Codeline apex-deterministic: false positive: entirely depends on f()
   */
  bool operator()() override {return f();}

  F f;
};

}  // namespace details

class node;

using node_sptr = std::shared_ptr<node>;

/// \class node
/// Represents a node in the graph
/// \cert
class EXECUTOR_PUBLIC node
{
private:
  // Axivion Next Line AutosarC++19_03-A11.3.1 Friend class is needed here
  friend class graph;
  std::unique_ptr<details::callee_base> f;

public:
  /// \brief Factory method which creates a node with a provided callable
  /// \param u The callable of the node
  /// \return A new node with the callable payload
  /// \cert
  template<class U,
    std::enable_if_t<std::is_same_v<callable_ret_t<U>, void>> * = nullptr>
  static node_sptr make(U && u)
  {
    auto n = std::make_shared<node>();
    auto wrapped = [f = std::forward<U>(u)] {
        f();
        return true;
      };
    n->f = std::make_unique<details::callee<decltype(wrapped)>>(std::move(wrapped));
    return n;
  }

  /// \brief Factory method which creates a node with a provided callable
  /// \param u The callable of the node
  /// \return A new node with the callable payload
  /// \cert
  template<class U,
    std::enable_if_t<std::is_same_v<callable_ret_t<U>, bool>> * = nullptr>
  static node_sptr make(U && u)
  {
    auto n = std::make_shared<node>();
    n->f = std::make_unique<details::callee<std::decay_t<U>>>(std::forward<U>(u));
    return n;
  }

  std::vector<node_sptr> adj;
};

/// \brief A helper function which connects nodes only if they are not connected yet
/// \param source The source node
/// \param dest The destination node
/// \return Whether connection was successful
/// \cert
inline bool connect(const node_sptr & source, node_sptr dest)
{
  if ((source == nullptr) ||
    (dest == nullptr) ||
    (std::find(source->adj.begin(), source->adj.end(), dest) != source->adj.end()))
  {
    return false;
  }

  source->adj.push_back(std::move(dest));
  return true;
}

}  // namespace executor
}  // namespace apex

#endif  // EXECUTOR2__NODE_HPP_
