/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#ifndef EXECUTOR2__DETAILS__LIVE__EXECUTOR_TASK_HPP_
#define EXECUTOR2__DETAILS__LIVE__EXECUTOR_TASK_HPP_

#include <executor2/executable_item.hpp>
#include <executor2/details/executor_task_base.hpp>
#include <executor2/details/item_task_base.hpp>
#include <executor2/details/live/task_base.hpp>
#include <executor2/details/graph_task_base.hpp>
#include <executor2/details/customization/execution_monitor.hpp>

#include <execution_monitor/execution_monitor.hpp>

#include <tracetools/tracetools.h>

#include <vector>
#include <functional>
#include <utility>

namespace apex
{
namespace executor
{
namespace details
{
namespace live
{
/// \class executor_task
/// A task holding one or more items to execute on a non-timer based event
/// \cert
//lint -e{9432} : multiple inheritance NOLINT
template<class B>
class executor_task : public executor_task_base, public task_base<B>
{
private:
  using ctor_argument = typename B::payload_type;
  using Base = task_base<B>;
  using Base::collect_waitable_items;
  using Base::execute_items;
  using Base::m_monitor;

public:
  /// \brief Creates a task
  /// \param index The index of the task in the executor
  /// \param factory A generic factory object
  /// \param items Items to add to the task
  /// \param condition A condition for execution of the nodes
  /// \cert
  executor_task(
    std::size_t index,
    const generic_factory & factory,
    ctor_argument items)
  : executor_task_base{index, factory},
    Base{*this, std::move(items)}
  {}

private:
  /// \brief Registers task's events in a waitset
  /// \param ws A waitset to register the events in
  /// \cert
  void register_events(rclcpp::dynamic_waitset::Waitset & ws) override
  {
    using rclcpp::dynamic_waitset::make_group;

    auto items = collect_waitable_items();
    if (items.empty()) {
      throw apex::runtime_error{"no triggering events found -- nothing to execute"};
    }

    if (m_monitor != nullptr) {
      register_execute_task_callback<true>(ws, std::move(items));
    } else {
      register_execute_task_callback<false>(ws, std::move(items));
    }
  }

  /// \brief Gets all the items of this task
  /// \return All the items of this task
  /// \cert
  const std::vector<executable_item_ptr> & get_items() const override
  {
    return Base::get_all_items();
  }

  /// \brief Executes the task
  ///
  /// Various parameters which are fixed at registration time are passed in as boolean template
  /// arguments. The compiler can thus optimize based on their values and does not
  /// have to re-check them each time the task is executed.
  /// \tparam monitored Whether the task should be monitored by the execution monitor
  /// \cert
  /// \deterministic
  /*
   AXIVION Next Codeline apex-deterministic: false positive: entirely depends on items execution
   */
  template<bool monitored>
  void execute_task()
  {
    namespace monitor = apex::execution_monitor;
    using monitor_type = typename Base::monitor;
    // runtime_guard is an RAII scope monitor; it is neither unused nor should it be const
    monitor::monitored_scope<monitor_type> monitored_scope;
    if (monitored) {
      monitored_scope = monitor::monitored_scope(*m_monitor, get_id());
    }
    (void) execute_items();
  }

  /// \brief Registers a callback in the given waitset that triggers this item when
  ///        any of the `data_triggered_items` trigger
  /// \tparam monitored Whether the task should be monitored by the execution monitor
  /// \tparam conditional Whether the task has an execution condition
  /// \param ws The waitset to register the callback in
  /// \param data_triggered_items All `WaitableItems` that may trigger the task
  /// \cert
  template<bool monitored>
  void register_execute_task_callback(
    rclcpp::dynamic_waitset::Waitset & ws,
    std::vector<rclcpp::dynamic_waitset::WaitableItem> data_triggered_items)
  {
    const auto wgroup = make_group(std::move(data_triggered_items),
        [this] {
          execute_task<monitored>();
        });
    (void)ws.add(wgroup);
    TRACETOOLS_TRACEPOINT(
      executor_task_add_triggers,
      static_cast<const void *>(this),
      static_cast<const void *>(wgroup.get()));
  }
};

using item_executor_task = executor_task<item_task_base>;
using graph_executor_task = executor_task<graph_task_base>;

}  // namespace live
}  // namespace details
}  // namespace executor
}  // namespace apex

#endif  // EXECUTOR2__DETAILS__LIVE__EXECUTOR_TASK_HPP_
