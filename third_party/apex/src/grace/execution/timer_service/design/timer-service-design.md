# timer_service

## Purpose / Use cases

The [Apex.Grace executor](executor-design.md) provides basic capabilities for executing items
periodically or when a timeout is reached. These capabilities, however, are limited to relatively
simple scenarios defined by the node integrator and may fall short when design requires more
control to be handled to the node implementer.
This package provides such capabilities in the form of timer services which can be shared between
nodes or used exclusively by them.

## Package structure

The package defines:

1. An abstract interface for acquiring current time: `current_time_interface`
1. An abstract interface for a timer service: `timer_service_interface` which extends
   the `current_time_interface`
1. A mid-level implementation which can serve foundation for any
   custom timer service implementations: `timer_service`
1. A concrete timer service implementation based on real clock (chrono-compatible):
   `clock_timer_service` and its two aliases `steady_clock_timer_service`
   and `high_resolution_clock_timer_service` instantiated with the corresponding `chrono` clocks
1. A concrete timer service implementation based on `/clock` topic (similar to `ROSTime`):
   `sim_timer_service`

## Design

### Timer service instances

Multiple service instances can co-exist in the same application, providing they all have
unique names. The name of an instance is the name of the underlying ROS node. It is a user's
responsibility to make sure these names are unique lest the behavior of the application
become undefined.

{{ code_snippet(
   "grace/execution/timer_service/test/test_timer_service_doc.cpp",
   {'tag': '//! [create_service1]'}) }}

There is a "shared-pointer-to-the-abstract-interface" type defined in the library:

{{ code_snippet(
   "grace/execution/timer_service/test/test_timer_service_doc.cpp",
   {'tag': '//! [create_service2]'}) }}

### Timers

Users may create, reschedule and cancel timers through an instance
of a timer service. Each timer is represented as a separate object.
Every such object
exposes a polling subscription to a uniquely named topic which gets published on each time the
timer is triggered. This subscription is a regular polling subscription which can be used as
any other
subscription by the `executor`. Timer events are just normal messages,
so they must be consumed by user
or else execution will be triggered again by the same timer event (message).
The timer interface
provides a few convenience methods to count the timer events and consume them by one call.

{{ code_snippet(
   "grace/execution/timer_service/test/test_timer_service_doc.cpp",
   {'tag': '//! [create_timers]'}) }}

!!! note
    Since timer events are just ROS messages, canceling or rescheduling a timer does not
    change any historic events already published prior to return of a corresponding
    control call. Subscription queues must be emptied manually after the call, if there is a need.

This design allows for transparent (from users' perspective)
[integration
with replay executor](replay-timer-service-design.md) or for support for any other kind of
simulated/mocked clocks. Switching between clock-sources
is supposed to be fully transparent, as users
are expected to operate through the abstract interface of the timer service only.

#### Named timers

All timers are automatically assigned a numeric id upon their creation. A timer can always be
looked up by its id. For the sake of convenience, timers can also be given an alphanumeric name
upon creation. A named timer can also be looked up by its name. The names (and ids)
must be unique per service instance and this is enforced by the API.

!!! note
    Looking up a timer by its name or id returns a pointer to the same timer object.
    It *does not* create an independent timer subscription

### Timer subscription

Like any ROS publisher, timer can have multiple subscribers.
Each subscription object encapsulates
an independent Apex.Grace polling subscription, meaning, it has an independent message queue. The
same effect can be achieved by manually subscribing to the timer's topic,
but a timer subscription object provides a convenient and coherent way to do so.

{{ code_snippet(
   "grace/execution/timer_service/test/test_timer_service_doc.cpp",
   {'tag': '//! [timer_subscriptions]'}) }}

### Auto-canceling timers

Creating a timer with interval 0 (or without interval) will cause a timer to auto-cancel
after its first expiration.

### Timer lifetime

The lifetime of timers is equal to the lifetime of the timer service
(in the default implementation). This means that even if a timer pointer is destroyed on the user
side,
it does not mean the timer is canceled. The only way to cancel a timer is to call the
appropriate method.

### Timer service server

If a timer service has to be shared between multiple processes or
over the network, it can be wrapped
into an instance of `timer_service_server`. The server will create the necessary
communication infrastructure in order to allow accessing the
underlying timer service over the network. The remote clients can create instances of
`remote_timer_service` to access the server-hosted timer service.
The `remote_timer_service` implements
`timer_service_interface` and can be used as such in any context. The only caveat is that due to
the determinism requirements, the `get_timer()` method of the `remote_timer_service` can only
return
timers which were already locally cached by `remote_timer_service` using one of the
non-deterministic methods. Remote timers can be forced to be cached locally
by calling a special non-deterministic method `get_remote_timer()` which is an
extension the `remote_timer_service` adds for such case.

{{ code_snippet(
   "grace/execution/timer_service/test/test_timer_service_doc.cpp",
   {'tag': '//! [remote_timer_service]'}) }}

#### Unchecked timer subscriptions

Since timers are basically regular topics, it is possible to create a `rclcpp` subscription
to a named timer without going via a service call. In this case, even though no validation
of timer existence is possible, it instead may save time on the start-up.
In order to achieve that,
it is recommended to use either the free `create_timer_topic_subscription` function
or the method `create_timer_subscription_unchecked` of the `remote_timer_service`
(in case there is a need to create a `remote_timer_service` for some other purpose anyway).
Note also, that `remote_timer_service` can be forced to start without waiting for a
`timer_service` in its constructor.

### Current time

Any timer service can be asked its current time by calling the `now()` method of the
`current_time_interface` which it must implement. The time is returned as the time since epoch
of the clock implementing the `timer_service`

!!! note
    Users must take into account the epoch of the underlying timer while
    using `now()` functionality and converting from one type of clock to another

The time returned by `now()` can be used to set up or reschedule timers in terms of absolute time
instead of the relative:

{{ code_snippet(
    "grace/execution/timer_service/test/test_timer_service_doc.cpp",
    {'tag': '//! [now]'}) }}

!!! note
    It is advisable to always work with the result of the `now()` call instead of creating a new
    absolute time point. It guarantees that this time point stays coherent even if the underlying
    clock changes with a different timer service implementation, e.g. replay or sim time

### Sim-time-based clock

The timer service implementation `sim_timer_service` gets the timer from the `/clock` topic
(like `ROSTime` does). The service is initialized with `now == 0`. No timers are fired
until the time is updated to a value different from `0` by a publisher on the `/clock` topic.
There are two additional methods available for the `sim_timer_service`:

1. `wait_for_clock()` that can be used if there is a need to wait until the timer service
   object gets a time different from zero before scheduling any timers
1. `now(time)` that overrides the current time with the provided one

## Assumptions / Known limits

### Thread-safety

All the APIs implemented in `timer_service` package are thread safe. The base implementation
class uses
locks to protect its internal state. An exclusive lock is held during the call to
`get_next_timer_id()` and `get_qos()` virtual functions. All the rest of the virtual functions
are called without any locks being held.

## Error detection and handling

All non-`noexcept` functions may throw an exception based on `std::exception` in case of error.
The only exception is `get_timer()` function which returns `nullptr` in case
a timer does not exist.

## Future extensions / Unimplemented parts

N/A
