/* This file was auto-generated by settings_converter */

#include "rt_mock_settings.hpp"
#include <settings/construct.hpp>

using apex::settings::construct::array;
using apex::settings::construct::dictionary;
using apex::settings::construct::make_value;
using apex::settings::construct::make_array;
using apex::settings::construct::make_dictionary;
using apex::settings::construct::get;

namespace apex
{
namespace settings
{
namespace generated
{
namespace rt_mock_settings
{

dictionary create()
{
  dictionary root;
  auto temp1 = make_dictionary();
  root["rt_settings"] = temp1;
  get<dictionary>(temp1)["proc_cpu_mask"] = make_value(5);
  get<dictionary>(temp1)["proc_max_mem_mb"] = make_value(0);
  get<dictionary>(temp1)["proc_prio"] = make_value(0);
  get<dictionary>(temp1)["proc_scheduler"] = make_value("round_robin");
  return root;
}

}  // namespace rt_mock_settings
}  // namespace generated
}  // namespace settings
}  // namespace apex
