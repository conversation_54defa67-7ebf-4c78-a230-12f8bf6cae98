// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>
#include <unistd.h>

#include "apexutils/apex_rt.h"

#include "apex_init/apex_init.hpp"
#include "settings/repository.hpp"

using apex::settings::construct::dictionary;
using apex::settings::construct::make_dictionary;
using apex::settings::construct::entry;

/// @test{
/// "req" : ["SWRQ_APEX_INIT_24", "SWRQ_APEX_INIT_25"]
/// }
TEST(apex_init_fifo, sched_other_is_successfully_set)
{
  int32_t other_priority_min = apex_get_os_priority_min(SCHED_OTHER);
  ASSERT_LE(other_priority_min, 1);

  apex::settings::construct::dictionary dict;
  dict["rt_settings"] = make_dictionary(
    entry("proc_prio", other_priority_min),
    entry("proc_scheduler", "other"));

  apex::settings::repository::set(dict);

  apex_ret_t retval = apex::pre_init(0U, nullptr, false);
  EXPECT_EQ(retval, APEX_RET_OK);
  EXPECT_EQ(apex::is_proc_rt(), false);

  struct sched_param param;
  const auto pid = getpid();
  sched_getparam(pid, &param);

  // check rt parameters are set correctly
  EXPECT_EQ(sched_getscheduler(pid), SCHED_OTHER);
  EXPECT_EQ(param.sched_priority, other_priority_min);
}
