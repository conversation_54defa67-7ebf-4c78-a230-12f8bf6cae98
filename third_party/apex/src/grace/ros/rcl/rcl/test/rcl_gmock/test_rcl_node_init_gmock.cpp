// Copyright 2020 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <rmw/rmw.h>
#include <memory>
#include "rcl/rcl.h"
#include "rcl/subscription.h"
#include "rcl/error_handling.h"

using ::testing::Return;
using ::testing::_;
using ::testing::NiceMock;
using ::testing::ReturnNull;

/*
 * The purpose of this test file is to test rcl_node_init
 * in the case that the following functions returns the invalid output
 *   - rmw_create_node
 *   - rcutils_strdup (only tested with APEX_CERT)
 */

// Mock the public APIs in rmw / rcutils package
class externalPkgMock
{
public:
  externalPkgMock() {}
#if defined(APEX_CERT)
  /*
   * The mocked rcutils_strdup function make the side effect when INITIALIZER(initialize)
   * in rmw_implementation_identifier_check.c is executed.
   * So, This function should be stub function with APEX_CERT
   */
  MOCK_METHOD2(rcutils_strdup, char *(const char * str, rcutils_allocator_t allocator));
#endif
  MOCK_METHOD3(rmw_create_node, rmw_node_t * (
      rmw_context_t * context,
      const char * name,
      const char * namespace_));
};
std::unique_ptr<externalPkgMock> rcl_node_mock;

class test_rcl_node_init_gmock : public ::testing::Test
{
public:
  rcl_node_t m_node;
  rcl_context_t m_context;
  rcl_node_options_t m_default_options;
  rcl_init_options_t m_init_options;

  void SetUp() override
  {
    rcl_ret_t ret;

    /* zero-initialized node */
    m_node = rcl_get_zero_initialized_node();
    m_init_options = rcl_get_zero_initialized_init_options();
    ret = rcl_init_options_init(&m_init_options, rcl_get_default_allocator());
    ASSERT_EQ(RCL_RET_OK, ret);

    m_context = rcl_get_zero_initialized_context();
    m_default_options = rcl_node_get_default_options();
    ret = rcl_init(0, nullptr, &m_init_options, &m_context);
    ASSERT_EQ(RCL_RET_OK, ret);
    rcutils_logging_set_output_handler(nullptr);
    rcl_node_mock = std::make_unique<NiceMock<externalPkgMock>>();
  }

  void TearDown() override
  {
    rcl_node_mock.reset();
    ASSERT_EQ(RCL_RET_OK, rcl_shutdown(&m_context));
    ASSERT_EQ(RCL_RET_OK, rcl_init_options_fini(&m_init_options));
  }
};

#ifdef __cplusplus
extern "C"
{
#endif

rmw_node_t *
rmw_create_node(
  rmw_context_t * context,
  const char * name,
  const char * namespace_)
{
  rmw_node_t * ret = nullptr;
  if (nullptr != rcl_node_mock) {
    ret = rcl_node_mock->rmw_create_node(context, name, namespace_);
  }
  return ret;
}

#if defined(APEX_CERT)
char * rcutils_strdup(const char * str, rcutils_allocator_t allocator)
{
  char * ret = nullptr;
  if (nullptr != rcl_node_mock) {
    ret = rcl_node_mock->rcutils_strdup(str, allocator);
  }
  return ret;
}
#endif /* defined(APEX_CERT) */

#ifdef __cplusplus
}
#endif

/**
 * Basic error checking test of rcl_node_init when rmw_create_node returns nullptr.
 *
 * @test{
 * "req" : ["SWRQ_RCL_506"]
 * }
 */
TEST_F(test_rcl_node_init_gmock, rmw_create_node_fail) {
  // When `rmw_create_node` that become the stub function is called,
  // the return value of `rmw_create_node` is set with nullptr
  EXPECT_CALL(*rcl_node_mock, rmw_create_node(_, "node", "/ns")).WillOnce(ReturnNull());

  rcl_ret_t ret = rcl_node_init(&m_node, "node", "/ns", &m_context, &m_default_options);
  EXPECT_EQ(RCL_RET_ERROR, ret) << rcl_get_error_string().str;

  bool is_valid = rcl_node_is_valid_except_context(&m_node);
  EXPECT_FALSE(is_valid) << rcl_get_error_string().str;
  rcl_reset_error();
}

#if defined(APEX_CERT)
/**
 * Basic error checking test of rcl_node_init when rcutils_strdup returns nullptr
 *
 * @test{
 * "req" : ["SWRQ_RCL_506"]
 * }
 */
TEST_F(test_rcl_node_init_gmock, rcutils_strdup_fail) {
  // the return value of `rcutils_strdup` is set with nullptr
  EXPECT_CALL(*rcl_node_mock, rcutils_strdup(_, _)).WillRepeatedly(ReturnNull());

  rcl_ret_t ret = rcl_node_init(&m_node, "node", "/", &m_context, &m_default_options);
  EXPECT_EQ(RCL_RET_ERROR, ret) << rcl_get_error_string().str;

  bool is_valid = rcl_node_is_valid_except_context(&m_node);
  EXPECT_FALSE(is_valid) << rcl_get_error_string().str;
  rcl_reset_error();

  ret = rcl_node_fini(&m_node);
  ASSERT_EQ(RCL_RET_OK, ret) << rcl_get_error_string().str;
}
#endif /* defined(APEX_CERT) */
