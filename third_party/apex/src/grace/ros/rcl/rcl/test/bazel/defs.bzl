# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

def _add_test(**kwargs):
    _name = kwargs.pop("name")
    _srcs = kwargs.pop("srcs")
    _deps = kwargs.pop("deps") if "deps" in kwargs else []
    _local_defines = kwargs.pop("local_defines") if "local_defines" in kwargs else []
    _isolated = kwargs.pop("isolated") if "isolated" in kwargs else False
    _srcs += native.glob([
        "rcl/*.hpp",
        "rcl/*.h",
        "rcl_gmock/*.hpp",
    ])
    _test_provider = apex_cc_test
    if _isolated:
        _deps.append("//grace/tools/ros_domain_coordinator:gtest_main")
    else:
        _deps.append("@googletest//:gtest_main")

    _test_provider(
        name = _name,
        srcs = _srcs,
        # TODO(kyle.marcey): #24903 Remove line below
        copts = ["-Wno-error"],
        local_defines = ["ROS_PACKAGE_NAME=\\\"rcl\\\""] + _local_defines,
        deps = [
            "//grace/ros/rcl/rcl",
        ] + _deps,
        **kwargs
    )

def add_rcl_qm_test(**kwargs):
    _add_test(
        target_compatible_with = select({
            "//conditions:default": [],
            "@apex//common/asil:d": ["@platforms//:incompatible"],
        }),
        **kwargs
    )

def add_rcl_isolated_qm_test(**kwargs):
    kwargs["isolated"] = True
    add_rcl_qm_test(**kwargs)

def add_rcl_test(**kwargs):
    _add_test(
        **kwargs
    )

def add_rcl_isolated_test(**kwargs):
    kwargs["isolated"] = True
    add_rcl_test(**kwargs)
