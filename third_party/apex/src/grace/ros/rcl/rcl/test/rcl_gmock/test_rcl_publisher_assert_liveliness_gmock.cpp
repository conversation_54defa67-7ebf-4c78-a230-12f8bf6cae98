// Copyright 2020 Apex.AI, Inc.
// All rights reserved.

#include <gmock/gmock.h>
#include <rcutils/logging.h>
#include <rmw/rmw.h>
#include <memory>
#include "test_misc_gmock.hpp"

/*
 * The purpose of this test file is to test rcl_publisher_assert_liveliness
 * in the case that `rmw_publisher_assert_liveliness` returns error
 */

// Mock the public APIs in rmw packages
class rmwMock
{
public:
  rmwMock() {}
  MOCK_METHOD1(rmw_publisher_assert_liveliness, rmw_ret_t(const rmw_publisher_t * publisher));
};
std::unique_ptr<rmwMock> rmw_mock;

class test_rcl_publisher_assert_liveliness_gmock : public TestRclMiscGMock
{
public:
  void SetUp() override
  {
    rmw_mock = std::make_unique<NiceMock<rmwMock>>();
    TestRclMiscGMock::SetUp();
  }

  void TearDown() override
  {
    TestRclMiscGMock::TearDown();
    rmw_mock.reset();
  }
};

rmw_ret_t
rmw_publisher_assert_liveliness(const rmw_publisher_t * publisher)
{
  return rmw_mock->rmw_publisher_assert_liveliness(publisher);
}

/**
 * The purpose of the test case is to test `rcl_publisher_assert_liveliness` function
 * when rmw_publisher_assert_liveliness returns RMW_RET_ERROR.
 *
 * @test{
 * "req" : ["SWRQ_RCL_440"]
 * }
 */
TEST_F(test_rcl_publisher_assert_liveliness_gmock, rcl_publisher_assert_liveliness) {
  rcl_ret_t ret = test_rcl_publisher_init();
  EXPECT_EQ(RCL_RET_OK, ret) << rcl_get_error_string().str;

  // When `rmw_publisher_assert_liveliness` that become the stub function is called,
  // and the return value of `rmw_publisher_assert_liveliness` is set with RMW_RET_ERROR
  EXPECT_CALL(*rmw_mock,
    rmw_publisher_assert_liveliness(_)).WillRepeatedly(Return(RMW_RET_ERROR));

  ret = rcl_publisher_assert_liveliness(&m_publisher);
  EXPECT_EQ(RCL_RET_ERROR, ret) << rcl_get_error_string().str;
  rcl_reset_error();

  test_rcl_publisher_fini();
}
