cmake_minimum_required(VERSION 3.5)

project(rcl)

find_package(apex_cmake REQUIRED)  # Required on all Apex.OS packages!
find_package(ament_cmake_ros REQUIRED)

find_package(rcl_interfaces REQUIRED)
find_package(rcl_logging_interface REQUIRED)
find_package(rcl_yaml_param_parser REQUIRED)
find_package(rcutils REQUIRED)
find_package(rmw REQUIRED)
find_package(rmw_implementation REQUIRED)
find_package(rosidl_runtime_c REQUIRED)
find_package(tracetools REQUIRED)

include(cmake/rcl_set_symbol_visibility_hidden.cmake)
if(NOT APEX_MICRO)
  include(cmake/get_default_rcl_logging_implementation.cmake)
  get_default_rcl_logging_implementation(RCL_LOGGING_IMPL)
endif()

# Default to C11
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 11)
endif()

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

set(${PROJECT_NAME}_NON_CERT_SRCS
    $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/rcl/arguments.c>
    src/rcl/event.c
    src/rcl/guard_condition.c
    src/rcl/lexer.c
    src/rcl/lexer_lookahead.c
    $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/rcl/logging_rosout.c>
    src/rcl/network_flow_endpoints.c
    $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/rcl/remap.c>
    src/rcl/rmw_implementation_identifier_check.c
    src/rcl/timer.c
    src/rcl/time.c
    src/rcl/wait.c
)

set(${PROJECT_NAME}_NON_CERT_HEADERS
    $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/rcl/arguments.h>
    include/rcl/event.h
    include/rcl/guard_condition.h
    include/rcl/lexer.h
    include/rcl/lexer_lookahead.h
    include/rcl/network_flow_endpoints.h
    $<$<NOT:$<BOOL:${APEX_MICRO}>>:include/rcl/remap.h>
    include/rcl/timer.h
    include/rcl/wait.h
)

set(${PROJECT_NAME}_CERT_SRCS
  src/rcl/common.c
  src/rcl/context.c
  src/rcl/domain_id.c
  src/rcl/client.c
  src/rcl/expand_topic_name.c
  src/rcl/graph.c
  src/rcl/init.c
  src/rcl/init_options.c
  src/rcl/localhost.c
  $<$<NOT:$<BOOL:${APEX_MICRO}>>:src/rcl/logging.c>
  src/rcl/log_level.c
  src/rcl/node.c
  src/rcl/node_options.c
  src/rcl/node_resolve_name.c
  src/rcl/publisher.c
  src/rcl/security.c
  src/rcl/subscription.c
  src/rcl/validate_enclave_name.c
  src/rcl/validate_topic_name.c
  src/rcl/service.c
)

set(${PROJECT_NAME}_CERT_HEADERS
    include/rcl/client.h
    include/rcl/context.h
    include/rcl/domain_id.h
    include/rcl/expand_topic_name.h
    include/rcl/graph.h
    include/rcl/init.h
    include/rcl/init_options.h
    include/rcl/localhost.h
    include/rcl/logging_rosout.h
    include/rcl/logging.h
    include/rcl/node.h
    include/rcl/node_options.h
    include/rcl/publisher.h
    include/rcl/security.h
    include/rcl/service.h
    include/rcl/subscription.h
    include/rcl/validate_enclave_name.h
    include/rcl/validate_topic_name.h
    src/rcl/arguments_impl.h
    src/rcl/common.h
    src/rcl/context_impl.h
    src/rcl/event_impl.h
    src/rcl/init_options_impl.h
    src/rcl/publisher_impl.h
    src/rcl/remap_impl.h
    src/rcl/subscription_impl.h
)

set(${PROJECT_NAME}_SRCS ${${PROJECT_NAME}_CERT_SRCS})
if(NOT APEX_CERT)
  list(APPEND ${PROJECT_NAME}_SRCS ${${PROJECT_NAME}_NON_CERT_SRCS})
endif()

add_library(${PROJECT_NAME} ${${PROJECT_NAME}_SRCS})
target_include_directories(${PROJECT_NAME} PUBLIC
  "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
  "$<INSTALL_INTERFACE:include>")
if(VECTORCAST_BUILD)
  vcast_set_io_cover_cxx(${PROJECT_NAME})
endif()
apex_set_compile_options(${PROJECT_NAME})
# TODO: Remved the next line #11077
include(CheckCXXCompilerFlag)
check_cxx_compiler_flag("-Wno-restrict" check_result)

if(CMAKE_SYSTEM_PROCESSOR STREQUAL "tricore")
  set(check_result OFF)
  target_compile_options(${PROJECT_NAME} PRIVATE -Wno-error=format=)
endif()

if(check_result)
  target_compile_options(${PROJECT_NAME} PUBLIC -Wno-restrict)
endif()


# specific order: dependents before dependencies
ament_target_dependencies(${PROJECT_NAME}
  "rcl_interfaces"
  "rcl_logging_interface"
  "rcutils"
  "rmw"
  "rmw_implementation"
  "rosidl_runtime_c"
  "tracetools"
)

if(NOT APEX_MICRO)
  ament_target_dependencies(${PROJECT_NAME}
    "rcl_yaml_param_parser" ${RCL_LOGGING_IMPL}
  )
endif()

# Causes the visibility macros to use dllexport rather than dllimport,
# which is appropriate when building the dll but not consuming it.
target_compile_definitions(${PROJECT_NAME} PRIVATE "RCL_BUILDING_DLL")
rcl_set_symbol_visibility_hidden(${PROJECT_NAME} LANGUAGE "C")

if(BUILD_TESTING AND NOT RCUTILS_DISABLE_FAULT_INJECTION)
  target_compile_definitions(${PROJECT_NAME} PUBLIC RCUTILS_ENABLE_FAULT_INJECTION)
endif()

install(
  TARGETS ${PROJECT_NAME} EXPORT ${PROJECT_NAME}
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin)

# rcl_lib_dir is passed as APPEND_LIBRARY_DIRS for each ament_add_gtest call so
# the librcl that they link against is on the library path.
# This is especially important on Windows.
# This is overwritten each loop, but which one it points to doesn't really matter.
set(rcl_lib_dir "$<TARGET_FILE_DIR:${PROJECT_NAME}>")

# specific order: dependents before dependencies
ament_export_include_directories(include)
ament_export_libraries(${PROJECT_NAME})
ament_export_targets(${PROJECT_NAME})

ament_export_dependencies(ament_cmake)
ament_export_dependencies(rcl_interfaces)
ament_export_dependencies(rcl_logging_interface)
ament_export_dependencies(rmw_implementation)
ament_export_dependencies(rmw)
ament_export_dependencies(rcutils)
ament_export_dependencies(rosidl_runtime_c)
ament_export_dependencies(tracetools)

if(NOT APEX_MICRO)
  ament_export_dependencies(rcl_yaml_param_parser)
  ament_export_dependencies(${RCL_LOGGING_IMPL})
endif()

if(BUILD_TESTING)
  # Run common linters
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()

  add_subdirectory(test)
endif()

ament_package(CONFIG_EXTRAS "rcl-extras.cmake")

install(
  DIRECTORY cmake
  DESTINATION share/${PROJECT_NAME}
)
install(
  DIRECTORY include/
  DESTINATION include
)
