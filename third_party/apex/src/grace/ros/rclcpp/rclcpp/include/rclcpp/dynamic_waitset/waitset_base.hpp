/// \copyright Copyright 2017-2021 Apex.AI, Inc.
/// All rights reserved.

#ifndef RCLCPP__DYNAMIC_WAITSET__WAITSET_BASE_HPP_
#define RCLCPP__DYNAMIC_WAITSET__WAITSET_BASE_HPP_

#include <rclcpp/polling_subscription.hpp>
#include <rclcpp/polling_client_base.hpp>
#include <rclcpp/polling_service_base.hpp>
#include <rclcpp/contexts/default_context.hpp>
#include <rclcpp/visibility_control.hpp>
#include <rclcpp/dynamic_waitset/guard_condition_base.hpp>
#include <rclcpp/dynamic_waitset/waitset_thread_pool.hpp>
#include <rclcpp/dynamic_waitset/background_worker.hpp>

#include <cpputils/variant.hpp>
#include <cpputils/variant_traits.hpp>
#include <tracetools/tracetools.h>

#include <memory>
#include <vector>
#include <type_traits>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <functional>

#define WAITSET_DEPRECATED __attribute__((visibility("default"), deprecated))

namespace rclcpp
{
namespace dynamic_waitset
{

/// \brief Thread Safety of Waitset
// TODO(Sumanth.Nirmal): Alias this to plexus waitset thread safety enum once rmw_apex is removed
enum class ThreadSafety : uint8_t
{
  // No thread safety guarantees
  NOT_THREAD_SAFE,

  // The waitset is thread-safe against concurrent attach/detach with wait
  // but only one thread shall wait() at the same time.
  THREAD_SAFE
};

/// \brief A waitable item
/// \cert
using WaitableItem = apex::variant<
  std::shared_ptr<GuardConditionBase>,
  std::shared_ptr<CountingGuardConditionBase>,
  std::shared_ptr<PollingSubscriptionBase>,
  std::shared_ptr<PollingClientBase>,
  std::shared_ptr<PollingServiceBase>
>;

namespace details
{
/// \brief A helper for creating a variant of raw pointers from a variant of shared pointers
/// to the same type
/// \cert
template<class T>
struct ptr_variant_from_shared_ptr_variant;

/// \brief A helper for creating a variant of raw pointers from a variant of shared pointers
/// to the same type
/// The specialization
/// \cert
template<class ... Args>
struct ptr_variant_from_shared_ptr_variant<apex::variant<std::shared_ptr<Args>...>>
{
  using type = apex::variant<Args *...>;
};

/// \brief A helper for creating a variant of raw pointers from a variant of shared pointers
/// The trait alias
/// \cert
template<class T>
using ptr_variant_from_shared_ptr_variant_t = typename ptr_variant_from_shared_ptr_variant<T>::type;

}  // namespace details

/// \brief Syntactic sugar for treating a waitable as a subscription
/// \param item The item
/// \return A subscription
/// \cert
/// \deterministic
inline std::shared_ptr<PollingSubscriptionBase> as_subscription(const WaitableItem & item)
{
  return apex::get<std::shared_ptr<PollingSubscriptionBase>>(item);
}

/// \brief Syntactic sugar for treating a waitable as a client
/// \param item The item
/// \return A client
/// \cert
/// \deterministic
inline std::shared_ptr<PollingClientBase> as_client(const WaitableItem & item)
{
  return apex::get<std::shared_ptr<PollingClientBase>>(item);
}

/// \brief Syntactic sugar for treating a waitable as a service
/// \param item The item
/// \return A service
/// \cert
/// \deterministic
inline std::shared_ptr<PollingServiceBase> as_service(const WaitableItem & item)
{
  return apex::get<std::shared_ptr<PollingServiceBase>>(item);
}

/// \brief Syntactic sugar for treating a waitable as a guard condition
/// \param item The item
/// \return A guard condition
/// \cert
/// \deterministic
inline std::shared_ptr<GuardConditionBase> as_guard_condition(const WaitableItem & item)
{
  return apex::get<std::shared_ptr<GuardConditionBase>>(item);
}

/// \brief Syntactic sugar for treating a waitable as a counting guard condition
/// \param item The item
/// \return A counting guard condition
/// \cert
/// \deterministic
inline std::shared_ptr<CountingGuardConditionBase> as_counting_guard_condition(
  const WaitableItem & item)
{
  return apex::get<std::shared_ptr<CountingGuardConditionBase>>(item);
}

/// \brief Transofrms a vector of one of the waitble variants into a vector of actual waitables
/// \param items The items to transform
/// \return The transformed items
template<class V,
  std::enable_if_t<apex::is_variant_member_type<V, WaitableItem>::value> * = nullptr>
std::vector<WaitableItem>
as_waitables(const std::vector<V> & items)
{
  std::vector<WaitableItem> result;
  result.reserve(items.size());
  for (const auto & item : items) {
    result.push_back(item);
  }

  return result;
}

/// \class WaitableGroup
/// \brief A subgroup of waitables
class RCLCPP_PUBLIC WaitableGroup
{
public:
  /// \brief The flags type
  using flags_t = std::uint32_t;
  /// \brief No flags
  static constexpr flags_t None = 0U;
  /// \brief Flag for a group which should never be executed on a thread pool
  static constexpr flags_t Immediate = 1U;

  WaitableGroup(const WaitableGroup &) = delete;
  WaitableGroup & operator=(const WaitableGroup &) = delete;
  virtual ~WaitableGroup() = default;

  /// \brief Tests if the group is flagged as active
  /// \return Whether the group is flagged as active
  /// \cert
  /// \deterministic
  bool is_hit() const noexcept {return m_hit;}

  /// \brief Flags the group as active/non-active
  /// \param hit The flag
  /// \cert
  /// \deterministic
  void set_hit(bool hit) noexcept {m_hit = hit;}

  /// \brief Returns all the waitables in the group
  /// \return All the waitables in the group
  /// \cert
  /// \deterministic
  const std::vector<WaitableItem> & get_waitables() const noexcept
  {
    return m_items;
  }

  /// \brief Returns the flags for this group
  /// \return The flags for this group
  /// \cert
  /// \deterministic
  const flags_t & get_flags() const noexcept
  {
    return m_flags;
  }

  /// \brief Default group has no callable so this method always throws
  /// This behavior should be modified in the implementations
  /// \cert
  /// \deterministic
  // Method is not conceptually const
  virtual void call()
  {
    rclcpp::exceptions::throw_from_rcl_error(RCL_RET_NOT_INIT,
      "No callable associated with the group");
  }

protected:
  friend inline std::shared_ptr<WaitableGroup> make_group(std::vector<WaitableItem>, flags_t);
  /// \brief Constructs a group
  /// \param items The waitables to include in the group
  /// \param flags The flags for the group
  /// \cert
  /// \deterministic
  WaitableGroup(std::vector<WaitableItem> items, flags_t flags) noexcept
  : m_items{std::move(items)}, m_flags{flags} {}

  std::vector<WaitableItem> m_items;
  bool m_hit{false};
  flags_t m_flags{0U};
};

/// \class CallableWaitableGroup
/// \tparam F The callable type
/// \brief A group of waitables with an associated callback
template<class F>
class CallableWaitableGroup : public WaitableGroup
{
public:
  /// \brief Calls the associated callable
  /// \cert
  /// \deterministic if m_f() is deterministic.
  void call() override
  {
    // *INDENT-OFF* (prevent uncrustify from making unnecessary indents here)
#ifndef TRACETOOLS_DISABLED
    try {
      TRACETOOLS_TRACEPOINT(waitable_group_callback_start, static_cast<const void *>(this));
#endif
      m_f();
#ifndef TRACETOOLS_DISABLED
      TRACETOOLS_TRACEPOINT(waitable_group_callback_end, false);
      // Intended catch and re-throw
    } catch (...) {
      TRACETOOLS_TRACEPOINT(waitable_group_callback_end, true);
      throw;
    }
#endif
    // *INDENT-ON*
  }

private:
  template<class U>
  friend std::shared_ptr<WaitableGroup> make_group(std::vector<WaitableItem>, U &&, flags_t);

  /// \brief Constructs a group with a callable
  /// \param items The waitables to include in the group
  /// \param f The associated callable
  /// \cert
  /// \deterministic if U is an rvalue ref and is movable.
  template<class U>
  CallableWaitableGroup(std::vector<WaitableItem> items, U && f, flags_t flags)
  : WaitableGroup(std::move(items), flags),
    m_f{std::forward<U>(f)} {}

  F m_f;
};

/// \brief A helper for creating a group without an associated callable
/// \param items The waitables to include in the group
/// \return The group without an associated callable
/// \cert
RCLCPP_PUBLIC
inline std::shared_ptr<WaitableGroup> make_group(
  std::vector<WaitableItem> items,
  WaitableGroup::flags_t flags = WaitableGroup::None)
{
  return std::shared_ptr<WaitableGroup>{new WaitableGroup(std::move(items), flags)};
}

/// \brief A helper for creating a group with an associated callable
/// \param items The waitables to include in the group
/// \param f The associated callable
/// \return The group with an associated callable
/// \cert
template<class U>
std::shared_ptr<WaitableGroup> make_group(
  std::vector<WaitableItem> items,
  U && f,
  WaitableGroup::flags_t flags = WaitableGroup::None)
{
  return std::shared_ptr<WaitableGroup>{new CallableWaitableGroup<std::decay_t<U>>(std::move(
        items), std::forward<U>(f), flags)};
}

/// \class WaitsetBase
/// \brief The base class for every waitset implementation
class RCLCPP_PUBLIC WaitsetBase
{
protected:
  using WaitableItemPtr = details::ptr_variant_from_shared_ptr_variant_t<WaitableItem>;

public:
  WaitsetBase() = default;
  WaitsetBase(const WaitsetBase &) = delete;
  WaitsetBase & operator=(const WaitsetBase &) = delete;
  virtual ~WaitsetBase() = default;

  /// \brief The type of predicate for interrupting handler execution
  using dispatch_unless_predicate_t = std::function<bool ()>;

  /// \brief Get number of waitable in the waitset
  /// \return The number of waitable in the waitset
  /// \cert
  /// \deterministic
  std::size_t size() const noexcept
  {
    return m_waitables.size();
  }

  /// \brief Test whether the waitset is empty
  /// \return Whether the waitset is empty
  /// \cert
  /// \deterministic
  bool empty() const noexcept
  {
    return m_waitables.empty();
  }

  /// \brief Add a waitable to the waitset
  /// \param item The waitable to add to the waitset
  /// \return True if the waitable was added, false if it already existed
  /// \cert
  //lint -e{1746} intentionally by value NOLINT
  bool add(WaitableItem item, WaitableGroup::flags_t flags = WaitableGroup::None);

  /// \brief Add waitables to the waitset
  /// \param items The waitables to add to the waitset
  /// \return True if all the waitables was added, false if at least one of the waitables
  /// has already existed in which case no waitables will be added
  /// \cert
  //lint -e{1746} intentionally by value NOLINT
  bool add(std::vector<WaitableItem> items, WaitableGroup::flags_t flags = WaitableGroup::None);

  /// \brief Add a waitable with a callable to the waitset
  /// \param item The waitable to add to the waitset
  /// \param callback The associated callable
  /// \return True if the waitable was added, false if it already existed
  /// \cert
  template<class U>
  //lint -e{1746} intentionally by value NOLINT
  bool add(WaitableItem item, U && callback, WaitableGroup::flags_t flags = WaitableGroup::None);

  /// \brief Add a waitable group to the waitset
  /// \param group The waitable group to add to the waitset
  /// \return True if the waitable group was added, false if at least one of the waitables
  /// in the groups already existed in which case nothing is added
  /// \cert
  //lint -e{1746} intentionally by value NOLINT
  bool add(std::shared_ptr<WaitableGroup> group);

  /// \brief Add waitable groups to the waitset
  /// \param groups The waitable groups to add to the waitset
  /// \return True if the waitable groups were added, false if at least one of the waitables
  /// in one of the groups already existed in which case nothing is added
  /// \cert
  //lint -e{1746} intentionally by value NOLINT
  bool add(std::vector<std::shared_ptr<WaitableGroup>> groups);

  /// \brief Test if a waitable was flagged as active by the last 'wait' operation
  /// \param item The waitable to test
  /// \return Whether the waitable was flagged as active by the last 'wait' operation
  /// \cert
  /// \deterministic
  bool operator[](const WaitableItem & item) const noexcept;

  /// \brief Test if the waitable group was flagged as active by the last 'wait' operation
  /// \param group The waitable group to test
  /// \return Whether the waitable group was flagged as active by the last 'wait' operation
  /// \cert
  /// \deterministic
  bool operator[](const std::shared_ptr<WaitableGroup> & group) const noexcept;

  /// \brief Enable a waitable so it participates in the next 'wait' operation
  /// Throws if the waitable does not belong to the waitset
  /// \param item A waitable to enable
  /// \cert
  /// \deterministic
  void enable(const WaitableItem & item)
  {
    set_status(as_non_owning_ptr_unchecked(item), true);
  }

  /// \brief Enable all waitables in the group so they participate in the next 'wait' operation
  /// Throws if any waitable in group does not belong to the waitset
  /// \param group A waitable group to enable
  /// \cert
  /// \deterministic
  void enable(const std::shared_ptr<WaitableGroup> & group)
  {
    set_status(group, true);
  }

  /// \brief Disable a waitable so it does not participates in the next 'wait' operation
  /// Throws if the waitable does not belong to the waitset
  /// \param item A waitable to disable
  /// \cert
  /// \deterministic
  void disable(const WaitableItem & item)
  {
    set_status(as_non_owning_ptr_unchecked(item), false);
  }

  /// \brief Disable all waitables in the group so they do not participate
  /// in the next 'wait' operation
  /// Throws if any waitable in group does not belong to the waitset
  /// \param group A waitable group to disable
  /// \cert
  /// \deterministic
  void disable(const std::shared_ptr<WaitableGroup> & group)
  {
    set_status(group, false);
  }

  /// \brief Wait until at least one of the waitables becomes active or until the timeout
  /// expires
  /// \param timeout The timeout for the wait operation, -1 is infinite
  /// \return True if at least one of the waitables became active or false in case of timeout
  /// \note This method is not thread-safe. Only one thread may wait on the waitset at any time
  /// \cert
  /// \deterministic
  //lint -e{1746} intentionally by value NOLINT
  bool wait(std::chrono::nanoseconds timeout)
  {
    reset_hits();
    return wait_native(timeout, WaitAction::Hit, nullptr);
  }

  /// \brief Wait until at least one of the waitables becomes active
  /// \note This method is not thread-safe. Only one thread may wait on the waitset at any time
  /// \cert
  /// \deterministic
  void wait()
  {
    {
      (void) wait(std::chrono::nanoseconds(-1));
    }
  }

  /// \brief Wait until at least one of the waitables becomes active or until the timeout
  /// expires
  /// Then, in case any waitables become active, execute their callbacks in the order
  /// of their discovery
  /// If the callback throws this function will throw
  /// \param timeout The timeout for the wait operation, -1 is infinite
  /// \return True if at least one of the waitables became active or false in case of timeout
  /// \cert
  /// \deterministic if all callables associated with the waitables are deterministic.
  //lint -e{1746} intentionally by value NOLINT
  bool wait_and_dispatch_all(std::chrono::nanoseconds timeout)
  {
    reset_hits();
    return wait_native(timeout, WaitAction::CallAll, nullptr);
  }

  /// \brief Wait until at least one of the waitables becomes active or until the timeout
  /// expires
  /// Then, in case any waitables become active, execute their callbacks in the order
  /// of their discovery unless the predicate evaluates to true
  /// If the callback throws this function will throw
  /// \param timeout The timeout for the wait operation, -1 is infinite
  /// \param unless The predicate to evaluate before executing the callbacks
  /// \return True if at least one of the waitables became active or false in case of timeout
  /// \cert
  /// \deterministic if all callables associated with the waitables are deterministic.
  bool wait_and_dispatch_all_unless(
    std::chrono::nanoseconds timeout,
    dispatch_unless_predicate_t & unless)
  {
    reset_hits();
    m_execute_unless_predicate = &unless;
    return wait_native(timeout, WaitAction::CallAll, nullptr);
  }

  /// \brief Wait until at least one of the waitables becomes active or until the timeout
  /// expires
  /// Then, in case any waitables become active, execute their callbacks in the order
  /// of their discovery
  /// If the callback throws this function will throw
  /// \param timeout The timeout for the wait operation, -1 is infinite
  /// \param thread_pool A thread pool to dispatch callbacks on
  /// \return True if at least one of the waitables became active or false in case of timeout
  /// \cert
  /// \deterministic if all callables associated with the waitables are deterministic.
  bool wait_and_dispatch_all(std::chrono::nanoseconds timeout, ThreadPool & thread_pool)
  {
    reset_hits();
    auto res = wait_native(timeout, WaitAction::CallAll, &thread_pool);
    thread_pool.wait_all();
    return res;
  }

  /// \brief Wait until at least one of the waitables becomes active or until the timeout
  /// expires
  /// Then, in case any waitables become active, execute their callbacks in the order
  /// of their discovery unless the predicate evaluates to true
  /// If the callback throws this function will throw
  /// \param timeout The timeout for the wait operation, -1 is infinite
  /// \param thread_pool A thread pool to dispatch callbacks on
  /// \param unless The predicate to evaluate before executing the callbacks
  /// \return True if at least one of the waitables became active or false in case of timeout
  /// \cert
  /// \deterministic if all callables associated with the waitables are deterministic.
  bool wait_and_dispatch_all_unless(
    std::chrono::nanoseconds timeout,
    dispatch_unless_predicate_t & unless,
    ThreadPool & thread_pool)
  {
    reset_hits();
    m_execute_unless_predicate = &unless;
    auto res = wait_native(timeout, WaitAction::CallAll, &thread_pool);
    thread_pool.wait_all();
    return res;
  }

  /// \brief Wait until at least one of the waitables becomes active
  /// Then, in case any waitables become active, execute their callbacks in the order
  /// of their discovery
  /// If the callback throws this function will throw
  /// \cert
  /// \deterministic if all callables associated with the waitables are deterministic.
  void wait_and_dispatch_all()
  {
    (void) wait_and_dispatch_all(std::chrono::nanoseconds(-1));
  }

  /// \brief Wait until at least one of the waitables becomes active
  /// Then, in case any waitables become active, execute their callbacks in the order
  /// of their discovery unless the predicate evaluates to true
  /// If the callback throws this function will throw
  /// \param unless The predicate to evaluate before executing the callbacks
  /// \cert
  /// \deterministic if all callables associated with the waitables are deterministic.
  void wait_and_dispatch_all_unless(dispatch_unless_predicate_t & unless)
  {
    (void) wait_and_dispatch_all_unless(std::chrono::nanoseconds(-1), unless);
  }

  /// \brief Wait until at least one of the waitables becomes active
  /// Then, in case any waitables become active, execute their callbacks in the order
  /// of their discovery
  /// If the callback throws this function will throw
  /// \param thread_pool A thread pool to dispatch callbacks on
  /// \cert
  /// \deterministic if all callables associated with the waitables are deterministic.
  void wait_and_dispatch_all(ThreadPool & thread_pool)
  {
    (void) wait_and_dispatch_all(std::chrono::nanoseconds(-1), thread_pool);
  }

  /// \brief Wait until at least one of the waitables becomes active
  /// Then, in case any waitables become active, execute their callbacks in the order
  /// of their discovery unless the predicate evaluates to true
  /// If the callback throws this function will throw
  /// \param thread_pool A thread pool to dispatch callbacks on
  /// \param unless The predicate to evaluate before executing the callbacks
  /// \cert
  /// \deterministic if all callables associated with the waitables are deterministic.
  void wait_and_dispatch_all_unless(dispatch_unless_predicate_t & unless, ThreadPool & thread_pool)
  {
    (void) wait_and_dispatch_all_unless(std::chrono::nanoseconds(-1), unless, thread_pool);
  }

  /// \brief Tests whether all of the individual waitables are hit
  /// \return Whether all of the individual waitables are hit
  /// \cert
  /// \deterministic
  bool all_waitables() const noexcept {return m_current_hits == m_waitables.size();}

  /// \brief Tests whether all of the groups are hit
  /// \return Whether all of the groups are hit
  /// \cert
  /// \deterministic
  bool all_groups() const noexcept {return m_current_group_hits == m_groups.size();}

  /// \brief Tests whether none of the individual waitables are hit
  /// \return Whether none of the individual waitables are hit
  /// \cert
  /// \deterministic
  bool none() const noexcept {return m_current_hits == 0U;}

  /// \brief Adds a worker that runs in the background while the waitset is waiting. The background
  /// worker can influence the outcome of the wait operation.
  ///
  /// The background worker will be `start()`-ed just before the waitset suspends and `stop()`-ed
  /// just after the waitset wakes up. While the background worker is active (i.e., between
  /// `start()` and `stop()`), the worker can interact with the waitset, for example by changing
  /// guard conditions in the waitset. While the background worker is paused (i.e., between
  /// `stop()` and `start()`), the background worker must not interact with the waitset and must
  /// not change any guard conditions in the waitset.
  ///
  /// The background worker must fulfill the following constraints:
  /// 1. It is not allowed to create any threads or allocate any memory
  /// after it is passed to this function
  /// 2. It is not allowed to destroy any threads or deallocate any memory unless the
  /// background_wait_worker object itself is being destroyed
  /// 3. The background worker must be fully functional when `background_worker::start()` returns.
  /// If `start()` depends on an asynchronous operation, `start()` must wait for it to
  /// complete before returning.
  /// 4. The background worker runs concurrently with the native `wait()` call. Any interaction
  /// with the `wait()` result must happen in a thread-safe manner
  /// 5. The background worker must be fully stopped when the `background_wait_worker::stop()`
  /// method returns. If `stop()` depends on an asynchronous operation, `stop()` must wait for it
  /// to complete before returning
  ///
  /// Note: some waitset implementations do not support background workers. Such implementations
  /// must override this method and throw an RCL_RET_UNSUPPORTED exception.
  ///
  /// \param worker The background worker
  /// \cert
  virtual void add_background_worker(background_worker & worker)
  {
    m_background_workers.push_back(&worker);
  }

  /// \brief The iterator over waitable type
  using const_iterator = std::vector<WaitableItemPtr>::const_iterator;

  /// \brief Returns a const iterator to the beginning of the waitables
  /// \cert
  /// \deterministic
  const_iterator begin() const noexcept {return m_waitables.begin();}

  /// \brief Returns a const iterator to the end of the waitables
  /// \cert
  /// \deterministic
  const_iterator end() const noexcept {return m_waitables.end();}

  /// \brief Returns a const iterator to the beginning of the waitables
  /// \cert
  /// \deterministic
  const_iterator cbegin() const noexcept {return m_waitables.cbegin();}

  /// \brief Returns a const iterator to the end of the waitables
  /// \cert
  /// \deterministic
  const_iterator cend() const noexcept {return m_waitables.cend();}

protected:
  class background_workers_runner
  {
public:
    explicit background_workers_runner(const std::vector<background_worker *> & workers) noexcept
    : m_workers{&workers}
    {
      for (const auto & worker : *m_workers) {
        worker->start();
      }
    }

    ~background_workers_runner()
    {
      for (const auto & worker : *m_workers) {
        worker->stop();
      }
    }

    background_workers_runner(const background_workers_runner &) = delete;
    background_workers_runner & operator=(const background_workers_runner &) = delete;

private:
    const std::vector<background_worker *> * const m_workers;
  };

  /// \brief Resets the flags of the individual waitables and groups
  /// \cert
  /// \deterministic
  void reset_hits() noexcept;

  /// \brief Set a status for a waitable (enabled/disabled)
  /// This implementation should throw if the waitable does not belong to the waitset
  /// \param ptr A waitable to set the status of
  /// \param status The status (enabled == true, disabled == false)
  /// \cert
  /// \deterministic Note: the implementation has to be deterministic
  virtual void set_status(WaitableItemPtr ptr, bool status) = 0;

  /// \brief Set status for all waitables in a group (enabled/disabled)
  /// This implementation should throw if any of the waitables does not belong to the waitset
  /// \param group A group to set the status of
  /// \param status The status (enabled == true, disabled == false)
  /// \cert
  /// \deterministic Note: the implementation has to be deterministic
  virtual void set_status(const std::shared_ptr<WaitableGroup> & group, bool status) = 0;

  /// \brief The action for the 'wait' functions
  enum class WaitAction : std::int32_t {Hit, CallAll};

  /// \brief Helper which implements the logic of a waitable becoming active
  /// Should be called by the implementation when it discovers a hit
  /// \param ptr The waitable which became active
  /// \param action The action forwarded from 'wait_native()' call
  /// \param thread_pool An optional thread pool to dispatch callbacks on
  /// \return False if the flag processing should stop after this call or true otherwise
  /// \cert
  /// \deterministic
  bool register_hit(WaitableItemPtr ptr, WaitAction action, ThreadPool * thread_pool);

  /// \brief An implementation defined 'add subscribers' functionality
  /// This function should use m_subscribers array to add new subscribers for the implementation
  /// \cert
  virtual void add_native() = 0;

  /// \brief An implementation defined 'wait' functionality
  /// \param timeout The timeout for wait, -1 is infinite
  /// \param action Wait action
  /// \param thread_pool An optional thread pool to dispatch callbacks on
  /// \return False if timeout happened or true if any of the subscribers became active
  /// \cert
  /// \deterministic Note: the implementation has to be deterministic
  /// Note: if timeout is -1 the function MUST block until it can return `true`
  //lint -e{1746} intentionally by value NOLINT
  virtual bool wait_native(
    std::chrono::nanoseconds timeout,
    WaitAction action,
    ThreadPool * thread_pool) = 0;

  /// \brief A visitor which extracts the raw pointer from a shared one
  struct convert_to_plain_ptr_visitor
  {
    /// \brief A visitor function which extracts the raw pointer from a shared one
    /// \param ptr The shared pointer
    /// \return The raw pointer
    /// \cert
    /// \deterministic
    template<class T>
    WaitableItemPtr operator()(const std::shared_ptr<T> & ptr) const noexcept
    {
      return ptr.get();
    }
  };

  /// \brief A visitor which extracts the raw pointer from a shared one checking it for null
  struct validate_and_convert_to_plain_ptr_visitor
  {
    /// \brief A visitor function which extracts the raw pointer from a shared one
    /// checking it for null
    /// \param ptr The shared pointer
    /// \return The raw pointer
    /// \cert
    /// \deterministic
    template<class T>
    WaitableItemPtr operator()(const std::shared_ptr<T> & ptr) const
    {
      if (ptr == nullptr) {
        rclcpp::exceptions::throw_from_rcl_error(RCL_RET_INVALID_ARGUMENT,
          "Bad waitable pointer");
      }
      return v(ptr);
    }

    convert_to_plain_ptr_visitor v;
  };

  /// \brief Returns a raw pointer to the item, testing it for null first
  /// \param item The waitable item
  /// \return The raw pointer to the waitable item
  /// \cert
  /// \deterministic
  static WaitableItemPtr as_non_owning_ptr(const WaitableItem & item)
  {
    return apex::visit(validate_and_convert_to_plain_ptr_visitor{}, item);
  }

  /// \brief Returns a raw pointer to the item
  /// \param item The waitable item
  /// \return The raw pointer to the waitable item
  /// \cert
  /// \deterministic
  static WaitableItemPtr as_non_owning_ptr_unchecked(const WaitableItem & item) noexcept
  {
    return apex::visit(convert_to_plain_ptr_visitor{}, item);
  }

#ifndef TRACETOOLS_DISABLED
  struct convert_to_void_ptr_visitor
  {
    /// \brief A visitor function which extracts the void pointer from a raw one
    /// \param ptr The raw pointer
    /// \return The void pointer
    template<class T>
    const void * operator()(const T * ptr) const noexcept
    {
      return static_cast<const void *>(ptr);
    }
  };

  /// \brief Returns a void pointer to the item pointer
  /// \param item The waitable item pointer
  /// \return The void pointer to the waitable item pointer
  inline const void * as_const_void_ptr(const WaitableItemPtr & item)
  {
    // return apex::visit([](const auto & w) {return static_cast<const void *>(w);}, item);
    return apex::visit(convert_to_void_ptr_visitor{}, item);
  }
#endif  // TRACETOOLS_DISABLED

  /// \brief A helper which defines the behavior of waitable items on testing their state
  /// \tparam DefaultTestFunction The default test function to fall back (== use on subscriptions)
  template<class DefaultTestFunction>
  struct hit_tester
  {
    /// \brief Creates a helper which defines the behavior of waitable items on testing their state
    /// \param func The default test function to fall back (== use on subscriptions)
    /// \cert
    /// \deterministic
    template<class U>
    explicit hit_tester(U && func)
    : default_test_function{std::forward<U>(func)}
    {
    }

    /// \brief Does nothing for a regular subscription
    /// \cert
    /// \deterministic
    //lint -e{9175}  No side effects function NOLINT
    bool operator()(const rclcpp::PollingSubscriptionBase *) const
    {
      return default_test_function();
    }

    /// \brief Does nothing for a regular client
    /// \cert
    /// \deterministic
    //lint -e{9175}  No side effects function NOLINT
    bool operator()(const rclcpp::PollingClientBase *) const
    {
      return default_test_function();
    }

    /// \brief Does nothing for a regular service
    /// \cert
    /// \deterministic
    //lint -e{9175}  No side effects function NOLINT
    bool operator()(const rclcpp::PollingServiceBase *) const
    {
      return default_test_function();
    }

    /// \brief Atomically reads and resets the flag of the guard condition
    /// \param guard The guard condition to operate on
    /// \cert
    /// \deterministic
    bool operator()(GuardConditionBase * guard) const
    {
      return guard->test_and_reset();
    }

    /// \brief Atomically reads and decreases the counter of the counting guard condition
    /// \param guard The counting guard condition to operate on
    /// \cert
    /// \deterministic
    bool operator()(CountingGuardConditionBase * guard) const
    {
      return guard->decrease() != 0U;
    }

    DefaultTestFunction default_test_function;
  };

  /// \brief Tests for a hit on a waitable and resets the state atomically when applicable
  /// \param item A waitable to run on
  /// \param default_test_function The default test function to fall back (== use on subscriptions)
  /// \return Whether the waitable was hit
  /// \cert
  /// \deterministic
  template<class U>
  static bool test_hit(WaitableItemPtr item, U && default_test_function) noexcept
  {
    const hit_tester<std::decay_t<U>> v{std::forward<U>(default_test_function)};
    return apex::visit(v, item);
  }

  /// \class MemberInfo
  /// The internal pair of the related waitable group and the "active" flag for it
  struct MemberInfo
  {
    /// \brief Creates the pair
    /// \param group The group related to the waitable
    /// \cert
    /// \deterministic
    explicit MemberInfo(WaitableGroup & group)
    : group{ & group}
    {}

    bool hit{false};
    WaitableGroup * group{nullptr};
  };

  std::unordered_map<WaitableItemPtr, MemberInfo> m_members;
  std::unordered_set<std::shared_ptr<WaitableGroup>> m_groups;
  std::vector<WaitableItemPtr> m_waitables;
  std::size_t m_current_hits{0U};
  std::size_t m_current_group_hits{0U};
  std::vector<background_worker *> m_background_workers;
  dispatch_unless_predicate_t * m_execute_unless_predicate{nullptr};
};

template<class U>
bool WaitsetBase::add(WaitableItem item, U && callback, WaitableGroup::flags_t flags)
{
  const auto ptr = as_non_owning_ptr(item);
  auto gr = make_group({std::move(item)}, std::forward<U>(callback), flags);
  const auto res = m_members.emplace(ptr, MemberInfo{*gr}).second;
  TRACETOOLS_TRACEPOINT(
    waitset_add_item,
    static_cast<const void *>(this),
    static_cast<const void *>(gr.get()),
    as_const_void_ptr(ptr));
  if (res) {
    m_groups.insert(std::move(gr));
    m_waitables.push_back(ptr);
    add_native();
  }

  return res;
}

}  // namespace dynamic_waitset
}  // namespace rclcpp

#endif  // RCLCPP__DYNAMIC_WAITSET__WAITSET_BASE_HPP_
