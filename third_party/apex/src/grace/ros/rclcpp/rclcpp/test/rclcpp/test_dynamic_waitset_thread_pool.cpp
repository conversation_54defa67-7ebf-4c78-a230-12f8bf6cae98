/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#include <gtest/gtest.h>

#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <rclcpp/dynamic_waitset/guard_condition.hpp>
#include <rclcpp/dynamic_waitset/waitset_thread_pool.hpp>

#include <algorithm>
#include <vector>
#include <set>
#include <memory>
#include <exception>
#include <utility>
#include <mutex>

namespace dw = rclcpp::dynamic_waitset;
using rclcpp::dynamic_waitset::make_group;
using rclcpp::dynamic_waitset::WaitableGroup;
using rclcpp::dynamic_waitset::WaitableItem;
using rclcpp::dynamic_waitset::ThreadPool;
using rclcpp::dynamic_waitset::GuardCondition;
using rclcpp::dynamic_waitset::GuardConditionBase;
using rclcpp::dynamic_waitset::as_waitables;

TEST(TestDynamicWaitsetThreadPool, run) {
  ThreadPool tp{5U};
  ASSERT_EQ(tp.get_concurrency(), 5U);
  const auto group_count = tp.get_concurrency() + 3;
  std::vector<std::int32_t> counters(group_count);
  std::set<std::thread::id> threads;
  std::vector<std::shared_ptr<GuardConditionBase>> gc(group_count);
  std::vector<std::shared_ptr<WaitableGroup>> groups(group_count);
  std::mutex m;

  std::generate(std::begin(gc), std::end(gc), [] {return std::make_shared<GuardCondition>();});
  auto i = 0;
  std::generate(std::begin(groups), std::end(groups),
    [&] {
      WaitableItem wi = gc[i];
      auto g = make_group({std::move(wi)}, [i, &counters, &threads, &m] {
        std::unique_lock<std::mutex> l{m};
        threads.insert(std::this_thread::get_id());
        ++counters[i];
      });
      ++i;
      return g;
    }
  );

  for (auto j = 1; j <= 10000; ++j) {
    for (const auto & group : groups) {
      tp.launch(*group);
    }
    ASSERT_NO_THROW(tp.wait_all());
    for (const auto cnt : counters) {
      ASSERT_EQ(cnt, j);
    }
  }

  ASSERT_EQ(threads.size(), tp.get_concurrency());
  ASSERT_NO_THROW(tp.stop());
}

TEST(TestDynamicWaitsetThreadPool, exception) {
  ThreadPool tp{5U};
  auto gc = std::make_shared<GuardCondition>();
  auto g = make_group({WaitableItem{std::move(gc)}}, [] {
        throw std::out_of_range{"oops"};
      });
  ASSERT_NO_THROW(tp.launch(*g));
  ASSERT_THROW(tp.wait_all(), std::out_of_range);
}
