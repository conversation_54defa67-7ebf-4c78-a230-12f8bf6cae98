# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pluginlib_resources")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_pkg//:mappings.bzl", "pkg_files")

ENTRY_POINTS = {
    "console_scripts": [
        "apex_topic_monitor = apex_topic_monitor.main:main",
    ],
}

ros_pkg(
    name = "apex_topic_monitor_pkg",
    description = "A Python GUI plugin to introspect Apex.Grace ROS topics.",
    license = "Apex.AI Proprietary License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "apex_topic_monitor",
    pluginlib_description_files = {
        "plugin.xml": "rqt_gui",
    },
    py_libraries = [":apex_topic_monitor"],
    share_data = [":apex_topic_monitor_share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "Programming Language :: Python",
        "Topic :: Software Development",
    ],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "@apex//grace/cli/ros2topic:ros2topic_pkg",
        "@apex//grace/ros/rclpy/rclpy:rclpy_pkg",
        "@apex//grace/rosidl/rosidl_runtime_py:rosidl_runtime_py_pkg",
        "@ros-visualization.python_qt_binding//:python_qt_binding_pkg",
        "@ros-visualization.qt_gui_core//qt_gui:qt_gui_pkg",
        "@ros-visualization.rqt//rqt_gui:rqt_gui_pkg",
        "@ros-visualization.rqt//rqt_gui_py:rqt_gui_py_pkg",
        "@ros-visualization.rqt//rqt_py_common:rqt_py_common_pkg",
    ],
)

pkg_files(
    name = "apex_topic_monitor_share_data",
    srcs = glob([
        "resource/*.ui",
    ]),
    prefix = "resource",
)

ament_pkg_resources(
    name = "ament_resources",
    package = "apex_topic_monitor",
    resources = {
        "package.xml": "share",
        "apex_topic_monitor_share_data": "share",
    },
)

ament_pluginlib_resources(
    name = "pluginlib_resources",
    data = [":ament_resources"],
    pluginlib_type = "rqt_gui",
    resources = {
        "apex_topic_monitor": "plugin.xml",
    },
    visibility = ["//visibility:public"],
)

py_entry_points_library(
    name = "apex_topic_monitor",
    srcs = glob(["src/**/*.py"]),
    data = [
        ":apex_topic_monitor_pkg.wheel_data",
        ":pluginlib_resources",
    ],
    entry_points = ENTRY_POINTS,
    imports = ["src"],
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/cli/ros2topic",
        "@apex//grace/ros/rclpy/rclpy",
        "@apex//grace/rosidl/rosidl_runtime_py",
        "@ros-visualization.python_qt_binding//:python_qt_binding",
        "@ros-visualization.rqt//rqt_gui:rqt_gui",
        "@ros-visualization.rqt//rqt_gui_py:rqt_gui_py",
        "@ros-visualization.rqt//rqt_py_common:rqt_py_common",
    ],
)

apex_py_test(
    name = "test_models",
    srcs = glob(["test/models/*.py"]),
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    # Coverage is not useful for these tests (AP) - skip it
    # TODO(AP): Check this as part of 35053
    tags = ["skip_coverage"],
    deps = [
        ":apex_topic_monitor",
        requirement("pytest-qt"),
    ],
)
