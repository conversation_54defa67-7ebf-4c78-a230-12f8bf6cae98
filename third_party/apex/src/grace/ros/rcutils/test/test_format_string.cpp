// Copyright 2017 Open Source Robotics Foundation, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <gtest/gtest.h>

#include <string>

#include "./allocator_testing_utils.h"

#include "rcutils/allocator.h"
#include "rcutils/format_string.h"

/// @test{
/// "req" : ["SWRQ_RCUTILS_547"]
/// }
TEST(test_format_string_limit, nominal) {
  {
    auto allocator = rcutils_get_default_allocator();
    char * formatted = rcutils_format_string_limit(allocator, 10, "%s", "test");
    EXPECT_STREQ("test", formatted);
    if (formatted) {
      allocator.deallocate(formatted, allocator.state);
    }
  }

  {
    auto allocator = rcutils_get_default_allocator();
    char * formatted = rcutils_format_string_limit(allocator, 3, "%s", "test");
    EXPECT_STREQ("te", formatted);
    if (formatted) {
      allocator.deallocate(formatted, allocator.state);
    }
  }

  {
    auto allocator = rcutils_get_default_allocator();
    char * formatted = rcutils_format_string_limit(allocator, 3, "string is too long %s", "test");
    EXPECT_STREQ("st", formatted);
    if (formatted) {
      allocator.deallocate(formatted, allocator.state);
    }
  }
}

/// @test{
/// "req" : ["SWRQ_RCUTILS_547"]
/// }
TEST(test_format_string_limit, invalid_arguments) {
  auto allocator = rcutils_get_default_allocator();
  auto failing_allocator = get_failing_allocator();

  char * formatted = rcutils_format_string_limit(allocator, 10, NULL);
  EXPECT_STREQ(NULL, formatted);

  formatted = rcutils_format_string_limit(failing_allocator, 10, "%s", "test");
  EXPECT_STREQ(NULL, formatted);

  formatted = rcutils_format_string_limit(rcutils_get_zero_initialized_allocator(), 10, "%s",
      "test");
  EXPECT_STREQ(NULL, formatted);
}
