# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

from pydantic import BaseModel
from pathlib import Path
from typing import List, Mapping


class SerializationConfig(BaseModel):
    """Convenience class to read json arguments."""

    type_model_path: Path
    dep_typemodels: List[Path]
    output_map: Mapping[str, str]
    import_lookup_map: Mapping[str, Mapping[str, str]]
    genfile_prefix: str


class CliConfig(BaseModel):
    serialization_config: SerializationConfig
