/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#include "process_manager/process_manager/process_manager_factory.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{

using apex::settings::inspect::array_view;
using apex::settings::inspect::dictionary_view;
using apex::settings::inspect::get;
using apex::settings::inspect::maybe;
using apex::settings::inspect::no_value;

using config::get_process_manager_config;

void create_log_directories(const ProcessManagerConfig & config)
{
  if (std::filesystem::exists(config.log_directory) &&
      !std::filesystem::is_empty(config.log_directory)) {
    throw apex::runtime_error("log directory is not empty: ", config.log_directory);
  }
  std::filesystem::create_directory(config.log_directory);
  if (config.essential_group.has_value()) {
    std::filesystem::create_directory(config.essential_group->log_directory);
  }
  if (config.framework_group.has_value()) {
    std::filesystem::create_directory(config.framework_group->log_directory);
  }
  for (const auto & process_group_config : config.process_groups) {
    std::filesystem::create_directory(process_group_config.log_directory);
  }
}

PROCESS_MANAGER_PUBLIC
std::unique_ptr<ProcessManager> create_process_manager(
  const ProcessManagerConfig & config, timer_service::timer_service_interface & timer_service)
{
  return std::make_unique<ProcessManager>(config, timer_service);
}

PROCESS_MANAGER_PUBLIC
std::shared_ptr<apex::process_manager::process_group::EssentialGroup> create_essential_group(
  const apex::process_manager::ProcessManagerConfig & config)
{
  using apex::process_manager::process_group::EssentialGroup;
  std::shared_ptr<EssentialGroup> essential_group{nullptr};
  if (config.essential_group.has_value()) {
    essential_group = std::make_shared<EssentialGroup>(config.essential_group.value());
  }
  return essential_group;
}

}  // namespace process_manager
}  // namespace apex
