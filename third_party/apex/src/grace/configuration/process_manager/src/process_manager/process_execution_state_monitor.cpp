/// \copyright Copyright 2025 Apex.AI, Inc.
/// All rights reserved.

#include "process_manager/process_manager/process_execution_state_monitor.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::process_manager
namespace process_manager
{

void ProcessExecutionStateMonitor::handle_process_execution_state_msg()
{
  auto msgs(m_process_execution_state_sub->take());
  for (const auto msg : msgs) {
    if (msg.info().valid()) {
      const auto pid = msg.data().pid;
      const auto process_group = find_group(pid);
      if (process_group) {
        handle_process_execution_for_process_group(process_group, pid);
      }
    }
  }
}

void ProcessExecutionStateMonitor::handle_process_execution_for_process_group(
  std::shared_ptr<process_group::ProcessGroup> process_group, int32_t pid)
{
  const auto & processes = process_group->get_processes();
  const auto found_proces =
    std::find_if(processes.begin(), processes.end(), [&pid](const auto & process) {
      return process.second->get_pid() == pid;
    });
  if (found_proces != processes.end()) {
    const auto & process = found_proces->second;
    report_execution_state(process_group, process, pid);
  }
}

void ProcessExecutionStateMonitor::report_execution_state(
  std::shared_ptr<process_group::ProcessGroup> process_group,
  std::shared_ptr<process_group::Process> process,
  int32_t pid)
{
  const auto & state = process->get_state();
  const auto & report_type = process->get_config().report_type;

  // note it is possible that a process reports before we are able to change the state to
  // STARTING
  if (report_type == ExecutionStateReportType::ByTopic) {
    if ((state == ProcessExecutionState::STARTING) || (state == ProcessExecutionState::IDLE)) {
      process->set_is_running_reported(true);
      process_group->get_event_communicator()->process_starting_event.trigger();
    } else {
      GROUP_LOG_WARN(process_group,
                     apex::no_separator{},
                     "Unexpected process running message received from process with PID '",
                     pid,
                     "'. Message received in a wrong ProcessExecutionState state");
    }
  } else {
    GROUP_LOG_WARN(process_group,
                   apex::no_separator{},
                   "Unexpected process running message received from process with PID '",
                   pid,
                   "'. Wrong ExecutionStateReportType configuration");
  }
}
}  // namespace process_manager
}  // namespace apex
