---
tags:
- process_manager
---

# State transition graph

A process group state transition will execute a series of actions starting and stopping processes.
This document explains how these state transitions work and how they are defined from a
launch file.

As explained in [Process Manager](process-manager-design.md#state-transition),
a state transition is a combination of start and stop actions which are executed in a pre-defined
order to comply with the origin state shutdown dependencies and the target state startup
dependencies.

Each state transition is implemented as an executable graph containing a sequence of `Start` and
`Stop` actions as nodes. The rationale behind using a graph for a state transition is to embed the
dependencies between processes as part of the graph and allow to execute concurrently those actions
which are not dependent.

For each origin and target state a state transition graph is created. Each state transition graph
contains `Start` or `Stop` actions as graph nodes.

All the graphs are pre-generated based on launch file and can be pre-visualized using the
introspection tool.

## Graph creation algorithm

To create the action graph the origin state shutdown process dependency graph and the target
state start-up process dependency graph are used

The graph is created in an iterative process:

1. An initial action list is created. The following rules are applied:
    - If a process is in the origin state and it is not in the target state, the process has to
      be stopped. It is added to the `Stop` action list
    - If a process is in not in the origin state and it is in the target state, the process has
      to be started. It is added to the `Start` action list
    - If a process is in both the origin and target state with the same start-up configuration
      the process should remain running. It is added to the `NoAction` list
    - If a process is in both the origin and target state but is has different start-up
      configurations it has to be restarted. The process is added to the `Stop` and `Start` action
      lists
1. All the processes in the `NoAction` list are used in an iterative process to find if they have
   some dependencies which will result in the process requiring starting or stopping. The dependency
   resolution process works as follows:
    - If any process in the `Start` or `Stop` list is reachable for a process in the `NoAction`
      list, it is moved to the `Start` or `Stop` list.
    - The same step is repeated for each process in the `NoAction` list until there are no more
      changes.
1. With the resulting list of actions, a shutdown action graph and start-up action graph are
   created based on the process dependencies.
1. The shutdown action graph and start-up action graphs are merged using the following rules:
    - A leaf action node of the shutdown action graph is connected to a root action node of the
      start-up action graph if they have any process in common which are part of the same
      [graph component](https://en.wikipedia.org/wiki/Component_(graph_theory)).

## Step-by-step example

Given the following launch file, in the following sections it is shown how a state transition
graph is created:

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_complex_graph.launch.yaml',
{'tag': '# [launch]'},
'yaml') }}

### Process dependencies

In order to generate the state transition from `STATE1` to `STATE2`, the processes contained in
each state are listed:

- `STATE1`: `A1`, `B`, `C`, `D`, `E`, `F`
- `STATE2`: `A2`, `B`, `C`, `D`, `E`, `F`, `G`

!!! note
    Process `A` has different start-up configuration in `STATE1` and `STATE2`. This is represented
    here as `A1` and `A2` for visualization purposes.

In the same way, `STATE1` shutdown and `STATE2` start-up process dependency graphs are required:

```mermaid
graph TD
    subgraph STATE1[<b>State 1: shutdown process dependencies<b/>]
     n0(F)
     n1(E) --> n0(F)
     n2(D)
     n3(C)
     n4(B) --> n2(D)
     n4(B) --> n3(C)
     n5(A1)
    end
```

```mermaid
graph TD
    subgraph STATE2[<b>State 2: start-up process dependencies<b/>]
     n0(G)
     n1(F)
     n2(E) --> n1(F)
     n3(D)
     n4(C) --> n0(G)
     n5(B) --> n3(D)
     n5(B) --> n4(C)
     n6(A2)
    end
```

### Initial action list

An initial action list action is created based on which processes are in each state and their
start-up configuration:

- `G` is not in `STATE1`. It is added to the `Start` list
- `A` has a different start-up configuration in `STATE2` (represented as `A2`). It is added to the
  `Stop` list (`A1`) and the `Start` list (`A2`)

This result in the following list of actions:

- `Start`: `A2`, `G`
- `Stop`: `A1`
- `NoAction`: `B`, `C`, `D`, `E`, `F`

### Dependency resolution

For each process in the `NoAction` list, it is checked if there are dependencies which will
result in this process requiring to start and stop it.

- `G` is in the `Start` list and is reachable for processes `B` and `C` as part of the start-up
  dependency graph. They are moved to the `Start` and `Stop` lists.

This result in the following list of actions:

- `Start`: `A2`, `G`, `B`, `C`
- `Stop`: `A1`, `B`, `C`
- `NoAction`: `D`, `E`, `F`

### Shutdown and start-up action graphs

With the resulting list, a shutdown and start-up action graphs are created:

`STATE1` shutdown action graph:

```mermaid
graph TD
    subgraph STATE1[<b>State 1<b/>]
     n0(Stop A)
     n1(Stop C) --> n3(Start G)
     n2(Stop B) --> n1(Stop C)
    end
```

State 2 start-up action graph:

```mermaid
graph TD
    subgraph STATE2[<b>State 2<b/>]
     n3(Start G) --> n5(Start C)
     n4(Start A)
     n5(Start C) --> n6(Start B)
     n6(Start B)
    end
```

### Shutdown and start-up action graphs merging

The leaf nodes of the `STATE1` shutdown action graph (red) are connected with root nodes of the
`STATE2` start-up action graph (green) in case they share processes in the same graph component.

```mermaid
graph TD
    subgraph STATE1[<b>State 1<b/>]
        subgraph STATE1_c1[<b>Component 1<b/>]
            n0(Stop A)
        end
        subgraph STATE1_c2[<b>Component 2<b/>]
            n2(Stop B)
            n1(Stop C)
        end
    end
    subgraph STATE2[<b>State 2<b/>]
        subgraph STATE2_c1[<b>Component 1<b/>]
            n4(Start A)
        end
        subgraph STATE2_c2[<b>Component 2<b/>]
            n6(Start B)
            n5(Start C)
            n3(Start G)
        end
    end

    style n0 fill: #E5B39B
    style n1 fill: #E5B39B
    style n3 fill: #D0F0C0
    style n4 fill: #D0F0C0
    
 n0(Stop A) .->  n4(Start A)
 n1(Stop C) .->  n3(Start G)
 n2(Stop B) --> n1(Stop C)
 n3(Start G) --> n5(Start C)
 n5(Start C) --> n6(Start B)
```

This result in the following action graph representing the sequence of actions which will be
executed to transition from `STATE1` to `STATE2`:

```mermaid
graph TD
 n0(Stop A) --> n4(Start A)
 n1(Stop C) --> n3(Start G)
 n2(Stop B) --> n1(Stop C)
 n3(Start G) --> n5(Start C)
 n4(Start A)
 n5(Start C) --> n6(Start B)
 n6(Start B)
```
