---
tags:
- Bazel
- process_manager
---

# How to use the Process Manager with <PERSON><PERSON>

This document shows best practices to use Process Manager when using Bazel build tool.

## Defining a `process_manager` target

In order to improve Process Manager launch support for <PERSON><PERSON>, a custom rule, `process_manager`,
was created.

This rule allows the user to define an executable target to run `apex_process_manager` with a
given launch file.

The main benefits are:

- Executables and settings files in a launch file are part of the Bazel dependency tree. If an
  executable or settings file is modified, the corresponding target will be rebuilt when launching
  a Process Manager file
- [Ament](https://design.ros2.org/articles/ament.html) machinery can be easily integrated into the
  launch file leveraging `settings_extensions` and ament based features such as the
  `find-pkg-prefix` and `find-pkg-share` substitution commands

A Bazel target for a launch file can be defined like this:

```py
# process_manager_examples:BUILD.bazel

load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")

process_manager(
    name = "example_bazel",
    data = [":process_manager_resources"],
    launch_file = "launch/example_bazel.launch.yaml",
    visibility = ["//visibility:public"],
)
```

In the launch file, the executable paths can be defined using relative paths:

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_bazel.launch.yaml',
{'tag': '# [launch]'},
'yaml') }}

1. The executable path is defined as a relative path

!!! note
    Since the run directory for the `process_manager` executable is the Bazel executable
    target `runfiles` directory (i.e. `my_app_launch.runfiles/<root_workspace_name>`),
    the executable paths inside the launch file can be defined relative to this directory

Finally, the target can be launched with `bazel run`:

```shell dollar
bazel run @apex//grace/examples/process_manager_examples:example_bazel
```

If any of the executables or settings files in the launch file are modified they
will be built again when we run the `process_manager` target. There is no need to
manually build the dependencies.

## Using `ament_pkg_resources`

It is possible to make use of  `ament_pkg_resources` to define the executables and settings
files used inside the launch file. This is useful to define paths based on the package name and
not based on absolute or relative paths.

Firstly, define the resources to include in the launch file:

```py
# process_manager_examples:BUILD.bazel

load("@apex//tools/ament/rules_ament:defs.bzl", "ament_pkg_resources")
ament_pkg_resources(
    name = "ament_resources",
    package = "process_manager_examples",
    resources = {
        "settings/my_settings.yaml": "share",
    },
)
```

In this example, the `minimal_example` executable is defined in a separate package:

```py
# process_manager_examples:BUILD.bazel

ament_pkg_resources(
    name = "process_manager_resources",
    package = "process_manager",
    resources = {
        "//grace/configuration/process_manager:minimal_process": "executable",
    },
    visibility = ["//visibility:public"],
)
```

!!! note
    In this example `minimal_example` executable is used for demonstration purposes. This
    executable is inside `process_manager` package but other executables are defined in
    different packages

```py
# process_manager_examples:BUILD.bazel

load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
process_manager(
    name = "example_settings",
    data = [
        ":ament_resources",
        "@apex//grace/configuration/process_manager:ament_resources",
    ],
    launch_file = "launch/example_settings.launch.yaml",
    visibility = ["//visibility:public"],
)
```

The resources are added in the `data` attribute

In the launch file, it is possible use `find-pkg-prefix` to define the paths for the
executables and settings files:

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_settings.launch.yaml',
{'tag': '# [launch]'},
'yaml') }}

1. The executable path is defined relative to the `process_manager` prefix
2. The settings file path is defined relative to the `process_manager_examples` share prefix

As before, the target can be launched with `bazel run`:

```shell dollar
bazel run @apex//grace/examples/process_manager_examples:example_settings
```

Note there is no need to source any workspace or to use an [install space](install-space.md).

### Using the introspection tool

In addition to running the launch file with the Process Manager, it is also possible to run the
[introspection tool](process-manager-design.md#launch-file-introspection-tool)
on a launch file to get information about its contents.

To see the available cli options, run

```shell dollar
bazel run @apex//grace/examples/process_manager_examples:example_settings.introspection -- --help
```

If neither `--print(-md)` or `--validate` are provided, the program will print an error, show the
help menu, then terminate.

For example, to validate if there are errors in the launch file:

```shell dollar
bazel run @apex//grace/examples/process_manager_examples:example_settings.introspection -- --validate
```

Additionally, to visualize the processed launch file in Markdown format:

```shell dollar
bazel run @apex//grace/examples/process_manager_examples:example_settings.introspection -- --print > my_app_introspection.md
```

## Default essential group

When using the `process_manager` Bazel rule, if there is no user-defined essential group in the
launch file, a built-in default essential group configuration is used with Apex.Ida 3.

{{ code_snippet(
'grace/configuration/process_manager/param/rmw_ida/default_essential.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

The default essential group can be disabled setting `start_default_essential_group` to `False`:

```bazel
# process_manager_examples:BUILD.bazel

load("@apex//grace/configuration/process_manager/rules_process_manager:defs.bzl", "process_manager")
process_manager(
    name = "example_settings",
    data = [
        ":ament_resources",
        "@apex//grace/configuration/process_manager:ament_resources",
    ],
    start_default_essential_group = False,
    launch_file = "launch/example_settings.launch.yaml",
    visibility = ["//visibility:public"],
)
```
