#! [launch AB]
launcher :
  node-name: "launchAB"
  stream-min-publish-interval-ms: 200
  logs-directory: "$(env HOME)/my_logs"
  processes:
    - path: "$(find-pkg-prefix foo)/lib/foo/to/my_exe1"
      args:
        - "--apex-settings-file"
        - "$(find-pkg-share foo)/param/settings1.yaml"
    - path: "$(find-pkg-prefix foo)/lib/foo/to/my_exe2"
      args:
        - "--apex-settings-file"
        - "$(find-pkg-share foo)/param/settings2.yaml"
    - path: "$(find-pkg-prefix foo)/lib/foo/to/my_exe3"
      args:
        - "--apex-settings-file"
        - "$(find-pkg-share foo)/param/settings3.yaml"
#! [launch AB]