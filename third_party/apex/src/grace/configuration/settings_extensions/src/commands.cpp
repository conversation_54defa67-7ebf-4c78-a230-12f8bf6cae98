/// \copyright Copyright 2022 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file contains APIs to parse Apex.OS settings dictionary built-in commands

#include "settings_extensions/commands.hpp"

#include <regex>
#include <string>
#include <vector>

#include "rcpputils/split.hpp"

/// \namespace apex
namespace apex
{
/// \namespace apex::settings_extensions
namespace settings_extensions
{

std::string get_command_content_as_string(const std::string & str, bool allow_nested_commands)
{
  std::string retval;
  // Checks that the string strictly matches the command format: $(...). i.e.
  // Valid example: '$(cmd arg)' -> 'cmd arg'
  // Invalid example: 'hello $(cmd arg) hello' -> ''
  static const std::regex rgx_nested_command_allowed(R"(^\$\((.*)\)$)");
  // same as previous regex but no nested command are allowed
  // Invalid example. 'hello $(cmd $(cmd arg)) hello' -> ''
  static const std::regex rgx_nested_commands_not_allowed(R"(^\$\(([^\)\n]*)\)$)");

  const std::regex & rgx =
    allow_nested_commands ? rgx_nested_command_allowed : rgx_nested_commands_not_allowed;

  std::smatch matches;

  if (std::regex_search(str, matches, rgx)) {
    retval = matches[1].str();
  }

  return retval;
}

std::vector<std::string> get_command_content_as_tokens(const std::string & str,
                                                       bool allow_nested_commands)
{
  return rcpputils::split(get_command_content_as_string(str, allow_nested_commands), ' ', true);
}

Command get_command(const std::string & str)
{
  auto tokens = get_command_content_as_tokens(str, false);
  if (tokens.empty()) {
    throw settings::settings_error("Failed to get a command from string: ", str);
  }
  return {tokens.at(0), {tokens.begin() + 1, tokens.end()}};
}

}  // namespace settings_extensions
}  // namespace apex
