/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#include <storage/generic/msg_utils.hpp>
#include <rosidl_typesupport_introspection_cpp/message_introspection.hpp>
#include <rosidl_typesupport_introspection_cpp/field_types.hpp>
#include <cpputils/string_view.hpp>
#include <cpputils/common_exceptions.hpp>
#include <storage/generic/dynamic_type_support.hpp>
#include <storage/generic/ros_binary_message.hpp>
#include <storage/json_parser/conversions.hpp>

#include <string>
#include <utility>

namespace apex
{
namespace storage
{
namespace generic
{

namespace
{
namespace field
{
constexpr auto data_header = "header";
constexpr auto db_name = "db_name";
constexpr auto transient = "transient";
constexpr auto instance_id = "instance_id";
constexpr auto creation_time_stamp = "creation_time_stamp";
constexpr auto time_stamp_nano = "nanosec";
constexpr auto time_stamp_sec = "sec";
constexpr auto request_id = "request_id";
constexpr auto request_id_writer_guid = "writer_guid";
constexpr auto request_id_sequence_number = "sequence_number";
}  // namespace field
namespace type
{
constexpr auto data_hdr_msg_namespace = "storage_msgs::msg";
constexpr auto data_hdr_msg_type_name = "DataHeader";
constexpr auto request_id_msg_namespace = "storage_msgs::msg";
constexpr auto request_id_msg_type_name = "RmwRequestId";
}  // namespace type

json_parser::json & get_header_field(json_parser::json & msg, const std::string & field_name)
{
  const auto hdr_iter = msg.find(field::data_header);
  if (hdr_iter != msg.end()) {
    const auto item_iter = hdr_iter->find(field_name);
    if (item_iter != hdr_iter->end()) {
      return *item_iter;
    }
  }
  throw apex::runtime_error{"bad header"};
}

const json_parser::json & get_const_header_field(
  const json_parser::json & msg,
  const std::string & field_name)
{
  return get_header_field(const_cast<json_parser::json &>(msg), field_name);
}
}  // namespace

/// \brief Tests if a message type has a proper storage header
bool is_compatible_message_type(
  const rosidl_typesupport_introspection_cpp::MessageMembers * type_info) noexcept
{
  using msg_type_info_t = rosidl_typesupport_introspection_cpp::MessageMembers;

  if (type_info->member_count_ < 1U) {return false;}
  auto member_info = type_info->members_[0];
  if (apex::string_view{member_info.name_} != field::data_header) {return false;}
  if (member_info.type_id_ != rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE) {
    return false;
  }
  type_info = reinterpret_cast<const msg_type_info_t *>(member_info.members_->data);
  if (apex::string_view{type_info->message_namespace_} != type::data_hdr_msg_namespace) {
    return false;
  }
  if (apex::string_view{type_info->message_name_} != type::data_hdr_msg_type_name) {return false;}
  if (type_info->member_count_ < 1U) {return false;}
  member_info = type_info->members_[0];
  if (apex::string_view{member_info.name_} != field::request_id) {return false;}
  if (member_info.type_id_ != rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE) {
    return false;
  }
  type_info = reinterpret_cast<const msg_type_info_t *>(member_info.members_->data);
  if (apex::string_view{type_info->message_namespace_} != type::request_id_msg_namespace) {
    return false;
  }
  if (apex::string_view{type_info->message_name_} != type::request_id_msg_type_name) {return false;}
  if (type_info->member_count_ != 2U) {return false;}
  member_info = type_info->members_[0];
  if (apex::string_view{member_info.name_} != field::request_id_writer_guid) {return false;}
  if (!member_info.is_array_ || member_info.is_upper_bound_ || member_info.array_size_ != 16) {
    return false;
  }
  if (member_info.type_id_ != rosidl_typesupport_introspection_cpp::ROS_TYPE_INT8) {return false;}
  member_info = type_info->members_[1];
  if (apex::string_view{member_info.name_} != field::request_id_sequence_number) {return false;}
  if (member_info.is_array_) {return false;}
  if (member_info.type_id_ != rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64) {return false;}
  return true;
}

bool is_transient(const json_parser::json & msg)
{
  const auto & flag = get_const_header_field(msg, field::transient);
  if (flag.is_boolean()) {
    return flag.get<bool>();
  }
  throw apex::runtime_error{"bad header"};
}

void set_transient(json_parser::json & msg, bool is_transient)
{
  get_header_field(msg, field::transient) = is_transient;
}

void set_creation_time_stamp(json_parser::json & msg, const builtin_interfaces::msg::Time & time)
{
  auto & ts = get_header_field(msg, field::creation_time_stamp);
  const auto ns_iter = ts.find(field::time_stamp_nano);
  const auto s_iter = ts.find(field::time_stamp_sec);
  if ((ns_iter != ts.end()) && (s_iter != ts.end())) {
    *ns_iter = time.nanosec;
    *s_iter = time.sec;
    return;
  }

  throw apex::runtime_error{"bad header"};
}

builtin_interfaces::msg::Time get_creation_time_stamp(const json_parser::json & msg)
{
  auto & ts = get_const_header_field(msg, field::creation_time_stamp);
  const auto ns_iter = ts.find(field::time_stamp_nano);
  const auto s_iter = ts.find(field::time_stamp_sec);
  if ((ns_iter != ts.end()) &&
    (s_iter != ts.end()) &&
    ns_iter->is_number_integer() &&
    s_iter->is_number_integer())
  {
    builtin_interfaces::msg::Time time;
    time.nanosec = ns_iter->get<std::uint32_t>();
    time.sec = s_iter->get<std::int32_t>();
    return time;
  }

  throw apex::runtime_error{"bad header"};
}

void set_db_name(json_parser::json & msg, std::string db_name)
{
  get_header_field(msg, field::db_name) = std::move(db_name);
}

std::string get_db_name(const json_parser::json & msg)
{
  return get_const_header_field(msg, field::db_name).get<std::string>();
}

void set_instance_id(json_parser::json & msg, std::uint32_t instance_id)
{
  get_header_field(msg, field::instance_id) = instance_id;
}

std::uint32_t get_instance_id(const json_parser::json & msg)
{
  const auto & instance_id = get_const_header_field(msg, field::instance_id);
  if (instance_id.is_number_integer()) {
    return instance_id.get<std::uint32_t>();
  }
  throw apex::runtime_error{"bad header"};
}

json_parser::json create_empty_message(const std::string & type_name)
{
  const auto type_support = type_support::get_type_support(type_name.c_str());
  const auto type_info = type_support::get_type_info(type_name.c_str());
  ros_binary_message msg{type_support, type_info};
  return json_parser::from_message(type_info, msg);
}

}  // namespace generic
}  // namespace storage
}  // namespace apex
