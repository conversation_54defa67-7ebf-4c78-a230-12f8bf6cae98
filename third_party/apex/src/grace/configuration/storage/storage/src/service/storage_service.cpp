/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#include <storage/service/storage_service.hpp>
#include <storage/service/data_store.hpp>
#include <storage/service/request_error.hpp>
#include <storage/service/database.hpp>
#include <storage/service/data_enumerator.hpp>
#include <storage/generic/dynamic_type_support.hpp>
#include <storage/common/commands.hpp>
#include <storage/common/names.hpp>
#include <storage/common/time.hpp>
#include <storage/common/qos.hpp>
#include <storage/generic/request_id.hpp>
#include <storage/generic/msg_utils.hpp>

#include <storage_msgs/srv/control.hpp>
#include <rclcpp/rclcpp.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <cpputils/common_exceptions.hpp>
#include <logging/logging_macros.hpp>

#include <string>
#include <cassert>
#include <memory>
#include <chrono>
#include <algorithm>
#include <type_traits>
#include <utility>
#include <vector>

namespace apex
{
namespace storage
{
namespace backend
{

using namespace std::string_literals;  // NOLINT
namespace type_support = ::apex::storage::generic::type_support;

storage_service::storage_service(
  std::string db_files_path /* = "." */,
  std::string instance_name /* = "" */,
  common::qos qos /* = common::qos{} */,
  std::chrono::nanoseconds timeout /* = std::chrono::seconds{1} */)
: m_instance_name{std::move(instance_name)},
  m_db_files_path{std::move(db_files_path)},
  m_service_ws{m_stop},
  m_timeout{timeout},
  m_qos{qos}
{
  if (!std::all_of(m_instance_name.begin(), m_instance_name.end(),
    /*
     AXIVION Next CodeLine MisraC++2023-24.5.1: Reason: Code Quality (Functional suitability),
     Justification: The valid range (must be unsigned char or EOF) of isalnum function argument is
     ensured via std::string. There is no UB.
     */
    [](const auto ch) {return (::isalnum(ch) != 0) || (ch == '_');}))
  {
    throw apex::runtime_error{"bad instance name"};
  }

  std::string node_name = "apex_storage_service_node";
  if (!m_instance_name.empty()) {
    node_name += "_" + m_instance_name;
    m_display_instance_name = m_instance_name;
  } else {
    m_display_instance_name = "<default>";
  }

  m_node = std::make_shared<rclcpp::Node>(node_name);
  m_logger = std::make_unique<logging::LoggerBase>(m_node.get());
  m_control_srv = m_node->create_polling_service<storage_msgs::srv::Control>(
    common::get_service_name(m_instance_name), m_qos.service);

  (void) m_service_ws.add(m_control_srv);

  if (!m_db_files_path.empty() && (m_db_files_path.back() != '/')) {
    m_db_files_path += '/';
  }

  m_enumerator = std::make_unique<data_enumerator>(m_databases, m_db_files_path, *m_logger);
  auto q = m_qos.publisher;
  m_enum_dbs_pub = m_node->create_publisher<storage_msgs::msg::EnumerationDatabaseData>(
    common::get_enumerate_topic_name("db", m_instance_name),
    q.transient_local());
  m_enum_stores_pub = m_node->create_publisher<storage_msgs::msg::EnumerationStoreData>(
    common::get_enumerate_topic_name("store", m_instance_name),
    q.transient_local());
}

void storage_service::run()
{
  while (true) {
    m_service_ws.wait();
    if (m_service_ws[m_stop]) {
      return;
    }

    const auto requests = m_control_srv->take_request();
    for (const auto & req : requests) {
      if (req.info().valid()) {
        try {
          dispatch_incoming_request(req.data(), req.request_header());
        } catch (const request_error & e) {
          send_error_response(req.request_header(), e.what());
        } catch (const std::exception & e) {
          send_error_response(req.request_header(), e.what());
          APEX_ERROR(*m_logger, apex::no_separator{},
            "Unexpected error from a storage service instance '", m_display_instance_name,
            "': ", e.what()
          );
          throw;
        } catch (...) {
          send_error_response(req.request_header(), "unknown error");
          APEX_ERROR(*m_logger, apex::no_separator{},
            "Unexpected error from a storage service instance '", m_display_instance_name, "'");
          throw;
        }
      }
    }
  }
}

void storage_service::stop() const
{
  m_stop->set_value(true);
}

void storage_service::reset_run_state()
{
  m_stop->set_value(false);
  m_data_infra.clear();
  m_databases.clear();
  m_database_count = 0;
  m_infa_count = 0;
  m_data_store_count = 0;
}

void storage_service::dispatch_incoming_request(
  const storage_msgs::srv::Control::Request::BorrowedType & req,
  const rmw_request_id_t & request_id)
{
  switch (req.command_code) {
    case common::commands::Registration:
      register_request(req.hdr, request_id);
      break;
    case common::commands::Save:
      save_request(req.hdr, request_id);
      break;
    case common::commands::Fetch:
      fetch_request(req.hdr, request_id);
      break;
    case common::commands::Commit:
      commit_request(req.hdr, request_id);
      break;
    case common::commands::Drop:
      drop_request(req.hdr, request_id);
      break;
    case common::commands::Enumerate:
      enumerate_request(req.hdr, request_id);
      break;
    default:
      throw request_error{"invalid command code:", static_cast<std::int32_t>(req.command_code)};
  }
}

void storage_service::register_request(
  const storage_msgs::msg::ControlHeader::BorrowedType & hdr,
  const rmw_request_id_t & request_id)
{
  (void) get_or_create_infrastructure_on_demand(hdr);
  send_response(request_id);
}

void storage_service::save_request(
  const storage_msgs::msg::ControlHeader::BorrowedType & hdr,
  const rmw_request_id_t & request_id)
{
  const auto clock_start = std::chrono::steady_clock::now();
  const auto p = get_or_create_infrastructure_on_demand(hdr);
  auto & data_infra = *p.first;
  auto & data_store = *p.second;

  const auto msgs = take_data(data_infra, hdr.count, request_id, clock_start);
  if (msgs.size() != hdr.count) {
    throw request_error{"insufficient data - received", msgs.size(),
            "instead of", hdr.count};
  }

  for (const auto & msg : msgs) {
    generic::set_zero_request_id(*msg);
  }
  data_store.save(msgs, hdr.history_length);

  for (const auto & msg : msgs) {
    data_infra.watch_pub->publish(rclcpp::SerializedMessage{*msg});
  }

  send_response(request_id, hdr.count);
}

void storage_service::fetch_request(
  const storage_msgs::msg::ControlHeader::BorrowedType & hdr,
  const rmw_request_id_t & request_id)
{
  const auto p = get_or_create_infrastructure_on_demand(hdr);
  const auto & data_infra = *p.first;
  const auto & data_store = *p.second;

  auto msgs = data_store.fetch(hdr.count, hdr.index);
  assert(msgs.size() <= hdr.count);
  send_response(request_id, static_cast<std::uint32_t>(msgs.size()));

  for (const auto & msg : msgs) {
    generic::set_request_id(request_id, *msg);
    data_infra.pub->publish(rclcpp::SerializedMessage{*msg});
  }
}

void storage_service::commit_request(
  const storage_msgs::msg::ControlHeader::BorrowedType & hdr,
  const rmw_request_id_t & request_id)
{
  if (hdr.db_name.empty()) {
    throw request_error{"invalid header data"};
  }

  const auto iter = m_databases.find(hdr.db_name);
  if (iter == m_databases.end()) {
    throw request_error{"no such database", hdr.db_name};
  } else {
    iter->second->commit();
    send_response(request_id);
  }
}

void storage_service::drop_request(
  const storage_msgs::msg::ControlHeader::BorrowedType & hdr,
  const rmw_request_id_t & request_id)
{
  if (hdr.db_name.empty()) {
    throw request_error{"invalid header data"};
  }

  if (hdr.msg_type_name.empty()) {
    assert(hdr.instance_id == 0U);
    auto & db = get_or_create_database(hdr);
    db.drop();
  } else {
    const auto p = get_or_create_infrastructure_on_demand(hdr);
    auto & data_store = *p.second;
    data_store.drop();
  }

  send_response(request_id);
}

void storage_service::enumerate_request(
  const storage_msgs::msg::ControlHeader::BorrowedType & hdr,
  const rmw_request_id_t & request_id)
{
  if (hdr.db_name.empty()) {
    send_enum_response(request_id, m_enumerator->databases(), *m_enum_dbs_pub);
  } else {
    send_enum_response(request_id, m_enumerator->stores(hdr.db_name), *m_enum_stores_pub);
  }
}

storage_service::infra_and_store_t
storage_service::get_or_create_infrastructure_on_demand(
  const storage_msgs::msg::ControlHeader::BorrowedType & hdr)
{
  const std::string type_name = hdr.msg_type_name;
  if (type_name.empty() || hdr.db_name.empty()) {
    throw request_error{"invalid header data"};
  }

  auto data_infra_iter = m_data_infra.find(type_name);
  if (data_infra_iter == m_data_infra.end()) {
    data_infra_iter = m_data_infra.emplace(type_name,
        create_infrastructure(type_name)).first;
    ++m_infa_count;
  }

  auto & db = get_or_create_database(hdr);

  const auto old_size = db.size();
  auto & data_store = db.get_store(
    common::get_data_store_name(type_name, hdr.instance_id),
    type_name,
    hdr.instance_id,
    data_infra_iter->second->type_support,
    data_infra_iter->second->type_info);

  if (db.size() > old_size) {
    ++m_data_store_count;
  }

  return {data_infra_iter->second.get(), & data_store};
}

database & storage_service::get_or_create_database(
  const storage_msgs::msg::ControlHeader::BorrowedType & hdr)
{
  auto db_iter = m_databases.find(hdr.db_name);
  if (db_iter == m_databases.end()) {
    db_iter = m_databases.emplace(hdr.db_name,
        std::make_unique<database>(common::get_db_file_name(hdr.db_name, m_db_files_path))).first;
    ++m_database_count;
  }
  return *db_iter->second;
}

void storage_service::send_response(
  rmw_request_id_t request_id,
  std::uint32_t message_count /*= 0U*/) const
{
  auto rsp = m_control_srv->borrow_loaned_response();
  rsp->message_count = message_count;
  m_control_srv->send_response(request_id, std::move(rsp));
}

void storage_service::send_error_response(
  rmw_request_id_t request_id,
  const std::string & error_string) const
{
  auto rsp = m_control_srv->borrow_loaned_response();
  rsp->status = 1U;
  rsp->error_string = error_string;
  m_control_srv->send_response(request_id, std::move(rsp));
}

std::vector<std::shared_ptr<rmw_serialized_message_t>> storage_service::take_data(
  data_infrastructure & data_infra,
  std::size_t count,
  const rmw_request_id_t & request_id,
  std::chrono::steady_clock::time_point clock_start) const
{
  std::vector<std::shared_ptr<rmw_serialized_message_t>> result;
  auto & msg_cache = data_infra.msg_cache;
  const auto guid_map_key = common::get_map_key(request_id);
  data_infrastructure::cache_queue_t * queue{nullptr};

  // Find a message queue which belongs to this particular service endpoint
  const auto queue_iter = msg_cache.find(guid_map_key);
  if (queue_iter != msg_cache.end()) {
    queue = &queue_iter->second;
    while (!queue->empty()) {
      auto & elem = queue->front();
      if (elem.first.sequence_number < request_id.sequence_number) {
        // The element in queue belongs to a request which is older than this one
        // Thus there is no chance for it to be used in the future so it is lost
        queue->pop();
      } else if (elem.first.sequence_number == request_id.sequence_number) {
        // This is a relevant one
        result.push_back(std::move(elem.second));
        queue->pop();
      } else {
        // The element belongs to a request which will be served in the future
        // It is safe to assume that all later additions to the queue are also in the future
        // so we can stop processing it for now
        break;
      }
    }
  }

  while (result.size() < count) {
    if (!data_infra.ws->wait(common::adjust_timeout(clock_start, m_timeout))) {
      break;
    }

    auto msgs = data_infra.sub->take_serialized();
    for (auto & msg : msgs) {
      const auto received_request_id = generic::get_request_id(*msg);
      if (common::from_same_source(request_id, received_request_id)) {
        if (request_id.sequence_number == received_request_id.sequence_number) {
          result.push_back(std::move(msg));
        } else if (request_id.sequence_number < received_request_id.sequence_number) {
          // From the same endpoint but belongs to a future request
          // Store in the cache
          if (queue == nullptr) {
            queue =
              &msg_cache.emplace(guid_map_key,
                data_infrastructure::cache_queue_t{}).first->second;
          }
          queue->emplace(received_request_id, std::move(msg));
        }  // else data from an older request, nothing to do, request is lost
      } else {
        // A data message belongs to an endpoint different from this this request
        // Store it for the future
        msg_cache[guid_map_key].emplace(received_request_id, std::move(msg));
      }
    }
  }

  return result;
}

std::unique_ptr<storage_service::data_infrastructure>
storage_service::create_infrastructure(const std::string & msg_type_name) const
{
  auto data_infra = std::make_unique<data_infrastructure>();
  try {
    auto qos = m_qos.subscriber;
    data_infra->sub = m_node->create_generic_polling_subscription(
      common::get_data_save_topic_name(msg_type_name, m_instance_name),
      msg_type_name,
      qos.transient_local());

    qos = m_qos.publisher;
    data_infra->pub = m_node->create_generic_publisher(
      common::get_data_fetch_topic_name(msg_type_name, m_instance_name),
      msg_type_name,
      qos.transient_local());

    data_infra->watch_pub = m_node->create_generic_publisher(
      common::get_watch_topic_name(msg_type_name, m_instance_name),
      msg_type_name,
      qos);

    data_infra->ws = std::make_unique<rclcpp::dynamic_waitset::Waitset>(data_infra->sub);
    data_infra->type_support = type_support::get_type_support(msg_type_name.c_str());
    data_infra->type_info = type_support::get_type_info(msg_type_name.c_str());
  } catch (const std::exception & e) {
    throw request_error{apex::no_separator{},
            "message type '", msg_type_name, "': ", e.what()};
  }

  if (!generic::is_compatible_message_type(data_infra->type_info)) {
    throw request_error{apex::no_separator{}, "message type '",
            msg_type_name, "' does not have a correct storage header"};
  }

  return data_infra;
}

}  // namespace backend
}  // namespace storage
}  // namespace apex
