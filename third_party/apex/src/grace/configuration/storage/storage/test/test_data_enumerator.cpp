/// \copyright Copyright 2017-2023 Apex.AI, Inc.
/// All rights reserved.

#include <gtest/gtest.h>

#include <storage/api/storage.hpp>
#include <storage/generic/storage.hpp>
#include <storage/service/data_enumerator.hpp>
#include <storage/service/storage_service.hpp>
#include <storage/common/names.hpp>
#include <storage/common/file.hpp>
#include <logging/logging.hpp>
#include <test_msgs/msg/basic_types.hpp>
#include <storage_test_msgs/msg/test_data_message.hpp>
#include <storage_test_msgs/msg/test_data_message2.hpp>

#include <fstream>
#include <memory>
#include <map>
#include <unordered_map>
#include <sstream>
#include <string>
#include <vector>

#include "common.hpp"
#include "service_runner.hpp"

using apex::storage::backend::storage_service;
using apex::storage::backend::data_enumerator;
using apex::storage::common::is_read_only;
using apex::storage::common::get_data_store_name;
using apex::storage::common::parse_store_name;
using msg_type = test_msgs::msg::BasicTypes;
using storage_test_msgs::msg::TestDataMessage;
using storage_test_msgs::msg::TestDataMessage2;
using storage_msgs::msg::EnumerationStoreData;
using test::service_runner;
using test::remove_db_file;
using test::remove_directory_recursively;
using namespace apex::storage;  // NOLINT
using apex::storage::common::get_db_file_name;
namespace generic = apex::storage::generic;

namespace
{

const char * test_dir_name = "test_database_enum_dir";
const std::string work_dir = std::string{test_dir_name} + "/";  // NOLINT

template<class T>
inline std::string padded_to_string(const T & value, std::int32_t witdh)
{
  std::ostringstream str;
  str << std::setw(witdh) << std::setfill('0') << value;
  return str.str();
}

class test_data_enumerator : public ::testing::Test
{
protected:
  void SetUp() override
  {
    remove_directory_recursively(test_dir_name);
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
    node = std::make_shared<rclcpp::Node>("test_data_enumerator");
    ASSERT_EQ(::mkdir(test_dir_name, S_IRWXU), 0);
  }

  void TearDown() override
  {
    rclcpp::shutdown();
  }

  using store_map_t = std::unordered_map<std::string, std::unordered_map<std::uint32_t,
      std::uint32_t>>;

  bool test_store_enum(const std::vector<EnumerationStoreData> & data, const store_map_t & map)
  {
    store_map_t new_map;
    for (const auto & rec : data) {
      for (const auto & store : rec.stores) {
        auto & inst_map = new_map[store.name];
        for (const auto & inst : store.instances) {
          inst_map[inst.id] = inst.history_length;
        }
      }
    }
    return new_map == map;
  }

  std::unordered_map<std::string,
    std::unique_ptr<apex::storage::backend::database>> fake_database_cache;
  rclcpp::Node::SharedPtr node;
};
}  // namespace

TEST_F(test_data_enumerator, database_props_on_disk) {
  data_enumerator e{fake_database_cache, work_dir, *apex::logging::LoggerBase::get_root_logger()};
  auto files = e.databases();
  ASSERT_TRUE(files.empty());
  ASSERT_EQ(::mkdir((work_dir + "nested").c_str(), S_IRWXU), 0);
  {
    std::ofstream wf(get_db_file_name("regular_file", work_dir), std::ios::out | std::ios::binary);
    wf.write("hello", 5);
    wf.close();
    ASSERT_TRUE(wf.good());
  }
  {
    const auto filename = get_db_file_name("read_only_file", work_dir);
    std::ofstream wf(filename, std::ios::out | std::ios::binary);
    wf.write("bye", 3);
    wf.close();
    ASSERT_TRUE(wf.good());
    ASSERT_EQ(::chmod(filename.c_str(), S_IRUSR), 0);
    ASSERT_TRUE(is_read_only(filename));
  }
  {
    std::ofstream wf(get_db_file_name("regular_file2", work_dir), std::ios::out | std::ios::binary);
    wf.write("another", 7);
    wf.close();
    ASSERT_TRUE(wf.good());
  }

  files = e.databases();
  ASSERT_EQ(files.size(), 1U);
  ASSERT_EQ(files[0].databases.size(), 3U);
  std::map<std::string, storage_msgs::msg::EnumerationDatabaseRecord> m;
  for (const auto & msg : files[0].databases) {
    m.emplace(msg.name, msg);
  }
  auto iter = m.find("regular_file");
  ASSERT_NE(iter, m.end());
  ASSERT_EQ(iter->second.file_size, 5U);
  ASSERT_EQ(iter->second.read_only, false);
  iter = m.find("read_only_file");
  ASSERT_NE(iter, m.end());
  ASSERT_EQ(iter->second.file_size, 3U);
  ASSERT_EQ(iter->second.read_only, true);
  iter = m.find("regular_file2");
  ASSERT_NE(iter, m.end());
  ASSERT_EQ(iter->second.file_size, 7U);
  ASSERT_EQ(iter->second.read_only, false);
}

TEST_F(test_data_enumerator, databases) {
  storage_service srv{test_dir_name};
  service_runner r{srv};
  {
    const auto com = communication::create(node);
    const auto db0 = com->get_database("db_name0");
    const auto db1 = com->get_database("db_name1");
    const auto db2 = com->get_database("db_name2");
    const auto db3 = com->get_database("db_name3");
    const auto db4 = com->get_database("db_name4");
    {
      // fake read-only db, on disk only, never loaded
      const std::string filename = get_db_file_name("db_name5", work_dir);
      std::ofstream wf(filename, std::ios::out | std::ios::binary);
      wf.write("hello", 5);
      wf.close();
      ASSERT_TRUE(wf.good());
      ASSERT_EQ(::chmod(filename.c_str(), S_IRUSR), 0);
      ASSERT_TRUE(is_read_only(filename));
    }

    {
      // fake db, but loaded and dropped
      const std::string filename = get_db_file_name("db_name6", work_dir);
      std::ofstream wf(filename, std::ios::out | std::ios::binary);
      wf.write("hello", 5);
      wf.close();
      ASSERT_TRUE(wf.good());
    }

    {
      // fake read-only db, but loaded and dropped
      const std::string filename = get_db_file_name("db_name7", work_dir);
      std::ofstream wf(filename, std::ios::out | std::ios::binary);
      wf.write("hello", 5);
      wf.close();
      ASSERT_TRUE(wf.good());
      ASSERT_EQ(::chmod(filename.c_str(), S_IRUSR), 0);
      ASSERT_TRUE(is_read_only(filename));
    }

    const auto db6 = com->get_database("db_name6");
    const auto db7 = com->get_database("db_name7");
    const auto db8 = com->get_database("db_name8");

    TestDataMessage msg1;
    // db0 is not really ever created
    db1->get_store<TestDataMessage>()->save(msg1);  // db1 is created in memory only
    db2->get_store<TestDataMessage>()->save(msg1);
    db2->commit();  // db2 is on disk
    db3->get_store<TestDataMessage>()->save(msg1);
    db3->commit();
    db3->drop();  // db3 is going to disappear on next commit though still on disk
    db4->get_store<TestDataMessage>()->save(msg1);
    db4->commit();
    db4->drop();
    db4->get_store<TestDataMessage>()->save(msg1);  // db4 was dropped but restored
    db6->drop();  // will be dropped
    db7->drop();  // won't really be dropped as it is read-only
    db8->get_store<TestDataMessage>()->save(msg1);  // db8 is created in memory only...
    db8->drop();  // ... and immediately dropped
  }
  auto gcom = generic::communication::create(node);
  auto dbs = gcom->enumerate();
  const auto databases_iter = dbs.find("databases");
  ASSERT_NE(databases_iter, dbs.end());
  ASSERT_TRUE(databases_iter->is_array());
  ASSERT_EQ(databases_iter->size(), 5U);
  ASSERT_EQ(databases_iter->at(0)["name"], "db_name1");
  ASSERT_EQ(databases_iter->at(0)["file_size"], 0U);
  ASSERT_EQ(databases_iter->at(0)["read_only"], false);
  ASSERT_EQ(databases_iter->at(1)["name"], "db_name2");
  ASSERT_NE(databases_iter->at(1)["file_size"], 0U);
  ASSERT_EQ(databases_iter->at(1)["read_only"], false);
  ASSERT_EQ(databases_iter->at(2)["name"], "db_name4");
  ASSERT_NE(databases_iter->at(2)["file_size"], 0U);
  ASSERT_EQ(databases_iter->at(2)["read_only"], false);
  ASSERT_EQ(databases_iter->at(3)["name"], "db_name5");
  ASSERT_EQ(databases_iter->at(3)["file_size"], 5U);
  ASSERT_EQ(databases_iter->at(3)["read_only"], true);
  ASSERT_EQ(databases_iter->at(4)["name"], "db_name7");
  ASSERT_EQ(databases_iter->at(4)["file_size"], 5U);
  ASSERT_EQ(databases_iter->at(4)["read_only"], true);
  ASSERT_NO_THROW(r.stop());
}

TEST_F(test_data_enumerator, databases_multiframe) {
  data_enumerator e{fake_database_cache, work_dir, *apex::logging::LoggerBase::get_root_logger()};
  for (auto i = 0; i < 600; ++i) {
    const std::string db_name = "regular_file" + padded_to_string(i, 3);
    std::ofstream wf(get_db_file_name(db_name, work_dir),
      std::ios::out | std::ios::binary);
    wf.write("hello", 5);
    wf.close();
    ASSERT_TRUE(wf.good());
  }
  const auto files = e.databases();
  ASSERT_EQ(files.size(), 3U);

  storage_service srv{test_dir_name};
  service_runner r{srv};

  auto gcom = generic::communication::create(node);
  auto dbs = gcom->enumerate();
  const auto databases_iter = dbs.find("databases");
  ASSERT_NE(databases_iter, dbs.end());
  ASSERT_TRUE(databases_iter->is_array());
  ASSERT_EQ(databases_iter->size(), 600);
  const auto & arr = *databases_iter;
  for (auto i = 0U; i < 600; ++i) {
    const std::string db_name = "regular_file" + padded_to_string(i, 3);
    ASSERT_EQ(arr[i]["name"], db_name);
    ASSERT_EQ(arr[i]["file_size"], 5U);
    ASSERT_EQ(arr[i]["read_only"], false);
  }
  ASSERT_NO_THROW(r.stop());
}

TEST_F(test_data_enumerator, databases_empty_list) {
  storage_service srv{test_dir_name};
  service_runner r{srv};
  const auto com = generic::communication::create(node);
  const auto dbs = com->enumerate();
  const auto databases_iter = dbs.find("databases");
  ASSERT_NE(databases_iter, dbs.end());
  ASSERT_TRUE(databases_iter->is_array());
  ASSERT_TRUE(databases_iter->empty());
}

TEST_F(test_data_enumerator, store_name_parser) {
  const auto msg_name = rosidl_generator_traits::name<msg_type>();
  auto store_name = get_data_store_name(msg_name, 0);
  auto store_name_parsed = parse_store_name(store_name);
  ASSERT_NE(store_name_parsed, apex::nullopt);
  ASSERT_EQ(store_name_parsed->first, msg_name);
  ASSERT_EQ(store_name_parsed->second, 0);
  store_name = get_data_store_name(msg_name, 42);
  store_name_parsed = parse_store_name(store_name);
  ASSERT_NE(store_name_parsed, apex::nullopt);
  ASSERT_EQ(store_name_parsed->first, msg_name);
  ASSERT_EQ(store_name_parsed->second, 42);

  ASSERT_EQ(parse_store_name("hello"), apex::nullopt);
  ASSERT_EQ(parse_store_name("hello0"), apex::nullopt);
  ASSERT_EQ(parse_store_name("@22"), apex::nullopt);
}

TEST_F(test_data_enumerator, stores_from_disk) {
  {
    storage_service srv{test_dir_name};
    service_runner r{srv};

    const auto com = communication::create(node);
    const auto db1 = com->get_database("db_name1");
    const auto db2 = com->get_database("db_name2");

    TestDataMessage msg1;
    TestDataMessage2 msg2;
    db1->get_store<TestDataMessage>()->save(msg1);
    db1->get_store<TestDataMessage>(1U)->save(msg1);
    db1->get_store<TestDataMessage2>(1U)->save(msg2);

    for (auto i = 0U; i < 11; ++i) {
      db2->get_store<TestDataMessage>(42U, 10U)->save(msg1);
      db2->get_store<TestDataMessage2>(88U, 20U)->save(msg2);
    }
    ASSERT_NO_THROW(r.stop());
  }

  store_map_t expect1;
  expect1[rosidl_generator_traits::name<TestDataMessage>()][0U] = 0U;
  expect1[rosidl_generator_traits::name<TestDataMessage>()][1U] = 0U;
  expect1[rosidl_generator_traits::name<TestDataMessage2>()][1U] = 0U;
  store_map_t expect2;
  expect2[rosidl_generator_traits::name<TestDataMessage>()][42U] = 10U;
  expect2[rosidl_generator_traits::name<TestDataMessage2>()][88U] = 10U;

  data_enumerator e{fake_database_cache, work_dir, *apex::logging::LoggerBase::get_root_logger()};
  const auto stores1 = e.stores("db_name1");
  ASSERT_EQ(stores1.size(), 1U);
  ASSERT_TRUE(test_store_enum(stores1, expect1));
  const auto stores2 = e.stores("db_name2");
  ASSERT_EQ(stores2.size(), 1U);
  ASSERT_TRUE(test_store_enum(stores2, expect2));
}

TEST_F(test_data_enumerator, stores) {
  {
    storage_service srv{test_dir_name};
    service_runner r{srv};

    const auto com = communication::create(node);
    const auto db1 = com->get_database("db_name1");
    TestDataMessage msg1;
    TestDataMessage2 msg2;
    // will exists on disk only
    db1->get_store<TestDataMessage>()->save(msg1);
    // will exists on disk only and then dropped
    db1->get_store<TestDataMessage>(1)->save(msg1);
    // will exists on disk and memory and then dropped
    db1->get_store<TestDataMessage>(2)->save(msg1);
    // will exists on disk and memory
    db1->get_store<TestDataMessage2>(0, 1)->save(msg2);
    db1->get_store<TestDataMessage2>(0, 1)->save(msg2);
    ASSERT_NO_THROW(r.stop());
  }

  storage_service srv{test_dir_name};
  service_runner r{srv};

  const auto com = communication::create(node);
  const auto db1 = com->get_database("db_name1");
  TestDataMessage msg1;
  TestDataMessage2 msg2;
  db1->get_store<TestDataMessage>(1)->drop();
  db1->get_store<TestDataMessage>(2)->save(msg1);
  db1->get_store<TestDataMessage>(2)->drop();
  db1->get_store<TestDataMessage>(3)->save(msg1);  // will exists in memory only
  db1->get_store<TestDataMessage2>(0, 2)->save(msg2);  // adds second
  db1->get_store<TestDataMessage2>(0, 3)->save_transient(msg2);  // adds transient

  auto gcom = generic::communication::create(node);
  auto gdb = gcom->get_database("db_name1");
  const auto stores = gdb->enumerate();
  const auto stores_iter = stores.find("stores");
  ASSERT_NE(stores_iter, stores.end());
  ASSERT_TRUE(stores_iter->is_array());
  ASSERT_EQ(stores_iter->size(), 2U);
  const auto & first = stores_iter->at(0);
  ASSERT_EQ(first["name"], rosidl_generator_traits::name<TestDataMessage>());
  ASSERT_TRUE(first["instances"].is_array());
  const auto & first_instances = first["instances"];
  ASSERT_EQ(first_instances.size(), 2U);
  ASSERT_EQ(first_instances[0]["id"], 0);
  ASSERT_EQ(first_instances[0]["history_length"], 0);
  ASSERT_EQ(first_instances[1]["id"], 3);
  ASSERT_EQ(first_instances[1]["history_length"], 0);
  const auto & second = stores_iter->at(1);
  ASSERT_EQ(second["name"], rosidl_generator_traits::name<TestDataMessage2>());
  ASSERT_TRUE(second["instances"].is_array());
  const auto & second_instances = second["instances"];
  ASSERT_EQ(second_instances.size(), 1U);
  ASSERT_EQ(second_instances[0]["id"], 0);
  ASSERT_EQ(second_instances[0]["history_length"], 3);
}

TEST_F(test_data_enumerator, all_stores_instances_dropped) {
  {
    storage_service srv{test_dir_name};
    service_runner r{srv};

    const auto com = communication::create(node);
    const auto db1 = com->get_database("db_name1");
    TestDataMessage msg1;
    TestDataMessage2 msg2;
    db1->get_store<TestDataMessage>(0)->save(msg1);
    db1->get_store<TestDataMessage>(1)->save(msg1);
    db1->get_store<TestDataMessage2>(0)->save(msg2);
    db1->get_store<TestDataMessage2>(1)->save(msg2);
    ASSERT_NO_THROW(r.stop());
  }

  storage_service srv{test_dir_name};
  service_runner r{srv};

  const auto com = communication::create(node);
  const auto db1 = com->get_database("db_name1");
  TestDataMessage2 msg2;
  db1->get_store<TestDataMessage2>(2)->save(msg2);
  db1->get_store<TestDataMessage>(0)->drop();
  db1->get_store<TestDataMessage>(1)->drop();
  db1->get_store<TestDataMessage2>(0)->drop();
  db1->get_store<TestDataMessage2>(1)->drop();
  db1->get_store<TestDataMessage2>(2)->drop();

  auto gcom = generic::communication::create(node);
  auto gdb = gcom->get_database("db_name1");
  const auto stores = gdb->enumerate();
  const auto stores_iter = stores.find("stores");
  ASSERT_NE(stores_iter, stores.end());
  ASSERT_TRUE(stores_iter->is_array());
  ASSERT_TRUE(stores_iter->empty());
}

TEST_F(test_data_enumerator, all_stores_instances_dropped_when_db_dropped) {
  {
    storage_service srv{test_dir_name};
    service_runner r{srv};

    const auto com = communication::create(node);
    const auto db1 = com->get_database("db_name1");
    TestDataMessage msg1;
    TestDataMessage2 msg2;
    db1->get_store<TestDataMessage>(0)->save(msg1);
    db1->get_store<TestDataMessage>(1)->save(msg1);
    db1->get_store<TestDataMessage2>(0)->save(msg2);
    db1->get_store<TestDataMessage2>(1)->save(msg2);
    ASSERT_NO_THROW(r.stop());
  }

  storage_service srv{test_dir_name};
  service_runner r{srv};

  const auto com = communication::create(node);
  const auto db1 = com->get_database("db_name1", std::chrono::seconds{180});
  ASSERT_NO_THROW(db1->drop());

  auto gcom = generic::communication::create(node);
  auto gdb = gcom->get_database("db_name1");
  const auto stores = gdb->enumerate();
  const auto stores_iter = stores.find("stores");
  ASSERT_NE(stores_iter, stores.end());
  ASSERT_TRUE(stores_iter->is_array());
  ASSERT_TRUE(stores_iter->empty());
}

TEST_F(test_data_enumerator, stores_many_instances_multiframe) {
  store_map_t expect;
  {
    storage_service srv{test_dir_name};
    service_runner r{srv};

    const auto com = communication::create(node);
    const auto db = com->get_database("db_name1");

    TestDataMessage msg;
    // slightly more than 255 * 255
    for (auto i = 0U; i < 65500; ++i) {
      db->get_store<TestDataMessage>(i)->save(msg);
      expect[rosidl_generator_traits::name<TestDataMessage>()][i] = 0U;
    }
    ASSERT_NO_THROW(r.stop());
  }

  data_enumerator e{fake_database_cache, work_dir, *apex::logging::LoggerBase::get_root_logger()};
  const auto stores = e.stores("db_name1");
  ASSERT_EQ(stores.size(), 2U);
  ASSERT_TRUE(test_store_enum(stores, expect));
}

TEST_F(test_data_enumerator, stores_empty_list) {
  storage_service srv{test_dir_name};
  service_runner r{srv};
  const auto com = generic::communication::create(node);
  const auto db = com->get_database("non-existent");
  const auto stores = db->enumerate();
  const auto stores_iter = stores.find("stores");
  ASSERT_NE(stores_iter, stores.end());
  ASSERT_TRUE(stores_iter->is_array());
  ASSERT_TRUE(stores_iter->empty());
}
