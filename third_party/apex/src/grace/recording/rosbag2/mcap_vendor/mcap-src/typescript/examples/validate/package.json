{"name": "@foxglove/mcap-example-validate", "version": "0.0.0", "private": true, "description": "MCAP validation example", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/foxglove/mcap.git"}, "author": {"name": "Foxglove Technologies", "email": "<EMAIL>"}, "homepage": "https://foxglove.dev/", "scripts": {"typecheck": "tsc -p tsconfig.json --noEmit", "lint:ci": "eslint --report-unused-disable-directives .", "lint": "eslint --report-unused-disable-directives --fix .", "validate": "yarn typescript:build && ts-node --files --project tsconfig.cjs.json scripts/validate.ts"}, "devDependencies": {"@foxglove/eslint-plugin": "1.0.1", "@foxglove/rosbag": "0.2.3", "@foxglove/rosmsg": "3.1.0", "@foxglove/rosmsg-serialization": "1.5.3", "@foxglove/rosmsg2-serialization": "1.1.1", "@foxglove/tsconfig": "1.1.0", "@foxglove/wasm-bz2": "0.1.1", "@foxglove/wasm-lz4": "1.0.2", "@foxglove/wasm-zstd": "1.0.1", "@mcap/core": "workspace:*", "@mcap/nodejs": "workspace:*", "@mcap/support": "workspace:*", "@types/jest": "29.5.8", "@types/lodash": "4.14.191", "@types/node": "18.13.0", "@typescript-eslint/eslint-plugin": "6.11.0", "@typescript-eslint/parser": "6.11.0", "commander": "11.1.0", "eslint": "8.54.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-es": "4.1.0", "eslint-plugin-filenames": "1.3.2", "eslint-plugin-import": "2.29.0", "eslint-plugin-jest": "27.6.0", "eslint-plugin-prettier": "5.0.1", "jest": "29.7.0", "lodash": "4.17.21", "prettier": "3.1.0", "protobufjs": "7.2.5", "ts-jest": "29.1.1", "ts-node": "10.9.1", "tsconfig-paths": "4.1.2", "typescript": "5.2.2"}}