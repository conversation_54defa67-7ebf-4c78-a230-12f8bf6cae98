// Generated by https://github.com/foxglove/schemas

syntax = "proto3";

import "foxglove/PackedElementField.proto";
import "foxglove/Pose.proto";
import "google/protobuf/timestamp.proto";

package foxglove;

// A collection of N-dimensional points, which may contain additional fields with information like normals, intensity, etc.
message PointCloud {
  // Timestamp of point cloud
  google.protobuf.Timestamp timestamp = 1;

  // Frame of reference
  string frame_id = 2;

  // The origin of the point cloud relative to the frame of reference
  foxglove.Pose pose = 3;

  // Number of bytes between points in the `data`
  fixed32 point_stride = 4;

  // Fields in the `data`
  repeated foxglove.PackedElementField fields = 5;

  // Point data, interpreted using `fields`
  bytes data = 6;
}
