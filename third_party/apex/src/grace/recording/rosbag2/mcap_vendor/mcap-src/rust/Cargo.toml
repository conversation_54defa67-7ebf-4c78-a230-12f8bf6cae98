[package]
name = "mcap"
description = "A library for reading and writing MCAP files"
homepage = "https://mcap.dev"
keywords = [ "foxglove", "mcap" ]
categories = [ "science::robotics", "compression" ]
repository = "https://github.com/foxglove/mcap"
documentation = "https://docs.rs/mcap"
readme = "README.md"
version = "0.10.0"
edition = "2021"
license = "MIT"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
binrw = "0.12.0"
byteorder = "1.4"
crc32fast = "1.3"
enumset = "1.0.11"
log = "0.4"
num_cpus = "1.13"
paste = "1.0"
thiserror = "1.0"
lz4 = { version = "1.27", optional = true }
async-compression = { version = "*", features = ["tokio"], optional = true }
tokio = { version = "1", features = ["io-util"] , optional = true }

[target.'cfg(target_arch = "wasm32")'.dependencies]
zstd = { version = "0.11", features = ["wasm"], optional = true }

[target.'cfg(not(target_arch = "wasm32"))'.dependencies]
zstd = { version = "0.11", features = ["zstdmt"], optional = true }

[features]
default = ["zstd", "lz4"]
zstd = ["dep:zstd", "async-compression/zstd"]
lz4 = ["dep:lz4"]
tokio = ["dep:async-compression", "dep:tokio"]

[dev-dependencies]
anyhow = "1"
atty = "0.2"
camino = "1.0"
clap = { version = "3.2", features = ["derive"]}
criterion = { version = "0.5.1", features = ["async_tokio"] }
itertools = "0.10"
memmap = "0.7"
rayon = "1.5"
serde = { version = "1.0.145", features = ["derive"] }
serde_json = "1"
simplelog = "0.12"
tempfile = "3.3"
tokio = { version = "1", features = ["io-util", "macros", "rt", "fs"] }

[[bench]]
name = "reader"
harness = false

[profile.bench]
opt-level = 3
debug = true
lto = true

[[example]]
name = "conformance_reader_async"
required-features = ["tokio"]
