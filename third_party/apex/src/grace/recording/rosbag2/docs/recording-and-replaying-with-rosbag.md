# Recording and replaying data with Rosbag2

## Introduction

This article describes how to record and replay data using Rosbag2. Recording and replaying data is
a very useful tool for a quicker algorithm development, testing, and validation.

## Sync status

The version of Rosbag2 shipped with Apex.<PERSON> is in sync with the [upstream
version](https://github.com/ros2/rosbag2) in ROS 2 Jazzy and has the following proprietary additions
to the API:

* `bag record` option: `--static-topics-path`

Other changes concern the implementation to support other Apex.Grace features such as zero-copy
transport, or to enhance performance. Refer to this [Apex.AI blog post about Rosbag2 from
2023](https://www.apex.ai/post/improvements-in-data-recording-path) for a description of some
performance benchmarks to learn about what the system is capable of, and how the performance is
improved in Apex.Grace compared to the upstream version of Rosbag2.

## Prerequisites

<!-- markdownlint-disable MD046 -->
=== "ADE/colcon"

    * Make sure to run all commands from ADE
    * Make sure to source Apex.Grace workspace with `source /opt/ApexOS/setup.bash`

=== "Bazel"

    * The article assumes a shell alias is defined for the ROS 2 CLI; e.g., in the `.bashrc` file define

      ```shell
      alias ros2='bazel run --noshow_progress --ui_event_filters=-info @apex//grace/cli --'
      ```

    * Invoke the CLI from the bazel workspace. The target `@apex//grace/cli` has the standard
      message types predefined. To record/replay [custom message
      types](/grace/doc/communication/defining-a-custom-message-type.md), customize the CLI by
      creating a [configured_binary
      target](/common/bazel/rules_deployment/doc/using-the-configured-tool-rule.md) and modifying
      the alias accordingly

<!-- markdownlint-disable restore -->

!!! Important
    This article only covers rosbag at a high level.
    To get detailed help on all options, use

    ```shell dollar
    ros2 bag --help
    ros2 bag <subcommand> --help`
    ```

    or refer to the [Rosbag2
    README](https://gitlab-customer.apex.ai/apexai/apexos-src/-/tree/25-02.00/apex_ws/src/grace/recording/rosbag2?ref_type=tags#using-rosbag2).

## Recording

To record specific topics:

```shell dollar
ros2 bag record <topic1> <topic2> ... <topicN>
```

Press ++ctrl+c++ to stop the recording.

To record all available topics:

```shell dollar
ros2 bag record -a
```

By default, `ros2 bag record` records data in the [MCAP](https://mcap.dev/spec) storage format. To
explicitly set the storage format, add the `-s mcap` or `-s sqlite3` option to the `ros2 bag record`
command:

```shell dollar
ros2 bag record -s mcap /topic1 /topic2
```

For more details about the possible `mcap` writer configuration options, refer to the
[`rosbag2_storage_mcap` plugin
README](https://gitlab-customer.apex.ai/apexai/apexos-src/-/tree/25-02.00/apex_ws/src/grace/recording/rosbag2/rosbag2_storage_mcap?ref_type=tags#writer-configuration).

As an alternative, Rosbag2 also supports the previous default of an SQLite3 database with `-s
sqlite3`. Refer to this [Apex.AI blog post about Rosbag2 from
2023](https://www.apex.ai/post/improvements-in-data-recording-path) for a performance comparison of
the two file formats.

!!! note
    `ros2 bag play` and `ros2 bag info` can detect the bag file format automatically, so there is
     no need to specify `-s mcap`.

Rosbag2 also supports storing custom metadata in the `metadata.yaml` file found inside the bag
folder. To add custom metadata fields, add the `--custom-data` option, followed by
`key=value` pairs, separated by spaces, when calling `ros2 bag record`

```shell dollar
ros2 bag record -o my_data /chatter --custom-data key1=value1 key2=value2
```

### Snapshot mode

In snapshot mode Rosbag2 maintains a transient cache of the recent messages sent on specified
topics. The messages are only written to permanent storage upon a user-invoked trigger.

For example, if a rare, anomalous behavior occurs, this feature allows the user to record the
most recent ROS topic messages when the behavior is observed, instead of having to record the
entire execution.

Start the `talker` demo node to publish on the `chatter` topic:

* Terminal 1 `ros2 run demo_nodes_cpp talker`

Start Rosbag2 in snapshot mode with a specified cache size in bytes:

* Terminal 2 `ros2 bag record --max-cache-size 256 --snapshot-mode --output my_data1 /chatter`

Trigger a snapshot via CLI:

* Terminal 3 `ros2 service call /rosbag2_recorder/snapshot rosbag2_interfaces/Snapshot`

## Validating a recording

To print info about the rosbag file, execute `ros2 bag info my_data`. Example output is shown below:

```plain
Files:             my_data.db3
Bag size:          48.4 KiB
Storage id:        sqlite3
Duration:          11.12s
Start:             Dec 19 2019 15:13:25.604 (1576797205.604)
End                Dec 19 2019 15:13:36.616 (1576797216.616)
Messages:          12
Topic information: Topic: /chatter | Type: std_msgs/msg/String | Count: 12 | Serialization Format: cdr
```

## Replaying

* Terminal 1 `ros2 run demo_nodes_cpp listener`
* Terminal 2 `ros2 bag play my_data`

Example output from the listener:

```plain
[INFO] [listener]: I heard: [Hello World: 1]
[INFO] [listener]: I heard: [Hello World: 2]
[INFO] [listener]: I heard: [Hello World: 3]
```

!!! note
    In the event of long or extended discovery between the `ros2 bag play`
    publisher and the Apex.Grace subscribers, initial messages in the bag file may
    not be delivered depending on the QOS of the Apex.Grace subscribers. In order
    to ensure initial messages are not lost, the Apex.Grace subscribers should use
    `transient_local` for durability
    [QOS](https://docs.ros.org/en/rolling/Concepts/About-Quality-of-Service-Settings.html#qos-policies).

### Simulation time

When using time simulation, Rosbag2 can be used as a ROS Time source publishing to the `/clock`
topic. There are several ways to enable `/clock` publishing.

* Terminal 1 `ros2 run demo_nodes_cpp listener`

Option 1: publish a `/clock` topic message at a fixed rate

* Terminal 2 `ros2 bag play my_data --clock 10`

Option 2: publish a `/clock` message for every topic

* Terminal 2 `ros2 bag play my_data --clock-topics-all`

Option 3: publish a `/clock` topic message for a subset of topics

* Terminal 2 `ros2 bag play my_data  --clock-topics /chatter`

To verify that the `/clock` topic is published:

```shell dollar
$ ros2 topic echo /clock
RMW Implementation: rmw_apex_middleware
clock:
  sec: 1645790265
  nanosec: 700658223
---
clock:
  sec: 1645790266
  nanosec: 700599405
---
```

!!! note
    In order to enable simulation in Apex.Grace and ROS 2 nodes, `use_sim_time` parameter must be set
    to true and passed to the node at the initialization. See the article on
    [using simulation time with
    `rosbag`](/grace/examples/sim_time_example/doc/using-simulation-time-with-rosbag.md).
