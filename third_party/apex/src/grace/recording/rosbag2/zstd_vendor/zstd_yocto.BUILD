load("@apex//grace/recording/rosbag2/zstd_vendor:defs.bzl", "ZSTD_VERSION")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

cc_import(
    name = "zstd_import",
    interface_library = "@yocto_sysroot//:usr/lib/libzstd.so",
    shared_library = "@yocto_sysroot//:usr/lib/libzstd.so.1",
    tags = ["same-ros-pkg-as: zstd_pkg"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

cc_library(
    name = "zstd",
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
    visibility = ["//visibility:public"],
    deps = [
        ":zstd_import",
    ],
)

ros_pkg(
    name = "zstd_pkg",
    bst_substitute_with = select({
        "@apex//common/build_system_transfer:conan_center": {
            "name": "zstd",
            "version": "1.5.5",
            "cmake_find_package": """\
find_package(zstd REQUIRED)
if (NOT TARGET {pkg_name}::zstd)
    add_library({pkg_name}::zstd ALIAS zstd::libzstd_static)
endif()
""",
        },
        "@apex//common/build_system_transfer:yocto_sdk": {
            "name": "zstd",
            "cmake_find_package": """\
find_package(PkgConfig REQUIRED)
pkg_check_modules(libzstd REQUIRED libzstd IMPORTED_TARGET)
if (NOT TARGET {pkg_name}::zstd)
    add_library({pkg_name}::zstd ALIAS PkgConfig::libzstd)
endif()
""",
        },
        "//conditions:default": {
        },
    }),
    cc_libraries = [":zstd"],
    description = "The zstd compression library",
    license = "BSD License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "zstd_vendor",
    version = ZSTD_VERSION,
    visibility = ["//visibility:public"],
    deps = [],
)
