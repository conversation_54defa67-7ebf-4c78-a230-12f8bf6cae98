<configuration name="Test rosbag2_performance_benchmarking:rosbag2_benchmark_test" type="BlazeCommandRunConfigurationType"
               factoryName="Bazel Command">
  <blaze-settings kind="py_test" handler-id="BlazePyRunConfigurationHandlerProvider" blaze-command="test">
    <blaze-target>
      //grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking:rosbag2_benchmark_test
    </blaze-target>
    <blaze-user-flag>--//grace/recording/rosbag2/rosbag2_performance/rosbag2_performance_benchmarking:enable_rosbag2_benchmark_tests=true</blaze-user-flag>
<!--    <blaze-user-flag>&#45;&#45;config=apex_middleware</blaze-user-flag>-->
    <blaze-user-flag>--cache_test_results=no</blaze-user-flag>
    <blaze-user-flag>--runs_per_test=1</blaze-user-flag>
    <blaze-user-flag>--test_keep_going</blaze-user-flag>
    <blaze-user-flag>--test_summary=detailed</blaze-user-flag>
    <blaze-user-flag>--test_output=streamed</blaze-user-flag>
    <blaze-user-flag>--test_verbose_timeout_warnings</blaze-user-flag>
    <env_state>
      <envs />
    </env_state>
  </blaze-settings>
  <method v="2">
    <option name="Blaze.BeforeRunTask" enabled="true" />
  </method>
</configuration>