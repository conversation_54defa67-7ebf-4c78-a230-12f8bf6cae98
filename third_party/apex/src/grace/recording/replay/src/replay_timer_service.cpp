/// \copyright Copyright 2017-2021 Apex.AI, Inc.
/// All rights reserved.

#include <replay/replay_timer_service.hpp>
#include <timer_service/common.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>
#include <cpputils/common_exceptions.hpp>

#include <limits>
#include <string>
#include <memory>
#include <cassert>

namespace apex
{
namespace replay
{

replay_timer_service::replay_timer_service(
  messenger & messenger,
  const std::string & node_name,
  const std::string & node_namespace /*= {}*/,
  //lint -e{1746} : No need to have const argument here NOLINT
  std::chrono::milliseconds max_reply_wait /*= std::chrono::seconds{1}*/)
: timer_service{node_name, node_namespace},
  m_max_reply_wait{max_reply_wait},
  m_messenger{&messenger},
  m_worker{[this] {run();}}
{
  m_start_event = m_messenger->get_waitable(
    apex::replay::messenger::get_broadcast_item_id(),
    apex::replay::command_type::COORDINATOR_IS_UP
  );
  m_worker.issue();
}

replay_timer_service::replay_timer_service(
  messenger & messenger,
  //lint -e{1746} : No need to have const argument here NOLINT
  std::chrono::milliseconds max_reply_wait /*= std::chrono::seconds{1}*/)
: replay_timer_service
  {
    messenger,
    apex::timer_service::next_default_service_name(),
    "",
    max_reply_wait
  }
{}

//lint -e{1540} : Messenger is not owned by this class NOLINT
replay_timer_service::~replay_timer_service()
{
  //lint -e{1550,1551}  : terminate on throw is the intended behavior NOLINT
  stop();    // terminate() is intended in case of exception
}

void replay_timer_service::stop()
{
  if (m_worker.joinable()) {
    m_stop->set_value(true);
    m_worker.join();
  }
}

apex::timer_service::types::clock::time replay_timer_service::now() const
{
  std::unique_lock<std::mutex> lock{m_mutex};
  apex::timer_service::types::clock::time result{};
  if (m_coordinator_started) {
    const auto begin = std::chrono::steady_clock::now();
    m_now_cln->wait_for_service(m_max_reply_wait);
    (void) m_now_cln->async_send_request(replay_msgs::srv::Now::Request{});
    if (!m_now_ws.wait(m_max_reply_wait - (std::chrono::steady_clock::now() - begin))) {
      throw apex::runtime_error{"timeout while waiting for answer from the control service"};
    }

    const auto responses = m_now_cln->take_response();
    for (const auto & response : responses) {
      if (response.info().valid()) {
        result.tv_sec = response.data().sec;
        result.tv_nsec = response.data().nanosec;
      }
    }
  }
  return result;
}

replay_timer_service::id_type replay_timer_service::get_next_timer_id()
{
  if (std::numeric_limits<id_type>::max() == m_id) {
    throw apex::runtime_error{"too many timers"};
  }
  ++m_id;
  return m_id;
}

//lint -e{9175} : Nothing to do for this implementation NOLINT
void replay_timer_service::create_timer_impl(const timer &) {}

void replay_timer_service::create_timer_impl(
  const timer & t,
  const time & time,
  units interval)
{
  reschedule(t, time, interval);
}

void replay_timer_service::create_timer_from_now_impl(
  const timer & t,
  units time_from_now,
  units interval)
{
  reschedule_from_now(t, time_from_now, interval);
}

void replay_timer_service::reschedule(
  const timer & t,
  const time & time,
  units interval)
{
  replay_msgs::msg::TimerControl msg;
  msg.start_ticks = time.to_duration().count();
  msg.absolute_time = true;
  msg.interval = interval.count();
  if (!send_timer_command(t, msg)) {
    throw apex::runtime_error{"unable to reschedule timer"};
  }
}

void replay_timer_service::reschedule_from_now(
  const timer & t,
  units time_from_now,
  units interval)
{
  replay_msgs::msg::TimerControl msg;
  msg.start_ticks = time_from_now.count();
  msg.interval = interval.count();
  if (!send_timer_command(t, msg)) {
    throw apex::runtime_error{"unable to reschedule timer"};
  }
}

void replay_timer_service::cancel(const timer & t)
{
  replay_msgs::msg::TimerControl msg;
  msg.cancel = true;
  if (!send_timer_command(t, msg)) {
    throw apex::runtime_error{"unable to cancel timer"};
  }
}

bool replay_timer_service::is_scheduled(const timer & t) const
{
  std::unique_lock<std::mutex> lock{m_mutex};
  const auto iter = m_timer_data_cache.find(t.id());
  assert(iter != m_timer_data_cache.end());
  return iter->second.active;
}

replay_timer_service::units replay_timer_service::interval(const timer & t) const
{
  std::unique_lock<std::mutex> lock{m_mutex};
  const auto iter = m_timer_data_cache.find(t.id());
  assert(iter != m_timer_data_cache.end());
  return iter->second.interval;
}

void replay_timer_service::run()
{
  //lint -e{716} : Not really an infinite loop NOLINT
  while (true) {
    rclcpp::dynamic_waitset::Waitset ws{{m_stop, m_event_sub, m_start_event}};
    ws.wait();
    if (ws[m_stop]) {
      break;
    }

    if (ws[m_start_event]) {
      std::unique_lock<std::mutex> lock{m_mutex};
      m_coordinator_started = true;
      m_control_pub->wait_for_matched(1U, m_max_reply_wait);
      if (!flush_message_queue()) {
        throw apex::runtime_error{"unable to send command message"};
      }
      lock.unlock();
      m_clock_cv.notify_all();
    }

    const auto events = m_event_sub->take();
    for (const auto & e : events) {
      if (e.info().valid()) {
        const auto & data = e.data();
        replay_msgs::msg::TimerEventReply msg;
        msg.timer_id = data.timer_id;
        msg.timer_queue_id = get_rclcpp_node().get_name();
        auto t = get_timer(data.timer_id);
        if (t == nullptr) {
          msg.success = false;
        } else {
          trigger(*t);
          msg.success = true;
          std::unique_lock<std::mutex> lock{m_mutex};
          m_timer_data_cache[data.timer_id].active = !data.expired;
        }
        m_event_pub->publish(msg);
      }
    }
  }    // while(true)
}

bool replay_timer_service::send_timer_command(
  const timer & t,
  replay_msgs::msg::TimerControl & msg)
{
  msg.timer_queue_id = get_rclcpp_node().get_name();
  msg.timer_id = t.id();
  msg.timer_topic = t.to_sub_ptr()->get_topic_name();

  std::unique_lock<std::mutex> lock{m_mutex};
  m_control_messages.push(msg);
  // set flag here since it reflects the logical state and must behave as if it is a live service
  auto & timer_data = m_timer_data_cache[msg.timer_id];
  timer_data.active = !msg.cancel;
  timer_data.interval = units{msg.interval};

  if (m_coordinator_started) {
    return flush_message_queue();
  }

  return true;
}

rclcpp::PublisherOptionsWithAllocator<std::allocator<void>>
replay_timer_service::get_publisher_options() noexcept
{
  rclcpp::PublisherOptionsWithAllocator<std::allocator<void>> pub_options;
  pub_options.ignore_hooks = true;
  return pub_options;
}

bool replay_timer_service::flush_message_queue()
{
  // reset any data which could be left from any previous errors
  (void) m_control_sub->take();

  while (!m_control_messages.empty()) {
    const auto msg = m_control_messages.front();
    m_control_pub->publish(msg);

    rclcpp::dynamic_waitset::Waitset ws{m_control_sub};
    if (!ws.wait(m_max_reply_wait)) {
      return false;
    }
    const auto loan = m_control_sub->take();
    if ((loan.size() != 1U) || (!loan.back().info().valid())) {
      return false;
    }

    const auto & data = loan.back().data();
    if ((data.timer_id != msg.timer_id) || !data.success) {
      return false;
    }
    m_control_messages.pop();
  }
  return true;
}
}  // namespace replay
}  // namespace apex
