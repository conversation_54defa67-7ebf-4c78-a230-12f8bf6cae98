# coordinator_interactive_protocol

## Purpose / Use cases

The [replay coordinator](coordinator-design.md) implements a protocol for controlling the
replay interactively via a service. In order to enable it, the coordinator
must be run with the [appropriate command line parameter](coordinator-design.md#cli-application).

## Design

All the interactive command are delivered via
the service named `apex_replay_interactive_command`. The commands consist of
a command code and an optional `number of steps` argument:

{{ code_snippet("grace/interfaces/replay_msgs/srv/InteractiveCommand.idl",
{"tag": "//! [InteractiveRequest]"}, "idl") }}

The reply consists of the result status of the command and the current state of the coordinator:

{{ code_snippet("grace/interfaces/replay_msgs/srv/InteractiveCommand.idl",
{"tag": "//! [InteractiveResponse]"}, "idl") }}

### Commands

These are supported commands:

1. `GET_STATE` -- Returns the current state and execution step count.
1. `RUN` -- Runs for `step_count` steps. A step is a full run of the execution graph triggered by
    an external message. If `step_count == 0` then runs until there are any messages left
   (runs to completion).
1. `BREAK` -- Pause running (if execution currently runs) and wait for further commands.
1. `STOP` -- Stop executing and shutdown the coordinator.

The commands are defined as following:

{{ code_snippet("grace/interfaces/replay_msgs/srv/InteractiveCommand.idl",
{"tag": "//! [InteractiveCommandCode]"}, "idl") }}

### Execution result

Execution result can be either `OK` or `ERROR` in case a wrong argument was
provided:

{{ code_snippet("grace/interfaces/replay_msgs/srv/InteractiveCommand.idl",
{"tag": "//! [InteractiveCommandStatus]"}, "idl") }}

### Start up

On the startup, the coordinator waits for meta information being sent from executors
or an interactive command. It will not start executing until a command to do so is received.
Note, that some time is required to collect all the meta information.

### Multiple commands

Multiple commands sent immediately one after another will override each other.
The commands are not queued.

### Breaking the execution

The execution is only broken between full executions of the graph.

### Client code-level breakpoints

In interactive mode the coordinator waits indefinitely for the input, but it cannot provide
any further guarantees for the support of the code-level breakpoints.
