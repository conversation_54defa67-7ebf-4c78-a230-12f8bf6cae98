load("@apex//tools/bazel/rules_docs:defs.bzl", "docs_chapter")

docs_chapter(
    name = "design",
    api_srcs = [
        "//grace/recording/replay:api_files",
    ],
    referenced_srcs = [
        "//grace/interfaces/replay_msgs:doc_files",
        "//grace/recording/replay:doc_files",
    ],
    subchapters = [
        ("coordinator", "coordinator-design.md"),
        ("messenger", "messenger-design.md"),
        ("Replay timer service design", "replay-timer-service-design.md"),
        ("Coordinator timer protocol", "coordinator-timer-protocol-design.md"),
        ("Coordinator interactive protocol", "coordinator-interactive-protocol-design.md"),
        ("Using the Apex.Grace executor for fixed-order replay", "//grace/examples/deterministic_replay_examples/design"),
    ],
)
