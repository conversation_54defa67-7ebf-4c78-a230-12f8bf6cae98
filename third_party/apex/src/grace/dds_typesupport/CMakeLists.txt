cmake_minimum_required(VERSION 3.5)

project(dds_typesupport)

find_package(ament_cmake_auto REQUIRED)

if(APEX_MICRO)
  message(STATUS "Component ${PROJECT_NAME} is disabled for Apex.Grace Micro build")
  ament_auto_package()
  return()
endif()

find_package(cpputils)
ament_auto_find_build_dependencies()

include_directories(include)

set(dds_typesupport_sources
    include/dds_typesupport/sample_info.hpp
    include/dds_typesupport/detail/sample_iterator.hpp
    include/dds_typesupport/loaned_sample.hpp
    include/dds_typesupport/traits.hpp
    src/dummy.cpp)

set_source_files_properties(${dds_typesupport_sources}
    PROPERTIES language "CXX")
add_library(${PROJECT_NAME} INTERFACE)

target_include_directories(${PROJECT_NAME} INTERFACE ${cpputils_INCLUDE_DIRS})
ament_export_include_directories(include
    ${cpputils_INCLUDE_DIRS}
    ${tracetools_INCLUDE_DIRS})
if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  find_package(apex_test_tools REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_auto_package()
