/// \copyright Copyright 2017-2019 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file defines an iterator for accessing LoanedSample elements of a LoanedSamples
/// sequence

#ifndef DDS_TYPESUPPORT__DETAIL__SAMPLE_ITERATOR_HPP_
#define DDS_TYPESUPPORT__DETAIL__SAMPLE_ITERATOR_HPP_

#include <iterator>
namespace apex
{
namespace dds_typesupport
{
namespace detail
{
/// \brief Iterator for accessing elements of a LoanedSamples sequence
/// \cert
/// \deterministic
template<typename Type>
class sample_iterator : public std::iterator<std::random_access_iterator_tag, Type>
{
public:
  using difference_type =
    typename std::iterator<std::random_access_iterator_tag, Type>::difference_type;
  using size_type = typename Type::size_type;

  // This is because the LoanedSample return type is essentially a reference to each of the
  // underlying sequences aggregated together
  using reference = typename Type::value_type;

  /// \brief Constructs a new empty iterator
  /// \cert
  /// \deterministic
  sample_iterator() noexcept
  : m_ptr(nullptr), m_idx(0)
  {
  }

  /// \brief Constructs a new sample_iterator from a pointer to a LoanedSamples sequence
  /// \param rhs Pointer to LoanedSamples sequence
  /// \cert
  /// \deterministic
  explicit sample_iterator(const Type * rhs) noexcept
  : m_ptr(rhs), m_idx(0)  // Determinism infraction check instrumented in loaned_samples::begin()
    // and sample_iterator operator++(int)
  {
  }

  /// \brief Constructs a new sample_iterator by copying an existing sample_iterator
  /// param rhs sample_iterator to copy from
  /// \cert
  /// \deterministic
  sample_iterator(const sample_iterator & rhs) noexcept
  : m_ptr(rhs.m_ptr), m_idx(rhs.m_idx)  // Determinism infraction check instrumented in
    // sample_iterator operator+(difference_type rhs)
  {
  }

  /// \brief Default destructor
  /// \cert
  /// \deterministic
  virtual ~sample_iterator() = default;

  /// \brief Copy assignment operator
  /// \param other The sample_iterator to copy from
  /// \cert
  /// \deterministic
  inline sample_iterator & operator=(const sample_iterator & other) noexcept
  {
    if (&other != this) {
      m_ptr = other.m_ptr;
      m_idx = other.m_idx;
    }
    return *this;
  }

  /// \brief Advances the iterator position by the specified amount
  /// \param rhs Number of elements to advance the iterator
  /// \returns Iterator pointing to the new location
  /// \cert
  /// \deterministic
  inline sample_iterator & operator+=(difference_type rhs) noexcept
  {
    m_idx += static_cast<size_type>(rhs);
    return *this;
  }

  /// \brief Decrements the iterator position by the specified amount
  /// \param rhs Number of elements to decrement the iterator position
  /// \returns Iterator pointing to the new location
  /// \cert
  /// \deterministic
  inline sample_iterator & operator-=(difference_type rhs) noexcept
  {
    m_idx -= static_cast<size_type>(rhs);
    return *this;
  }

  /// \brief Dereferences the iterator to access the element at the current iterator position
  /// \returns Element at current iterator position
  /// \cert
  /// \deterministic
  inline typename Type::value_type operator*() const noexcept
  {
    return (*m_ptr)[m_idx];
  }

  /// \brief Accesses the element at the provided index
  /// \returns Element at provided index
  /// \cert
  /// \deterministic
  inline const typename Type::value_type operator[](const difference_type rhs) const noexcept
  {
    return (*m_ptr)[rhs];
  }

  /// \brief Accesses the element at the provided index
  /// \returns Element at provided index
  /// \cert
  /// \deterministic
  inline typename Type::value_type operator[](difference_type rhs) noexcept
  {
    return (*m_ptr)[rhs];
  }

  /// \brief Increments the iterator position
  /// \returns Iterator pointing to new location
  /// \cert
  /// \deterministic
  inline sample_iterator & operator++() noexcept
  {
    // *INDENT-OFF* (prevent uncrustify from making unnecessary whitespace around `++m_idx`)
    ++m_idx;
    // *INDENT-ON*
    return *this;
  }

  /// \brief Decrements the iterator position
  /// \returns Iterator pointing to new location
  /// \cert
  /// \deterministic
  inline sample_iterator & operator--() noexcept
  {
    // *INDENT-OFF* (prevent uncrustify from making unnecessary whitespace around `--m_idx`)
    --m_idx;
    // *INDENT-ON*
    return *this;
  }

  /// \brief Increments the iterator position
  /// \returns Iterator pointing to new location
  /// \cert
  /// \deterministic
  inline sample_iterator operator++(int) noexcept
  {
    sample_iterator tmp(*this);
    // *INDENT-OFF* (prevent uncrustify from making unnecessary whitespace around `++m_idx`)
    ++m_idx;
    // *INDENT-ON*
    return tmp;
  }

  /// \brief Decrements the iterator position
  /// \returns Iterator pointing to new location
  /// \cert
  /// \deterministic
  inline sample_iterator operator--(int) noexcept
  {
    sample_iterator tmp(*this);
    --m_idx;
    return tmp;
  }

  /// \brief Calculates the difference between this iterator and another
  /// \param rhs sample_iterator to compare with
  /// \returns Difference in position between the two iterators
  /// \cert
  /// \deterministic
  inline difference_type operator-(const sample_iterator & rhs) const noexcept
  {
    return difference_type(m_idx - rhs.m_idx);
  }

  /// \brief Advances the iterator position by the specified amount
  /// \param rhs Number of elements to advance the iterator
  /// \returns Iterator pointing to the new location
  /// \cert
  /// \deterministic
  inline sample_iterator operator+(difference_type rhs) const noexcept
  {
    sample_iterator tmp(*this);
    tmp.m_idx += rhs;
    return tmp;
  }

  /// \brief Decrements the iterator position by the specified amount
  /// \param rhs Number of elements to decrement the iterator
  /// \returns Iterator pointing to the new location
  /// \cert
  /// \deterministic
  inline sample_iterator operator-(difference_type rhs) const noexcept
  {
    sample_iterator tmp(*this);
    tmp.m_idx -= rhs;
    return tmp;
  }

  /// \brief Compares iterator for equality
  /// \param rhs Iterator to compare against
  /// \returns True if iterators point to the same sequence and have the same position
  /// \cert
  /// \deterministic
  inline bool operator==(const sample_iterator & rhs) const noexcept
  {
    return (m_ptr == rhs.m_ptr) && (m_idx == rhs.m_idx);
  }

  /// \brief Compares iterator for inequality
  /// \param rhs Iterator to compare against
  /// \returns True if iterators do not point to the same sequence or have a different position
  /// \cert
  /// \deterministic
  inline bool operator!=(const sample_iterator & rhs) const noexcept
  {
    return !(*this == rhs);
  }

  /// \brief Compares iterators
  /// \param rhs Iterator to compare against
  /// \returns True if this iterator points to the same sequence as rhs and has a position past rhs
  /// \cert
  /// \deterministic
  inline bool operator>(const sample_iterator & rhs) const noexcept
  {
    return (m_ptr == rhs.m_ptr) && (m_idx > rhs.m_idx);
  }

  /// \brief Compares iterators
  /// \param rhs Iterator to compare against
  /// \returns True if this iterator points to the same sequence as rhs and has a position
  /// before rhs
  /// \cert
  /// \deterministic
  inline bool operator<(const sample_iterator & rhs) const noexcept
  {
    return (m_ptr == rhs.m_ptr) && (m_idx < rhs.m_idx);
  }

  /// \brief Compares iterators
  /// \param rhs Iterator to compare against
  /// \returns True if this iterator points to the same sequence as rhs and has a position
  /// equal to or past rhs
  /// \cert
  /// \deterministic
  inline bool operator>=(const sample_iterator & rhs) const noexcept
  {
    return !(*this < rhs);
  }

  /// \brief Compares iterators
  /// \param rhs Iterator to compare against
  /// \returns True if this iterator points to the same sequence as rhs and has a position
  /// equal to or before rhs
  /// \cert
  /// \deterministic
  inline bool operator<=(const sample_iterator & rhs) const noexcept
  {
    return !(*this > rhs);
  }

private:
  const Type * m_ptr;
  typename Type::size_type m_idx;
};
}  // namespace detail
}  // namespace dds_typesupport
}  // namespace apex

#endif  // DDS_TYPESUPPORT__DETAIL__SAMPLE_ITERATOR_HPP_
