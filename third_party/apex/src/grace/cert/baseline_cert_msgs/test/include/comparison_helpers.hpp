// Copyright 2020 Apex.AI, Inc.
// All rights reserved.

#ifndef COMPARISON_HELPERS_HPP_
#define COMPARISON_HELPERS_HPP_

template<typename T>
void test_equal_messages(T msg1, T msg2)
{
  bool is_equal;
  bool is_not_equal;
  {
    is_equal = (msg1 == msg2);
    is_not_equal = (msg1 != msg2);
  }
  ASSERT_TRUE(is_equal);
  ASSERT_FALSE(is_not_equal);
}

template<typename T>
void test_different_messages(T msg1, T msg2)
{
  bool is_equal;
  bool is_not_equal;
  {
    is_equal = (msg1 == msg2);
    is_not_equal = (msg1 != msg2);
  }
  ASSERT_FALSE(is_equal);
  ASSERT_TRUE(is_not_equal);
}

#endif  // COMPARISON_HELPERS_HPP_
