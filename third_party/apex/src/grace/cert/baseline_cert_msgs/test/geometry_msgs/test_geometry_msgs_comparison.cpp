// Copyright 2020 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>
#include <geometry_msgs/msg/accel.hpp>
#include <geometry_msgs/msg/accel_stamped.hpp>
#include <geometry_msgs/msg/accel_with_covariance.hpp>
#include <geometry_msgs/msg/accel_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/inertia.hpp>
#include <geometry_msgs/msg/inertia_stamped.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <geometry_msgs/msg/point32.hpp>
#include <geometry_msgs/msg/point_stamped.hpp>
#include <geometry_msgs/msg/polygon.hpp>
#include <geometry_msgs/msg/polygon_stamped.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <geometry_msgs/msg/pose_array.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/pose_with_covariance.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/quaternion.hpp>
#include <geometry_msgs/msg/quaternion_stamped.hpp>
#include <geometry_msgs/msg/transform.hpp>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/twist_with_covariance.hpp>
#include <geometry_msgs/msg/twist_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/vector3.hpp>
#include <geometry_msgs/msg/vector3_stamped.hpp>
#include <geometry_msgs/msg/wrench.hpp>
#include <geometry_msgs/msg/wrench_stamped.hpp>
#include <comparison_helpers.hpp>

#include <string>


/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, accel_msg) {
  geometry_msgs::msg::Accel msg1;
  geometry_msgs::msg::Accel msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field angular
  msg1.angular.x++;
  msg1.set__linear(msg2.linear);
  test_different_messages(msg1, msg2);

  // different field linear
  msg1.set__angular(msg2.angular);
  msg1.linear.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, accel_stamped_msg) {
  geometry_msgs::msg::AccelStamped msg1;
  geometry_msgs::msg::AccelStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__accel(msg2.accel);
  test_different_messages(msg1, msg2);

  // different field accel
  msg1.set__header(msg2.header);
  msg1.accel.angular.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, accel_with_covariance_msg) {
  geometry_msgs::msg::AccelWithCovariance msg1;
  geometry_msgs::msg::AccelWithCovariance msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field accel
  msg1.accel.linear.x++;
  msg1.set__covariance(msg2.covariance);
  test_different_messages(msg1, msg2);

  // different field covariance
  msg1.set__accel(msg2.accel);
  msg1.covariance.at(0)++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, accel_with_covariance_stamped_msg) {
  geometry_msgs::msg::AccelWithCovarianceStamped msg1;
  geometry_msgs::msg::AccelWithCovarianceStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__accel(msg2.accel);
  test_different_messages(msg1, msg2);

  // different field accel
  msg1.set__header(msg2.header);
  msg1.accel.accel.linear.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, inertia_msg) {
  geometry_msgs::msg::Inertia msg1;
  geometry_msgs::msg::Inertia msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field com
  msg1 = msg2;
  msg1.com.x++;
  test_different_messages(msg1, msg2);

  // different field ixx
  msg1 = msg2;
  msg1.ixx++;
  test_different_messages(msg1, msg2);

  // different field ixy
  msg1 = msg2;
  msg1.ixy++;
  test_different_messages(msg1, msg2);

  // different field ixz
  msg1 = msg2;
  msg1.ixz++;
  test_different_messages(msg1, msg2);

  // different field iyy
  msg1 = msg2;
  msg1.iyy++;
  test_different_messages(msg1, msg2);

  // different field iyz
  msg1 = msg2;
  msg1.iyz++;
  test_different_messages(msg1, msg2);

  // different field izz
  msg1 = msg2;
  msg1.izz++;
  test_different_messages(msg1, msg2);

  // different field m
  msg1 = msg2;
  msg1.m++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, inertia_stamped_msg) {
  geometry_msgs::msg::InertiaStamped msg1;
  geometry_msgs::msg::InertiaStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1 = msg2;
  msg1.header.stamp.sec++;
  test_different_messages(msg1, msg2);

  // different field inertia
  msg1 = msg2;
  msg1.inertia.m++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, point_msg) {
  geometry_msgs::msg::Point msg1;
  geometry_msgs::msg::Point msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field x
  msg1 = msg2;
  msg1.x++;
  test_different_messages(msg1, msg2);

  // different field y
  msg1 = msg2;
  msg1.y++;
  test_different_messages(msg1, msg2);

  // different field z
  msg1 = msg2;
  msg1.z++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, point32_msg) {
  geometry_msgs::msg::Point32 msg1;
  geometry_msgs::msg::Point32 msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field x
  msg1 = msg2;
  msg1.x++;
  test_different_messages(msg1, msg2);

  // different field y
  msg1 = msg2;
  msg1.y++;
  test_different_messages(msg1, msg2);

  // different field z
  msg1 = msg2;
  msg1.z++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, point_stamped_msg) {
  geometry_msgs::msg::PointStamped msg1;
  geometry_msgs::msg::PointStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__point(msg2.point);
  test_different_messages(msg1, msg2);

  // different field point
  msg1.set__header(msg2.header);
  msg1.point.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, polygon_msg) {
  geometry_msgs::msg::Polygon msg1;
  geometry_msgs::msg::Polygon msg2;

  msg1.set__points(msg2.points);

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.points.resize(1U);
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, polygon_stamped_msg) {
  geometry_msgs::msg::PolygonStamped msg1;
  geometry_msgs::msg::PolygonStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__polygon(msg2.polygon);
  test_different_messages(msg1, msg2);

  // different field polygon
  msg1.set__header(msg2.header);
  msg1.polygon.points.resize(1U);
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, pose_msg) {
  geometry_msgs::msg::Pose msg1;
  geometry_msgs::msg::Pose msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field orientation
  msg1.orientation.x++;
  msg1.set__position(msg2.position);
  test_different_messages(msg1, msg2);

  // different field position
  msg1.set__orientation(msg2.orientation);
  msg1.position.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, pose_stamped_msg) {
  geometry_msgs::msg::PoseStamped msg1;
  geometry_msgs::msg::PoseStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__pose(msg2.pose);
  test_different_messages(msg1, msg2);

  // different field pose
  msg1.set__header(msg2.header);
  msg1.pose.position.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, pose2_d_msg) {
  geometry_msgs::msg::Pose2D msg1;
  geometry_msgs::msg::Pose2D msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field x
  msg1.x++;
  msg1.set__y(msg2.y);
  msg1.set__theta(msg2.theta);
  test_different_messages(msg1, msg2);

  // different field y
  msg1.set__x(msg2.x);
  msg1.y++;
  msg1.set__theta(msg2.theta);
  test_different_messages(msg1, msg2);

  // different field theta
  msg1.set__x(msg2.x);
  msg1.set__y(msg2.y);
  msg1.theta++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, pose_array_msg) {
  geometry_msgs::msg::PoseArray msg1;
  geometry_msgs::msg::PoseArray msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__poses(msg2.poses);
  test_different_messages(msg1, msg2);

  // different field poses
  msg1.set__header(msg2.header);
  msg1.poses.resize(1U);
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, pose_with_covariance_msg) {
  geometry_msgs::msg::PoseWithCovariance msg1;
  geometry_msgs::msg::PoseWithCovariance msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field pose
  msg1.pose.position.x++;
  msg1.set__covariance(msg2.covariance);
  test_different_messages(msg1, msg2);

  // different field covariance
  msg1.set__pose(msg2.pose);
  msg1.covariance.at(0U)++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, pose_with_covariance_stamped_msg) {
  geometry_msgs::msg::PoseWithCovarianceStamped msg1;
  geometry_msgs::msg::PoseWithCovarianceStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__pose(msg2.pose);
  test_different_messages(msg1, msg2);

  // different field pose
  msg1.set__header(msg2.header);
  msg1.pose.pose.position.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, quaternion_msg) {
  geometry_msgs::msg::Quaternion msg1;
  geometry_msgs::msg::Quaternion msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field x
  msg1 = msg2;
  msg1.x++;
  test_different_messages(msg1, msg2);

  // different field y
  msg1 = msg2;
  msg1.y++;
  test_different_messages(msg1, msg2);

  // different field z
  msg1 = msg2;
  msg1.z++;
  test_different_messages(msg1, msg2);

  // different field w
  msg1 = msg2;
  msg1.w++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, quaternion_stamped_msg) {
  geometry_msgs::msg::QuaternionStamped msg1;
  geometry_msgs::msg::QuaternionStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__quaternion(msg2.quaternion);
  test_different_messages(msg1, msg2);

  // different field quaternion
  msg1.set__header(msg2.header);
  msg1.quaternion.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, transform_msg) {
  geometry_msgs::msg::Transform msg1;
  geometry_msgs::msg::Transform msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field rotation
  msg1.rotation.x++;
  msg1.set__translation(msg2.translation);
  test_different_messages(msg1, msg2);

  // different field translation
  msg1.set__rotation(msg2.rotation);
  msg1.translation.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, transform_stamped_msg) {
  geometry_msgs::msg::TransformStamped msg1;
  geometry_msgs::msg::TransformStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__child_frame_id(msg2.child_frame_id);
  msg1.set__transform(msg2.transform);
  test_different_messages(msg1, msg2);

  // different field child_frame_id
  msg1.set__header(msg2.header);
  msg1.child_frame_id = "hello world";
  msg1.set__transform(msg2.transform);
  test_different_messages(msg1, msg2);

  // different field transform
  msg1.set__header(msg2.header);
  msg1.set__child_frame_id(msg2.child_frame_id);
  msg1.transform.translation.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, twist_msg) {
  geometry_msgs::msg::Twist msg1;
  geometry_msgs::msg::Twist msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field linear
  msg1.linear.x++;
  msg1.set__angular(msg2.angular);
  test_different_messages(msg1, msg2);

  // different field angular
  msg1.set__linear(msg2.linear);
  msg1.angular.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, twist_stamped_msg) {
  geometry_msgs::msg::TwistStamped msg1;
  geometry_msgs::msg::TwistStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__twist(msg2.twist);
  test_different_messages(msg1, msg2);

  // different field twist
  msg1.set__header(msg2.header);
  msg1.twist.angular.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, twist_with_covariance_msg) {
  geometry_msgs::msg::TwistWithCovariance msg1;
  geometry_msgs::msg::TwistWithCovariance msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field twist
  msg1.twist.angular.x++;
  msg1.set__covariance(msg2.covariance);
  test_different_messages(msg1, msg2);

  // different field covariance
  msg1.set__twist(msg2.twist);
  msg1.covariance.at(0U)++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, twist_with_covariance_stamped_msg) {
  geometry_msgs::msg::TwistWithCovarianceStamped msg1;
  geometry_msgs::msg::TwistWithCovarianceStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__twist(msg2.twist);
  test_different_messages(msg1, msg2);

  // different field twist
  msg1.set__header(msg2.header);
  msg1.twist.twist.angular.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, vector3_msg) {
  geometry_msgs::msg::Vector3 msg1;
  geometry_msgs::msg::Vector3 msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field x
  msg1.x++;
  msg1.set__y(msg2.y);
  msg1.set__z(msg2.z);
  test_different_messages(msg1, msg2);

  // different field y
  msg1.set__x(msg2.x);
  msg1.y++;
  msg1.set__z(msg2.z);
  test_different_messages(msg1, msg2);

  // different field z
  msg1.set__x(msg2.x);
  msg1.set__y(msg2.y);
  msg1.z++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, vector3_stamped_msg) {
  geometry_msgs::msg::Vector3Stamped msg1;
  geometry_msgs::msg::Vector3Stamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__vector(msg2.vector);
  test_different_messages(msg1, msg2);

  // different field vector
  msg1.set__header(msg2.header);
  msg1.vector.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, wrench_msg) {
  geometry_msgs::msg::Wrench msg1;
  geometry_msgs::msg::Wrench msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field force
  msg1.force.x++;
  msg1.set__torque(msg2.torque);
  test_different_messages(msg1, msg2);

  // different field torque
  msg1.set__force(msg2.force);
  msg1.torque.x++;
  test_different_messages(msg1, msg2);
}

/// @test{
/// "req" : ["SWRQ_BASELINE_CERT_MSGS_270"]
/// }
TEST(Test_geometry_msgs_comparison, wrench_stamped_msg) {
  geometry_msgs::msg::WrenchStamped msg1;
  geometry_msgs::msg::WrenchStamped msg2;

  // equal messages
  test_equal_messages(msg1, msg2);

  // different field header
  msg1.header.stamp.sec++;
  msg1.set__wrench(msg2.wrench);
  test_different_messages(msg1, msg2);

  // different field wrench
  msg1.set__header(msg2.header);
  msg1.wrench.torque.x++;
  test_different_messages(msg1, msg2);
}
