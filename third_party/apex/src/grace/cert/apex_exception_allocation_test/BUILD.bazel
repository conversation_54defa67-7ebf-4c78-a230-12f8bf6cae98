load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

filegroup(
    name = "test_apex_exceptions_memory_allocation_srcs_with_req_ids",
    srcs = ["test/test_apex_exceptions_memory_allocation.cpp"],
    visibility = [
        "//grace/cert/apex_malloc/doc/internal:__subpackages__",
    ],
)

apex_cc_test(
    name = "test_apex_exceptions_memory_allocation",
    srcs = [":test_apex_exceptions_memory_allocation_srcs_with_req_ids"],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    tags = [
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
    ],
    deps = [
        "//common/configuration/settings",
        "//common/cpputils",
        "//common/threading",
        "//grace/cert/apex_malloc",
        "//grace/ros/rclcpp/rclcpp",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = ["test/test_apex_exceptions_memory_allocation.cpp"],
    visibility = [
        "//grace/cert/apex_malloc/design:__subpackages__",
    ],
)
