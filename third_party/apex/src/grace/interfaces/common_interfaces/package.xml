<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>common_interfaces</name>
  <version>2.2.3</version>
  <description>common_interfaces contains messages and services that are widely used by other ROS packages.</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>

  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>actionlib_msgs</exec_depend>
  <exec_depend>builtin_interfaces</exec_depend>
  <exec_depend>diagnostic_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>shape_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>std_srvs</exec_depend>
  <exec_depend>stereo_msgs</exec_depend>
  <exec_depend>trajectory_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
