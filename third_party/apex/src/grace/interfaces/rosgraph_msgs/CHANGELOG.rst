^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package rosgraph_msgs
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

1.0.3 (2021-04-06)
------------------
* Change index.ros.org -> docs.ros.org. (`#122 <https://github.com/ros2/rcl_interfaces/issues/122>`_)
* Updating Quality Declaration (`#120 <https://github.com/ros2/rcl_interfaces/issues/120>`_)
* Update README.md (`#119 <https://github.com/ros2/rcl_interfaces/issues/119>`_)
* Contributors: <PERSON>, shonigmann

1.0.2 (2021-02-22)
------------------
* Update quality declaration to QL 1. (`#116 <https://github.com/ros2/rcl_interfaces/issues/116>`_)
* Update package maintainers. (`#112 <https://github.com/ros2/rcl_interfaces/issues/112>`_)
* Contributors: <PERSON>, <PERSON>, <PERSON> <PERSON>rawner

1.0.1 (2020-06-29)
------------------
* Increase Quality level of packages to 3 (`#108 <https://github.com/ros2/rcl_interfaces/issues/108>`_)
* Add Security Vulnerability Policy pointing to REP-2006. (`#106 <https://github.com/ros2/rcl_interfaces/issues/106>`_)
* Updating QD to reflect package versions (`#107 <https://github.com/ros2/rcl_interfaces/issues/107>`_)
* Contributors: Chris Lalancette, brawner

1.0.0 (2020-05-26)
------------------
* Add quality declarations for each package except test_msgs (`#92 <https://github.com/ros2/rcl_interfaces/issues/92>`_)
* Contributors: brawner

0.8.0 (2019-09-26)
------------------

0.7.4 (2019-05-29)
------------------

0.7.3 (2019-05-20)
------------------

0.7.2 (2019-05-08)
------------------

0.7.1 (2019-04-26)
------------------

0.7.0 (2019-04-14)
------------------

0.6.2 (2019-01-11)
------------------

0.6.1 (2018-12-06)
------------------

0.6.0 (2018-11-16)
------------------
* use add_compile_options instead of setting only cxx flags
* Contributors: Mikael Arguedas

0.5.0 (2018-06-24)
------------------
* move rosgraph_msgs to rcl_interfaces (`#34 <https://github.com/ros2/rcl_interfaces/issues/34>`_)
* Contributors: Mikael Arguedas

0.4.0 (2017-12-08)
------------------
