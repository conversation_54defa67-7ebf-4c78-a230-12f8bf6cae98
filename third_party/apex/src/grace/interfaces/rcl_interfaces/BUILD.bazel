load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

msgs_library(
    name = "rcl_interfaces",
    srcs = glob([
        "msg/*.idl",
    ]),
    rosmsg_srcs = glob([
        "srv/*.srv",
    ]),
    visibility = ["//visibility:public"],
    wheel_data = ":rcl_interfaces_pkg.wheel_data",
    deps = [
        "@apex//grace/interfaces/builtin_interfaces",
    ],
)

ros_pkg(
    name = "rcl_interfaces_pkg",
    description = "The ROS client library common interfaces.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [
        ":rcl_interfaces",
    ],
    pkg_name = "rcl_interfaces",
    version = "1.0.3",
    visibility = ["//visibility:public"],
    deps = ["@apex//grace/interfaces/builtin_interfaces:builtin_interfaces_pkg"] + ROSIDL_COMMON_PKGS,
)

filegroup(
    name = "doc_files",
    srcs = [
        "msg/ParameterValue.idl",
    ],
    visibility = ["//grace/demos/demo_storage/demo_storage/design:__subpackages__"],
)
