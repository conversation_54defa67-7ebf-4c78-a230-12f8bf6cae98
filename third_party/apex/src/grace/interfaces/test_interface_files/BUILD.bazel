filegroup(
    name = "test_interface_files_idl_files",
    srcs = select({
        "//common/asil:d": [
            "msg/BasicTypes.idl.in",
            "msg/BoundedCollection.idl.in",
            "msg/BoundedSequences.idl.in",
            "msg/CamelCase.idl.in",
            "msg/CommonConstants.idl.in",
            "msg/Constants.idl.in",
            "msg/Defaults.idl.in",
            "msg/Empty.idl.in",
            "msg/FlatSequencesBounded.idl.in",
            "msg/FloatingPointValues.idl.in",
            "msg/IncludedConstants.idl.in",
            "msg/MultiDeclaration.idl.in",
            "msg/MultipleStructs.idl.in",
            "msg/MultipleStructsSequence.idl.in",
            "msg/Nested.idl.in",
            "msg/NestedType.idl.in",
            "msg/SomeEnum2.idl.in",
            "msg/UnionsMessage.idl.in",
        ],
        "//conditions:default": [
            "msg/Arrays.idl.in",
            "msg/BasicTypes.idl.in",
            "msg/BoundedCollection.idl.in",
            "msg/BoundedSequences.idl.in",
            "msg/CamelCase.idl.in",
            "msg/CommonConstants.idl.in",
            "msg/Constants.idl.in",
            "msg/Defaults.idl.in",
            "msg/Empty.idl.in",
            "msg/EnumSequence.idl.in",
            "msg/EnumsMessage.idl.in",
            "msg/FlatSequencesBounded.idl.in",
            "msg/FloatingPointValues.idl.in",
            "msg/LegacyEnumSequence.idl.in",
            "msg/LegacyEnumsMessage.idl.in",
            "msg/MultiDeclaration.idl.in",
            "msg/MultiNested.idl.in",
            "msg/MultipleStructs.idl.in",
            "msg/MultipleStructsSequence.idl.in",
            "msg/Nested.idl.in",
            "msg/NestedType.idl.in",
            "msg/SameEnumerators.idl.in",
            "msg/ScopedEnumsMessage.idl.in",
            "msg/SomeEnum2.idl.in",
            "msg/StringSilent.idl.in",
            "msg/Strings.idl.in",
            "msg/UnboundedSequences.idl.in",
            "msg/UnionsMessage.idl.in",
        ],
    }) + select({
        "//grace/rmw_implementation:use_ida": [
            "RootDirectory.idl.in",
            "interface/nested/NestedDirectory.idl.in",
            "msg/MultipleNamespacesWithSameStruct.idl.in",
            "msg/MultipleNestedMultiNamespaces.idl.in",
            "msg/ShortNamespace.idl.in",
            "msg/SomeWeirdFileName.idl.in",
        ],
        "//conditions:default": [],
    }),
    visibility = ["//visibility:public"],
)

filegroup(
    name = "test_interface_files_srv_files",
    srcs = select({
        "//common/asil:d": [
            "srv/BasicTypes.srv",
            "srv/Empty.srv",
        ],
        "//conditions:default": [
            "srv/Arrays.srv",
            "srv/BasicTypes.srv",
            "srv/Empty.srv",
        ],
    }),
    visibility = ["//visibility:public"],
)

filegroup(
    name = "test_interface_files_action_files",
    srcs = select({
        "//common/asil:d": [],
        "//conditions:default": [
            "action/Fibonacci.action",
        ],
    }),
    visibility = ["//visibility:public"],
)

filegroup(
    name = "doc_files",
    srcs = ["msg/BoundedSequences.idl.in"],
    visibility = ["//grace:__subpackages__"],
)
