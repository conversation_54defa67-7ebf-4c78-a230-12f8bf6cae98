// Generated from test_interface_files/msg/EnumsMessage.idl.in
// for package @package_name@
#include "@package_name@/msg/SameEnumerators.idl"
#include "@package_name@/msg/SomeEnum2.idl"

module @package_name@ {
  enum SomeEnum {
    ENUMERATOR1,
    ENUMERATOR2
  };

  module msg {
    struct EnumsMessage {
      SomeEnum enum_value;

      SomeEnum2 enum2_value;

      @default (value="ENUMERATOR2")
      SomeEnum enum_default_value;

      @default (value="ENUMERATOR4")
      SomeEnum2 enum2_default_value;

      SomeEnum static_array_values[7];

      sequence<SomeEnum, 7> bounded_array_values;

      sequence<SomeEnum> dynamic_array_values;
    };
  };
};
