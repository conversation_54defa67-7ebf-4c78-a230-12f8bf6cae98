// generated from rosidl_adapter/resource/msg.idl.em
// with input from diagnostic_msgs/msg/DiagnosticArray.msg
// generated code does not contain a copyright notice

#include "diagnostic_msgs/msg/DiagnosticStatus.idl"
#include "std_msgs/msg/Header.idl"

module diagnostic_msgs {
  module msg {
    @verbatim (language="comment", text=
      " This message is used to send diagnostic information about the state of the robot.")
    struct DiagnosticArray {
      @verbatim (language="comment", text=
        " for timestamp")
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        " an array of components being reported on")
      sequence<diagnostic_msgs::msg::DiagnosticStatus, 16> status;
    };
  };
};
