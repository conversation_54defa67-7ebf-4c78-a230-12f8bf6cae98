load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

msgs_library(
    name = "visualization_msgs",
    srcs = glob([
        "msg/*.idl",
    ]),
    rosmsg_srcs = glob([
        "srv/*.srv",
    ]),
    visibility = ["//visibility:public"],
    wheel_data = ":visualization_msgs_pkg.wheel_data",
    deps = [
        "@apex//grace/interfaces/geometry_msgs",
        "@apex//grace/interfaces/std_msgs",
    ],
)

ros_pkg(
    name = "visualization_msgs_pkg",
    description = "A package containing some visualization and interaction related message definitions.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI, Inc.",
    msg_libraries = [
        ":visualization_msgs",
    ],
    version = "2.2.3",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/builtin_interfaces:builtin_interfaces_pkg",
        "@apex//grace/interfaces/geometry_msgs:geometry_msgs_pkg",
        "@apex//grace/interfaces/std_msgs:std_msgs_pkg",
    ] + ROSIDL_COMMON_PKGS,
)
