load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

msgs_library(
    name = "statistics_msgs",
    srcs = glob([
        "msg/*.idl",
    ]),
    visibility = ["//visibility:public"],
    wheel_data = ":statistics_msgs_pkg.wheel_data",
    deps = [
        "@apex//grace/interfaces/builtin_interfaces",
    ],
)

ros_pkg(
    name = "statistics_msgs_pkg",
    description = "Message definitions for reporting statistics for topics and system resources.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [
        ":statistics_msgs",
    ],
    version = "1.0.3",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//grace/interfaces/builtin_interfaces:builtin_interfaces_pkg",
    ] + ROSIDL_COMMON_PKGS,
)
