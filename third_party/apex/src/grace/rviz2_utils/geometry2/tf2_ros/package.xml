<package format="2">
  <name>tf2_ros</name>
  <version>0.11.4</version>
  <description>
    This package contains the ROS bindings for the tf2 library, for both Python and C++.
  </description>
  <author><PERSON><PERSON><PERSON></author>
  <author><PERSON><PERSON></author>
  <maintainer email="<EMAIL>">Tu<PERSON></maintainer>
  <license>BSD</license>

  <url type="website">http://www.ros.org/wiki/tf2_ros</url>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>apex_cmake</buildtool_depend>

  <build_depend>geometry_msgs</build_depend>
  <build_depend>message_filters</build_depend>
  <build_depend>rclcpp</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_msgs</build_depend>

  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>message_filters</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_msgs</exec_depend>

  <build_export_depend>rclcpp</build_export_depend>

  <!-- TODO(tfoote) dependencies from unported sections restore when ported
  <depend>actionlib</depend>
  <depend>actionlib_msgs</depend>
  <depend>xmlrpcpp</depend> -->
  <!-- <build_depend version_gte="1.11.1">message_filters</build_depend> -->
  <!-- <build_depend>rosgraph</build_depend> -->
  <!-- <build_depend>rospy</build_depend> -->
  <!-- <build_depend>tf2_py</build_depend> -->
  <!-- <run_depend>message_filters</run_depend>
  <run_depend>rospy</run_depend>
  <run_depend>tf2_py</run_depend>-->

  <!-- <test_depend>rostest</test_depend> -->

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>apex_test_tools</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
