/// \copyright Copyright 2021 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file defines types used in apex::tf

#ifndef TF2__TYPES_HPP_
#define TF2__TYPES_HPP_

#include <cstdint>

namespace apex
{
namespace tf
{
/// \brief Possible return values for the `apex::tf::can_transform` functions
enum class CanTransformResult : uint8_t
{
  No = 0,
  Yes,
  UnableToAcquireLock
};

/// \brief Convert `apex::tf::CanTransformResult` to `bool`
///
/// Intended usage: `if (successfully(can_transform(...))`
/// \return `true` if transform can be applied, `false` otherwise
inline bool successfully(CanTransformResult result)
{
  return result == CanTransformResult::Yes;
}

}  // namespace tf
}  // namespace apex

#endif   // TF2__TYPES_HPP_
