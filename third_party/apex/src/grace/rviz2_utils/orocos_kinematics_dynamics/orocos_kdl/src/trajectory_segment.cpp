/***************************************************************************
  tag: <PERSON>  Mon May 10 19:10:36 CEST 2004  trajectory_segment.cxx

                        trajectory_segment.cxx -  description
                           -------------------
    begin                : Mon May 10 2004
    copyright            : (C) 2004 Erwin Aertbelien
    email                : <EMAIL>

 ***************************************************************************
 *   This library is free software; you can redistribute it and/or         *
 *   modify it under the terms of the GNU Lesser General Public            *
 *   License as published by the Free Software Foundation; either          *
 *   version 2.1 of the License, or (at your option) any later version.    *
 *                                                                         *
 *   This library is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERC<PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU     *
 *   Lesser General Public License for more details.                       *
 *                                                                         *
 *   You should have received a copy of the GNU Lesser General Public      *
 *   License along with this library; if not, write to the Free Software   *
 *   Foundation, Inc., 59 Temple Place,                                    *
 *   Suite 330, Boston, MA  02111-1307  USA                                *
 *                                                                         *
 ***************************************************************************/
/*****************************************************************************
 *  \author
 *  	Erwin Aertbelien, Div. PMA, Dep. of Mech. Eng., K.U.Leuven
 *
 *  \version
 *		ORO_Geometry V0.2
 *
 *	\par History
 *		- $log$
 *
 *	\par Release
 *		$Id: trajectory_segment.cpp,v 1.1.1.1.2.7 2003/07/23 16:44:26 psoetens Exp $
 *		$Name:  $
 ****************************************************************************/


#include "trajectory_segment.hpp"


namespace KDL {


Trajectory_Segment::Trajectory_Segment(Path* _geom, VelocityProfile* _motprof, bool _aggregate):
	motprof(_motprof),geom(_geom), aggregate(_aggregate)
{
    // assume everything is set or at least check if Duration() != 0
}

Trajectory_Segment::Trajectory_Segment(Path* _geom, VelocityProfile* _motprof, double _duration, bool _aggregate):
	motprof(_motprof),geom(_geom), aggregate(_aggregate)
{
    // the duration was specified so assume motprof not yet set.
    motprof->SetProfileDuration(0, geom->PathLength(), _duration);
}


double Trajectory_Segment::Duration() const
{
	return motprof->Duration();
}

Frame Trajectory_Segment::Pos(double time) const
{
	return geom->Pos(motprof->Pos(time));
}

Twist Trajectory_Segment::Vel(double time) const
{
	return geom->Vel(motprof->Pos(time),motprof->Vel(time));
}

Twist Trajectory_Segment::Acc(double time) const
{
	return geom->Acc(motprof->Pos(time),motprof->Vel(time),motprof->Acc(time));
}


void Trajectory_Segment::Write(std::ostream& os) const
{
	os << "SEGMENT[ " << std::endl;
	os << "  ";geom->Write(os);os << std::endl;
	os << "  ";motprof->Write(os);os << std::endl;
	os << "]";
}

Trajectory_Segment::~Trajectory_Segment()
{
    if (aggregate)
        {
            delete motprof;
            delete geom;
        }
}
Path* Trajectory_Segment::GetPath() {
	return geom;
}

VelocityProfile* Trajectory_Segment::GetProfile() {
	return motprof;
}


}
