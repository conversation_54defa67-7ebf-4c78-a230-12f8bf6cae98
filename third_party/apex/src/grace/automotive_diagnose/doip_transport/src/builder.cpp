// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include "doip_transport/builder.hpp"

#include <memory>
#include <utility>

#include "diagnostic_common/timer/timer.hpp"
#include "doip_transport/config/config_parser.hpp"
#include "executor2/executor_factory.hpp"

namespace apex
{
namespace doip_transport
{
std::shared_ptr<DoIpTransport> Builder::build(
  settings::inspect::dictionary_view cfg,
  const diagnostic_common::NodeContextRef & node_context,
  const executor::live_executor_ptr & executor)
{
  executor->set_file_monitor(m_fm);

  auto doip_settings = config::parse(cfg);

  // m_max_connections +1 because of additional timer for cyclic vehicle announcement msg.
  const auto required_num_of_timers = doip_settings.m_max_connections + 1;

  auto doip_timer =
    std::make_shared<apex::diagnostic_common::timer::Timer>(node_context, required_num_of_timers);
  (void)executor->add(doip_timer);

  auto doip_exec =
    std::make_shared<DoIpTransport>(node_context, std::move(doip_settings), std::move(doip_timer));

  (void)executor->add(doip_exec);

  return doip_exec;
}

std::shared_ptr<DoIpTransport> Builder::build(
  settings::inspect::dictionary_view cfg,
  const diagnostic_common::NodeContextPtr & node_context,
  const executor::live_executor_ptr & executor)
{
  return build(cfg, node_context->as_ref(), executor);
}

}  // namespace doip_transport
}  // namespace apex
