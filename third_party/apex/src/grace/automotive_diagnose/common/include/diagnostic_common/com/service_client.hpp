/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief Wrapper handling requests and responses for pooling service

#ifndef DIAGNOSTIC_COMMON__COM__SERVICE_CLIENT_HPP_
#define DIAGNOSTIC_COMMON__COM__SERVICE_CLIENT_HPP_

#include <atomic>  // To use std::atomic_bool
#include <functional>  // To use std::function
#include <memory>  // To use std::shared_ptr
#include <utility>  // To use move

#include "cpputils/common_exceptions.hpp"  // To use apex::out_of_range
#include "diagnostic_common/node_context.hpp"  // To use NodeContextPtr
#include "executor2/executable_item.hpp"  // To use apex base
#include "logging/logging_macros.hpp"  // To use APEX_WARN
#include "timer_service/clock_timer_service.hpp"  // To use timer_service

namespace apex
{
namespace diagnostic_common
{
namespace com
{

/// \class RequestState
/// \brief Representing the state of the request
struct RequestState
{
  RequestState() : sequence_number{0}, expired{false}, busy{false} {}

  /// \brief Activates request handling.
  /// \param[in] seq_num The sequence number of the request.
  /// \param[in] timeout_val The value of a request timeout.
  void activate(const int64_t seq_num, const std::chrono::milliseconds timeout_val)
  {
    sequence_number = seq_num;
    busy = true;
    expired = false;
    // start a single shot timeout timer
    timer->reschedule_from_now(timeout_val);
  }

  /// \brief Resets the internal state.
  void reset_state()
  {
    if (!expired) {
      timer->cancel();
    }
    sequence_number = 0;
    expired = false;
    busy = false;
  }

  /// \brief Expires request.
  void expire()
  {
    expired = true;
    busy = false;
  }

  /// \brief Check if the request has timed out already.
  /// \return `true` if the request timed out.
  /// \return `false` if the request did not time out.
  bool has_timed_out() const
  {
    return busy && timer->test_and_reset();
  }

  /// \brief Sequence number of the request
  int64_t sequence_number;
  /// \brief Pointer to the timer instance.
  timer_service::timer_ptr timer;
  /// \brief State indicating if the request has timed out
  std::atomic_bool expired;
  /// \brief Marks if the request is busy processing
  std::atomic_bool busy;
};

/// \class ServiceClient
/// \brief Class handling of requests and responses from the pooling service
/// \tparam ServiceT Type of handled service
template <class ServiceT>
class ServiceClient : public apex::executor::executable_item
{
public:
  /// \brief Create a ServiceClient instance for handling a single request and response at a time
  /// from the pooling client adding the timeout for receiving response.
  ///
  /// The ServiceClient instance created by this constructor uses the timer_service instance of the
  /// NodeContext.
  /// \param[in] node_context The parent node used for creation of polling client.
  /// \param[in] service_name The name of the service.
  /// \param[in] logger The logger.
  /// \throws apex::out_of_range in case no timer_service instance is present in the NodeContext.
  ServiceClient(const diagnostic_common::NodeContextRef & node_context,
                const string_strict256_t & service_name,
                logging::Logger<> && logger)
  : executor::executable_item(node_context.node),
    m_logger{std::move(logger)},
    m_client{get_rclcpp_node().template create_polling_client<ServiceT>(service_name)},
    m_timer_service{node_context.services.timer_service()}
  {
    if (!m_timer_service) {
      throw out_of_range(
        "ServiceClient instantiated within a NodeContext without a timer_service instance. Provide "
        "it or use a different constructor.");
    }
    m_request_state.timer = m_timer_service->create_timer();
  }

  /// \brief Checks if service is ready for processing
  /// \return true If service is ready, false otherwise
  bool is_ready() const
  {
    try {
      return m_client->service_is_ready() && !is_busy();
    } catch (...) {
      return false;
    }
  }

  /// \brief Checks if service is busy processing the request.
  /// \return true If service is busy, false otherwise.
  const bool is_busy() const
  {
    return m_request_state.busy.load();
  }

protected:
  using FillRequestCallback =
    std::function<void(typename ServiceT::Request::BorrowedType & request)>;

  /// \brief Sends a request to a service server. If timeout occurs, discard that message
  /// and trigger on_timeout method.
  /// \param[in] fill_request_cb Callback invoked to fill the request payload.
  /// \param[in] timeout_val value of milliseconds after which the message will be discarded. If set
  /// to `std::chrono::milliseconds::max()`, the request is not timed out. The default timeout value
  /// is `milliseconds::max()`.
  /// \return True if the message was successfully sent.
  /// \return False if the client is not ready to send the request.
  [[nodiscard]] bool async_send_request(
    FillRequestCallback fill_request_cb,
    const std::chrono::milliseconds timeout_val = std::chrono::milliseconds::max())
  {
    assert(fill_request_cb);
    if (!is_ready()) {
      return false;
    }

    auto loaned_request = m_client->borrow_loaned_request();
    fill_request_cb(*loaned_request);
    const auto seq_num = m_client->async_send_request(std::move(loaned_request));
    m_request_state.activate(seq_num, timeout_val);
    return true;
  }

  /// \brief Triggered when the request is timed out.
  virtual void on_timeout()
  {
    APEX_WARN(m_logger,
              "Abstract on_timeout method invoked. This implementation should have been overriden "
              "in relevant use cases.");
  }

  /// \brief Triggered when the response is received.
  /// \param msg received response message.
  virtual void on_response(const typename ServiceT::Response::BorrowedType & msg) = 0;

private:
  /// \brief Handle the request timer.
  void handle_timer()
  {
    if (!m_request_state.expired && m_request_state.has_timed_out()) {
      m_request_state.expire();
      on_timeout();
    }
  }

  /// \brief Handle the service responses.
  void handle_service_respones()
  {
    for (const auto & r : m_client->take_response()) {
      if (r.info().valid()) {
        if (m_request_state.sequence_number != r.request_header().sequence_number) {
          // response unrelated to the active request, this is a faulty, late response
          APEX_WARN(m_logger,
                    "Late service response! Expected seq_num=",
                    m_request_state.sequence_number,
                    ", got seq_num=",
                    r.request_header().sequence_number);
          continue;
        }
        if (!m_request_state.expired) {
          on_response(r.data());
        }
        m_request_state.reset_state();
      }
    }
  }

protected:
  apex::executor::client_list get_triggering_clients_impl() const override
  {
    return {m_client};
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_request_state.timer->to_sub_ptr()};
  }

  bool execute_impl() override
  {
    handle_timer();
    handle_service_respones();
    return true;
  }

protected:
  /// \brief Node logger.
  apex::logging::Logger<> m_logger;

private:
  /// \brief Pooling client responsible for sending request to service
  typename rclcpp::PollingClient<ServiceT>::SharedPtr m_client;
  /// \brief The timer_service instance.
  timer_service::timer_service_ptr m_timer_service;
  /// \brief Internal state of the request
  RequestState m_request_state;
};

}  // namespace com
}  // namespace diagnostic_common
}  // namespace apex

#endif  // DIAGNOSTIC_COMMON__COM__SERVICE_CLIENT_HPP_
