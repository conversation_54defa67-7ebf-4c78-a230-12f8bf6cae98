// Copyright 2023 Apex.AI, Inc
// All rights reserved.
/// \file
/// \brief Configuration error thrown in the settings parser.

#ifndef DIAGNOSTIC_COMMON__CONFIG_ERROR_HPP_
#define DIAGNOSTIC_COMMON__CONFIG_ERROR_HPP_

#include <utility>  // To use std::forward

#include "cpputils/common_exceptions.hpp"
#include "diagnostic_common/visibility_control.hpp"

namespace apex
{
namespace diagnostic_common
{

/// \class config_error
/// \brief The Configuration error thrown in the parser when some of the value in settings is
/// invalid.
class DIAGNOSTIC_COMMON_PUBLIC config_error : public apex::runtime_error
{
public:
  /// \brief Construct a new config error object.
  /// \tparam Args The variadic argument pack with error message.
  /// There must exist the conversion from passed type to the string.
  /// \param[in] args The messages that are concatenated to the string.
  template <class... Args>
  explicit config_error(Args &&... args) noexcept : apex::runtime_error{std::forward<Args>(args)...}
  {
  }
};

}  // namespace diagnostic_common
}  // namespace apex

#endif  // DIAGNOSTIC_COMMON__CONFIG_ERROR_HPP_
