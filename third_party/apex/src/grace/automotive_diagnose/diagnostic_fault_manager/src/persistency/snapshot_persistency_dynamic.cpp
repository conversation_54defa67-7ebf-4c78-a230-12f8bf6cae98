// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include "diagnostic_fault_manager/persistency/snapshot_persistency_dynamic.hpp"

#include <memory>
#include <unordered_map>
#include <utility>

namespace apex
{
namespace diagnostic_fault_manager
{
namespace persistency
{

SnapshotPersistencyDynamic::SnapshotPersistencyDynamic(
  std::shared_ptr<const config::DfmConfig> config,
  std::unordered_map<uint32_t, mem_area_to_snapshot_data_store_map> && dtc_snapshot_stores)
: SnapshotPersistencyBase(config, std::move(dtc_snapshot_stores))
{
  assert(m_dfm_config);

  for (const auto & dtc_mask_dtc_pair : m_dfm_config->m_dtcs) {
    m_dtc_snapshot_cache.insert({dtc_mask_dtc_pair.second.m_mask, nullptr});
  }
}

std::shared_ptr<DtcSnapshotDb> SnapshotPersistencyDynamic::get_dtc_snapshot(
  const config::DtcMask & dtc_mask, const optional<uint8_t> & mem_area_id)
{
  auto & snapshot_instance = m_dtc_snapshot_cache.at(dtc_mask);

  if (!snapshot_instance) {
    snapshot_instance = std::make_shared<DtcSnapshotDb>();
  }

  if (mem_area_id) {
    (void)m_dtc_snapshot_stores.at(dtc_mask).at(*mem_area_id)->fetch(*snapshot_instance);
  } else {
    const uint8_t primary_memory_area = m_dfm_config->m_dtcs.at(dtc_mask).m_primary_mem_area_id;
    // Casting to void as a return value can be skipped. In the case of not having this snapshot
    // instance in the DB, the newly created value is returned.
    (void)m_dtc_snapshot_stores.at(dtc_mask).at(primary_memory_area)->fetch(*snapshot_instance);
  }

  return snapshot_instance;
}

}  // namespace persistency
}  // namespace diagnostic_fault_manager
}  // namespace apex
