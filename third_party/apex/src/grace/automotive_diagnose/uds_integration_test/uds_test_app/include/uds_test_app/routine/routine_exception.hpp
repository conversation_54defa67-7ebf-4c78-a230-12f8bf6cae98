// Copyright 2023 Apex.AI, Inc
// All rights reserved.
/// \file
/// \brief Configuration error thrown in the settings parser.

#ifndef UDS_TEST_APP__ROUTINE__ROUTINE_EXCEPTION_HPP_
#define UDS_TEST_APP__ROUTINE__ROUTINE_EXCEPTION_HPP_

#include <utility>  // for std::forward

#include "cpputils/common_exceptions.hpp"
#include "diagnostic_common/visibility_control.hpp"

namespace apex
{
namespace uds_test_app
{
namespace routine
{

/// \class RoutineSequenceException
/// \brief The Routine Sequence exception that is throw on invalid sequence call.
class UDS_TEST_APP_PUBLIC RoutineSequenceException : public runtime_error
{
public:
  /// \copydoc runtime_error::runtime_error
  template <class... Args>
  explicit RoutineSequenceException(Args &&... args) noexcept
  : runtime_error{std::forward<Args>(args)...}
  {
  }
};

/// \class RoutineGeneralException
/// \brief The Routine General Exception use in case of exceptions occurred during the routine
/// handling.
class UDS_TEST_APP_PUBLIC RoutineGeneralException : public runtime_error
{
public:
  /// \copydoc runtime_error::runtime_error
  template <class... Args>
  explicit RoutineGeneralException(Args &&... args) noexcept
  : runtime_error{std::forward<Args>(args)...}
  {
  }
};

}  // namespace routine
}  // namespace uds_test_app
}  // namespace apex

#endif  // UDS_TEST_APP__ROUTINE__ROUTINE_EXCEPTION_HPP_
