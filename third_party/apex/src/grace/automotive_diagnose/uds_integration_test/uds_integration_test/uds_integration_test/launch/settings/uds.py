# Copyright 2023 Apex.AI, Inc
# All rights reserved.

"""Settings dictionary for configuring the `uds_server`."""


uds_dids = [
    {"type": "static", "did": 0xFF00, "data": [0x03, 0x00, 0x00, 0x00]},
    {
        "type": "async",
        "did": 0xF190,
        "read_service": "/uds/did/example1/read",
        "write_service": "/uds/did/example1/write",
    },
    {
        "type": "async",
        "did": 0x0042,  # threshold - custom did
        "read_service": "/uds/did/example_threshold/read",
        "write_service": "/uds/did/example_threshold/write",
    },
    {
        "type": "async",
        "did": 0xF18C,
        "read_service": "/uds/did/example2/read",
        "write_service": "/uds/did/example2/write",
    },
    {
        "type": "async",
        "did": 0xF18D,
        "read_service": "/uds/did/example3/read",
        "write_service": "/uds/did/example3/write",
    },
    {
        "type": "async",
        "did": 0x0043,  # custom did
        "read_service": "/uds/did/unresponsive_server/read",
        "write_service": "/uds/did/unresponsive_server/write",
    },
    {
        "type": "async",
        "did": 0xF19F,  # with delay
        "read_service": "/uds/did/example1/read",
        "write_service": "/uds/did/example1/write",
    },
    {
        "type": "async",
        "did": 0x0040,  # reserved for session 0x40
        "read_service": "/uds/did/example1/read",
        "write_service": "/uds/did/example1/write",
    },
    {
        "type": "async",
        "did": 0x0041,  # reserved for session 0x41
        "read_service": "/uds/did/example1/read",
        "write_service": "/uds/did/example1/write",
    },
    {
        "type": "async",
        "did": 0x0100,  # CalibratedValue
        "read_service": "/uds/did/example1/read",
    },
    {
        "type": "async",
        "did": 0x0101,  # CalibrationValue
        "read_service": "/uds/did/example1/read",
        "write_service": "/uds/did/example1/write",
    }
]

common_security_level_controller_settings = {
  'failure_threshold': 3,
  'delay_ms': 1000
}

individual_security_level_controller_settings = {
  'levels': [
    {
      'level': 0x01,
      'failure_threshold': 3,
      'delay_ms': 1000
    },
    {
      'level': 0x03,
      'failure_threshold': 3,
      'delay_ms': 1000
    },
    {
      'level': 0x1B,
      'failure_threshold': 3,
      'delay_ms': 1000
    },
    {
      'level': 0x41,
      'failure_threshold': 3,
      'delay_ms': 1000
    }
  ]
}

uds_addons = {
  'security_level_controller': {
    'use_external_service': False,
    'built_in_controller_settings': {
      'level_availability_service': '/is_security_level_available',
      'level_unlocking_result_topic': '/security_level_unlocking_result',
      'type': 'individual',
      'common_controller_settings': common_security_level_controller_settings,
      'individual_controller_settings': individual_security_level_controller_settings,
    }
  }
}

uds_services = [
  {'sid': 0x22},
  {'sid': 0x2E},
  {'sid': 0x10},
  {
    'sid': 0x27,
    'get_seed_service': '/get_seed',
    'compare_key_service': '/compare_key',
    'level_availability_service': '/is_security_level_available',
    'level_unlocking_result_topic': '/security_level_unlocking_result',
    'static_seed': False,
    'enabled_security_levels': [0x01, 0x03, 0x7D]
  },
  {'sid': 0x3E},
  {'sid': 0x31},
]

uds_sessions = [
  {
    'id': 0x01,  # default session
    'p2': 200,
    'p2_star': 1000,
    's3': 3000,
    'max_pending_responses': 3
  },
  {
    'id': 0x03,  # extended session
    'p2': 500,
    'p2_star': 1000,
    's3': 3000,
    'max_pending_responses': 3
  },
  {
    'id': 0x40,  # custom session
    'p2': 500,
    'p2_star': 1000,
    's3': 200,
    'max_pending_responses': 3
  },
  {
    'id': 0x41,  # session that has allowed addresses set
    'p2': 500,
    'p2_star': 2000,
    'max_pending_responses': 3
  }
]

uds_access_control = [
  {
    'session_id': 0x01,
    'sid': 0x10,
    'subfunctions': [
      {'subfunction': 0x01},
      {'subfunction': 0x02},
      {'subfunction': 0x03},
      {'subfunction': 0x04},
      {'subfunction': 0x40},
      {'subfunction': 0x41},
    ]
  },
  {
    'session_id': 0x01,
    'sid': 0x22,
    'dids': [
      {'did': 0xFF00},
      {'did': 0xF190},
      {'did': 0x0042},
      {'did': 0x0043},
      {'did': 0xF19F},
      {'did': 0x0100},
      {'did': 0x0101},
    ]
  },
  {
    'session_id': 0x01,
    'sid': 0x19,
    'subfunctions': [
      {'subfunction': 0x01},
      {'subfunction': 0x02},
      {'subfunction': 0x03},
      {'subfunction': 0x04},
      {'subfunction': 0x05},
      {'subfunction': 0x06},
      {'subfunction': 0x07},
      {'subfunction': 0x08},
      {'subfunction': 0x09},
      {'subfunction': 0x0A},
      {'subfunction': 0x0B},
      {'subfunction': 0x0C},
      {'subfunction': 0x0D},
      {'subfunction': 0x0E},
      {'subfunction': 0x14},
      {'subfunction': 0x15},
      {'subfunction': 0x16},
      {'subfunction': 0x17},
      {'subfunction': 0x18},
      {'subfunction': 0x19},
      {'subfunction': 0x1A},
      {'subfunction': 0x42},
      {'subfunction': 0x55},
      {'subfunction': 0x56},
    ]
  },
  {
    'session_id': 0x01,
    'sid': 0x14
  },
  {
    'session_id': 0x01,
    'sid': 0x85,
    'subfunctions': [
      {'subfunction': 0x01},
      {'subfunction': 0x02},
    ]
  },
  {
    'session_id': 0x01,
    'sid': 0x2E,
    'dids': [
      {'did': 0xF190},
      {'did': 0x0043}
    ]
  },
  {
    'session_id': 0x03,
    'sid': 0x10,
    'subfunctions': [
      {'subfunction': 0x01},
      {'subfunction': 0x02},
      {'subfunction': 0x03},
      {'subfunction': 0x04},
      {'subfunction': 0x40},
      {'subfunction': 0x41},
    ]
  },
  {
    'session_id': 0x03,
    'sid': 0x27,
    'subfunctions': [
      {'subfunction': 0x01},
      {'subfunction': 0x02},
      {'subfunction': 0x03},
      {'subfunction': 0x04},
      {'subfunction': 0x7D},
      {'subfunction': 0x7E},
    ]
  },
  {
    'session_id': 0x03,
    'sid': 0x22,
    'dids': [
      {'did': 0xFF00},
      {'did': 0xFF01},
      {
        'did': 0xF190,
        'security_level': [0x01]
      },
      {'did': 0x0042},
      {'did': 0x0043},
      {'did': 0x0100},
    ]
  },
  {
    'session_id': 0x03,
    'sid': 0x2E,
    'security_level': [0x01],
    'dids': [
      {'did': 0xF190},
      {'did': 0x0042},
      {'did': 0x0043},
      {'did': 0x0101},
    ]
  },
  {
    'session_id': 0x03,
    'sid': 0x31,
    'routines': [
      {'routine_id': 0x0001},
    ]
  },
  {
    'session_id': 0x03,
    'sid': 0x3E,
    'subfunctions': [
      {'subfunction': 0x00},
    ]
  },
  {
    'session_id': 0x40,
    'sid': 0x10,
    'subfunctions': [
      {'subfunction': 0x01},
      {'subfunction': 0x02},
      {'subfunction': 0x03},
      {'subfunction': 0x04},
      {'subfunction': 0x40},
      {'subfunction': 0x41},
    ]
  },
  {
    'session_id': 0x40,
    'sid': 0x27,
    'subfunctions': [
      {'subfunction': 0x01},
      {'subfunction': 0x02},
      {'subfunction': 0x03},
      {'subfunction': 0x04},
      {'subfunction': 0x7D},
      {'subfunction': 0x7E},
    ]
  },
  {
    'session_id': 0x40,
    'sid': 0x22,
    'dids': [
      {'did': 0x0040}
    ]
  },
  {
    'session_id': 0x40,
    'sid': 0x2E,
    'dids': [
      {'did': 0x0040}
    ]
  },
  {
    'session_id': 0x40,
    'sid': 0x3E,
    'subfunctions': [
      {'subfunction': 0x00},
    ]
  },
  {
    'session_id': 0x41,
    'sid': 0x10,
    'subfunctions': [
      {'subfunction': 0x01},
      {'subfunction': 0x02},
      {'subfunction': 0x03},
      {'subfunction': 0x04},
      {'subfunction': 0x40},
      {'subfunction': 0x41},
    ]
  },
  {
    'session_id': 0x41,
    'sid': 0x22,
    'allowed_addresses': [0x4455, 0x3344],
    'dids': [
      {
        'did': 0x0041,
        'allowed_addresses': [0x4455]
      },
      {
        'did': 0xF190
      }
    ]
  },
  {
    'session_id': 0x41,
    'sid': 0x2E,
    'allowed_addresses': [0x4455, 0x3344],
    'dids': [
      {
        'did': 0x0041,
        'allowed_addresses': [0x4455]
      }
    ]
  },
  {
    'session_id': 0x41,
    'sid': 0x3E,
    'subfunctions': [
      {'subfunction': 0x00},
    ]
  },
]

routines = [{
    'routine_id': 0x0001,
    'routine_service': '/uds/routine_0001',
    'supported_subfunctions': [
        "start",
        "stop",
        "result",
    ],
}, ]

uds_settings_dict = {
  'diagnostic': {
    'uds': {
      'req_topic': '/uds/req',
      'res_topic': '/uds/rsp',
      'env_topic': '/uds/env',
      'session_event_topic': '/uds/session_event',
      'dids': uds_dids,
      'addons': uds_addons,
      'routines': routines,
      'services': uds_services,
      'sessions': uds_sessions,
      'access_control': uds_access_control
    }
  }
}
