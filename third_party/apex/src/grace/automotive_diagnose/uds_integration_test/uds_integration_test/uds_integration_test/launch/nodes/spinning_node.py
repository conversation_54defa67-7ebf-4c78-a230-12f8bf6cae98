# Copyright 2023 Apex.AI, Inc
# All rights reserved.

from rclpy.node import Node
from launch_testing_ros import MessagePump


class SpinningNode:
    """
    Wrapper for rclpy.Node and MessagePump.

    Enables the functionality of MessagePump and manages
    the lifecyles of both containing objects.
    """

    def __init__(self, node: Node):
        """
        Create SpinningNode.

        :param rclpy.Node node: The node that will be spun.
        """
        self.node = node
        self.__msg_pump = MessagePump(self.node)

    def async_spin(self):
        """Run MessagePump on containing Node."""
        self.__msg_pump.start()

    def shutdown(self):
        """Stop the spin of MessagePump and destroys containing Node."""
        self.__msg_pump.stop()
        self.node.destroy_node()
