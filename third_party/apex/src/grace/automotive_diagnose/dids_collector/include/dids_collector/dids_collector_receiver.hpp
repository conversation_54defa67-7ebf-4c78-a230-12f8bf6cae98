// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

/// \file
/// \brief An executable item that receives external read DID requests.

#ifndef DIDS_COLLECTOR__DIDS_COLLECTOR_RECEIVER_HPP_
#define DIDS_COLLECTOR__DIDS_COLLECTOR_RECEIVER_HPP_

#include <memory>

#include "diagnostic_common/node_context.hpp"
#include "dids_collector/dids_collector_did_reader.hpp"
#include "dids_collector/visibility_control.hpp"
#include "executor2/apex_node_base.hpp"
#include "logging/logging.hpp"

#include "diagnostic_fault_manager_msgs/msg/update_data_by_identifier.hpp"
#include "uds_msgs/srv/uds_read_data_by_identifier.hpp"

namespace apex
{
namespace dids_collector
{

using UdsReadDid = uds_msgs::srv::UdsReadDataByIdentifier;

/// \class DidsCollectorReceiver
/// \brief An executable item that receives external read DID requests.
class DIDS_COLLECTOR_PUBLIC DidsCollectorReceiver : public executor::executable_item
{
public:
  /// \brief Construct new DidsCollectorReceiver object.
  /// \param[in] node_context context of the parent node.
  /// \param[in] dids_reader pointer to DidsCollectorDidReader interface, which communicates with
  /// applications to obtain DID updates.
  DidsCollectorReceiver(const diagnostic_common::NodeContextRef & node_context,
                        std::shared_ptr<DidsCollectorDidReaderBase> dids_reader);

private:
  /// \brief Implements execution logic.
  bool execute_impl() override;
  /// \return Return all the triggering services types.
  executor::service_list get_triggering_services_impl() const override;

  /// \brief The DidsCollectorReceiver logger object.
  apex::logging::Logger<> m_logger;
  /// \brief Pointer to DidsCollectorDidReader interface, which communicates with
  /// applications to obtain DID updates.
  std::shared_ptr<DidsCollectorDidReaderBase> m_did_reader;
  /// \brief Service instance to send responses for uds_msgs::srv::UdsReadDataByIdentifier requests.
  rclcpp::PollingService<UdsReadDid>::SharedPtr m_incoming_requests_service;
};

}  // namespace dids_collector
}  // namespace apex

#endif  // DIDS_COLLECTOR__DIDS_COLLECTOR_RECEIVER_HPP_
