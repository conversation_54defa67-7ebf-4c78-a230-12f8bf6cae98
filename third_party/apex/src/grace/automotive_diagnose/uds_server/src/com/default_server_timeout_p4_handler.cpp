// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include "uds_server/com/default_server_timeout_p4_handler.hpp"

#include "logging/logging_macros.hpp"  // To use APEX_ERROR_R

namespace apex
{
namespace uds_server
{
namespace com
{

void DefaultServerTimeoutP4Handler::on_timeout_p4()
{
  // TODO(misha): Send event
  APEX_ERROR_R("Max number of pending responses reached without the final response!");
}

}  // namespace com
}  // namespace uds_server
}  // namespace apex
