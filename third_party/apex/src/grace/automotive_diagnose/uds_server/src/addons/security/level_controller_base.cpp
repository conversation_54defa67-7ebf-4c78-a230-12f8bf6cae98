// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include "uds_server/addons/security/level_controller_base.hpp"

#include <memory>  // To use std::shared_ptr<>
#include <utility>  // To use std::move

namespace apex
{
namespace uds_server
{
namespace addons
{
namespace security
{

LevelControllerBase::LevelControllerBase(const diagnostic_common::NodeContextRef & context,
                                         const LevelControllerConfigBase & config)
: executor::executable_item(context.node),
  m_level_unlocking_result_sub{
    context.node.create_polling_subscription<UdsSecurityLevelUnlockingResult>(
      config.m_level_unlocking_result_topic, rclcpp::DefaultQoS())},
  m_level_availability_srv{context.node.create_polling_service<UdsIsSecurityLevelAvailable>(
    config.m_level_availability_service_name, rclcpp::DefaultQoS())}
{
}

bool LevelControllerBase::execute_impl()
{
  process_level_unlocking_result_msgs();
  process_level_availability_reqs();
  return true;
}

void LevelControllerBase::process_level_unlocking_result_msgs()
{
  const auto loaned_msgs = m_level_unlocking_result_sub->take();
  for (const auto & msg : loaned_msgs) {
    if (msg.info().valid()) {
      if (msg.data().success) {
        on_level_unlocking_success(msg.data().security_level);
      } else {
        on_level_unlocking_failure(msg.data().security_level);
      }
    }
  }
}

void LevelControllerBase::process_level_availability_reqs()
{
  auto loaned_reqs = m_level_availability_srv->take_request();
  for (const auto & req : loaned_reqs) {
    if (req.info().valid()) {
      auto response = m_level_availability_srv->borrow_loaned_response();
      response.get().is_available = is_level_available(req.data().security_level);
      auto header = req.request_header();
      m_level_availability_srv->send_response(header, std::move(response));
    }
  }
}

}  // namespace security
}  // namespace addons
}  // namespace uds_server
}  // namespace apex
