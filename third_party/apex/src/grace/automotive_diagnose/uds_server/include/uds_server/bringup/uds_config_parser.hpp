/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The parser of the UDS Server configuration.

#ifndef UDS_SERVER__BRINGUP__UDS_CONFIG_PARSER_HPP_
#define UDS_SERVER__BRINGUP__UDS_CONFIG_PARSER_HPP_

#include <memory>  // for std::shared_ptr<>
#include <string>  // for std::string
#include <utility>  // for std::pair<>
#include <vector>  // for std::vector<>

#include "diagnostic_common/config_error.hpp"
#include "diagnostic_common/node_context.hpp"  // for NodeContextPtr
#include "diagnostic_common/timer/timer_base.hpp"  // for TimerBase
#include "executor2/executable_item_ptr.hpp"  // for executable_item_ptr
#include "settings/inspect/types.hpp"  // for dictionary_view
#include "uds_server/bringup/config.hpp"
#include "uds_server/services/did/did_pool.hpp"  // for DidEntry
#include "uds_server/services/security/security_access_service_config.hpp"  // for SecurityAccessServiceConfig
#include "uds_server/services/shared/diagnostic_service.hpp"
#include "uds_server/session/default_session.hpp"  // for DefaultSession
#include "uds_server/session/session.hpp"  // for Session
#include "uds_server/visibility_control.hpp"  // for UDS_SERVER_PUBLIC


namespace apex
{
namespace uds_server
{
namespace bringup
{
using dictionary_view = apex::settings::inspect::dictionary_view;
using DidEntry = services::did::DidEntry;
using DidEntries = std::vector<DidEntry>;
using ExecutableDids = std::vector<executor::executable_item_ptr>;
using DefaultSessionPtr = std::shared_ptr<session::DefaultSession>;
using SessionPtr = std::shared_ptr<session::Session>;
using NonDefaultSessions = std::vector<SessionPtr>;
using Sessions = std::pair<DefaultSessionPtr, NonDefaultSessions>;

/// \brief Parse the request topic name from a config.
/// \param[in] cfg The dictionary_view with a UDS configuration.
/// \throws diagnostic_common::config_error on invalid cfg.
/// \return std::string with a request topic name.
UDS_SERVER_PUBLIC std::string parse_req_topic(dictionary_view cfg);
/// \brief Parse the response topic name from a config.
/// \param[in] cfg The dictionary_view with a UDS configuration.
/// \throws diagnostic_common::config_error on invalid cfg.
/// \return std::string response topic name.
UDS_SERVER_PUBLIC std::string parse_res_topic(dictionary_view cfg);
/// \brief Parse the env monitor topic name from a config.
/// \param[in] cfg The dictionary_view with a UDS configuration.
/// \throws diagnostic_common::config_error on invalid cfg.
/// \return std::string env monitor topic name.
UDS_SERVER_PUBLIC std::string parse_env_topic(dictionary_view cfg);
/// \brief Parse the session event topic name from a config.
/// \param[in] cfg The dictionary_view with a UDS configuration.
/// \throws diagnostic_common::config_error on invalid cfg.
/// \return std::string session event topic name.
UDS_SERVER_PUBLIC std::string parse_session_event_topic(dictionary_view cfg);
/// \brief Parse a DID from a config to get all static and async DID readers and writers.
/// \param[in] cfg The dictionary_view with a UDS configuration.
/// \param[in] context The node context required to create executable dids.
/// \param[in] timer The pointer to shared timer object.
/// \throws diagnostic_common::config_error on invalid cfg.
/// \return std::pair<DidEntries, ExecutableDids> with all DidEntries and list
/// of executable dids.
UDS_SERVER_PUBLIC std::pair<DidEntries, ExecutableDids> parse_dids(
  dictionary_view cfg, const diagnostic_common::NodeContextRef & context);
/// \brief Parse the service IDs from a config to get a list of enabled services.
/// \param[in] cfg The dictionary_view with a UDS configuration.
/// \throws diagnostic_common::config_error on invalid cfg.
/// \return std::vector<constants::UdsSid> with list of all supported sids.
UDS_SERVER_PUBLIC std::vector<services::shared::UdsSid> parse_sids(dictionary_view cfg);
/// \brief Parse the session configuration from a config.
/// \param[in] cfg The dictionary_view with a UDS configuration.
/// \throws diagnostic_common::config_error on invalid cfg.
/// \return Sessions The default session pointer and list on supported
/// non default session pointers.
UDS_SERVER_PUBLIC Sessions parse_sessions(dictionary_view cfg);
/// \brief Parse the security service settings from a config to create SecurityAccessService.
/// \param[in] cfg The dictionary_view with a UDS configuration.
/// \throws diagnostic_common::config_error on invalid cfg .
/// \return SecurityAccessSrvConfig the Security Access Service config.
UDS_SERVER_PUBLIC services::security::SecurityAccessSrvConfig parse_security_access_service(
  dictionary_view cfg);

}  // namespace bringup
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__BRINGUP__UDS_CONFIG_PARSER_HPP_
