/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief UDS Service predefined sessions from ISO 14229.

#ifndef UDS_SERVER__SESSION__PREDEFINED_SESSION_HPP_
#define UDS_SERVER__SESSION__PREDEFINED_SESSION_HPP_

namespace apex
{
namespace uds_server
{
namespace session
{

/// \enum  PredefinedSession
/// \brief Predefined sessions according to ISO 14229-1:2020, Table 25
enum class PredefinedSession : uint8_t
{
  INVALID = 0x00,  // Not applicable (Reserved)
  DEFAULT = 0x01,
  PROGRAMMING = 0x02,
  EXTENDED_DIAGNOSTIC = 0x03,
  SAFETY_SYSTEM_DIAGNOSTIC = 0x04,
};

}  // namespace session
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__SESSION__PREDEFINED_SESSION_HPP_
