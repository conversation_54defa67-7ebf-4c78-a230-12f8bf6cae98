/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The service that handle SID=0x22 Read By DID

#ifndef UDS_SERVER__SERVICES__DID__READ_BY_DID_SERVICE_HPP_
#define UDS_SERVER__SERVICES__DID__READ_BY_DID_SERVICE_HPP_

#include <algorithm>  // for std::copy
#include <functional>  // for std::reference_wrapper
#include <memory>  // for std::shared_ptr

#include "diagnostic_common/node_context.hpp"
#include "logging/logging_macros.hpp"
#include "uds_server/bringup/config.hpp"
#include "uds_server/services/did/did_pool.hpp"  // for DidReadersPool
#include "uds_server/services/did/observers.hpp"  // for DidReaderObserver
#include "uds_server/services/shared/diagnostic_service_with_context.hpp"  // for DiagnosticServiceWithContext
#include "uds_server/visibility_control.hpp"

namespace apex
{
namespace uds_server
{
namespace services
{
namespace did
{
using UdsTransportMessage = uds_msgs::msg::UdsTransportMessage;

/// \struct ReadByDidData
/// \brief The DID Data structure that holds current DID readers cache, requested DIDs and response
/// data.
struct ReadByDidData
{
  /// \brief Take the data by setting the internal state using request data and did_pool DidReaders.
  /// \param[in] request The service request with Read DID data.
  /// \param[in] did_pool The DID pool for fetching the requested readers.
  void take(const shared::ServiceRequest & request,
            const std::shared_ptr<DidReadersPool> & did_pool);

  /// \brief Cleanup the data.
  void cleanup();

  /// \brief Check if the data is valid/
  /// \return true If take was called and the request had proper data.
  /// \return false If data is invalid.
  bool is_valid() const;

  /// \brief Check if this context is available to take.
  /// \return true If the context is available to take().
  /// \return false If the context shall not be taken
  bool is_available() const;

  /// \brief If the data is not valid, take the error reason.
  /// \return shared::UdsResponseCode the NRC that should be sent to the tester.
  shared::UdsResponseCode error_reason() const;

  /// \brief Append read data to internal buffer.
  /// \param did The DID that shall be added to the internal buffer.
  /// \param data The data of the read DID.
  /// \return true If data could be added to the internal buffer.
  /// \return false If the internal buffer size is too small to append additional data.
  bool append_data(const uint16_t did, const DataRecord & data);

  /// \brief Check if the data is ready to be sent to the tester.
  /// \return true If all DID data was read.
  /// \return false If some DID data is still pending.
  bool is_ready() const;

  /// \brief The cache of a DID readers used for reading the data.
  static_vector<std::shared_ptr<DidReader>> readers_cache{
    bringup::MAX_SIZE_OF_DID_IN_A_SINGLE_REQUEST};
  /// \brief The vector of requested DIDs.
  static_vector<uint16_t> request_dids{bringup::MAX_SIZE_OF_DID_IN_A_SINGLE_REQUEST};
  /// \brief The internal buffer for aggregating the response bytes.
  static_vector<uint8_t> response_data{bringup::MAX_SIZE_OF_DID_PAYLOAD};
  /// \brief The flag that marks, if the data is available to take.
  bool available{true};
  /// \brief The optional negative response code that holds value if error occurred.
  apex::optional<shared::UdsResponseCode> nrc{apex::nullopt};
  /// \brief The number of DIDs already read.
  size_t number_of_did_read{0U};
  /// \brief The flag that mark, if all requested_dids was proceed.
  bool finished_reading{false};
};

/// \struct ReadDidContext
/// \brief The Read By DID request state that aggregate
/// details of requests and build the response.
struct ReadDidContext : shared::ServiceContextBase
{
  /// \brief Constructs the ReadDidContext and take the ReadByDid data.
  /// \param[in] service_request The service request with Read Data by Identifier payload.
  /// \param[in] did_data The reference to the Did Data context.
  /// \param[in] did_pool The DID Pool with DID readers.
  ReadDidContext(const shared::ServiceRequest & service_request,
                 ReadByDidData & did_data,
                 const std::shared_ptr<DidReadersPool> & did_pool)
  : shared::ServiceContextBase{service_request}, did_data{std::ref(did_data)}
  {
    data().take(service_request, did_pool);
  }

  /// \brief Get the reference to the did data.
  /// \return ReadByDidData& The reference to the DID data.
  ReadByDidData & data()
  {
    return did_data.get();
  }

  /// \brief Convert the context to the positive response.
  /// WARNING: this should be called only if data().is_ready() returns true.
  /// \return shared::PositiveResponse The positive response build from this context.
  shared::PositiveResponse to_positive_response() const
  {
    return shared::PositiveResponse{
      request_id, tester_address, [data_ptr = &did_data.get().response_data](auto & payload) {
        (void)std::copy(data_ptr->begin(), data_ptr->end(), std::back_inserter(payload));
      }};
  }

  /// \brief The reference to the DID Data.
  std::reference_wrapper<ReadByDidData> did_data;
};

/// \class ReadByDidService
/// \brief The Read By Did (0x22) diagnostic service implementation
/// that follows the ISO-14229-1 Chapter 11.2.
class UDS_SERVER_PUBLIC ReadByDidService
: public shared::DiagnosticServiceWithContext<ReadDidContext>,
  public DidReaderObserver
{
public:
  /// \brief Construct a new Read By Did Service object.
  /// \param[in] response_pub The publisher of a response.
  /// \param[in] did_pool The pool with the DID Readers.
  /// \param[in] context The Node Context for logger creation
  ReadByDidService(shared::ResponsePublisherPtr response_pub,
                   std::shared_ptr<DidReadersPool> did_pool,
                   const diagnostic_common::NodeContextRef & context);

  /// \copydoc DiagnosticService::get_sid()
  shared::UdsSid get_sid() const noexcept override
  {
    return shared::UdsSid::READ_DATA_BY_IDENTIFIER_REQ;
  }

  /// \copydoc DidReaderObserver::notify_about_read_success()
  void notify_about_read_success(const uint64_t request_id,
                                 const uint16_t did,
                                 const DataRecord & data) override;

  /// \copydoc DidReaderObserver::notify_about_read_error()
  void notify_about_read_error(const uint64_t request_id, const uint16_t did) override;

protected:
  /// \copydoc shared::DiagnosticService::supports_subfunction
  bool supports_subfunction() const override
  {
    return false;
  }

  /// \copydoc shared::DiagnosticService::has_valid_msg_size
  bool has_valid_msg_size(const shared::ServiceRequest & request) const override;

  /// \copydoc shared::DiagnosticService::do_process_request
  void do_process_request(const shared::ServiceRequest & request) override;

private:
  /// \brief The pool with a DID Readers.
  std::shared_ptr<DidReadersPool> m_did_pool;
  /// \brief Node logger.
  logging::Logger<> m_logger;
  /// \brief The static vector of Did Data contexts.
  static_vector<ReadByDidData> m_context_data;
};

}  // namespace did
}  // namespace services
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__SERVICES__DID__READ_BY_DID_SERVICE_HPP_
