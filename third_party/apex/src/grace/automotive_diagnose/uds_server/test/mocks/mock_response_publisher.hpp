/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The ResponsePublisher Mock.

#ifndef UDS_SERVER__TEST__MOCKS__MOCK_RESPONSE_PUBLISHER_HPP_
#define UDS_SERVER__TEST__MOCKS__MOCK_RESPONSE_PUBLISHER_HPP_

#include <memory>  // for std::shared_ptr

#include "gmock/gmock.h"
#include "uds_server/com/response_publisher_base.hpp"

namespace apex
{
namespace uds_server
{
namespace com
{

class MockResponsePublisher : public ResponsePublisherBase
{
public:
  MOCK_METHOD1(publish_positive_response, void(const services::shared::PositiveResponse &));
  MOCK_METHOD1(publish_negative_response, void(const services::shared::NegativeResponse &));
};

}  // namespace com
}  // namespace uds_server
}  // namespace apex


#endif  // UDS_SERVER__TEST__MOCKS__MOCK_RESPONSE_PUBLISHER_HPP_
