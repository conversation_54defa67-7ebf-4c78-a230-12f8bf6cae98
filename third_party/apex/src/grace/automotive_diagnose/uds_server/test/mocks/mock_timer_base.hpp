/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The TimerBase Mock.

#ifndef UDS_SERVER__TEST__MOCKS__MOCK_TIMER_BASE_HPP_
#define UDS_SERVER__TEST__MOCKS__MOCK_TIMER_BASE_HPP_

#include "diagnostic_common/timer/timer_base.hpp"
#include "gmock/gmock.h"

#include "gmock/gmock-more-actions.h"  // for SaveArg

namespace apex
{
namespace diagnostic_common
{
namespace timer
{

class MockTimerBase : public TimerBase
{
public:
  MOCK_METHOD3(add_timeout,
               uint32_t(std::chrono::milliseconds, std::function<void(uint32_t)>, bool));
  MOCK_METHOD1(remove_timeout, bool(uint32_t));
  MOCK_METHOD1(restart_timeout, bool(uint32_t));

  /// \brief Capture timeout callback on next add_timeout call.
  /// \param[in] mock The mock on which add_timeout will be called.
  /// \param[out] captured_cb The std::function to which callback will be captured.
  /// \param[in] task_id The task ID to be returned by the add_timeout method
  static void capture_timeout_cb(MockTimerBase & mock,
                                 std::function<void(uint32_t)> & captured_cb,
                                 uint32_t task_id = 1)
  {
    using ::testing::_;
    using ::testing::DoAll;
    using ::testing::Return;
    using ::testing::SaveArg;

    EXPECT_CALL(mock, add_timeout(_, _, _))
      .Times(1)
      .WillOnce(DoAll(SaveArg<1>(&captured_cb), Return(task_id)));
  }
};

}  // namespace timer
}  // namespace diagnostic_common
}  // namespace apex

#endif  // UDS_SERVER__TEST__MOCKS__MOCK_TIMER_BASE_HPP_
