/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The SubfunctionAccess Mock.

#ifndef UDS_SERVER__TEST__MOCKS__MOCK_SUBFUNCTION_ACCESS_HPP_
#define UDS_SERVER__TEST__MOCKS__MOCK_SUBFUNCTION_ACCESS_HPP_

#include <gmock/gmock.h>

#include "uds_server/services/shared/subfunction_access.hpp"

namespace apex
{
namespace uds_server
{
namespace services
{
namespace shared
{

class MockSubfunctionAccess : public SubfunctionAccess
{
public:
  MOCK_CONST_METHOD3(is_subfunction_accessible,
                     AccessStatus(const AccessToken &, const UdsSid, const uint8_t));
};

}  // namespace shared
}  // namespace services
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__TEST__MOCKS__MOCK_SUBFUNCTION_ACCESS_HPP_
