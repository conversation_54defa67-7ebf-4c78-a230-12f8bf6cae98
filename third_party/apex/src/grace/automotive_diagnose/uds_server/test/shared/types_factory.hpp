/// \copyright Copyright 2022 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The factory methods for common types

#ifndef UDS_SERVER__TEST__SHARED__TYPES_FACTORY_HPP_
#define UDS_SERVER__TEST__SHARED__TYPES_FACTORY_HPP_

#include "uds_server/services/auth/auth_roles.hpp"  // for AuthRoles
#include "uds_server/services/security/security_access.hpp"  // for SecurityLevel
#include "uds_server/services/shared/access_types.hpp"  // for AccessToken
#include "uds_server/services/shared/service_response.hpp"  // for NegativeResponse

namespace uds = apex::uds_server;

namespace shared
{
inline uds::services::shared::AccessToken create_access_token(
  const uds_msgs::msg::UdsAddress & tester_address,
  const uint8_t session_id = 0x01,
  const uds::services::security::MaybeSecurityLevel & sec_lvl = apex::nullopt,
  const uds::services::auth::AuthRoles role = uds::services::auth::AuthRoles::NONE)
{
  return uds::services::shared::AccessToken{session_id, sec_lvl, role, tester_address};
}
}  // namespace shared

namespace apex
{
namespace uds_server
{
namespace services
{
namespace shared
{

inline bool operator==(const NegativeResponse & lhs, const NegativeResponse & rhs)
{
  return (lhs.error_code == rhs.error_code) && (lhs.request_id == rhs.request_id) &&
         (lhs.service_id == rhs.service_id) && (lhs.tester_address == rhs.tester_address);
}

inline bool operator!=(const NegativeResponse & lhs, const NegativeResponse & rhs)
{
  return !(lhs == rhs);
}

inline bool operator==(const AccessToken & lhs, const AccessToken & rhs)
{
  return (lhs.m_session_id == rhs.m_session_id) && (lhs.m_security_level == rhs.m_security_level) &&
         (lhs.m_role == rhs.m_role) && (lhs.m_tester_address == rhs.m_tester_address);
}

inline bool operator!=(const AccessToken & lhs, const AccessToken & rhs)
{
  return !(lhs == rhs);
}

}  // namespace shared
}  // namespace services
}  // namespace uds_server
}  // namespace apex

#endif  // UDS_SERVER__TEST__SHARED__TYPES_FACTORY_HPP
