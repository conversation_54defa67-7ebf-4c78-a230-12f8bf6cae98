// Copyright 2022 Apex.AI, Inc
// All rights reserved.

#include <memory>  // for std::shared_ptr<>
#include <utility>  // for std::move

#include "gtest/gtest.h"  // for TEST, EXPECT_*
#include "mocks/mock_observers.hpp"  // for MockSessionObserver
#include "mocks/mock_session_event_publisher.hpp"  // for MockSessionEventPublisher
#include "mocks/mock_timer_base.hpp"  // for MockTimerBase
#include "uds_server/services/shared/access_types.hpp"  // for access types
#include "uds_server/session/predefined_session.hpp"  // for PredefinedSession
#include "uds_server/session/session_manager.hpp"  // for SessionManager, SessionsSupported, SupportedSids

#include "uds_msgs/msg/uds_address.hpp"  // for UdsAddress

namespace uds = apex::uds_server;
namespace session = uds::session;

using SessionId = uds::session::SessionId;
using DefaultSession = uds::session::DefaultSession;
using Session = uds::session::Session;
using SecurityLevel = uds::services::security::SecurityLevel;

using UdsAddress = uds_msgs::msg::UdsAddress;
using UdsAddressTypeEnum = UdsAddress::UdsAddressTypeEnum;

// test usings
using ::testing::_;
using ::testing::DoAll;
using ::testing::InSequence;
using ::testing::Return;
using ::testing::SaveArg;

namespace
{
const UdsAddress FIRST_CLIENT_ID = UdsAddress::create_msg(UdsAddressTypeEnum::PHYSICAL, 0x01u);
const UdsAddress SECOND_CLIENT_ID = UdsAddress::create_msg(UdsAddressTypeEnum::PHYSICAL, 0x02u);

constexpr SessionId DEFAULT_SESSION_ID{static_cast<SessionId>(session::PredefinedSession::DEFAULT)};
constexpr SessionId NON_DEFAULT_SESSION_ID{
  static_cast<SessionId>(session::PredefinedSession::EXTENDED_DIAGNOSTIC)};
constexpr SessionId SECOND_NON_DEFAULT_SESSION_ID{0x05U};
constexpr uint32_t TASK_ID = 1;

struct SessionManagerTest : public ::testing::Test
{
  SessionManagerTest()
  : m_def_time_params{},
    m_non_def_time_params{},
    m_default_session{std::make_shared<DefaultSession>(m_def_time_params)},
    m_additional_session{std::make_shared<Session>(NON_DEFAULT_SESSION_ID, m_non_def_time_params)},
    m_second_additional_session{
      std::make_shared<Session>(SECOND_NON_DEFAULT_SESSION_ID, m_non_def_time_params)},
    m_mock_timer{std::make_shared<apex::diagnostic_common::timer::MockTimerBase>()},
    m_mock_session_observer{},
    m_mock_session_event_pub{std::make_shared<uds::session::MockSessionEventPublisher>()},
    m_sut{m_default_session,
          uds::session::NonDefaultSessions{2u, {m_additional_session, m_second_additional_session}},
          m_mock_session_event_pub,
          m_mock_timer}
  {
  }

  void expect_no_timer_call()
  {
    using ::testing::_;
    EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(0);
    EXPECT_CALL(*m_mock_timer, restart_timeout(_)).Times(0);
    EXPECT_CALL(*m_mock_timer, remove_timeout(_)).Times(0);
  }

  void call_timeout(const uint32_t task_id = TASK_ID)
  {
    ASSERT_TRUE(m_timer_cb);
    m_timer_cb(task_id);
  }


  session::TimingParameters m_def_time_params;
  session::TimingParameters m_non_def_time_params;
  std::shared_ptr<DefaultSession> m_default_session;
  std::shared_ptr<Session> m_additional_session;
  std::shared_ptr<Session> m_second_additional_session;
  std::shared_ptr<apex::diagnostic_common::timer::MockTimerBase> m_mock_timer;
  session::MockSessionObserver m_mock_session_observer;
  std::shared_ptr<session::MockSessionEventPublisher> m_mock_session_event_pub;
  uds::session::SessionManager m_sut;
  std::function<void(uint32_t)> m_timer_cb;
};
}  // namespace

TEST_F(SessionManagerTest, shall_start_default_session_on_construction)
{
  expect_no_timer_call();
  EXPECT_TRUE(m_sut.is_default_session_active());
}

TEST_F(SessionManagerTest, shall_return_false_on_not_supported_session_change)
{
  expect_no_timer_call();
  EXPECT_CALL(*m_mock_session_event_pub, publish_session_transition(_, _)).Times(0);

  EXPECT_FALSE(m_sut.change_session(SessionId{0xFFu}, FIRST_CLIENT_ID));
}

TEST_F(SessionManagerTest, shall_properly_change_session_and_start_timer)
{
  EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillOnce(Return(TASK_ID));
  EXPECT_CALL(*m_mock_session_event_pub,
              publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
    .Times(1);

  EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
}

TEST_F(SessionManagerTest, shall_reset_default_session_on_change_when_it_is_active)
{
  // since there is no actual session change manager shall not publish session transition
  EXPECT_CALL(*m_mock_session_event_pub,
              publish_session_transition(DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
    .Times(0);

  // reset of default session shall not call timer
  expect_no_timer_call();
  EXPECT_TRUE(m_sut.is_default_session_active());
  EXPECT_TRUE(m_sut.register_client(FIRST_CLIENT_ID));
  // set role
  m_sut.change_auth_role(FIRST_CLIENT_ID, uds::services::auth::AuthRoles::ROLE_1);
  const auto access_token = m_sut.generate_access_token(FIRST_CLIENT_ID);

  EXPECT_EQ(access_token.m_role, uds::services::auth::AuthRoles::ROLE_1);

  // change session to the default one, that is already active
  EXPECT_TRUE(m_sut.change_session(DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
  const auto access_token_default = m_sut.generate_access_token(FIRST_CLIENT_ID);

  EXPECT_EQ(access_token_default.m_role, uds::services::auth::AuthRoles::NONE);
}

TEST_F(SessionManagerTest, shall_reset_non_default_session_on_change_when_it_is_active)
{
  EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillOnce(Return(TASK_ID));
  EXPECT_CALL(*m_mock_timer, restart_timeout(_)).Times(1);

  // since there is no actual session change twice the manager shall publish once
  EXPECT_CALL(*m_mock_session_event_pub,
              publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
    .Times(1);

  const SecurityLevel sec_lvl{0x13};
  // change session to non_default_session shall trigger add_timeout
  EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
  m_sut.change_security_level(sec_lvl);
  const auto access_token = m_sut.generate_access_token(FIRST_CLIENT_ID);
  EXPECT_EQ(access_token.m_security_level, sec_lvl);

  // calling change with the active non-default session id shall reset it's value
  // and refresh timer
  EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
  const auto access_token_reset = m_sut.generate_access_token(FIRST_CLIENT_ID);
  EXPECT_FALSE(access_token_reset.m_security_level);
}

TEST_F(SessionManagerTest, shall_register_client_on_first_call_and_do_nothing_on_subsequent_calls)
{
  constexpr uint8_t SUBSEQUENT_CALLS_NUM = 20u;
  // check precondition - default session can handle multiple clients
  EXPECT_TRUE(m_sut.is_default_session_active());

  // since there is no actual session change manager shall not publish session transition
  EXPECT_CALL(*m_mock_session_event_pub, publish_session_transition(DEFAULT_SESSION_ID, _))
    .Times(0);

  // each call shall be true
  for (size_t i = 0u; i < SUBSEQUENT_CALLS_NUM; ++i) {
    EXPECT_TRUE(m_sut.register_client(FIRST_CLIENT_ID));
  }
  // the other client call shall also be handled
  EXPECT_TRUE(m_sut.register_client(SECOND_CLIENT_ID));
}

TEST_F(SessionManagerTest, shall_not_register_second_client_on_non_default_session_active)
{
  InSequence sq;
  EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillOnce(Return(TASK_ID));
  EXPECT_CALL(*m_mock_session_event_pub,
              publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
    .Times(1);
  EXPECT_CALL(*m_mock_timer, restart_timeout(TASK_ID)).Times(1).WillOnce(Return(true));

  // change session to non default with a first client ID
  EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
  // the first client request shall be handled
  EXPECT_TRUE(m_sut.register_client(FIRST_CLIENT_ID));
  // the second client request shall not be handled
  EXPECT_FALSE(m_sut.register_client(SECOND_CLIENT_ID));
}

TEST_F(SessionManagerTest, shall_change_auth_role_and_security_level)
{
  EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillOnce(Return(TASK_ID));

  EXPECT_CALL(*m_mock_session_event_pub,
              publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
    .Times(1);

  // set test values
  constexpr auto role = uds::services::auth::AuthRoles::ROLE_1;
  constexpr SecurityLevel sec_lvl{0x13};

  // change session to non-default because only non-default session
  // supports security lvl
  EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));

  // check initial role and sec lvl
  auto session_param_before = m_sut.generate_access_token(FIRST_CLIENT_ID);
  EXPECT_EQ(session_param_before.m_role, uds::services::auth::AuthRoles::NONE);
  EXPECT_FALSE(session_param_before.m_security_level);

  // change role and sec lvl
  m_sut.change_auth_role(FIRST_CLIENT_ID, role);
  m_sut.change_security_level(sec_lvl);

  // check if role and sec lvl was changed
  auto session_param_after = m_sut.generate_access_token(FIRST_CLIENT_ID);
  EXPECT_EQ(session_param_after.m_role, uds::services::auth::AuthRoles::ROLE_1);
  EXPECT_EQ(session_param_after.m_security_level, sec_lvl);
}

TEST_F(SessionManagerTest, shall_not_reset_timer_in_default_session_on_refresh_request)
{
  expect_no_timer_call();
  // check precondition
  EXPECT_TRUE(m_sut.is_default_session_active());
  // call refresh
  m_sut.refresh_active_session();
}

TEST_F(SessionManagerTest, shall_reset_timer_on_refresh_request)
{
  // the add timeout and restart timeout shall be called
  {
    InSequence seq;
    EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillOnce(Return(TASK_ID));
    EXPECT_CALL(*m_mock_session_event_pub,
                publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
      .Times(1);
    EXPECT_CALL(*m_mock_timer, restart_timeout(_)).Times(1);

    // change to non-default session
    EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
    // call refresh
    m_sut.refresh_active_session();
  }
}

TEST_F(SessionManagerTest, shall_notify_observer_on_session_changed)
{
  // register the observer
  m_sut.register_observer(&m_mock_session_observer);
  // setup expected calls
  {
    InSequence seq;
    EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillOnce(Return(TASK_ID));
    EXPECT_CALL(m_mock_session_observer, notify_about_session_change(_)).Times(1);

    EXPECT_CALL(*m_mock_session_event_pub,
                publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
      .Times(1);

    // change session
    EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
  }
}

TEST_F(SessionManagerTest, shall_stop_timer_on_returning_back_to_default_session)
{
  // setup expected calls
  {
    InSequence seq;
    EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillOnce(Return(TASK_ID));
    EXPECT_CALL(*m_mock_session_event_pub,
                publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
      .Times(1);
    EXPECT_CALL(*m_mock_timer, remove_timeout(_)).Times(1);

    // change session to non-default one
    EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));

    EXPECT_CALL(*m_mock_session_event_pub,
                publish_session_transition(DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
      .Times(1);

    // return back to default session
    EXPECT_TRUE(m_sut.change_session(DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
  }
}

TEST_F(SessionManagerTest,
       shall_remove_old_timer_on_change_from_non_default_to_another_non_default_session)
{
  // setup expected calls
  // each new non-default session change should remove old timeout if exist
  // and add a new one
  {
    InSequence seq;

    EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillRepeatedly(Return(TASK_ID));
    EXPECT_CALL(*m_mock_session_event_pub,
                publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
      .Times(1);
    EXPECT_CALL(*m_mock_timer, remove_timeout(_)).Times(1);

    // change session to non-default
    EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));

    EXPECT_CALL(*m_mock_timer, add_timeout(_, _, _)).Times(1).WillRepeatedly(Return(TASK_ID));
    EXPECT_CALL(*m_mock_session_event_pub,
                publish_session_transition(SECOND_NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
      .Times(1);

    // change session to new non-default
    EXPECT_TRUE(m_sut.change_session(SECOND_NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
  }
}

TEST_F(SessionManagerTest, shall_return_to_default_session_on_timeout)
{
  // setup expected calls
  // each new non-default session change should remove old timeout if exist
  // and add a new one
  {
    InSequence seq;
    apex::diagnostic_common::timer::MockTimerBase::capture_timeout_cb(*m_mock_timer, m_timer_cb);
    EXPECT_CALL(*m_mock_session_event_pub,
                publish_session_transition(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID))
      .Times(1);
    EXPECT_CALL(*m_mock_timer, remove_timeout(_)).Times(1);
    EXPECT_CALL(*m_mock_session_event_pub, publish_session_reset()).Times(1);
    // change session to non-default
    EXPECT_TRUE(m_sut.change_session(NON_DEFAULT_SESSION_ID, FIRST_CLIENT_ID));
    // call timeout and check if the default session is now set
    call_timeout();
  }

  EXPECT_TRUE(m_sut.is_default_session_active());
}
