// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <algorithm>  // for std::copy
#include <memory>  // for std::shared_ptr, std::make_shared, std::make_unique

#include "diagnostic_common/node_context.hpp"
#include "fixtures/diagnostic_test_srv_client_fixture.hpp"
#include "mocks/mock_clear_dtc_client.hpp"
#include "uds_server/services/dtc/async_clear_dtc_client.hpp"

#include "uds_msgs/srv/uds_clear_dtc.hpp"

using ::testing::_;
using ::testing::Invoke;

namespace uds = apex::uds_server;
namespace diagnostic_common = apex::diagnostic_common;

namespace
{

using AsyncClearDtcClient = uds::services::dtc::AsyncClearDtcClient;
using UdsClearDtc = uds_msgs::srv::UdsClearDtc;
using ClearDtcResponse = UdsClearDtc::Response;
using ClearDtcRequest = UdsClearDtc::Request;
using AsyncClearDtcTestSrvClientFixture =
  diagnostic_common::testing::DiagnosticTestSrvClientNodeFixture<AsyncClearDtcClient, UdsClearDtc>;
using MockClearDtcObserver = uds::services::dtc::MockClearDtcObserver;

const auto SERVICE_NAME = "test_service";
const auto INVALID_SERVICE_NAME = "invalid_service";

struct AsyncClearDtcClientNodeFixture : AsyncClearDtcTestSrvClientFixture
{
  AsyncClearDtcClientNodeFixture()
  : AsyncClearDtcTestSrvClientFixture{"AsyncClearDtcClientNodeFixture", SERVICE_NAME},
    m_observer{std::make_shared<MockClearDtcObserver>()}
  {
    set_sut(std::make_unique<AsyncClearDtcClient>(m_context->as_ref(), SERVICE_NAME));
    m_sut->register_observer(m_observer.get());
  }

  std::shared_ptr<MockClearDtcObserver> m_observer;
};

struct AsyncClearDtcClientServiceNotAvailable : AsyncClearDtcTestSrvClientFixture
{
  AsyncClearDtcClientServiceNotAvailable()
  : AsyncClearDtcTestSrvClientFixture{"AsyncClearDtcClientServiceNotAvailable",
                                      INVALID_SERVICE_NAME},
    m_observer{std::make_shared<MockClearDtcObserver>()}
  {
    set_sut(std::make_unique<AsyncClearDtcClient>(m_context->as_ref(), SERVICE_NAME));
    m_sut->register_observer(m_observer.get());
  }

  std::shared_ptr<MockClearDtcObserver> m_observer;
};

}  // namespace

TEST_F(AsyncClearDtcClientNodeFixture, shall_notify_about_success_on_clear_success)
{
  constexpr uint64_t REQUEST_ID{1U};
  constexpr std::array<uint8_t, 4> params{{1U, 2U, 3U, 4U}};
  // setup mocks
  EXPECT_CALL(*m_observer, notify_about_success(REQUEST_ID)).Times(1);

  m_sut->clear_dtc(REQUEST_ID, [&params](typename ClearDtcRequest::BorrowedType & request) {
    std::copy(params.begin(), params.end(), std::back_inserter(request.parameters));
  });
  wait_for_request();
  send_response(
    [](typename ClearDtcResponse::BorrowedType & response) {
      response.response_code = ClearDtcResponse::BorrowedType::ResponseCode::POSITIVE_ACK;
    },
    [&params](const typename ClearDtcRequest::BorrowedType & request) {
      for (size_t i = 0; i < request.parameters.size(); ++i) {
        EXPECT_EQ(request.parameters.at(i), params.at(i));
      }
    });
  // wait for a response and call execute
  execute_on_response_received();
}

TEST_F(AsyncClearDtcClientNodeFixture, shall_notify_about_error_on_clear_failure)
{
  constexpr uint64_t REQUEST_ID{1U};
  // setup mocks
  EXPECT_CALL(*m_observer,
              notify_about_error(
                REQUEST_ID, uds::services::shared::UdsResponseCode::GENERAL_PROGRAMMING_FAILURE))
    .Times(1);

  m_sut->clear_dtc(REQUEST_ID, [](auto &) {
    // empty
  });
  wait_for_request();
  send_response(
    [](ClearDtcResponse::BorrowedType & response) {
      response.response_code =
        ClearDtcResponse::BorrowedType::ResponseCode::GENERAL_PROGRAMMING_FAILURE;
    },
    [](const auto &) {
      // empty
    });
  // wait for a response and call execute
  execute_on_response_received();
}

TEST_F(AsyncClearDtcClientServiceNotAvailable, shall_notify_about_error_on_service_no_available)
{
  constexpr uint64_t REQUEST_ID{1U};
  EXPECT_CALL(
    *m_observer,
    notify_about_error(REQUEST_ID, uds::services::shared::UdsResponseCode::CONDITIONS_NOT_CORRECT))
    .Times(1);
  m_sut->clear_dtc(REQUEST_ID, [](auto &) {
    // empty
  });
}
