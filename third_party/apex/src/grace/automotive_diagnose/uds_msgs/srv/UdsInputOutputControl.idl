module uds_msgs
{
  enum ControlParameter
  {
    RETURN_CONTROL_TO_ECU,
    RESET_TO_DEFAULT,
    FREEZE_CURRENT_STATE,
    SHORT_TERM_ADJUSTMENT
  };

  enum InputOutputControlStatus
  {
    OK,
    ERROR,
    TIMEOUT
  };

  module srv
  {
    struct UdsInputOutputControl_Request
    {
      @verbatim (language="comment", text=
        " The target data identifier.")
      uint16 did;

      @verbatim (language="comment", text=
        " The type of control to set over the target did.")
      ControlParameter control_parameter;

      @verbatim (language="comment", text=
        " The Input Output Control payload.")
      sequence<uint8, 128> control_state;
    };

    struct UdsInputOutputControl_Response
    {
      @verbatim (language="comment", text=
        " Input Output Control response status.")
      InputOutputControlStatus status;

      @verbatim (language="comment", text=
        " The target data identifier.")
      uint16 did;

      @verbatim (language="comment", text=
        " The type of control to set over the target did.")
      ControlParameter control_parameter;

      @verbatim (language="comment", text=
        " The Input Output Control payload.")
      sequence<uint8, 128> control_state;
    };
  };
};
