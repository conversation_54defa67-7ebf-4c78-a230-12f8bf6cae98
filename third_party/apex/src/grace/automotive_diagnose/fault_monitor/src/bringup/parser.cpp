// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include "fault_monitor/bringup/parser.hpp"

#include <string>  // for std::string
#include <vector>  // for std::vector

#include "cpputils/safe_cast.hpp"  // for safe_cast
#include "diagnostic_common/config_error.hpp"  // for config_error
#include "settings/construct/getters.hpp"  // for get
#include "settings/inspect.hpp"
#include "settings/inspect/dictionary_view.hpp"  // for dictionary_view
#include "settings/inspect/node_view.hpp"  // for node_view
#include "settings/inspect/types.hpp"

namespace apex
{
namespace fault_monitor
{
namespace bringup
{
namespace parser
{
namespace
{
using config_error = diagnostic_common::config_error;
using settings::inspect::array_view;
using settings::inspect::get;
using settings::inspect::integer;
using settings::inspect::maybe;
using settings::inspect::node_view;
using settings::inspect::string_view;
}  // namespace


std::string parse_event_topic(const dictionary_view & cfg)
{
  const auto maybe_topic = get<maybe<string_view>>(cfg, "application/fault_monitors_topic");

  if (!maybe_topic) {
    throw config_error(
      "Error: Missing \"fault_monitors_topic\" value. It is required and expected as an string.");
  }

  return std::string{maybe_topic.value()};
}

std::string parse_command_topic(const dictionary_view & cfg)
{
  const auto maybe_topic = get<maybe<string_view>>(cfg, "application/diagnostic_command_topic");

  if (!maybe_topic) {
    throw config_error(
      "Error: Missing \"diagnostic_command_topic\" value. It is required and expected as an "
      "string.");
  }

  return std::string{maybe_topic.value()};
}

std::string parse_internal_topic(const dictionary_view & cfg)
{
  const auto maybe_topic = get<maybe<string_view>>(cfg, "application/internal_event_topic");

  if (!maybe_topic) {
    throw config_error(
      "Error: Missing \"internal_event_topic\" value. It is required for remote configuration, and "
      "expected as an string.");
  }

  return std::string{maybe_topic.value()};
}

std::string parse_monitor_type(const node_view & cfg)
{
  const auto maybe_type = get<maybe<string_view>>(cfg, "type");

  if (!maybe_type) {
    throw config_error(
      "Error: Missing \"type\" value. It is required in a fault monitor configuration, and "
      "expected as an string.");
  }

  return std::string{maybe_type.value()};
}

uint32_t parse_event_id(const node_view & cfg)
{
  const auto maybe_event_id = get<maybe<integer>>(cfg, "event_id");

  if (!maybe_event_id) {
    throw config_error(
      "Error: Missing \"event_id\" value. It's required in fault monitor configuration, and "
      "expected as an integer.");
  }

  try {
    return cast::safe_cast<uint32_t>(maybe_event_id.value());
  } catch (cast::cast_error &) {
    throw config_error("Error: Invalid \"event_id\" value:", maybe_event_id.value());
  }
}

std::vector<FaultMonitorsConfiguration> parse_fault_monitors_config(const dictionary_view & cfg)
{
  std::vector<FaultMonitorsConfiguration> monitors;

  const auto monitors_view = get<maybe<array_view>>(cfg, "application/fault_monitors");

  if (!monitors_view) {
    throw config_error("Error: Missing \"fault_monitors\" list.");
  }

  for (const auto & monitor_node_view : monitors_view.value()) {
    const auto event_id = parse_event_id(monitor_node_view);
    const auto type = parse_monitor_type(monitor_node_view);
    monitors.push_back({event_id, type, monitor_node_view});
  }

  return monitors;
}

}  // namespace parser
}  // namespace bringup
}  // namespace fault_monitor
}  // namespace apex
