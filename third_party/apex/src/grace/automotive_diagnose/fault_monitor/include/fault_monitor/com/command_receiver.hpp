/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief Receiver of the diagnostic commands topics.

#ifndef FAULT_MONITOR__COM__COMMAND_RECEIVER_HPP_
#define FAULT_MONITOR__COM__COMMAND_RECEIVER_HPP_

#include <memory>  // for std::shared_ptr

#include "bounded_vector/bounded_vector.hpp"  // for apex::BoundedVector
#include "diagnostic_common/node_context.hpp"  // for NodeContext
#include "executor2/executable_item.hpp"  // for executable_item
#include "fault_monitor/monitor/monitor_registry_base.hpp"  // for MonitorsRegistryPtr
#include "fault_monitor/visibility_control.hpp"
#include "logging/logging.hpp"  // for Logger, APEX_INFO
#include "string/string_strict.hpp"  // for string_strict256_t

#include "diagnostic_fault_manager_msgs/msg/dfm_commands.hpp"  // for DFMCommands

namespace apex
{
namespace fault_monitor
{
namespace com
{
using DFMCommandsMsg = diagnostic_fault_manager_msgs::msg::DFMCommands;
using EventIdsSeq = typename DFMCommandsMsg::BorrowedType::_event_ids_type;
using CommandType = DFMCommandsMsg::BorrowedType::CommandTypeEnum;
using CommandTarget = DFMCommandsMsg::BorrowedType::CommandTargetEnum;

/// \class CommandReceiver
/// \brief The receiver of the Diagnostics Commands
class FAULT_MONITOR_PUBLIC CommandReceiver : public executor::executable_item
{
public:
  /// \brief Constructs new object of the CommandReceiver.
  /// \param[in] node_context The context of the parent node.
  /// \param[in] subscribe_topic The command topic name.
  /// \param[in] monitors_registry The registry of the Fault Monitors.
  CommandReceiver(const diagnostic_common::NodeContextRef & node_context,
                  const string_strict256_t & subscribe_topic,
                  monitor::MonitorsRegistryPtr monitors_registry);

private:
  bool execute_impl() override;

  /// \brief Process the command on all of the Fault Monitors in the registry.
  /// \param[in] command The incoming command.
  void process_command(const CommandType command) const;

  /// \brief Process the command on the selected Fault Monitors from the registry.
  /// \param[in] command The incoming command.
  /// \param[in] ids The sequence of event_ids that the linked Fault Monitors from the registry
  /// will process the command.
  void process_command(const CommandType command, const EventIdsSeq & ids) const;

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_command_subscriber};
  }

private:
  /// \brief The subscriber of the DFMCommands.
  const rclcpp::PollingSubscription<DFMCommandsMsg>::SharedPtr m_command_subscriber;
  /// \brief The registry of the Fault Monitors.
  const monitor::MonitorsRegistryPtr m_registry;
  /// \brief The CommandReceiver logger object.
  logging::Logger<> m_logger;
};

using CommandReceiverPtr = std::shared_ptr<CommandReceiver>;

}  // namespace com
}  // namespace fault_monitor
}  // namespace apex

#endif  // FAULT_MONITOR__COM__COMMAND_RECEIVER_HPP_
