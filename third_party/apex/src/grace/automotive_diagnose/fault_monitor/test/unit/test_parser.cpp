// Copyright 2023 Apex.AI, Inc
// All rights reserved.

#include <string>
#include <vector>

#include "diagnostic_common/config_error.hpp"
#include "fault_monitor/bringup/parser.hpp"
#include "fixtures/diagnostic_test_fixture.hpp"
#include "gtest/gtest.h"  // for TEST_F, ASSERT_*, EXPECT_*
#include "settings/construct/types.hpp"
#include "settings/fault_monitor_settings.hpp"

namespace parser = apex::fault_monitor::bringup::parser;
namespace common = apex::diagnostic_common;
namespace settings = apex::settings::generated::fault_monitor;

using apex::settings::construct::array;
using apex::settings::construct::dictionary;
using apex::settings::construct::get;
using apex::settings::construct::make_array;
using apex::settings::construct::make_dictionary;
using apex::settings::construct::make_value;
using apex::settings::inspect::dictionary_view;

namespace
{
struct DiagFmParserFixture : common::testing::DiagnosticTestFixture
{
  DiagFmParserFixture()
  : m_settings{settings::create()},
    m_settings_view{m_settings},
    m_empty_settings{},
    m_empty_settings_view{m_empty_settings}
  {
  }

  const dictionary m_settings;
  const dictionary_view m_settings_view;
  const dictionary m_empty_settings;
  const dictionary_view m_empty_settings_view;
};
}  // namespace

TEST_F(DiagFmParserFixture, shall_return_event_topic)
{
  const auto event_topic = parser::parse_event_topic(m_settings_view);
  EXPECT_EQ(event_topic, "/event/status");
}

TEST_F(DiagFmParserFixture, shall_throw_on_invalid_event_topic)
{
  EXPECT_THROW(parser::parse_event_topic(m_empty_settings_view), common::config_error);
}

TEST_F(DiagFmParserFixture, shall_return_internal_event_topic)
{
  const auto event_topic = parser::parse_internal_topic(m_settings_view);
  EXPECT_EQ(event_topic, "/internal/event/status");
}

TEST_F(DiagFmParserFixture, shall_throw_on_internal_event_topic)
{
  EXPECT_THROW(parser::parse_internal_topic(m_empty_settings_view), common::config_error);
}

TEST_F(DiagFmParserFixture, shall_return_command_topic)
{
  const auto event_topic = parser::parse_command_topic(m_settings_view);
  EXPECT_EQ(event_topic, "/diag/cmd");
}

TEST_F(DiagFmParserFixture, shall_throw_on_invalid_command_topic)
{
  EXPECT_THROW(parser::parse_command_topic(m_empty_settings_view), common::config_error);
}

TEST_F(DiagFmParserFixture, shall_parse_fault_monitors_data)
{
  const auto fault_monitors_data = parser::parse_fault_monitors_config(m_settings_view);
  EXPECT_EQ(fault_monitors_data.size(), 2U);

  EXPECT_EQ(fault_monitors_data.at(0).event_id, 0U);
  EXPECT_EQ(fault_monitors_data.at(0).type, "counter");

  EXPECT_EQ(fault_monitors_data.at(1).event_id, 1U);
  EXPECT_EQ(fault_monitors_data.at(1).type, "remote");
}

TEST_F(DiagFmParserFixture, shall_throw_on_missing_monitors_list)
{
  EXPECT_THROW(parser::parse_fault_monitors_config(m_empty_settings_view), common::config_error);
}

TEST_F(DiagFmParserFixture, shall_throw_on_missing_event_id)
{
  dictionary root;
  auto app = make_dictionary();
  root["application"] = app;
  auto fms = make_array();
  get<dictionary>(app)["fault_monitors"] = fms;
  auto element = make_dictionary();
  get<dictionary>(element)["type"] = make_value("foo");
  get<dictionary>(element)["event_idd"] = make_value(12);
  get<array>(fms).push_back(element);

  EXPECT_THROW(parser::parse_fault_monitors_config(dictionary_view{root}), common::config_error);
}

TEST_F(DiagFmParserFixture, shall_throw_on_invalid_event_id)
{
  dictionary root;
  auto app = make_dictionary();
  root["application"] = app;
  auto fms = make_array();
  get<dictionary>(app)["fault_monitors"] = fms;
  auto element = make_dictionary();
  get<dictionary>(element)["type"] = make_value("foo");
  get<dictionary>(element)["event_id"] = make_value("string_val");
  get<array>(fms).push_back(element);

  EXPECT_THROW(parser::parse_fault_monitors_config(dictionary_view{root}), common::config_error);
}

TEST_F(DiagFmParserFixture, shall_throw_on_missing_type)
{
  dictionary root;
  auto app = make_dictionary();
  root["application"] = app;
  auto fms = make_array();
  get<dictionary>(app)["fault_monitors"] = fms;
  auto element = make_dictionary();
  get<dictionary>(element)["typo"] = make_value("foo");
  get<dictionary>(element)["event_id"] = make_value(12);
  get<array>(fms).push_back(element);

  EXPECT_THROW(parser::parse_fault_monitors_config(dictionary_view{root}), common::config_error);
}

TEST_F(DiagFmParserFixture, shall_throw_on_invalid_type_data)
{
  dictionary root;
  auto app = make_dictionary();
  root["application"] = app;
  auto fms = make_array();
  get<dictionary>(app)["fault_monitors"] = fms;
  auto element = make_dictionary();
  get<dictionary>(element)["type"] = make_value(12);
  get<dictionary>(element)["event_id"] = make_value(12);
  get<array>(fms).push_back(element);

  EXPECT_THROW(parser::parse_fault_monitors_config(dictionary_view{root}), common::config_error);
}
