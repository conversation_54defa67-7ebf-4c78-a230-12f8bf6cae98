// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include <memory>  // for std::shared_ptr
#include <unordered_map>  // for std::unordered_map
#include <utility>  // for std::move

#include "fault_monitor/monitor/monitor_registry.hpp"  // for MonitorsRegistry
#include "fixtures/diagnostic_test_fixture.hpp"  // for DiagnosticTestFixture
#include "mocks/mock_fault_monitor.hpp"  // for MockFaultMonitorBase
#include "shared/utils.hpp"  // for gen_monitors()

#include "diagnostic_fault_manager_msgs/msg/dfm_commands.hpp"  // for DFMCommands

namespace fm = apex::fault_monitor;
namespace common = apex::diagnostic_common;

namespace
{
using MonitorsMap = fm::monitor::MonitorsMap;
using DFMCommandsMsg = diagnostic_fault_manager_msgs::msg::DFMCommands;

constexpr uint8_t MONITORS_SIZE = 3U;

struct MonitorRegistryFixture : common::testing::DiagnosticTestFixture
{
  MonitorRegistryFixture()
  : m_monitors{shared::gen_monitors(MONITORS_SIZE)},
    m_sut{std::make_shared<fm::monitor::MonitorsRegistry>(MonitorsMap(m_monitors))}
  {
  }

  const MonitorsMap m_monitors;
  fm::monitor::MonitorsRegistryPtr m_sut;
};
}  // namespace

TEST_F(MonitorRegistryFixture, shall_find_all_monitors)
{
  for (auto id = 0U; id < MONITORS_SIZE; ++id) {
    auto monitor = m_sut->find_monitor(id);
    ASSERT_EQ(m_monitors.at(id), monitor);
  }
}

TEST_F(MonitorRegistryFixture, incorrect_id_provided)
{
  const auto monitor = m_sut->find_monitor(MONITORS_SIZE + 1U);
  ASSERT_EQ(nullptr, monitor);
}

TEST_F(MonitorRegistryFixture, shall_return_correct_registry_boundary)
{
  // make sure that the given boundaries enables iterating and
  // provides the same results.
  size_t registry_size{0};
  auto monitors_it = m_monitors.cbegin();

  for (auto it = m_sut->begin(); it != m_sut->end(); ++it, ++registry_size, ++monitors_it) {
    EXPECT_EQ(it->second, monitors_it->second);
    EXPECT_EQ(it->first, monitors_it->first);
  }

  EXPECT_EQ(registry_size, m_monitors.size());
}
