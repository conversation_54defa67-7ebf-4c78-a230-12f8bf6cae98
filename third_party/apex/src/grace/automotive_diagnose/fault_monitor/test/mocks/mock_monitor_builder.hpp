/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The MonitorBuilderBase Mock.

#ifndef FAULT_MONITOR__TEST__MOCKS__MOCK_MONITOR_BUILDER_HPP_
#define FAULT_MONITOR__TEST__MOCKS__MOCK_MONITOR_BUILDER_HPP_

#include "diagnostic_common/node_context.hpp"  // for NodeContextRef
#include "fault_monitor/com/fault_event_publisher_base.hpp"  // for FaultEventPublisherBasePtr
#include "fault_monitor/monitor/fault_monitor_base.hpp"  // for FaultMonitorBasePtr
#include "fault_monitor/monitor/monitor_builder_base.hpp"  // for MonitorBuilderBase
#include "gmock/gmock.h"

namespace apex
{
namespace fault_monitor
{
namespace monitor
{

class MockMonitorBuilder : public MonitorBuilderBase
{
public:
  MockMonitorBuilder(const diagnostic_common::NodeContextRef & context,
                     const executor::executor_ptr & executor)
  : MonitorBuilderBase(context, executor)
  {
  }

  MOCK_METHOD3(build,
               FaultMonitorBasePtr(const EventId,
                                   const com::FaultEventPublisherBasePtr,
                                   const settings::inspect::node_view &));
};

}  // namespace monitor
}  // namespace fault_monitor
}  // namespace apex

#endif  // FAULT_MONITOR__TEST__MOCKS__MOCK_MONITOR_BUILDER_HPP_
