/// \copyright Copyright 2023 Apex.AI, Inc
/// All rights reserved.
/// \file
/// \brief The MonitorRegistryBase Mock.

#ifndef FAULT_MONITOR__TEST__MOCKS__MOCK_MONITORS_REGISTRY_BASE_HPP_
#define FAULT_MONITOR__TEST__MOCKS__MOCK_MONITORS_REGISTRY_BASE_HPP_

#include <memory>  // for std::shared_ptr

#include "fault_monitor/monitor/fault_monitor.hpp"  // for FaultMonitor
#include "fault_monitor/monitor/fault_monitor_base.hpp"  // for FaultMonitorBasePtr
#include "fault_monitor/monitor/monitor_registry_base.hpp"  // for MonitorsRegistryBase
#include "gmock/gmock.h"

namespace apex
{
namespace fault_monitor
{
namespace monitor
{

class MockMonitorRegistry : public MonitorsRegistryBase
{
public:
  MOCK_CONST_METHOD1(find_monitor, std::shared_ptr<FaultMonitor>(const EventId));
  MOCK_CONST_METHOD0(begin, MonitorsMap::const_iterator());
  MOCK_CONST_METHOD0(end, MonitorsMap::const_iterator());
};

}  // namespace monitor
}  // namespace fault_monitor
}  // namespace apex

#endif  // FAULT_MONITOR__TEST__MOCKS__MOCK_MONITORS_REGISTRY_BASE_HPP_
