# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

load("@apex//grace/thirdparty/assimp_vendor:defs.bzl", "LOCAL_ASSIMP_CONFIG")
load("@apex//tools/bazel/rules_repo:defs.bzl", "empty_repository", "local_library_workspace")
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

def load_assimp_repositories():
    # load locally if its enabled via cli
    # bazel build --@assimp//:local @assimp//...
    # install with: `sudo apt-get install -y libassimp-dev`
    if LOCAL_ASSIMP_CONFIG.setup_local_assimp:
        maybe(
            name = "local_assimp",
            repo_rule = local_library_workspace,
            paths_patterns = {
                "include": LOCAL_ASSIMP_CONFIG.include_pattern,
                "lib": LOCAL_ASSIMP_CONFIG.lib_pattern,
            },
            build_file = LOCAL_ASSIMP_CONFIG.build_file,
        )

    maybe(
        name = "assimp",
        repo_rule = empty_repository,
        build_file = "@apex//grace/thirdparty/assimp_vendor:assimp.BUILD",
    )
