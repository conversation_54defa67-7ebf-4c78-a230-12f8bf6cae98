# Copyright 2025 Apex.AI, Inc.
# All rights reserved.

"""
Set up @rules_ros as external repository, giving access to the open source ROS2 way
to depend on third-party software. It uses an index file to declare dependencies
(see :apex.repos). After updating this index, please call

    bazel run @rules_ros//repos/config:repos_lock.update -- --tar

"""

exports_files([
    "apex.repos",
    "setup_ros_repositories.lock.bzl",
])

filegroup(
    name = "qt_gui_cpp_tests",
    srcs = [":ros-visualization/qt_gui_core/test_imports_shiboken.py"],
    visibility = ["@ros-visualization.qt_gui_core//qt_gui_cpp:__pkg__"],
)
