# Copyright 2024 Apex.AI, Inc.
# All rights reserved.
load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")

apex_cc_library(
    name = "urdfdom_model",
    srcs = [
        "src/joint.cpp",
        "src/link.cpp",
        "src/model.cpp",
        "src/pose.cpp",
        "src/pose.hpp",
    ],
    hdrs = glob(["include/urdf_model/**"]) + [
        "include/urdf_parser/exportdecl.h",
        "include/urdf_parser/urdf_parser.h",
    ],
    includes = ["include/urdf_parser/"],
    strip_include_prefix = "include",
    visibility = ["//visibility:public"],
    deps = [
        "@console_bridge",
        "@ros.urdfdom_headers//:urdf_exception",
        "@ros.urdfdom_headers//:urdf_model",
        "@ros.urdfdom_headers//:urdf_model_state",
        "@ros.urdfdom_headers//:urdf_sensor",
        "@ros.urdfdom_headers//:urdf_world",
        "@tinyxml2",
    ],
)

apex_cc_library(
    name = "urdfdom_world",
    srcs = ["src/world.cpp"],
    visibility = ["//visibility:public"],
    deps = [":urdfdom_model"],
)

apex_cc_library(
    name = "urdfdom_sensor",
    srcs = ["src/urdf_sensor.cpp"],
    visibility = ["//visibility:public"],
    deps = [
        ":urdfdom_model",
    ],
)

apex_cc_library(
    name = "urdfdom_model_state",
    srcs = [
        "src/twist.cpp",
        "src/urdf_model_state.cpp",
    ],
    visibility = ["//visibility:public"],
)
