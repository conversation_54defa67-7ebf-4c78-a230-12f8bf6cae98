load("@bazel_skylib//rules:common_settings.bzl", "bool_flag")

# bool flag to enable:
# bazel build --@opencv//:local @opencv//...
bool_flag(
    name = "local",
    build_setting_default = False,
    visibility = ["//visibility:public"],
)

config_setting(
    name = "use_local",
    flag_values = {":local": "true"},
    visibility = ["//visibility:public"],
)

cc_library(
    name = "empty",
    target_compatible_with = ["@platforms//:incompatible"],
)

alias(
    name = "core",
    actual = select({
        "use_local": "@local_opencv//:core",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "calib3d",
    actual = select({
        "use_local": "@local_opencv//:calib3d",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "dnn",
    actual = select({
        "use_local": "@local_opencv//:dnn",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "features2d",
    actual = select({
        "use_local": "@local_opencv//:features2d",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "flann",
    actual = select({
        "use_local": "@local_opencv//:flann",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "highgui",
    actual = select({
        "use_local": "@local_opencv//:highgui",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "imgcodecs",
    actual = select({
        "use_local": "@local_opencv//:imgcodecs",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "imgproc",
    actual = select({
        "use_local": "@local_opencv//:imgproc",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "objdetect",
    actual = select({
        "use_local": "@local_opencv//:objdetect",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "videoio",
    actual = select({
        "use_local": "@local_opencv//:videoio",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "opencv",
    actual = select({
        "use_local": "@local_opencv//:opencv",
        "//conditions:default": ":empty",
    }),
    visibility = ["//visibility:public"],
)
