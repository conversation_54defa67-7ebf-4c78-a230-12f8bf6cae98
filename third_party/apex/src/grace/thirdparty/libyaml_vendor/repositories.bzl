load("@apex//grace/thirdparty/libyaml_vendor:defs.bzl", "LIBYAML_VERSION")
load("@apex//tools/bazel/rules_repo:defs.bzl", "empty_repository")
load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

def load_libyaml_repositories():
    maybe(
        name = "libyaml-src",
        repo_rule = http_archive,
        urls = [
            "https://github.com/yaml/libyaml/releases/download/{version}/yaml-{version}.zip".format(version = LIBYAML_VERSION),
            "https://github.com/ApexAI/libyaml/releases/download/{version}/yaml-{version}.zip".format(version = LIBYAML_VERSION),
            "https://artifactory.ops.apex.ai/artifactory/misc-depend/libyaml/yaml-{version}.zip".format(version = LIBYAML_VERSION),
        ],
        sha256 = "45ec4bc54856a45e9815c897f8f7236c541b7673e18d49504335ece464aa02cc",
        strip_prefix = "yaml-{version}".format(version = LIBYAML_VERSION),
        build_file = "@apex//grace/thirdparty/libyaml_vendor:libyaml_src.BUILD",
    )

    maybe(
        name = "libyaml-yocto-kirkstone-aarch64",
        repo_rule = empty_repository,
        build_file = "@apex//grace/thirdparty/libyaml_vendor:libyaml_yocto.BUILD",
        repo_mapping = {"@yocto_sysroot": "@cc_toolchain_linux-ubuntu_jammy-gcc11_yocto-x86_64_linux-poky_kirkstone-aarch64_sysroot"},
    )
    maybe(
        name = "libyaml-yocto-dunfell-aarch64",
        repo_rule = empty_repository,
        build_file = "@apex//grace/thirdparty/libyaml_vendor:libyaml_yocto.BUILD",
        repo_mapping = {"@yocto_sysroot": "@cc_toolchain_linux-ubuntu_focal-gcc9_yocto-x86_64_linux-poky_dunfell-aarch64_sysroot"},
    )
    maybe(
        name = "libyaml",
        repo_rule = empty_repository,
        build_file = "@apex//grace/thirdparty/libyaml_vendor:libyaml.BUILD",
    )
