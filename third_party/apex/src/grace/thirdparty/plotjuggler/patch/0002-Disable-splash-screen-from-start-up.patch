From 33013e780b7454d25abd2cdb3f7ec0a943bbb8c5 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 20 Jan 2023 17:54:34 +0100
Subject: [PATCH] Disable splash screen from start-up

---
 plotjuggler_app/main.cpp | 5 ++++-
 1 file changed, 4 insertions(+), 1 deletion(-)

diff --git a/plotjuggler_app/main.cpp b/plotjuggler_app/main.cpp
index 0e183759..0788722b 100644
--- a/plotjuggler_app/main.cpp
+++ b/plotjuggler_app/main.cpp
@@ -1,4 +1,7 @@
 /*
+ *
+ * This file is a modification of <PERSON><PERSON><PERSON><PERSON><PERSON> [https://github.com/facontidavide/PlotJuggler] released under MPL 2.0
+ *
  * This Source Code Form is subject to the terms of the Mozilla Public
  * License, v. 2.0. If a copy of the MPL was not distributed with this
  * file, You can obtain one at https://mozilla.org/MPL/2.0/.
@@ -376,7 +379,7 @@ int main(int argc, char* argv[])
 
   if (!parser.isSet(nosplash_option) &&
       !(parser.isSet(loadfile_option) || parser.isSet(layout_option)))
-  // if(false) // if you uncomment this line, a kitten will die somewhere in the world.
+  if(false) // if you uncomment this line, a kitten will die somewhere in the world.
   {
     QPixmap main_pixmap;
 
-- 
2.38.1

