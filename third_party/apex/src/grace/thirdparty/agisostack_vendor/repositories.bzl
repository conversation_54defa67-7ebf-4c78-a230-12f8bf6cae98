load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository")
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

_AGISOSTACK_MAJOR_VERSION = "0"
_AGISOSTACK_MINOR_VERSION = "3"
_AGISOSTACK_PATCH_VERSION = "0"
AGISOSTACK_VERSION = "{}.{}.{}".format(_AGISOSTACK_MAJOR_VERSION, _AGISOSTACK_MINOR_VERSION, _AGISOSTACK_PATCH_VERSION)

_AGISOSTACK_COMMIT_HASH = "d044d4ae2fb52bc7e006fb0fce51b3cce8711a88"
_AGISOSTACK_URL = "https://github.com/Open-Agriculture/AgIsoStack-plus-plus.git"

def load_agisostack_repositories():
    maybe(
        name = "agisostack",
        repo_rule = git_repository,
        remote = _AGISOSTACK_URL,
        commit = _AGISOSTACK_COMMIT_HASH,
        build_file = "@apex//grace/thirdparty/agisostack_vendor:agisostack.BUILD",
        patches = [
            "@apex//grace/thirdparty/agisostack_vendor:examples.patch",
        ],
        patch_args = ["-p1", "-t"],
    )
