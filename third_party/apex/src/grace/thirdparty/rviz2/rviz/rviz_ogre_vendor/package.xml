<?xml version="1.0"?>
<?xml-model
  href="http://download.ros.org/schema/package_format2.xsd"
  schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>rviz_ogre_vendor</name>
  <version>6.1.5</version>
  <description>
    Wrapper around ogre3d, it provides a fixed CMake module and an ExternalProject build of ogre.
  </description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>  <!-- the contents of this package are Apache 2.0 -->
  <license>MIT</license>  <!-- ogre is MIT (https://www.ogre3d.org/licensing) -->

  <url type="website">https://www.ogre3d.org/</url>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>pkg-config</build_depend>

  <build_depend>libfreetype6-dev</build_depend>
  <build_export_depend>libfreetype6-dev</build_export_depend>
  <exec_depend>libfreetype6</exec_depend>

  <depend>libx11-dev</depend>
  <depend>libxaw</depend>
  <depend>libxrandr</depend>
  <depend>opengl</depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
