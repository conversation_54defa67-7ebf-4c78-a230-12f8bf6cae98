{"folders": [{"path": "."}, {"path": "/Users/<USER>/rviz2_ws/build"}, {"path": "/Users/<USER>/rviz2_ws/install"}], "settings": {"sublimeclang_options": ["-I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1", "-I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../lib/clang/8.0.0/include", "-I/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.11.sdk/usr/include", "-I/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.11.sdk/System/Library/Frameworks", "-I/usr/local/Cellar/qt/5.9.1/include", "-I/usr/local/Cellar/qt/5.9.1/Frameworks/QtCore.framework/Versions/Current/Headers", "-I/usr/local/Cellar/qt/5.9.1/Frameworks/QtGui.framework/Versions/Current/Headers", "-I/usr/local/Cellar/qt/5.9.1/Frameworks/QtWidgets.framework/Versions/Current/Headers", "-I/Users/<USER>/ros2_ws/install/include", "-I/Users/<USER>/rviz2_ws/install/opt/rviz_ogre_vendor/include", "-I/Users/<USER>/rviz2_ws/install/opt/rviz_ogre_vendor/include/OGRE", "-I/Users/<USER>/rviz2_ws/install/opt/rviz_ogre_vendor/include/OGRE/Overlay", "-I${folder:${project_path:rviz.sublime-project}}/rviz_rendering/include", "-I${folder:${project_path:rviz.sublime-project}}/rviz_common/include"]}}