alias(
    name = "s2n_tls",
    actual = select({
        "@apex//common/platforms/apex_detail:yocto-dunfell-aarch64": "@s2n_tls-yocto-dunfell-aarch64//:s2n_tls",
        "@apex//common/platforms/apex_detail:yocto-kirkstone-aarch64": "@s2n_tls-yocto-kirkstone-aarch64//:s2n_tls",
        "//conditions:default": "@s2n_tls-src//:s2n_tls",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "s2n_tls_pkg",
    actual = select({
        "@apex//common/platforms/apex_detail:yocto-dunfell-aarch64": "@s2n_tls-yocto-dunfell-aarch64//:s2n_tls_pkg",
        "@apex//common/platforms/apex_detail:yocto-kirkstone-aarch64": "@s2n_tls-yocto-kirkstone-aarch64//:s2n_tls_pkg",
        "//conditions:default": "@s2n_tls-src//:s2n_tls_pkg",
    }),
    visibility = ["//visibility:public"],
)
