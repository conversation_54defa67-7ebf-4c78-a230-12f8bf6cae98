cmake_minimum_required(VERSION 3.5)
set(AWS_CRT_CPP_VERSION "0.26.6")
project(aws_crt_cpp_vendor VERSION ${AWS_CRT_CPP_VERSION})

find_package(ament_cmake_auto REQUIRED)
find_package(apex_cmake REQUIRED)


# Skip the package since this is part of the yocto-sdk
if(CMAKE_POKY_LINUX)
  message(WARNING "${PROJECT_NAME} is skipped because the libs are part of the yocto-sdk")
  ament_package()
  return()
endif()

if(NOT DEFINED ENV{ENABLE_PROTOBUF})
  message(WARNING "ENABLE_PROTOBUF is not set. Skipping ${PROJECT_NAME}")
  ament_package()
  return()
endif()

include(FetchContent)

# do not treat warning as a errors
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-error -Wno-unused-parameter")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-error -Wno-unused-parameter -Wno-missing-field-initializers")

# use system openssl instead of aws-cl, otherwise openssl symbols will conflict with aws-cl and cause segfaults
# see details here: https://github.com/awslabs/aws-lc/blob/v0.0.2/INCORPORATING.md#symbols
set(USE_OPENSSL ON CACHE INTERNAL "")

# disable testing for thirdparty library
set(BUILD_TESTING OFF)

# workaround for
# https://github.com/awslabs/aws-checksums/issues/8#issuecomment-440027793
if(CMAKE_BUILD_TYPE STREQUAL "Coverage")
  set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -DDEBUG_BUILD")
endif()

FetchContent_Declare(aws-crt-cpp
  GIT_REPOSITORY https://github.com/awslabs/aws-crt-cpp.git
  GIT_TAG        "v${AWS_CRT_CPP_VERSION}"
  SOURCE_DIR    "${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source"
  UPDATE_COMMAND ""
  PATCH_COMMAND
                git submodule update --init --recursive &&
                git submodule foreach --recursive git clean -xfd &&
                git reset HEAD --hard &&
                git submodule foreach --recursive git reset --hard &&
                cd ${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source/ &&
                git apply ${CMAKE_CURRENT_SOURCE_DIR}/patch/Add_socket_options_api_to_Mqtt5ClientBuilder.patch &&
                cd ${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source/crt/aws-c-common &&
                git apply ${CMAKE_CURRENT_SOURCE_DIR}/patch/aws_c_common_qnx_support.patch &&
                cd ${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source/crt/s2n &&
                git apply ${CMAKE_CURRENT_SOURCE_DIR}/patch/s2n_tls_qnx_support.patch &&
                cd ${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source/crt/aws-c-cal &&
                git apply ${CMAKE_CURRENT_SOURCE_DIR}/patch/aws_c_cal_qnx_support.patch &&
                cd ${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source/crt/aws-c-io &&
                git apply ${CMAKE_CURRENT_SOURCE_DIR}/patch/aws_c_io_qnx_support.patch &&
                git apply ${CMAKE_CURRENT_SOURCE_DIR}/patch/qnx_event_loop.patch &&
                # Remove detected vulnerabilities #32586
                ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source/crt/aws-lc
                ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source/crt/s2n/bindings/rust
                ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_BINARY_DIR}/aws-crt-cpp-source/.git/modules/crt/aws-lc
)

FetchContent_MakeAvailable(aws-crt-cpp)

ament_export_libraries(
  aws-crt-cpp
  aws-c-http
  aws-c-mqtt
  aws-c-cal
  aws-c-auth
  aws-c-common
  aws-c-compression
  aws-c-io
  aws-checksums
  aws-c-event-stream
  aws-c-s3
  aws-c-sdkutils
  s2n
  crypto
)
ament_export_include_directories(${CMAKE_INSTALL_PREFIX}/include)

ament_auto_package()
