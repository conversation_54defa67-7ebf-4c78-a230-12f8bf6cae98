alias(
    name = "aws_c_io",
    actual = select({
        "@apex//common/platforms/apex_detail:yocto-dunfell-aarch64": "@aws_c_io-yocto-dunfell-aarch64//:aws_c_io",
        "@apex//common/platforms/apex_detail:yocto-kirkstone-aarch64": "@aws_c_io-yocto-kirkstone-aarch64//:aws_c_io",
        "//conditions:default": "@aws_c_io-src//:aws_c_io",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "aws_c_io_pkg",
    actual = select({
        "@apex//common/platforms/apex_detail:yocto-dunfell-aarch64": "@aws_c_io-yocto-dunfell-aarch64//:aws_c_io_pkg",
        "@apex//common/platforms/apex_detail:yocto-kirkstone-aarch64": "@aws_c_io-yocto-kirkstone-aarch64//:aws_c_io_pkg",
        "//conditions:default": "@aws_c_io-src//:aws_c_io_pkg",
    }),
    visibility = ["//visibility:public"],
)
