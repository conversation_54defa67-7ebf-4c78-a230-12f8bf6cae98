<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>test_rosidl_generator_py</name>
  <version>0.1.0</version>
  <description>Tests for rosidl_generator_py.</description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apex.AI</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>apex_cmake</buildtool_depend>
  <buildtool_depend>python_cmake_module</buildtool_depend>
  <buildtool_depend>rosidl_default_generators</buildtool_depend>

  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>rclpy</test_depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
