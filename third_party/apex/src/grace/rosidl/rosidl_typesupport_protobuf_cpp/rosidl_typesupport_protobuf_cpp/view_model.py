# Copyright (c) 2024 Apex.AI, Inc.
# All rights reserved.

from __future__ import annotations

from pydantic import BaseModel
from typing import List, Optional


from rosidl_parser_ng.generator.includes_view import IncludesView  # noqa: TCH002
from rosidl_parser_ng.model import CTypeName  # noqa: TCH002


class ServiceView(BaseModel):
    name: CTypeName
    namespaces: List[str]
    ros_name_full: str
    message_namespace_name: str


class MemberView(BaseModel):
    name: str
    convert_to_proto_block: str
    convert_to_ros_block_nonflat: str
    convert_to_ros_block_flat: str


class UnionMemberView(MemberView):
    cases_block_ros: str
    cases_block_proto: str
    is_default_member: bool


class StructView(BaseModel):
    name: CTypeName
    namespaces: List[str]
    proto_name_full: str
    ros_name_full: str
    message_namespace_name: str
    members: List[MemberView]
    sort_order: int


class UnionView(StructView):
    discriminator_to_proto: str
    discriminator_to_ros_flat: str
    discriminator_to_ros_nonflat: str
    default_member: Optional[UnionMemberView] = None


class ProtobufCppModel(BaseModel):
    structs: List[StructView]
    unions: List[UnionView]
    services: List[ServiceView]


class GeneratorProtobufCpp(BaseModel):
    idl: ProtobufCppModel
    package_name: str
    filename_prefix: str
    cpp_includes: IncludesView
    hpp_includes: IncludesView
