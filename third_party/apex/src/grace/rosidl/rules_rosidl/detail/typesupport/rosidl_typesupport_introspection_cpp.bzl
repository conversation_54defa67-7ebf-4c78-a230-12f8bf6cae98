load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//common/bazel/helpers:snake_case.bzl", "to_snake_case_with_exceptions")
load("@apex//common/bazel/rules_cc:defs.bzl", "cc_library_with_hdrs_extracted_from_srcs")
load("@apex//ida/bazel:detail/providers.bzl", "IdaIdlInfo", "TypeModel", "create_ida_idl_info", "get_real_short_path")
load("@apex//tools/bazel/rules_repo:defs.bzl", "sub_target_name")
load("@bazel_skylib//lib:paths.bzl", "paths")
load(":detail/common_config.bzl", "create_common_config")
load(":detail/common_config.bzl", "create_common_config_with_models")
load(":providers.bzl", "MsgsInfo")

_generator_name = "rosidl_typesupport_introspection_cpp"

def _rosidl_typesupport_introspection_cpp_impl(ctx):
    def output_files_create(path):
        basename = paths.basename(path)
        dirname = paths.dirname(path)
        snake_case_name = to_snake_case_with_exceptions(basename)
        return [
            paths.join(dirname, "detail", snake_case_name + "__rosidl_typesupport_introspection_cpp.hpp"),
            paths.join(dirname, "detail", snake_case_name + "__rosidl_typesupport_introspection_cpp.cpp"),
        ]

    providers = []
    if len(ctx.attr.srcs) == 1 and TypeModel in ctx.attr.srcs[0]:
        if len(ctx.attr.srcs) != 1 or TypeModel not in ctx.attr.srcs[0]:
            fail("Nothing to generate.")

        def output_files_create_from_typemodel(ctx, _, tm):
            output_map = {}
            for dsl_file in tm.dsl_files:
                dsl_path = get_real_short_path(dsl_file)
                dsl_short_path = dsl_path

                if ctx.label.package and dsl_short_path.startswith(ctx.label.package):
                    dsl_short_path = dsl_short_path.removeprefix(ctx.label.package + "/")
                if ctx.attr.pkg_name and dsl_short_path.startswith(ctx.attr.pkg_name):
                    dsl_short_path = dsl_short_path.removeprefix(ctx.attr.pkg_name + "/")

                snake_case_name = to_snake_case_with_exceptions(paths.basename(dsl_short_path))
                base_name = paths.dirname(paths.replace_extension(dsl_short_path, ""))
                file_path = paths.join(base_name, snake_case_name)

                declared_files_for_dsl_file = []
                for output_file_name in output_files_create(file_path):
                    declared_files_for_dsl_file.append(ctx.actions.declare_file(output_file_name))

                output_map[dsl_path] = declared_files_for_dsl_file

            return output_map

        workspace_root = ctx.label.workspace_root
        if workspace_root:
            genfile_prefix = paths.join(ctx.genfiles_dir.path, workspace_root) + "/"
        else:
            genfile_prefix = ctx.genfiles_dir.path + "/"
        cfg = create_common_config_with_models(
            ctx,
            output_file_create_func = output_files_create_from_typemodel,
            generator_name = _generator_name,
            genfile_prefix = genfile_prefix,
        )
        providers.append(create_ida_idl_info(
            workspace = ctx.label.workspace_root,
            generator_name = _generator_name,
            own_mapping = cfg.gen_file_mapping,
            all_deps = ctx.attr.msg_deps,
        ))

    else:
        cfg = create_common_config(
            ctx,
            output_file_create_func = output_files_create,
            generator_name = _generator_name,
        )
        cfg = struct(
            idl_deps = cfg.idl_deps,
            json_file = cfg.json_file,
            outputs = cfg.outputs,
            inputs = ctx.files.srcs + [cfg.json_file] + cfg.idl_deps + cfg.inputs,
        )
        providers.append(IdaIdlInfo())  # Needed for consistency between build config

    # run generator
    args = ctx.actions.args()
    args.add("--generator-arguments-file", cfg.json_file.path)
    ctx.actions.run(
        executable = ctx.executable._generator,
        arguments = [args],
        inputs = cfg.inputs,
        outputs = cfg.outputs,
        progress_message = "Generating %{label}",
        mnemonic = "GenerateIntrospectionTypesupportCpp",
    )

    return [
        DefaultInfo(
            files = depset(direct = cfg.outputs),
        ),
        RuleMetaInfo(),
    ] + providers

_rosidl_typesupport_introspection_cpp = rule(
    implementation = _rosidl_typesupport_introspection_cpp_impl,
    attrs = {
        "srcs": attr.label_list(),
        "target_name": attr.string(),
        "pkg_name": attr.string(),
        "msg_deps": attr.label_list(
            doc = "Message dependencies",
            providers = [[MsgsInfo], [IdaIdlInfo], []],
            default = [],
        ),
        "_generator": attr.label(
            default = "@apex//grace/rosidl/rosidl_typesupport_introspection_cpp:generator",
            executable = True,
            cfg = "exec",
            doc = "A generator used to actually generate",
        ),
    },
)

def cc_rosidl_typesupport_introspection_cpp_library(
        *,
        name,
        target_name,
        pkg_name,
        srcs,
        deps,
        msg_deps,
        target_compatible_with = [],
        testonly = False,
        **kwargs):
    _rosidl_typesupport_introspection_cpp(
        name = sub_target_name("files", name),
        target_name = target_name,
        pkg_name = pkg_name,
        srcs = srcs,
        msg_deps = msg_deps,
        tags = kwargs.get("tags", default = []) or [],
        testonly = testonly,
    )
    cc_library_with_hdrs_extracted_from_srcs(
        name = name,
        srcs = [sub_target_name("files", name)],
        deps = deps,
        strip_include_prefix = select({
            "@apex//grace/rmw_implementation:use_ida": "",
            "//conditions:default": _generator_name,
        }),
        testonly = testonly,
        **kwargs
    )
