load("@apex//common/bazel/aspects/dependencies:locator.bzl", "nested_locator_for_label")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "DynamicLinkingHint")
load("@apex//common/bazel/rules_cc:providers.bzl", "FusedCcLibraryInfo")
load(":providers.bzl", "RosidlProtobufCppInfo")

def _cpp_msgs_protobuf_library_impl(ctx):
    target = ctx.attr.msgs[RosidlProtobufCppInfo].rosidl_typesupport_protobuf_cpp
    rm_info = RuleMetaInfo(
        deps = [nested_locator_for_label(ctx.attr, "msgs", RosidlProtobufCppInfo, "rosidl_typesupport_protobuf_cpp")],
        dynamic_linking_hint = DynamicLinkingHint(),
    )
    return [target[CcInfo], target[FusedCcLibraryInfo], rm_info]

_cpp_msgs_protobuf_library = rule(
    implementation = _cpp_msgs_protobuf_library_impl,
    attrs = {
        "msgs": attr.label(providers = [RosidlProtobufCppInfo]),
        "_incompatible_with_qnx": attr.label(
            default = "@apex//grace/rosidl/rules_rosidl/compatibility:cc_incompatible_with_qnx",
        ),
        "_incompatible_with_asil": attr.label(
            default = "@apex//common/asil:only_qm",
        ),
    },
    provides = [CcInfo, FusedCcLibraryInfo, RuleMetaInfo],
)

def cpp_msgs_protobuf_library(*, msgs, tags = None, **kwargs):
    """Extract CPP Protobuf language bindings from a msgs_library.

    Produces a cc_library.

    Arguments:
        name: A unique name for this target
        msgs: A msgs_library target
        tags: see [Bazel common attributes](https://bazel.build/reference/be/common-definitions#common.tags)
        **kwargs: [Additional common attributes](https://bazel.build/reference/be/common-definitions)
    """
    tags = (tags or []) + ["same-ros-pkg-as: {}".format(msgs)]
    _cpp_msgs_protobuf_library(
        msgs = msgs,
        tags = tags,
        **kwargs
    )
