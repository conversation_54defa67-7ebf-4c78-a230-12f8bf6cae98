load("@apex//common/bazel/aspects/dependencies:locator.bzl", "nested_locator_for_label")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "InstallSpaceHint")
load("@rules_python//python:py_info.bzl", "PyInfo")
load(":providers.bzl", "RosidlPyInfo")

def _py_msgs_library_impl(ctx):
    if not ctx.attr.msgs:
        return [
            PyInfo(transitive_sources = depset()),
            RuleMetaInfo(install_space_hint = InstallSpaceHint(py_need_no_wheel = True)),
            DefaultInfo(),
        ]
    target = ctx.attr.msgs[RosidlPyInfo].rosidl_generator_py
    rm_info = RuleMetaInfo(
        deps = [nested_locator_for_label(ctx.attr, "msgs", RosidlPyInfo, "rosidl_generator_py")],
        install_space_hint = InstallSpaceHint(py_need_no_wheel = True),
    )
    return [target[PyInfo], target[DefaultInfo], rm_info]

_py_msgs_library = rule(
    implementation = _py_msgs_library_impl,
    attrs = {
        "msgs": attr.label(providers = [RosidlPyInfo]),
        "_incompatible_with_qnx": attr.label(
            default = "@apex//grace/rosidl/rules_rosidl/compatibility:py_incompatible_with_qnx",
        ),
        "_incompatible_with_asil": attr.label(
            default = "@apex//common/asil:only_qm",
        ),
        "_incompatible_with_no_py_bindings": attr.label(
            default = "@apex//grace/rosidl/rules_rosidl/compatibility:py_incompatible_with_no_py_bindings",
        ),
    },
    provides = [PyInfo, DefaultInfo, RuleMetaInfo],
)

def py_msgs_library(*, msgs, tags = None, **kwargs):
    """Extract Python language bindings from a msgs_library.

    Produces a py_library.

    Arguments:
        name: A unique name for this target
        msgs: A msgs_library target
        tags: see [Bazel common attributes](https://bazel.build/reference/be/common-definitions#common.tags)
        **kwargs: [Additional common attributes](https://bazel.build/reference/be/common-definitions)
    """

    # Associate this target with the same ros_pkg as the primary msgs library
    tags = (tags or []) + ["same-ros-pkg-as: {}".format(msgs)]
    _py_msgs_library(
        msgs = select({
            "@apex//grace/rosidl/rules_rosidl:no_py_bindings": None,
            "//conditions:default": msgs,
        }),
        tags = tags,
        **kwargs
    )
