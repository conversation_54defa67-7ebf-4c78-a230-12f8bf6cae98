# rosidl_typesupport_interface

`rosidl_typesupport_interface` is a package that provides macros that define the rosidl typesupport interface.

## Features

`rosidl_typesupport_interface` provides an interface of C/C++ macros for rosidl typesupport packages.
They are available in the `macros.h` header.

## Quality Declaration

This package claims to be in the **Quality Level 1** category, see the [Quality Declaration](QUALITY_DECLARATION.md) for more details.
