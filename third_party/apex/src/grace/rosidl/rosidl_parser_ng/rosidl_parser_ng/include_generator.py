# Copyright (c) 2024 Apex.AI, Inc.
# All rights reserved.

import re

from typing import Mapping, Set


class IncludeGenerator:
    def __init__(
        self,
        include_map: Mapping[str, Mapping[str, str]],
    ):
        self.include_map = include_map

    def get_rosidl_generator_cpp_includes(self, dsl_file: str) -> Set[str]:
        include = self.include_map["rosidl_generator_cpp"][dsl_file]
        include_file = self.preprocess_include(include, "rosidl_generator_cpp")
        return {
            f"{include_file}.hpp",
        }

    def get_rosidl_generator_c_includes(self, dsl_file: str) -> Set[str]:
        include = self.include_map["rosidl_generator_c"][dsl_file]
        include_file = self.preprocess_include(include, "rosidl_generator_c")
        return {
            f"{include_file}.h",
        }

    def get_rosidl_typesupport_protobuf_cpp_includes(self, dsl_file: str) -> Set[str]:
        include = self.include_map["rosidl_typesupport_protobuf_cpp"][dsl_file]
        include_file = self.preprocess_include(
            include, "rosidl_typesupport_protobuf_cpp"
        )
        # TODO(hunter.allen): figure out why we get the "detail" directory
        include_file = include_file.replace("/detail/", "/")
        return {
            re.sub(
                "(.+)__type_support$",
                r"\1__rosidl_typesupport_protobuf_cpp.hpp",
                include_file,
            )
        }

    def get_rosidl_typesupport_protobuf_c_includes(self, dsl_file: str) -> Set[str]:
        include = self.include_map["rosidl_typesupport_protobuf_c"][dsl_file]
        include_file = self.preprocess_include(
            include, "rosidl_typesupport_protobuf_c"
        )
        return {f"{include_file}.hpp"}

    def get_rosidl_adapter_proto_includes(self, dsl_file: str) -> Set[str]:
        # map from include to a file name
        dsl_file = self.include_map["rosidl_adapter_proto"][dsl_file]
        dsl_file = self.preprocess_include(dsl_file, "rosidl_adapter_proto")
        return {f"{dsl_file}.pb.h"}

    def preprocess_include(self, include: str, generator_name: str):
        return include

    def dsl_include_to_file(self, include: str):
        # find the file in the maps
        for gen in self.include_map.keys():
            for key in self.include_map[gen].keys():
                if include in key:
                    return key
        assert False, f"Unable to find include '{include}' in map!"
        return None

    def get_all_includes(self, dsl_file: str, gen_list: Set[str] = {}) -> Set[str]:
        ret = set()
        dsl_file = self.dsl_include_to_file(dsl_file)
        if "rosidl_generator_cpp" in gen_list:
            ret = ret.union(self.get_rosidl_generator_cpp_includes(dsl_file))
        if "rosidl_generator_c" in gen_list:
            ret = ret.union(self.get_rosidl_generator_c_includes(dsl_file))
        if "rosidl_adapter_proto" in gen_list:
            ret = ret.union(self.get_rosidl_adapter_proto_includes(dsl_file))
        if "rosidl_typesupport_protobuf_cpp" in gen_list:
            ret = ret.union(self.get_rosidl_typesupport_protobuf_cpp_includes(dsl_file))
        if "rosidl_typesupport_protobuf_c" in gen_list:
            ret = ret.union(self.get_rosidl_typesupport_protobuf_c_includes(dsl_file))
        return ret
