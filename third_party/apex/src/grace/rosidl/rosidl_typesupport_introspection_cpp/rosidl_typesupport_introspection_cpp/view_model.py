# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

# ruff: noqa: TCH002

from __future__ import annotations

from typing import List

from pydantic import BaseModel, Field
from rosidl_parser_ng.generator.includes_view import IncludesView
from rosidl_parser_ng.model import CTypeName


class MemberView(BaseModel):
    """View of a struct member."""

    name: CTypeName  # type: ignore
    introspection_block: str
    size_function: str
    get_const_function: str
    get_function: str
    fetch_function: str
    assign_function: str
    resize_function: str
    set_function: str
    to_string_function: str
    element_size_function: str
    union_member_get_const_function: str
    union_member_get_function: str
    union_discriminator_setup_function: str


class StructView(BaseModel):
    """View of a struct."""

    name: CTypeName  # type: ignore
    path: List[str]
    name_full: str
    namespaces_prefix: str
    namespaces_open_block: str
    namespaces_close_block: str
    type_support_function_name: str
    type_support_declaration_block: str
    members: List[MemberView]


class UnionView(BaseModel):
    """View of a union."""

    name: CTypeName  # type: ignore
    path: List[str]
    name_full: str
    namespaces_prefix: str
    namespaces_open_block: str
    namespaces_close_block: str
    type_support_function_name: str
    type_support_declaration_block: str
    members: List[MemberView]


class IdlView(BaseModel):
    """View of ROSIDL model."""

    structs: List[StructView]
    unions: List[UnionView]
    tmpl_source_includes: IncludesView


class RosServiceView(BaseModel):
    """View of ROS service."""

    path: List[str]
    tmpl_header_block: str
    tmpl_source_block: str


class RosActionView(BaseModel):
    """View of ROS action."""

    path: List[str]
    tmpl_header_block: str
    tmpl_source_block: str


class TypeSupportIntrospectionCpp(BaseModel):
    """View model for type support introspection C++."""

    filename_prefix: str
    idl: IdlView
    ros_service_blocks: List[RosServiceView] = Field(default_factory=list)
    ros_action_blocks: List[RosActionView] = Field(default_factory=list)
