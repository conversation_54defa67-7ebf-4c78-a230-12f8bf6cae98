# Copyright 2019 Open Source Robotics Foundation, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import copy
import re

import pytest

from rosidl_runtime_py import set_message_fields
from test_msgs_fixtures import message_fixtures
from test_msgs.msg import LegacyEnumsMessage
from test_msgs import SomeEnum


def test_set_message_fields_none():
    # Smoke-test on a bunch of messages
    msgs = []
    msgs.extend(message_fixtures.get_msg_arrays())
    msgs.extend(message_fixtures.get_msg_basic_types())
    msgs.extend(message_fixtures.get_msg_bounded_sequences())
    msgs.extend(message_fixtures.get_msg_builtins())
    msgs.extend(message_fixtures.get_msg_constants())
    msgs.extend(message_fixtures.get_msg_defaults())
    msgs.extend(message_fixtures.get_msg_empty())
    msgs.extend(message_fixtures.get_msg_multi_nested())
    msgs.extend(message_fixtures.get_msg_nested())
    msgs.extend(message_fixtures.get_msg_strings())
    msgs.extend(message_fixtures.get_msg_unbounded_sequences())
    msgs.extend(message_fixtures.get_msg_enums_message())
    msgs.extend(message_fixtures.get_msg_legacy_enums_message())
    msgs.extend(message_fixtures.get_msg_unions_message())

    for m in msgs:
        original_m = copy.copy(m)
        set_message_fields(m, {})
        # Assert message is not modified when setting no fields
        assert original_m == m


def test_set_message_fields_partial():
    original_msg = message_fixtures.get_msg_basic_types()[0]
    original_msg.bool_value = False
    original_msg.char_value = 3
    original_msg.int32_value = 42

    modified_msg = copy.copy(original_msg)
    values = {}
    values['bool_value'] = True
    values['char_value'] = 1
    values['int32_value'] = 24
    set_message_fields(modified_msg, values)

    for _attr in original_msg.__slots__:
        # Remove underscore prefix
        attr = _attr[1:]
        if attr in values:
            assert getattr(modified_msg, attr) == values[attr]
        else:
            assert getattr(modified_msg, attr) == getattr(original_msg, attr)


def test_set_enums_message_fields():
    def _convert_to_enum(value):
        try:
            return SomeEnum(value)
        except ValueError:
            return SomeEnum[value]

    msg_list = message_fixtures.get_msg_enums_message()
    original_msg = msg_list[0]

    modified_msg = copy.copy(original_msg)
    values = {}
    values['enum_value'] = 0
    values['enum_default_value'] = 'ENUMERATOR1'
    values['dynamic_array_values'] = ['ENUMERATOR2',
                                      'ENUMERATOR1']
    values['bounded_array_values'] = [1, 'ENUMERATOR1', SomeEnum.ENUMERATOR2]
    set_message_fields(modified_msg, values)

    for _attr in original_msg.__slots__:
        # Remove underscore prefix
        attr = _attr[1:]
        if attr in values:
            if isinstance(values[attr], list):
                test = [_convert_to_enum(v) for v in values[attr]]
            else:
                test = _convert_to_enum(values[attr])
            assert getattr(modified_msg, attr) == test
        else:
            assert getattr(modified_msg, attr) == getattr(original_msg, attr)


def test_set_legacy_enums_message_fields():
    def _convert_to_legacy_enum(value):
        try:
            return LegacyEnumsMessage.SomeLegacyEnum(value)
        except ValueError:
            return LegacyEnumsMessage.SomeLegacyEnum[value]

    msg_list = message_fixtures.get_msg_legacy_enums_message()
    original_msg = msg_list[0]

    modified_msg = copy.copy(original_msg)
    values = {}
    values['enum_value'] = 0
    values['enum_default_value'] = 'ENUMERATOR1'
    values['dynamic_array_values'] = ['ENUMERATOR2',
                                      'ENUMERATOR1']
    values['bounded_array_values'] = [1, 'ENUMERATOR1',
                                      LegacyEnumsMessage.SomeLegacyEnum.ENUMERATOR2]
    set_message_fields(modified_msg, values)

    for _attr in original_msg.__slots__:
        # Remove underscore prefix
        attr = _attr[1:]
        if attr in values:
            if isinstance(values[attr], list):
                test = [_convert_to_legacy_enum(v) for v in values[attr]]
            else:
                test = _convert_to_legacy_enum(values[attr])
            assert getattr(modified_msg, attr) == test
        else:
            assert getattr(modified_msg, attr) == getattr(original_msg, attr)


def test_set_enums_message_fields_invalid():
    msg_list = message_fixtures.get_msg_enums_message()
    msg = msg_list[0]

    values = {}
    values['enum_value'] = 5
    values['bounded_array_values'] = [1, 4]
    with pytest.raises(ValueError):
        set_message_fields(msg, values)


def test_set_message_fields_full():
    msg_list = message_fixtures.get_msg_basic_types()
    msg0 = msg_list[0]
    msg1 = msg_list[1]

    # Set msg0 values to the values of msg1
    values = {}
    for _attr in msg1.__slots__:
        # Remove underscore prefix
        attr = _attr[1:]
        values[attr] = getattr(msg1, attr)
    set_message_fields(msg0, values)

    assert msg0 == msg1


def test_set_message_fields_invalid():
    msg = message_fixtures.get_msg_basic_types()[0]
    invalid_field = {}
    invalid_field['test_invalid_field'] = 42
    with pytest.raises(AttributeError):
        set_message_fields(msg, invalid_field)

    invalid_type = {}
    invalid_type['int32_value'] = 'this is not an integer'
    with pytest.raises(ValueError):
        set_message_fields(msg, invalid_type)

    msg = message_fixtures.get_msg_nested()[0]
    with pytest.raises(TypeError):
        set_message_fields(msg, 'not_a_dict')


def test_set_nested_namespaced_fields():
    unbounded_sequence_msg = message_fixtures.get_msg_unbounded_sequences()[1]
    test_values = {
        'basic_types_values': [
            {'float64_value': 42.42, 'int8_value': 42},
            {'float64_value': 11.11, 'int8_value': 11}
        ]
    }
    set_message_fields(unbounded_sequence_msg, test_values)
    assert unbounded_sequence_msg.basic_types_values[0].float64_value == 42.42
    assert unbounded_sequence_msg.basic_types_values[0].int8_value == 42
    assert unbounded_sequence_msg.basic_types_values[0].uint8_value == 0
    assert unbounded_sequence_msg.basic_types_values[1].float64_value == 11.11
    assert unbounded_sequence_msg.basic_types_values[1].int8_value == 11
    assert unbounded_sequence_msg.basic_types_values[1].uint8_value == 0

    arrays_msg = message_fixtures.get_msg_arrays()[0]
    test_values = {
        'basic_types_values': [
            {'float64_value': 42.42, 'int8_value': 42},
            {'float64_value': 11.11, 'int8_value': 11},
            {'float64_value': 22.22, 'int8_value': 22},
        ]
    }
    set_message_fields(arrays_msg, test_values)
    assert arrays_msg.basic_types_values[0].float64_value == 42.42
    assert arrays_msg.basic_types_values[0].int8_value == 42
    assert arrays_msg.basic_types_values[0].uint8_value == 0
    assert arrays_msg.basic_types_values[1].float64_value == 11.11
    assert arrays_msg.basic_types_values[1].int8_value == 11
    assert arrays_msg.basic_types_values[1].uint8_value == 0
    assert arrays_msg.basic_types_values[2].float64_value == 22.22
    assert arrays_msg.basic_types_values[2].int8_value == 22
    assert arrays_msg.basic_types_values[2].uint8_value == 0


def test_set_message_fields_nested_type():
    msg_basic_types = message_fixtures.get_msg_basic_types()[0]
    msg0 = message_fixtures.get_msg_nested()[0]

    msg0.basic_types_value.bool_value = False
    msg0.basic_types_value.char_value = 3
    msg0.basic_types_value.int32_value = 42

    assert msg0.basic_types_value != msg_basic_types

    test_values = {}
    test_values['basic_types_value'] = msg_basic_types
    set_message_fields(msg0, test_values)

    assert msg0.basic_types_value == msg_basic_types


def test_set_unions_message_fields():
    unions_msg = copy.copy(message_fixtures.get_msg_unions_message()[0])
    test_values = {"union_value": {"double_value": 42.55, "_d": 2}}
    set_message_fields(unions_msg, test_values)
    assert unions_msg.union_value._d == 2
    assert unions_msg.union_value.double_value == 42.55

    test_values = {"nested_union_value": {"_d": 1, "int_value": 42}}
    set_message_fields(unions_msg, test_values)
    assert unions_msg.nested_union_value._d == 1
    assert unions_msg.nested_union_value.int_value == 42

    test_values = {
        "nested_union_value": {
            "_d": 21,
            "union_value": {"_d": 1, "string_value": "foo"},
        }
    }
    set_message_fields(unions_msg, test_values)
    assert unions_msg.nested_union_value._d == 21
    assert unions_msg.nested_union_value.union_value._d == 1
    assert unions_msg.nested_union_value.union_value.string_value == "foo"

    test_values = {
        "bounded_union_sequence": [
            {"_d": 0, "long_value": 42},
            {"_d": 1, "string_value": "foo"},
            {"double_value": 42.55, "_d": 2},
        ]
    }
    set_message_fields(unions_msg, test_values)
    assert unions_msg.bounded_union_sequence[0]._d == 0
    assert unions_msg.bounded_union_sequence[0].long_value == 42
    assert unions_msg.bounded_union_sequence[1]._d == 1
    assert unions_msg.bounded_union_sequence[1].string_value == "foo"
    assert unions_msg.bounded_union_sequence[2]._d == 2
    assert unions_msg.bounded_union_sequence[2].double_value == 42.55

    test_values = {"union_array": []}
    for _ in unions_msg.union_array:
        test_values["union_array"].append({"_d": 0, "long_value": 441})
    set_message_fields(unions_msg, test_values)
    for v in unions_msg.union_array:
        assert v._d == 0
        assert v.long_value == 441

    test_values = {
        "bounded_nested_union_sequence": [
            {"_d": 8, "union_value": {"_d": 1, "string_value": "bar"}},
            {"_d": 0, "int_sequence": [5, 6, 7]},
            {"union_value": {"_d": 0, "long_value": 56}, "_d": 21},
        ]
    }
    set_message_fields(unions_msg, test_values)
    assert unions_msg.bounded_nested_union_sequence[0]._d == 8
    assert unions_msg.bounded_nested_union_sequence[0].union_value._d == 1
    assert unions_msg.bounded_nested_union_sequence[0].union_value.string_value == "bar"
    assert unions_msg.bounded_nested_union_sequence[1]._d == 0
    assert unions_msg.bounded_nested_union_sequence[1].int_sequence[0] == 5
    assert unions_msg.bounded_nested_union_sequence[1].int_sequence[1] == 6
    assert unions_msg.bounded_nested_union_sequence[1].int_sequence[2] == 7
    assert unions_msg.bounded_nested_union_sequence[2]._d == 21
    assert unions_msg.bounded_nested_union_sequence[2].union_value._d == 0
    assert unions_msg.bounded_nested_union_sequence[2].union_value.long_value == 56


def test_set_unions_message_fields_invalid():
    unions_msg = copy.copy(message_fixtures.get_msg_unions_message()[0])

    test_values = {"union_value": {"double_value": 42.55, "_d": 3}}
    with pytest.raises(
        ValueError, match="Invalid discriminator UnionEnum.UNUSED for 'ExampleUnion' union"
    ):
        set_message_fields(unions_msg, test_values)

    test_values = {"union_value": {"double_value": 42.55, "_d": 1}}
    with pytest.raises(
        AttributeError,
        match=re.escape(
            "Currently set discriminator (<UnionEnum.STRING: 1>) "
            "is invalid for member 'double_value'"
        ),
    ):
        set_message_fields(unions_msg, test_values)

    test_values = {"union_value": {"long_value": 42}}
    with pytest.raises(
        ValueError,
        match=re.escape(
            "Trying to set 'ExampleUnion' union type field 'union_value', but "
            "discriminator (_d) is not provided in the {'long_value': 42} value"
        ),
    ):
        set_message_fields(unions_msg, test_values)

    test_values = {"bounded_union_sequence": [{"long_value": 59}]}
    with pytest.raises(
        ValueError,
        match=re.escape(
            "Trying to set 'ExampleUnion' union type field 'bounded_union_sequence', but "
            "discriminator (_d) is not provided in the {'long_value': 59} value"
        ),
    ):
        set_message_fields(unions_msg, test_values)
