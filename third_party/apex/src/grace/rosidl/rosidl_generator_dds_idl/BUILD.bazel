load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@bazel_skylib//rules:copy_file.bzl", "copy_file")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary", "py_library")

py_library(
    name = "rosidl_generator_dds_idl_python",
    srcs = glob([
        "rosidl_generator_dds_idl/*.py",
    ]),
    data = glob(["rosidl_generator_dds_idl/templates/**/*.j2"]),
    imports = ["."],
    deps = [
        "//grace/rmw_apex/apex_middleware_typefiles_generator",
        "//grace/rosidl/rosidl_cmake:rosidl_cmake_python",
        "//grace/rosidl/rosidl_parser_ng:rosidl_parser_ng_python",
        requirement("jinja2"),
        requirement("pydantic"),
    ],
)

copy_file(
    name = "rosidl_generator_dds_idl_with_py_extension",
    src = "bin/rosidl_generator_dds_idl",
    out = "generator.py",
    allow_symlink = True,
)

py_binary(
    name = "generator",
    srcs = [
        "rosidl_generator_dds_idl_with_py_extension",
    ],
    legacy_create_init = 0,  # required for py_binaries used on execution platform
    visibility = ["//visibility:public"],
    deps = [
        ":rosidl_generator_dds_idl_python",
    ],
)

apex_py_test(
    name = "test_generated_files",
    srcs = [
        "test/test_generator.py",
    ],
    deps = [
        ":rosidl_generator_dds_idl_python",
    ],
)
