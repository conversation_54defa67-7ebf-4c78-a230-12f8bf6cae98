// Copyright (c) 2022 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include "rosidl_typesupport_introspection_tests/fixtures.hpp"
#include "rosidl_typesupport_introspection_tests/gtest/macros.hpp"
#include "rosidl_typesupport_introspection_tests/gtest/message_introspection_test.hpp"
#include "rosidl_typesupport_introspection_tests/api.hpp"
#include "rosidl_typesupport_introspection_tests/type_traits.hpp"

#include "introspection_libraries_under_test.hpp"

namespace rosidl_typesupport_introspection_tests
{
namespace testing
{
namespace
{

template<typename FlatSequencesBoundedMessageT>
class FlatSequencesBoundedMessageIntrospectionTest
  : public MessageIntrospectionTest<FlatSequencesBoundedMessageT>
{
};

using FlatSequencesBoundedMessageTypes = ::testing::Types<
  rosidl_typesupport_introspection_tests__msg__FlatSequencesBounded,
  rosidl_typesupport_introspection_tests::msg::FlatSequencesBounded>;
TYPED_TEST_SUITE(FlatSequencesBoundedMessageIntrospectionTest, FlatSequencesBoundedMessageTypes);

// cppcheck-suppress syntaxError
TYPED_TEST(FlatSequencesBoundedMessageIntrospectionTest, MessageDescriptorIsCorrect)
{
  using FlatSequencesBoundedMessageT = TypeParam;

  using TypeSupportLibraryT =
    typename introspection_traits<FlatSequencesBoundedMessageT>::TypeSupportLibraryT;
  using MessageDescriptorT = typename TypeSupportLibraryT::MessageDescriptorT;
  const MessageDescriptorT * message_descriptor = this->GetMessageDescriptor();

  EXPECT_STREQ(
    get_message_namespace(message_descriptor),
    TypeSupportLibraryT::messages_namespace);
  EXPECT_STREQ(get_message_name(message_descriptor), "FlatSequencesBounded");
  EXPECT_EQ(get_message_size(message_descriptor), sizeof(FlatSequencesBoundedMessageT));
  ASSERT_EQ(get_member_count(message_descriptor), 3u);

  {
    auto * member_descriptor = get_member_descriptor(message_descriptor, 0u);
    EXPECT_STREQ(get_member_name(member_descriptor), "bool_value");
    EXPECT_TRUE(is_base_type_member(member_descriptor, ROS_TYPE_BOOLEAN));
    EXPECT_TRUE(has_bounded_sequence_structure(member_descriptor, 10u));
    EXPECT_TRUE(member_descriptor->is_flat_);
  }

  {
    auto * member_descriptor = get_member_descriptor(message_descriptor, 1u);
    EXPECT_STREQ(get_member_name(member_descriptor), "string_value");
    EXPECT_TRUE(is_string_member(member_descriptor, 10u));
  }

  {
    auto * member_descriptor = get_member_descriptor(message_descriptor, 2u);
    EXPECT_STREQ(get_member_name(member_descriptor), "strings_value");
    EXPECT_TRUE(is_base_type_member(member_descriptor, ROS_TYPE_STRING));
    EXPECT_TRUE(has_bounded_sequence_structure(member_descriptor, 10u));
  }
}

TYPED_TEST(FlatSequencesBoundedMessageIntrospectionTest, CanReadTypeErasedMessage)
{
  using FlatSequencesBoundedMessageT = TypeParam;

  const auto message_ptr = Example<FlatSequencesBoundedMessageT>::Make();
  const FlatSequencesBoundedMessageT & message = *message_ptr;
  const void * type_erased_message = message_ptr.get();

  using TypeSupportLibraryT =
    typename introspection_traits<FlatSequencesBoundedMessageT>::TypeSupportLibraryT;
  using MessageDescriptorT = typename TypeSupportLibraryT::MessageDescriptorT;
  const MessageDescriptorT * message_descriptor = this->GetMessageDescriptor();
  ASSERT_EQ(get_member_count(message_descriptor), 3u);

  EXPECT_ITERABLE_MEMBER_EQ(
    type_erased_message, message, bool_value,
    get_member_descriptor(message_descriptor, 0u));

  EXPECT_MEMBER_EQ(
    type_erased_message, message, string_value,
    get_member_descriptor(message_descriptor, 1u));

  EXPECT_ITERABLE_MEMBER_EQ(
    type_erased_message, message, strings_value,
    get_member_descriptor(message_descriptor, 2u));
}

TYPED_TEST(FlatSequencesBoundedMessageIntrospectionTest, CanWriteTypeErasedMessage)
{
  using FlatSequencesBoundedMessageT = TypeParam;

  const auto message_ptr = Example<FlatSequencesBoundedMessageT>::Make();
  const FlatSequencesBoundedMessageT & message = *message_ptr;

  auto type_erased_message_copy = this->MakeTypeErasedMessage();
  const FlatSequencesBoundedMessageT & message_copy =
    *reinterpret_cast<FlatSequencesBoundedMessageT *>(type_erased_message_copy.get());
  EXPECT_NE(message, message_copy);

  using TypeSupportLibraryT =
    typename introspection_traits<FlatSequencesBoundedMessageT>::TypeSupportLibraryT;
  using MessageDescriptorT = typename TypeSupportLibraryT::MessageDescriptorT;
  const MessageDescriptorT * message_descriptor = this->GetMessageDescriptor();
  ASSERT_EQ(get_member_count(message_descriptor), 3u);

  EXPECT_SEQUENCE_MEMBER_ASSIGNMENT(
    type_erased_message_copy.get(), message, bool_value,
    get_member_descriptor(message_descriptor, 0u));

  EXPECT_MEMBER_ASSIGNMENT(
    type_erased_message_copy.get(), message, string_value,
    get_member_descriptor(message_descriptor, 1u));

  EXPECT_SEQUENCE_MEMBER_ASSIGNMENT(
    type_erased_message_copy.get(), message, strings_value,
    get_member_descriptor(message_descriptor, 2u));

  EXPECT_EQ(message, message_copy);
}

}  // namespace
}  // namespace testing
}  // namespace rosidl_typesupport_introspection_tests
