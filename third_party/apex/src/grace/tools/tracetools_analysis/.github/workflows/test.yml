name: Test
on:
  pull_request:
  push:
    branches:
      - rolling
  schedule:
    - cron: "0 5 * * *"
jobs:
  build-and-test:
    runs-on: ubuntu-latest
    container:
      image: ubuntu:24.04
    continue-on-error: ${{ matrix.build-type == 'binary' }}
    strategy:
      matrix:
          distro:
            - rolling
          build-type:
            - binary
            - source
    env:
      ROS2_REPOS_FILE_URL: 'https://raw.githubusercontent.com/ros2/ros2/${{ matrix.distro }}/ros2.repos'
    steps:
    - uses: actions/checkout@v3
    - uses: ros-tooling/setup-ros@master
      with:
        required-ros-distributions: ${{ matrix.build-type == 'binary' && matrix.distro || '' }}
        use-ros2-testing: true
    - uses: ros-tooling/action-ros-ci@master
      with:
        package-name: ros2trace_analysis tracetools_analysis
        target-ros2-distro: ${{ matrix.distro }}
        vcs-repo-file-url: ${{ matrix.build-type == 'source' && env.ROS2_REPOS_FILE_URL || '' }}
        colcon-defaults: |
          {
            "build": {
              "mixin": [
                "coverage-pytest"
              ]
            },
            "test": {
              "mixin": [
                "coverage-pytest"
              ],
              "executor": "sequential",
              "retest-until-pass": 2,
              "pytest-args": ["-m", "not xfail"]
            }
          }
    - uses: codecov/codecov-action@v3
      with:
        files: ros_ws/coveragepy/.coverage
