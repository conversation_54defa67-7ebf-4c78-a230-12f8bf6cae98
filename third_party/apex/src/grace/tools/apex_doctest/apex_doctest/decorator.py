# Copyright 2019 Apex.AI, Inc.
# All rights reserved.

DECORATOR_USED = False

import pkg_resources


def doc_check_launch_description(documentation_name='', documentation_package=''):

    # Mark that the decorator was used so we can give better error messages in test
    # cases that require the decorator
    global DECORATOR_USED
    DECORATOR_USED = True

    def _deco(fn):

        def _generate_test_description_wrapper():
            ret = fn()
            # Tests without a test context return a Launch Description, but tests with
            # a test context return a (LaunchDescription, LaunchContext) tuple
            if isinstance(ret, tuple):
                launch_description = ret[0]
                context = ret[1]
            else:
                launch_description = ret
                context = {}

            # Make the launch description available to tests
            context['_doc_launch_description'] = launch_description

            if documentation_name and documentation_package:
                for entry_point in pkg_resources.iter_entry_points('apex_doctest_tags'):
                    tag_clss = entry_point.load()
                    for action in tag_clss.launch_description_actions(documentation_name,
                                                                      documentation_package):
                        launch_description.add_action(action)

            return launch_description, context

        return _generate_test_description_wrapper

    return _deco
