<!--
For general questions, please ask on ROS answers: https://answers.ros.org, make sure to include at least the `ros2` tag and the rosdistro version you are running, e.g. `ardent`.
For general design discussions, please post on discourse: https://discourse.ros.org/c/ng-ros
Not sure if this is the right repository? Open an issue on https://github.com/ros2/ros2/issues
For Bug report or feature requests, please fill out the relevant category below
-->

## Bug report

**Required Info:**

- Operating System:
  - <!-- OS and version (e.g. Windows 10, Ubuntu 16.04...) -->
- Installation type:
  - <!-- binaries or from source  -->
- Version or commit hash:
  - <!-- Output of git rev-parse HEAD, release version, or repos file  -->
- DDS implementation:
  - <!-- rmw_implementation used (e.g. Fast-RTPS, RTI Connext, etc -->
- Client library (if applicable):
  - <!-- e.g. rclcpp, rclpy, or N/A -->

#### Steps to reproduce issue
<!-- Detailed instructions on how to reliably reproduce this issue http://sscce.org/
``` code that can be copy-pasted is preferred ``` -->
```

```

#### Expected behavior

#### Actual behavior

#### Additional information

<!-- If you are reporting a bug delete everything below
     If you are requesting a feature deleted everything above this line -->
----
## Feature request

#### Feature description
<!-- Description in a few sentences what the feature consists of and what problem it will solve -->

#### Implementation considerations
<!-- Relevant information on how the feature could be implemented and pros and cons of the different solutions -->
