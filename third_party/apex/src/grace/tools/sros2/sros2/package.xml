<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>sros2</name>
  <version>0.11.0</version>
  <description>Command line tools for managing SROS2 keys</description>
  <maintainer email="<EMAIL>">ROS Security Working Group</maintainer>
  <license>Apache License 2.0</license>

  <author email="<EMAIL>"><PERSON></author>
  <author><PERSON><PERSON><PERSON></author>

  <depend>rclpy</depend>
  <depend>ros2cli</depend>

  <exec_depend>ament_index_python</exec_depend>
  <exec_depend>python3-lxml</exec_depend>
  <exec_depend>python3-cryptography</exec_depend>
  <exec_depend>python3-importlib-resources</exec_depend>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>
  <!-- Single entrypoint to depend on launch based testing -->
  <test_depend>ros_testing</test_depend>
  <test_depend>test_msgs</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
