import glob
from setuptools import setup

package_name = 'apex_test_runner'

setup(
   name=package_name,
   version='0.1.0',
   license='Apex.AI',
   description='Tools to assist automatic playback and recording of rosbag2 files',
   author='<PERSON>',
   author_email='<EMAIL>',
   url='www.apex.ai',
   packages=[package_name],
   data_files=[
      ('share/ament_index/resource_index/packages',
       ['resource/' + package_name]),
      ('share/' + package_name, ['package.xml']),
      ('share/' + package_name + '/cfg', glob.glob('cfg/*'))
     ],
   install_requires=['pandas'],
   zip_safe=True,
   tests_require=['pytest'],
   entry_points={
      'console_scripts': [
          'apex_test_runner = apex_test_runner.runner:main',
      ]
   }
)
