// Copyright 2024 Apex.AI, Inc.
// All rights reserved.
#include <gtest/gtest.h>

#include <executor2/apex_node_base.hpp>
#include <executor2/executor_factory.hpp>
#include <executor2/executor_runner.hpp>
#include <timer_service/clock_timer_service.hpp>
#include <std_msgs/msg/int32.hpp>
#include <std_msgs/msg/bool.hpp>

//! [Include]
#include <apex_integration_test_node/apex_integration_test_node.hpp>
//! [Include]

#include <chrono>
#include <memory>
#include <utility>

//! [Using]
using IntegrationTestNodeFixture =
  apex::tools::apex_integration_test_node::ApexIntegrationTestNode;
using IntegrationTestNodeFixtureParameterized =
  apex::tools::apex_integration_test_node::ApexIntegrationTestNodeParameterized<int32_t>;
using std_msgs::msg::Bool;
using std_msgs::msg::Int32;
using namespace std::chrono_literals;
//! [Using]

namespace
{

constexpr auto test_input_topic = "/node_under_test/test_input_topic";
constexpr auto test_output_topic = "/node_under_test/test_output_topic";


class NodeUnderTest : public apex::executor::apex_node_base
{
public:
  NodeUnderTest()
  : apex::executor::apex_node_base("is_positive") {}

  using InputType = std_msgs::msg::Int32;
  using OutputType = std_msgs::msg::Bool;

private:
  bool execute_impl() override
  {
    const auto input_msgs{m_sub->take(1)};
    auto output_msg = m_pub->borrow_loaned_message();
    output_msg.get().data = (input_msgs.front().data().data > 0);
    m_pub->publish(std::move(output_msg));
    return true;
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_sub};
  }

  apex::executor::publisher_list get_publishers_impl() const override
  {
    return {m_pub};
  }

private:
  rclcpp::PollingSubscription<InputType>::SharedPtr m_sub{
    get_rclcpp_node().create_polling_subscription<InputType>(
      ::test_input_topic,
      rclcpp::DefaultQoS())};
  rclcpp::Publisher<OutputType>::SharedPtr m_pub{
    get_rclcpp_node().create_publisher<OutputType>(
      ::test_output_topic, rclcpp::DefaultQoS())};
};

//! [Arrange]
template<typename FixtureT>
void run_test(int32_t value_in_message, FixtureT * fixture)
{
  auto test_publisher = fixture->template create_publisher<Int32>(::test_input_topic);
  auto msg = test_publisher->borrow_loaned_message();   // (1)!
  msg.get().data = value_in_message;

  const auto node_under_test = std::make_shared<::NodeUnderTest>();  // (2)!

  auto result_subscription =
    fixture->template create_polling_subscription<NodeUnderTest::OutputType>(
    ::test_output_topic
    );  // (3)!

  const auto max_wait_time{10s};  // (4)!
  apex::timer_service::steady_clock_timer_service timer_srv;

  auto exec = apex::executor::executor_factory::create();
  exec->add_with_timeout(node_under_test, timer_srv, max_wait_time);  // (5)!

  rclcpp::dynamic_waitset::Waitset waitset;  // (6)!
  waitset.add(result_subscription);
  //! [Arrange]

  //! [Act]
  test_publisher->publish(std::move(msg));  // (1)!

  {
    using apex::threading::thread_attributes;
    using apex::threading::scheduler;

    apex::executor::executor_runner runner {  // (2)!
      *exec,
      thread_attributes::build().scheduling_policy(scheduler::normal)
    };
  }

  const bool waitset_triggered = waitset.wait(max_wait_time);  // (3)!
  //! [Act]

  //! [Assert]
  ASSERT_TRUE(waitset_triggered) <<
    "System under test didn't produce output within " <<
    std::chrono::duration_cast<std::chrono::seconds>(max_wait_time).count() << " s";

  auto loaned_messages{result_subscription->take()};
  ASSERT_EQ(loaned_messages.size(), 1);
  const auto & received_msg = loaned_messages.front();
  ASSERT_TRUE(received_msg.info().valid());
  EXPECT_EQ(received_msg.data().data, value_in_message > 0);
}
//! [Assert]

//! [Fixture]
TEST_F(IntegrationTestNodeFixture, NormalTest)
{
  run_test(15, this);
}
//! [Fixture]

//! [Parameterized]
INSTANTIATE_TEST_SUITE_P(
  IntegrationTestNodeParameterizedTests,
  IntegrationTestNodeFixtureParameterized,
  ::testing::Values(-13, 0, 5, 42));

TEST_P(IntegrationTestNodeFixtureParameterized, ParameterizedTest)
{
  auto input = GetParam();
  run_test(input, this);
}
//! [Parameterized]

}  // namespace
