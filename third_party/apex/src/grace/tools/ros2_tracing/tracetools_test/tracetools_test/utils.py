# Copyright 2019 <PERSON>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Utils for tracetools_test that are not strictly test-related."""

import os
from pathlib import Path
import shutil
import subprocess
from typing import List
from typing import Optional
from typing import Tuple

from launch import Action
from launch import LaunchDescription
from launch import LaunchService
from launch_ros.actions import Node
from tracetools_launch.action import Trace
from tracetools_read import DictEvent
from tracetools_read import get_event_name
from tracetools_trace.tools.path import append_timestamp


def run_and_trace(
    base_path: str,
    session_name_prefix: str,
    ros_events: List[str],
    kernel_events: List[str],
    package_name: str,
    node_names: List[str],
    namespace: Optional[str],
    additional_actions: List[Action],
) -> Tuple[int, str]:
    """
    Run a node while tracing.

    :param base_path: the base path where to put the trace directory
    :param session_name_prefix: the session name prefix for the trace directory
    :param ros_events: the list of ROS UST events to enable
    :param kernel_events: the list of kernel events to enable
    :param package_name: the name of the package to use
    :param node_names: the names of the nodes to execute
    :param namespace: the ROS namespace for the node(s)
    :param additional_actions: the list of additional actions to append
    :return: exit code, full generated path
    """
    session_name = append_timestamp(session_name_prefix)
    full_path = os.path.join(base_path, session_name)

    launch_actions = []
    # Add trace action
    launch_actions.append(
        Trace(
            session_name=session_name,
            append_timestamp=False,
            base_path=base_path,
            events_ust=ros_events,
            events_kernel=kernel_events,
        )
    )
    # Add nodes
    for node_name in node_names:
        n = Node(
            package=package_name,
            executable=node_name,
            namespace=namespace,
            output='screen',
            # Explicitly request to use the current environment
            env=None,
        )
        launch_actions.append(n)
    launch_actions.extend(additional_actions)
    ld = LaunchDescription(launch_actions)
    ls = LaunchService()
    ls.include_launch_description(ld)

    exit_code = ls.run()

    return exit_code, full_path


def cleanup_trace(full_path: str) -> None:
    """
    Cleanup trace data.

    :param full_path: the full path to the main trace directory
    """
    shutil.rmtree(full_path)


def get_event_names(events: List[DictEvent]) -> List[str]:
    """
    Get a list of events names from a list of events.

    :param events: the events of the trace
    :return: the list of event names
    """
    return [get_event_name(e) for e in events]


def launch_ida_resource_creator() -> subprocess.Popen:
    """
    Launch Apex.Ida v3 resource creator process if it is available.

    :return: the process, if the resource creator binary is vailable
    """
    # TODO(christophe.bedard): once we remove Apex.Ida v2, remove this check and unconditionally
    #  launch resource creator
    resource_creator_path = Path('ida/resource_creator/resource_creator')
    if not resource_creator_path.is_file():
        return None
    return subprocess.Popen(resource_creator_path)  # noqa: S603


def stop_ida_resource_creator(resource_creator_process: Optional[subprocess.Popen]) -> None:
    """
    Stop Apex.Ida v3 resource_creator process if there is one.

    :param resource_creator_process: the resource_creator process, or `None`
    """
    if not resource_creator_process:
        return

    # Process should still be alive
    assert resource_creator_process.poll() is None

    resource_creator_process.terminate()
    try:
        resource_creator_process.wait(1)
    except subprocess.TimeoutExpired:
        resource_creator_process.kill()
