// Copyright 2023 Apex.AI, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef LTTNGPY__CONFIG_HPP_
#define LTTNGPY__CONFIG_HPP_

#cmakedefine LTTNGPY_DISABLED @LTTNGPY_DISABLED@
#define LTTNG_CTL_VERSION "@LTTNG_CTL_VERSION@"
#define LTTNG_CTL_VERSION_MAJOR (@LTTNG_CTL_VERSION_MAJOR@)
#define LTTNG_CTL_VERSION_MINOR (@LTTNG_CTL_VERSION_MINOR@)
#define LTTNG_CTL_VERSION_PATCH (@LTTNG_CTL_VERSION_PATCH@)

#endif  // LTTNGPY__CONFIG_HPP_
