// Copyright 2024 Apex.AI, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <chrono>
#include <memory>

#include "apex_init/apex_init.hpp"
#include "executor2/apex_node_base.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "rclcpp/rclcpp.hpp"
#include "test_tracetools/mark_process.hpp"

#include "executor_test_node_utils.hpp"
#include "pipe_utils.hpp"

using namespace std::chrono_literals;

namespace
{
/**
 * Test node which is meant to only run once and be triggered by a file descriptor for tracing
 * instrumentation test coverage purposes.
 */
class executor_file_monitor : public executor_test_node
{
public:
  explicit executor_file_monitor(const pipe_t & pipe)
  : executor_test_node("executor_file_monitor"),
    m_pipe(pipe),
    m_shared_pollfd(rclcpp::dynamic_waitset::make_shared_pollfd(
        apex::cast::safe_cast<int32_t>(m_pipe[PipeIn])))
  {}

private:
  apex::executor::file_descriptor_list get_file_descriptors_impl() const override
  {
    return {m_shared_pollfd};
  }

  bool execute_impl() override
  {
    if (is_done()) {
      return true;
    }

    if (m_shared_pollfd->revents & (POLLNVAL | POLLERR)) {
      throw apex::runtime_error{"Invalid poll() on input fd"};
    }

    if (m_shared_pollfd->revents & POLLIN) {
      (void)read_pipe(m_pipe);
      m_done.store(true);
    }

    return true;
  }

  const pipe_t m_pipe;
  rclcpp::dynamic_waitset::shared_pollfd m_shared_pollfd;
};
}  // namespace

int main(int argc, char ** argv)
{
  test_tracetools::mark_trace_test_process();

  if (apex::pre_init(argc, argv) != APEX_RET_OK) {
    throw apex::runtime_error("Failed to initialize");
  }

  const auto pipe = create_pipe();

  const auto exec = apex::executor::executor_factory::create();
  rclcpp::dynamic_waitset::file_monitor fm;
  exec->set_file_monitor(fm);
  const auto node = std::make_shared<executor_file_monitor>(pipe);
  exec->add(node);
  const apex::executor::executor_runner runner{*exec};

  write_pipe(pipe, 42);

  while (!node->is_done()) {
    std::this_thread::sleep_for(2ms);
  }

  close_pipe(pipe);

  return EXIT_SUCCESS;
}
