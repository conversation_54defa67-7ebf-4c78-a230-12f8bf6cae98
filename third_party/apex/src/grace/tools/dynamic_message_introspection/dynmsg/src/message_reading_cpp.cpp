// Copyright 2020 Open Source Robotics Foundation, Inc.
// Copyright 2021 <PERSON>
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <iostream>
#include <sstream>
#include <string>
#include <vector>

#include "bounded_vector/bounded_vector.hpp"
#include "rcutils/logging_macros.h"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"

#include "string/string.hpp"

#include "dynmsg/config.hpp"
#include "dynmsg/message_reading.hpp"
#include "dynmsg/string_utils.hpp"
#include "dynmsg/typesupport.hpp"
#include "string/string_strict.hpp"

namespace dynmsg
{
namespace cpp
{

namespace impl
{

#ifndef DYNMSG_VALUE_ONLY
// Convert primitive ROS types to a string representation
std::string
member_type_to_string(const MemberInfo_Cpp & member_info)
{
  std::stringstream result;
  switch (member_info.type_id_) {
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_FLOAT:
      result << "float";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_DOUBLE:
      result << "double";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_LONG_DOUBLE:
      result << "long double";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_CHAR:
      result << "char";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WCHAR:
      result << "wchar";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_BOOLEAN:
      result << "boolean";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_OCTET:
      result << "octet";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8:
      result << "uint8";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT8:
      result << "int8";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT16:
      result << "uint16";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT16:
      result << "int16";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32:
      result << "uint32";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32:
      result << "int32";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_ENUM:
      result << "enum";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT64:
      result << "uint64";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64:
      result << "int64";
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_STRING:
      result << "string";
      // Strings may have an upper bound
      if (member_info.string_upper_bound_ > 0) {
        result << "<=" << member_info.string_upper_bound_;
      }
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WSTRING:
      result << "wstring";
      // WStrings may have an upper bound
      if (member_info.string_upper_bound_ > 0) {
        result << "<=" << member_info.string_upper_bound_;
      }
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UNION:
      // For nested types, the string representation must include the name space as well as the
      // type name
      result <<
        reinterpret_cast<const TypeInfo_Cpp *>(member_info.members_->data)->message_namespace_ <<
        "/" <<
        reinterpret_cast<const TypeInfo_Cpp *>(member_info.members_->data)->message_name_;
      break;
    default:
      // Don't throw an error, just print out "UNKNOWN" then keep persevering through the message
      result << "UNKNOWN";
      break;
  }
  // If this member is a sequence of some kind, indicate that in the type
  if (member_info.is_array_) {
    result << '[';
    if (member_info.is_upper_bound_) {
      result << "<=";
    }
    if (member_info.array_size_ > 0) {
      result << member_info.array_size_;
    }
    result << ']';
  }
  return result.str();
}
#endif  // DYNMSG_VALUE_ONLY

// Get the size of primitive types
size_t
size_of_member_type(uint8_t type_id, const MemberInfo_Cpp & member_info)
{
  DYNMSG_DEBUG(std::cout << "DEBUG: size_of_member_type: " << (int)type_id << std::endl);
  switch (type_id) {
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_FLOAT:
      return sizeof(float);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_DOUBLE:
      return sizeof(double);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_LONG_DOUBLE:
      return sizeof(long double);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_CHAR:
      return sizeof(uint8_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WCHAR:
      return sizeof(uint16_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_BOOLEAN:
      return sizeof(bool);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_OCTET:
      return sizeof(uint8_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8:
      return sizeof(uint8_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT8:
      return sizeof(int8_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT16:
      return sizeof(uint16_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT16:
      return sizeof(int16_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32:
      return sizeof(uint32_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32:
      return sizeof(int32_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_ENUM:
      return sizeof(int32_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT64:
      return sizeof(uint64_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64:
      return sizeof(int64_t);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_STRING:
      if (member_info.string_upper_bound_ > 0) {
        // Since StringStrict capacity does not change the size we can use
        // an arbitrary sized string to get the size
        return sizeof(apex::string_strict256d_t);
      } else {
        return sizeof(std::string);
      }
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WSTRING:
      return sizeof(std::u16string);
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UNION:
      assert(0 && "Cannot get the size of a nested message");
      return 0;
    default:
      assert(0 && "Cannot get the size of an unknown message type");
      return 0;
  }
}

// Convert the binary data for an individual element of an array member to YAML
void member_to_yaml_array_item(
  const MemberInfo_Cpp & member_info,
  const void * member,
  const std::size_t member_idx,
  YAML::Node & array_node,
  std::size_t truncate_length = NO_LENGTH)
{
  DYNMSG_DEBUG(
    std::cout << "DEBUG: member_to_yaml_array_item: " << (int)member_info.type_id_ << std::endl);
  const uint8_t * member_data = nullptr;
  if (rosidl_typesupport_introspection_cpp::ROS_TYPE_STRING == member_info.type_id_) {
    char * tmp = new char[member_info.element_size_function(member, member_idx) + 1];
    member_info.fetch_function(member, member_idx, tmp);
    array_node.push_back(std::string(tmp));
    delete[] tmp;
    return;
  } else if (nullptr != member_info.get_const_function) {
    /* if the type is not a bool (or is a bounded vector of bools), we can use get_const */
    member_data =
      reinterpret_cast<const uint8_t *>(member_info.get_const_function(member, member_idx));
  } else if (rosidl_typesupport_introspection_cpp::ROS_TYPE_BOOLEAN == member_info.type_id_) {
    bool tmp;
    member_info.fetch_function(member, member_idx, &tmp);
    member_data = reinterpret_cast<const uint8_t *>(&tmp);
  } else {
    std::cerr << "WARN: get_const() == nullptr && type_id_ != ROS_TYPE_BOOLEAN" << std::endl;
    array_node.push_back(std::string("???"));
    return;
  }

  switch (member_info.type_id_) {
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_FLOAT:
      array_node.push_back(*reinterpret_cast<const float *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_DOUBLE:
      array_node.push_back(*reinterpret_cast<const double *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_LONG_DOUBLE:
      array_node.push_back(*reinterpret_cast<const long double *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_CHAR:
#ifdef DYNMSG_YAML_CPP_BAD_INT8_HANDLING
      array_node.push_back(std::to_string(*reinterpret_cast<const uint8_t *>(member_data)));
#else
      array_node.push_back(*reinterpret_cast<const uint8_t *>(member_data));
#endif
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WCHAR:
      array_node.push_back(*reinterpret_cast<const uint16_t *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_BOOLEAN:
      array_node.push_back(*reinterpret_cast<const bool *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_OCTET:
#ifdef DYNMSG_YAML_CPP_BAD_INT8_HANDLING
      array_node.push_back(std::to_string(*reinterpret_cast<const uint8_t *>(member_data)));
#else
      array_node.push_back(*reinterpret_cast<const uint8_t *>(member_data));
#endif
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8:
#ifdef DYNMSG_YAML_CPP_BAD_INT8_HANDLING
      array_node.push_back(std::to_string(*reinterpret_cast<const uint8_t *>(member_data)));
#else
      array_node.push_back(*reinterpret_cast<const uint8_t *>(member_data));
#endif
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT8:
#ifdef DYNMSG_YAML_CPP_BAD_INT8_HANDLING
      array_node.push_back(std::to_string(*reinterpret_cast<const int8_t *>(member_data)));
#else
      array_node.push_back(*reinterpret_cast<const int8_t *>(member_data));
#endif
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT16:
      array_node.push_back(*reinterpret_cast<const uint16_t *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT16:
      array_node.push_back(*reinterpret_cast<const int16_t *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32:
      array_node.push_back(*reinterpret_cast<const uint32_t *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32:
      array_node.push_back(*reinterpret_cast<const int32_t *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_ENUM:
      {
        constexpr size_t len{APEX_STRING_SIZE};
        std::array<char, len> member_string{};
        member_info.to_string_function(member_data, member_string.data(), len);
        array_node.push_back(truncate(std::string(member_string.data()), truncate_length));
      }
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT64:
      array_node.push_back(*reinterpret_cast<const uint64_t *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64:
      array_node.push_back(*reinterpret_cast<const int64_t *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WSTRING:
      // WStrings require going through some intermediate formats
      array_node.push_back(
        u16string_to_string(*reinterpret_cast<const std::u16string *>(member_data)));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UNION:
      // For nested types, don't copy the data out of the buffer directly. Recursively read the
      // nested type into the YAML.
      RosMessage_Cpp nested_member;
      nested_member.type_info = reinterpret_cast<const TypeInfo_Cpp *>(member_info.members_->data);
      nested_member.data = const_cast<uint8_t *>(member_data);
      array_node.push_back(message_to_yaml(nested_member));
      break;
    default:
      assert(0 && "Unknown value for unknown type");
      break;
  }
}

// Convert an individual member's value from binary to YAML
void basic_value_to_yaml(
  const MemberInfo_Cpp & member_info,
  const uint8_t * member_data,
  YAML::Node & member,
  std::size_t truncate_length = NO_LENGTH)
{
  DYNMSG_DEBUG(
    std::cout << "DEBUG: basic_value_to_yaml: " <<
      member_info.name_ << ":" << (int)member_info.type_id_ << std::endl);
  // return;
  switch (member_info.type_id_) {
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_FLOAT:
      member["value"] = *reinterpret_cast<const float *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_DOUBLE:
      member["value"] = *reinterpret_cast<const double *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_LONG_DOUBLE:
      member["value"] = *reinterpret_cast<const long double *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_CHAR:
#ifdef DYNMSG_YAML_CPP_BAD_INT8_HANDLING
      member["value"] = std::to_string(*reinterpret_cast<const uint8_t *>(member_data));
#else
      member["value"] = *reinterpret_cast<const uint8_t *>(member_data);
#endif
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WCHAR:
      member["value"] = *reinterpret_cast<const uint16_t *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_BOOLEAN:
      member["value"] = *reinterpret_cast<const bool *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_OCTET:
#ifdef DYNMSG_YAML_CPP_BAD_INT8_HANDLING
      member["value"] = std::to_string(*reinterpret_cast<const uint8_t *>(member_data));
#else
      member["value"] = *reinterpret_cast<const uint8_t *>(member_data);
#endif
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8:
#ifdef DYNMSG_YAML_CPP_BAD_INT8_HANDLING
      member["value"] = std::to_string(*reinterpret_cast<const uint8_t *>(member_data));
#else
      member["value"] = *reinterpret_cast<const uint8_t *>(member_data);
#endif
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT8:
#ifdef DYNMSG_YAML_CPP_BAD_INT8_HANDLING
      member["value"] = std::to_string(*reinterpret_cast<const int8_t *>(member_data));
#else
      member["value"] = *reinterpret_cast<const int8_t *>(member_data);
#endif
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT16:
      member["value"] = *reinterpret_cast<const uint16_t *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT16:
      member["value"] = *reinterpret_cast<const int16_t *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32:
      member["value"] = *reinterpret_cast<const uint32_t *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32:
      member["value"] = *reinterpret_cast<const int32_t *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_ENUM:
      {
        constexpr size_t len{APEX_STRING_SIZE};
        std::array<char, len> member_string{};
        member_info.to_string_function(member_data, member_string.data(), len);
        member["value"] = truncate(std::string(member_string.data()), truncate_length);
      }
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT64:
      member["value"] = *reinterpret_cast<const uint64_t *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64:
      member["value"] = *reinterpret_cast<const int64_t *>(member_data);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_STRING:
      {
        const size_t len = member_info.size_function(member_data) + 1;
        char * const member_string = new char[len];
        member_info.to_string_function(member_data, member_string, len);
        member["value"] = truncate(std::string(member_string), truncate_length);
        delete[] member_string;
      }
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WSTRING:
      // WStrings require going through some intermediate formats
      member["value"] =
        u16string_to_string(*reinterpret_cast<const std::u16string *>(member_data));
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UNION:
      // For nested types, don't copy the data out of the buffer directly. Recursively read the
      // nested type into the YAML.
      RosMessage_Cpp nested_member;
      nested_member.type_info = reinterpret_cast<const TypeInfo_Cpp *>(member_info.members_->data);
      nested_member.data = const_cast<uint8_t *>(member_data);
      member["value"] = message_to_yaml(nested_member,
          truncate_length);
      break;
    default:
      assert(0 && "unknown type");
      break;
  }
}

// Convert a dynamically-sized sequence to YAML - implementation function
void
dynamic_array_to_yaml_impl(
  const MemberInfo_Cpp & member_info,
  const void * member_data,
  YAML::Node & array_node,
  const std::size_t & truncate_length = NO_LENGTH)
{
  DYNMSG_DEBUG(
    std::cout << "DEBUG: dynamic_array_to_yaml_impl: " << (int)member_info.type_id_ << std::endl);
  const std::size_t size = member_info.size_function(member_data);
  for (std::size_t ii = 0; ii < size; ++ii) {
    if (truncate_length != 0 && ii >= truncate_length) {
      array_node.push_back(std::string("..."));
      break;
    }
    member_to_yaml_array_item(
      member_info,
      member_data,
      ii,
      array_node);
  }
}

// Convert a dynamically-sized sequence to YAML
void
dynamic_array_to_yaml(
  const MemberInfo_Cpp & member_info,
  const uint8_t * member_data,
  YAML::Node & array_node,
  const std::size_t & truncate_length = NO_LENGTH)
{
  DYNMSG_DEBUG(
    std::cout << "DEBUG: dynamic_array_to_yaml: " << (int)member_info.type_id_ << std::endl);
  switch (member_info.type_id_) {
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_FLOAT:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_DOUBLE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_LONG_DOUBLE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_CHAR:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WCHAR:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_BOOLEAN:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_OCTET:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT8:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT16:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT16:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_ENUM:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT64:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_STRING:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WSTRING:
      dynamic_array_to_yaml_impl(
        member_info,
        reinterpret_cast<const void *>(member_data),
        array_node,
        truncate_length);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UNION:
      RosMessage_Cpp nested_member;
      nested_member.type_info = reinterpret_cast<const TypeInfo_Cpp *>(member_info.members_->data);
      for (size_t i = 0; i < member_info.size_function(member_data); i++) {
        if (truncate_length != 0 && i >= truncate_length) {
          array_node.push_back(std::string("..."));
          break;
        }
        // Recursively read the nested type into the array element in the YAML representation
        nested_member.data = reinterpret_cast<uint8_t *>(
          member_info.get_function(const_cast<uint8_t *>(member_data), i));
        array_node.push_back(message_to_yaml(nested_member));
      }
      break;
    default:
      assert(0 && "Unknown value for unknown type");
      break;
  }
}

// Convert a dynamically-sized bounded sequence to YAML
void
bounded_array_to_yaml(
  const MemberInfo_Cpp & member_info,
  const uint8_t * member_data,
  YAML::Node & array_node,
  std::size_t truncate_length = NO_LENGTH
)
{
  DYNMSG_DEBUG(
    std::cout << "DEBUG: bounded_array_to_yaml: " << (int)member_info.type_id_ << std::endl);
  switch (member_info.type_id_) {
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_FLOAT:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_DOUBLE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_LONG_DOUBLE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_CHAR:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WCHAR:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_BOOLEAN:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_OCTET:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT8:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT8:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT16:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT16:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT32:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT32:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_ENUM:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UINT64:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_INT64:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_STRING:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_WSTRING:
      dynamic_array_to_yaml_impl(
        member_info,
        reinterpret_cast<const void *>(member_data),
        array_node,
        truncate_length);
      break;
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE:
    case rosidl_typesupport_introspection_cpp::ROS_TYPE_UNION:
      RosMessage_Cpp nested_member;
      nested_member.type_info = reinterpret_cast<const TypeInfo_Cpp *>(member_info.members_->data);
      for (size_t i = 0; i < member_info.size_function(member_data); i++) {
        if (truncate_length != 0 && i >= truncate_length) {
          array_node.push_back(std::string("..."));
          break;
        }
        // Recursively read the nested type into the array element in the YAML representation
        nested_member.data = reinterpret_cast<uint8_t *>(
          member_info.get_function(const_cast<uint8_t *>(member_data), i));
        array_node.push_back(message_to_yaml(nested_member));
      }
      break;
    default:
      assert(0 && "Unknown value for unknown type");
      break;
  }
}

// Convert a fixed-sized sequence (a C-style array) to YAML
void fixed_array_to_yaml(
  const MemberInfo_Cpp & member_info,
  uint8_t * member_data,
  YAML::Node & array_node,
  std::size_t truncate_length = NO_LENGTH)
{
  DYNMSG_DEBUG(
    std::cout << "DEBUG: fixed_array_to_yaml: " << (int)member_info.type_id_ << std::endl);
#ifdef DYNMSG_PARSER_DEBUG
  size_t element_size(0);
  if (member_info.type_id_ == rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE ||
    member_info.type_id_ == rosidl_typesupport_introspection_cpp::ROS_TYPE_UNION)
  {
    element_size = reinterpret_cast<const TypeInfo_Cpp *>(member_info.members_->data)->size_of_;
  } else {
    element_size = size_of_member_type(member_info.type_id_, member_info);
  }
  DYNMSG_DEBUG(std::cout << element_size << ", " << member_info.array_size_ << std::endl);
#endif  // DYNMSG_PARSER_DEBUG
  for (size_t ii = 0; ii < member_info.array_size_; ++ii) {
    if (truncate_length != 0 && ii >= truncate_length) {
      array_node.push_back(std::string("..."));
      break;
    }
    member_to_yaml_array_item(
      member_info,
      reinterpret_cast<void *>(member_data),
      ii,
      array_node,
      truncate_length);
  }
}

// Read one member of a message into a YAML node and return that node
YAML::Node member_to_yaml(
  const MemberInfo_Cpp & member_info,
  uint8_t * member_data,
  std::size_t truncate_length = NO_LENGTH)
{
  DYNMSG_DEBUG(std::cout << "DEBUG: member_to_yaml: " << (int)member_info.type_id_ << std::endl);
  YAML::Node member;
#ifndef DYNMSG_VALUE_ONLY
  member["type"] = member_type_to_string(member_info);
  if (member_info.default_value_ != nullptr) {
    member["default"] = "default value here";
  }
#endif

  if (member_info.is_array_) {
    YAML::Node array;
    if (member_info.is_upper_bound_) {
      bounded_array_to_yaml(member_info, member_data, array, truncate_length);
    } else if (member_info.array_size_ == 0) {
      dynamic_array_to_yaml(member_info, member_data, array, truncate_length);
    } else {
      fixed_array_to_yaml(member_info, member_data, array, truncate_length);
    }
#ifdef DYNMSG_VALUE_ONLY
    return array;
#else
    member["value"] = array;
#endif
  } else {
    if (member_info.type_id_ == rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE ||
      member_info.type_id_ == rosidl_typesupport_introspection_cpp::ROS_TYPE_UNION)
    {
      RosMessage_Cpp nested_member;
      nested_member.type_info = reinterpret_cast<const TypeInfo_Cpp *>(member_info.members_->data);
      nested_member.data = const_cast<uint8_t *>(member_data);
#ifdef DYNMSG_VALUE_ONLY
      return message_to_yaml(nested_member, truncate_length);
#else
      member["value"] = message_to_yaml(nested_member, truncate_length);
#endif
    } else {
      basic_value_to_yaml(member_info, member_data, member, truncate_length);
#ifdef DYNMSG_VALUE_ONLY
      return member["value"];
#endif
    }
  }

  return member;
}

}  // namespace impl

YAML::Node
message_to_yaml(
  const RosMessage_Cpp & message,
  std::size_t truncate_length)
{
  YAML::Node yaml_msg;

  DYNMSG_DEBUG(std::cout << "DEBUG: message_to_yaml" << std::endl);
  DYNMSG_DEBUG(
    std::cout << "DEBUG: message.type_info message_namespace_: " <<
      message.type_info->message_namespace_ << std::endl);
  DYNMSG_DEBUG(
    std::cout << "DEBUG: message.type_info message_name_: " <<
      message.type_info->message_name_ << std::endl);

  // Iterate over the members of the message, converting the binary data for each into a node in
  // the YAML representation
  for (uint32_t ii = 0; ii < message.type_info->member_count_; ++ii) {
    // Get the introspection information for this particular member
    const MemberInfo_Cpp & member_info = message.type_info->members_[ii];
    DYNMSG_DEBUG(std::cout << "DEBUG: member_info name: " << member_info.name_ << std::endl);

    // Use union_member_get_function function to get a pointer to the union member's data
    uint8_t * member_data = nullptr;
    if (nullptr != member_info.union_member_get_function) {
      auto raw_data = member_info.union_member_get_function(message.data);
      if (nullptr == raw_data) {
        continue;
      }
      member_data = reinterpret_cast<uint8_t *>(raw_data);
    } else {
      // Get a pointer to the member's data in the binary buffer
      member_data = &message.data[member_info.offset_];
    }
    // Recursively (because some members may be non-primitive types themselves) convert the member
    // to YAML
    yaml_msg[member_info.name_] = impl::member_to_yaml(member_info, member_data, truncate_length);
  }
  return yaml_msg;
}

}  // namespace cpp
}  // namespace dynmsg
