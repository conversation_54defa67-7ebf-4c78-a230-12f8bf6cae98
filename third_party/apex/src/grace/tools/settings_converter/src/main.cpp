// Copyright 2020 Apex.AI, Inc.
// All rights reserved.

#include <boost/program_options.hpp>
#include <boost/filesystem.hpp>

#include <cpputils/common_exceptions.hpp>
#include <settings_converter/generator.hpp>
#include <settings_converter/transformer.hpp>

#include <iostream>
#include <string>

namespace po = boost::program_options;

int main(const int argc, char ** const argv)
{
  std::string input_file;
  std::string output_directory;
  std::string output_name;
  std::string name_space;
  bool disable_deduction{false};
  bool only_transform{false};
  std::size_t double_precision{8U};

  po::options_description desc("Allowed options");
  desc.add_options()("help,h", "print usage message")("input,i",
    po::value<std::string>(&input_file), "input yaml file to parse")("outdir,o",
    po::value<std::string>(&output_directory)->default_value(boost::filesystem::current_path().
    string()),
    "path for output files")("output-name,n",
    po::value<std::string>(&output_name)->default_value("my_settings"),
    "output files name stem")("namespace,s",
    po::value<std::string>(&name_space)->default_value("my_settings"),
    "namespace to generate settings in")("disable-deduction,d",
    po::bool_switch(&disable_deduction)->default_value(false),
    "disable value type deduction for yaml")("double-precision,f",
    po::value<std::size_t>(&double_precision)->default_value(double_precision),
    "The precision of floating point conversions")("only-transform",
    po::bool_switch(&only_transform)->default_value(false),
    "Only transform the input dictionary by processing the commands in the yaml");

  try {
    po::variables_map vm;
    po::store(parse_command_line(argc, argv, desc), vm);
    po::notify(vm);

    if (vm.count("help")) {
      std::cout << desc << "\n";
      return 0;
    }

    if (input_file.empty()) {
      throw apex::runtime_error("no input file specified");
    }

    if (output_name.empty()) {
      throw apex::runtime_error("no output files name specified");
    }

    if (only_transform) {
      #ifdef WITH_SETTINGS_EXTENSIONS_SUPPORT
      apex::settings_converter::transform_file(input_file, output_directory, output_name,
        disable_deduction);
      #else
      throw apex::runtime_error{
        "Option --only-transform is not available in Apex.OS Cert (#18416)"};
      #endif
    } else {
      apex::settings_converter::generate(input_file, output_directory, output_name, name_space,
        double_precision, disable_deduction);
    }
  } catch (const std::exception & e) {
    std::cerr << "Error: " << e.what() << "\n";
    return 1;
  }

  return 0;
}
