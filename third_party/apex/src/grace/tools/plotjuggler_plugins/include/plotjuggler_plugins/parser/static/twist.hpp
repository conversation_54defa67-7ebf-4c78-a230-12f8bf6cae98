// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#ifndef PLOTJUGGLER_PLUGINS__PARSER__STATIC__TWIST__HPP_
#define PLOTJUGGLER_PLUGINS__PARSER__STATIC__TWIST__HPP_

#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/twist_with_covariance.hpp>

#include <string>
#include <vector>

#include "plotjuggler_plugins/parser/static_parser_base.hpp"
#include "plotjuggler_plugins/parser/static/quaternion.hpp"
#include "plotjuggler_plugins/parser/static/header.hpp"

namespace apex
{
namespace plotjuggler
{

class TwistMsgParser : public StaticParserBase<geometry_msgs::msg::Twist>
{
public:
  TwistMsgParser(const std::string & topic_name, PJ::PlotDataMapRef & plot_data)
  : StaticParserBase<geometry_msgs::msg::Twist>(topic_name, plot_data)
  {
  }

  void parseMessageImpl(const geometry_msgs::msg::Twist & msg, double & timestamp) override
  {
    if (!_initialized) {
      _initialized = true;
      m_data.push_back(&getSeries(_topic_name + "/linear/x"));
      m_data.push_back(&getSeries(_topic_name + "/linear/y"));
      m_data.push_back(&getSeries(_topic_name + "/linear/z"));

      m_data.push_back(&getSeries(_topic_name + "/angular/x"));
      m_data.push_back(&getSeries(_topic_name + "/angular/y"));
      m_data.push_back(&getSeries(_topic_name + "/angular/z"));
    }

    m_data[0]->pushBack({timestamp, msg.linear.x});
    m_data[1]->pushBack({timestamp, msg.linear.y});
    m_data[2]->pushBack({timestamp, msg.linear.z});

    m_data[3]->pushBack({timestamp, msg.angular.x});
    m_data[4]->pushBack({timestamp, msg.angular.y});
    m_data[5]->pushBack({timestamp, msg.angular.z});
  }

private:
  std::vector<PJ::PlotData *> m_data;
  bool _initialized = false;
};

class TwistStampedMsgParser : public StaticParserBase<geometry_msgs::msg::TwistStamped>
{
public:
  TwistStampedMsgParser(const std::string & topic_name, PJ::PlotDataMapRef & plot_data)
  : StaticParserBase<geometry_msgs::msg::TwistStamped>(topic_name, plot_data),
    _header_parser(topic_name + "/header", plot_data),
    m_twist_parser(topic_name + "/twist", plot_data)
  {
  }

  void parseMessageImpl(const geometry_msgs::msg::TwistStamped & msg, double & timestamp) override
  {
    _header_parser.parse(msg.header, timestamp, use_header_stamp());
    m_twist_parser.parseMessageImpl(msg.twist, timestamp);
  }

private:
  HeaderMsgParser _header_parser;
  TwistMsgParser m_twist_parser;
};

class TwistCovarianceMsgParser : public StaticParserBase<geometry_msgs::msg::TwistWithCovariance>
{
public:
  TwistCovarianceMsgParser(const std::string & topic_name, PJ::PlotDataMapRef & plot_data)
  : StaticParserBase<geometry_msgs::msg::TwistWithCovariance>(topic_name, plot_data),
    m_twist_parser(topic_name + "/twist", plot_data),
    m_covariance(topic_name + "/covariance", plot_data)
  {
  }

  void parseMessageImpl(
    const geometry_msgs::msg::TwistWithCovariance & msg,
    double & timestamp) override
  {
    m_twist_parser.parseMessageImpl(msg.twist, timestamp);
    m_covariance.parse(msg.covariance, timestamp);
  }

private:
  TwistMsgParser m_twist_parser;
  CovarianceParser<6> m_covariance;
};

}  // namespace plotjuggler
}  // namespace apex

#endif  // PLOTJUGGLER_PLUGINS__PARSER__STATIC__TWIST__HPP_
