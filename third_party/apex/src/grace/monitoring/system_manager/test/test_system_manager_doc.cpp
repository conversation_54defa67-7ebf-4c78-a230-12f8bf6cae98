// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include <memory>  // NOLINT

#include "event_registry/event_registry.hpp"
#include "example_app.hpp"
#include "executor2/executor_factory.hpp"
#include "executor2/executor_runner.hpp"
#include "rclcpp/rclcpp.hpp"
#include "system_manager/system_manager.hpp"
#include "system_manager/system_manager_main.hpp"
#include "timer_service/clock_timer_service.hpp"

namespace
{
class system_manager_test_doc : public ::testing::Test
{
public:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
  }

  void TearDown() override
  {
    (void)rclcpp::shutdown();
  }
};
// cppcheck-suppress syntaxError
TEST_F(system_manager_test_doc, add_custom_events)
{
  //! [add_custom_events]
  class custom_system_manager : public apex::system_manager::system_manager
  {
  public:
    using apex::system_manager::system_manager::system_manager;

  private:
    void register_event_handlers(apex::dispatcher::local_dispatcher_base & disp) override
    {
      disp.add_handler(
        [this](apex::dispatcher::types::event_cref_t ev) {
          std::cout << "My custom handler";
          log_event(ev);
        },
        apex::dispatcher::filter{{example_app::AppId.data()}, {}, {example_app::event::AppStart}});
    }
  };

  apex::system_manager::settings s;
  apex::timer_service::steady_clock_timer_service timer_srv;
  custom_system_manager sm{s, timer_srv};
  const auto exec = apex::executor::executor_factory::create();
  const auto com_exec = apex::executor::executor_factory::create();
  sm.register_for_execution(*exec, *com_exec);
  apex::executor::executor_runner r{*exec};
  //! [add_custom_events]
}

TEST_F(system_manager_test_doc, custom_on_error)
{
  //! [custom_error]
  class custom_system_manager : public apex::system_manager::system_manager
  {
  public:
    using apex::system_manager::system_manager::system_manager;

  private:
    void on_error(apex::dispatcher::types::event_cref_t ev) override
    {
      std::cout << "Error!\n";
      log_event(ev);
      pm_group_change_state("my_group", "my_error_state");
    }
  };
  //! [custom_error]
}

TEST_F(system_manager_test_doc, custom_on_unexpected_task)
{
  //! [custom_unexpected]
  class custom_system_manager : public apex::system_manager::system_manager
  {
  public:
    using apex::system_manager::system_manager::system_manager;

  private:
    void on_unexpected_exit(apex::dispatcher::types::event_cref_t ev) override
    {
      std::cout << "Unexpected exit from process with PID" << ev.int_data << "\n";
      log_event(ev);
      pm_group_change_state("my_group", "my_another_state");
    }
  };
  //! [custom_unexpected]
}

TEST_F(system_manager_test_doc, custom_on_infraction)
{
  //! [custom_infraction]
  class custom_system_manager : public apex::system_manager::system_manager
  {
  public:
    using apex::system_manager::system_manager::system_manager;

  private:
    void on_infraction(const infraction_t & infraction) override
    {
      std::cout << "Ignoring an infraction in the process with PID" << infraction.pid << "\n";
    }
  };
  //! [custom_infraction]
}

TEST_F(system_manager_test_doc, running_custom_system_manager)
{
  //! [running_custom_system_manager]
  using namespace apex::system_manager;  // NOLINT
  using namespace apex::timer_service;  // NOLINT
  using apex::settings::inspect::dictionary_view;

  // Custom system manager implementation
  class custom_system_manager : public system_manager
  {
  public:
    using system_manager::system_manager;

  private:
    void register_event_handlers(apex::dispatcher::local_dispatcher_base &) override
    { /*...*/
    }
    void on_error(apex::dispatcher::types::event_cref_t) override
    { /*...*/
    }
    void on_infraction(const infraction_t &) override
    { /*...*/
    }
    void on_unexpected_exit(apex::dispatcher::types::event_cref_t) override
    { /*...*/
    }
    void on_unexpected_exit_from_framework_group(
      apex::dispatcher::types::event_cref_t) override /*...*/
    {
    }
    void on_kick_watchdog() override
    { /*...*/
    }
    void on_shutdown(shutdown_reason) override
    { /*...*/
    }
  };

  // System manager factory that produces the custom system manager
  class custom_system_manager_factory : public system_manager_factory
  {
  public:
    system_manager_ptr create_system_manager(const dictionary_view & settings_root) override
    {
      // The timer service specific type can be customized here
      m_timer_srv = std::make_unique<steady_clock_timer_service>();
      return std::make_unique<custom_system_manager>(settings_root, *m_timer_srv);
    }

  private:
    timer_service_ptr m_timer_srv;
  };

  /* The executable's main() function that makes use of the custom system manager:

  std::int32_t main(const std::int32_t argc, char ** const argv) {
    custom_system_manager_factory factory;
    return apex::system_manager::exe_main(factory, argc, argv);
  }

  */
  //! [running_custom_system_manager]
}

}  // namespace
