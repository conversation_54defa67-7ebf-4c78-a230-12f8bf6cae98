/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef DISPATCHER__REMOTE_DISPATCHER_BASE_HPP
#define DISPATCHER__REMOTE_DISPATCHER_BASE_HPP

#include <chrono>
#include <utility>
#include <vector>

#include "dispatcher/filter.hpp"
#include "dispatcher/types.hpp"
#include "dispatcher/visibility.hpp"

namespace apex::dispatcher
{
/// \brief The interface for remote dispatchers
/// \cert
class DISPATCHER_PUBLIC remote_dispatcher_base
{
public:
  remote_dispatcher_base() = default;
  remote_dispatcher_base(const remote_dispatcher_base &) = delete;
  remote_dispatcher_base & operator=(const remote_dispatcher_base &) = delete;
  virtual ~remote_dispatcher_base() = default;

  /// \brief Add a remote event handler to the dispatcher
  /// \param service_name The name of the remote service
  /// \param timeout How long to wait for the service in the worst case
  /// \param optional Whether this handler is optional
  /// \param flts The filter list for the handler
  /// \cert
  virtual void add_remote_handler(const std::string & service_name,
                                  std::chrono::nanoseconds timeout,
                                  bool optional,
                                  const std::vector<filter> & flts) = 0;

  /// \brief Add a remote event handler to the dispatcher
  /// \param service_name The name of the remote service
  /// \param timeout How long to wait for the service in the worst case
  /// \param optional Whether this handler is optional
  /// \param flt The filter for the handler
  /// \cert
  void add_remote_handler(const std::string & service_name,
                          std::chrono::nanoseconds timeout,
                          bool optional,
                          const filter & flt)
  {
    add_remote_handler(service_name, timeout, optional, std::vector<filter>{flt});
  }
};
}  // namespace apex::dispatcher

#endif  // DISPATCHER__REMOTE_DISPATCHER_BASE_HPP
