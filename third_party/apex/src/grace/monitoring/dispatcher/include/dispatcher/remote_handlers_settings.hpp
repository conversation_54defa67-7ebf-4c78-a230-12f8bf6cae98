/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef DISPATCHER__REMOTE_HANDLERS_SETTINGS_HPP
#define DISPATCHER__REMOTE_HANDLERS_SETTINGS_HPP

#include "dispatcher/remote_handlers_registrator.hpp"
#include "dispatcher/visibility.hpp"
#include "settings/inspect.hpp"

namespace apex::dispatcher::settings
{
using dictionary_view = apex::settings::inspect::dictionary_view;

/// \brief Iterates over the external handlers section of the config file
/// and collects all the filters and timeouts for all the active handlers
/// \cert
DISPATCHER_PUBLIC
handlers::external_desc_t get_external_handlers(const dictionary_view & dict);
}  // namespace apex::dispatcher::settings

#endif  // DISPATCHER__REMOTE_HANDLERS_SETTINGS_HPP
