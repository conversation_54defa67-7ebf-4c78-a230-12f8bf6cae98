/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include "dispatcher/dispatcher_par.hpp"

#include <cstdint>
#include <map>
#include <mutex>
#include <string>

#include "rclcpp/rclcpp.hpp"
#include "threading/thread_attributes.hpp"
#include "threading/unlock_guard.hpp"

namespace apex::dispatcher
{
dispatcher_par::dispatcher_par(
  std::size_t thread_count,
  const std::string & name /* = common::get_default_instance_name() */,
  const std::string & name_space /*= e.g. ""*/,
  apex::threading::thread_attributes attrs /*= e.g. apex::threading::thread_attributes::build()*/,
  std::chrono::nanoseconds workers_feedback_timeout /*= e.g. std::chrono::seconds{1}*/
  )
: base{name, name_space}, m_workers_feedback_timeout{workers_feedback_timeout}
{
  m_workers.reserve(thread_count);
  for (auto i = 0U; i < thread_count; ++i) {
    m_workers.emplace_back([this] { worker(); }, attrs);
  }
}

// Exception leads to terminate as intended
//    If this panics, the destructor's noexcept specification  will be violated, leading to
//    std::terminate. This is intentional, as the program is in an unrecoverable/undefined state
//    at this point but is not allowed to throw from the destructor
dispatcher_par::~dispatcher_par()
{
  for (auto & worker : m_workers) {
    if (worker.valid() && worker.joinable()) {
      worker.join();
    }
  }
}

void dispatcher_par::run_impl(std::int64_t event_count)
{
  m_events_count = event_count;
  for (auto & worker : m_workers) {
    worker.issue();
    m_active_workers.acquire();
  }
  while (true) {
    std::unique_lock l{m_work_mutex};
    if (m_break) {
      l.unlock();
      m_work_cv.notify_all();
      break;
    }
    m_ws->wait();
    if ((*m_ws)[m_stop]) {
      m_break = true;
      l.unlock();
      m_work_cv.notify_all();
      break;
    }
    if (!m_free_workers_cv.wait_for(
          l, m_workers_feedback_timeout, [this] { return m_break || (m_free_workers > 0U); })) {
      APEX_ERROR(m_logger,
                 apex::no_separator{},
                 "One or more worker threads is stuck, terminating the process...");
      std::terminate();
    }
    if (!m_break) {
      l.unlock();
      m_work_cv.notify_one();
    }
  }  // while(true)

  if (!m_active_workers.wait_for(m_workers_feedback_timeout)) {
    APEX_ERROR(m_logger,
               apex::no_separator{},
               "One or more worker threads is stuck on shutdown, terminating the process...");
    std::terminate();
  }
}

void dispatcher_par::worker()
{
  while (true) {
    std::unique_lock l{m_work_mutex};
    m_free_workers++;
    l.unlock();
    m_free_workers_cv.notify_one();
    l.lock();
    rclcpp::LoanedSamples<types::event_t::BorrowedType> sample;
    const auto get_sample = [&sample, this] {
      sample = m_event_sub->take(1);
      return !sample.empty();
    };

    m_work_cv.wait(l, [&get_sample, this] { return m_break || get_sample(); });
    m_free_workers--;
    if (m_break) {
      break;
    }
    if (sample[0].info().valid()) {
      const auto ev = sample[0].data();
      if (m_events_count > 0) {
        ++m_events_processed;
        if (m_events_processed == m_events_count) {
          m_break = true;
        }
      }
      l.unlock();
      call_handlers(ev);
    }
  }
  m_active_workers.release();
}

void dispatcher_par::call_handlers(types::event_cref_t ev) const
{
  std::unique_lock lock{m_mutex};
  call_handlers_for_app("", ev, lock);
  if (!ev.app_id.empty()) {
    call_handlers_for_app(ev.app_id, ev, lock);
  }
}

void dispatcher_par::call_handlers_for_app(const types::app_id_t & app_id,
                                           types::event_cref_t ev,
                                           std::unique_lock<std::mutex> & lock) const
{
  const auto iter = m_handlers.find(app_id);
  if (iter != m_handlers.end()) {
    call_handlers_for_instances(iter->second, ev, lock);
  }
}

void dispatcher_par::call_handlers_for_instances(const instance_to_event_map_t & instances,
                                                 types::event_cref_t ev,
                                                 std::unique_lock<std::mutex> & lock) const
{
  call_handlers_for_instance(0U, instances, ev, lock);
  if (ev.instance_id != 0U) {
    call_handlers_for_instance(ev.instance_id, instances, ev, lock);
  }
}

void dispatcher_par::call_handlers_for_instance(const types::instance_id_t & instance_id,
                                                const instance_to_event_map_t & instances,
                                                types::event_cref_t ev,
                                                std::unique_lock<std::mutex> & lock) const
{
  const auto iter = instances.find(instance_id);
  if (iter != instances.end()) {
    call_handlers_for_events(iter->second, ev, lock);
  }
}

void dispatcher_par::call_handlers_for_events(const event_to_hnd_map_t & events,
                                              types::event_cref_t ev,
                                              std::unique_lock<std::mutex> & lock) const
{
  call_handlers_for_event(0U, events, ev, lock);
  if (ev.event_id != 0U) {
    call_handlers_for_event(ev.event_id, events, ev, lock);
  }
}

void dispatcher_par::call_handlers_for_event(const types::event_id_t & event_id,
                                             const event_to_hnd_map_t & events,
                                             types::event_cref_t ev,
                                             std::unique_lock<std::mutex> & lock) const
{
  const auto iter = events.find(event_id);
  if (iter != events.end()) {
    execute_handlers(iter->second, ev, lock);
  }
}

void dispatcher_par::execute_handlers(const handler_ptr_list_t & hnds,
                                      types::event_cref_t ev,
                                      std::unique_lock<std::mutex> & lock) const
{
  for (auto i = 0U; i < hnds.size(); ++i) {
    const auto hnd = hnds[i];
    threading::unlock_guard ul{lock};
    execute_and_log_errors(*hnd, ev);
  }
}
}  // namespace apex::dispatcher
