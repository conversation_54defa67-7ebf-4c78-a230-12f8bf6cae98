load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "ROSIDL_COMMON_PKGS", "msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "event_registry_interfaces_pkg",
    description = "A package containing containing interfaces for the event registry.",
    license = "Apex.AI",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    msg_libraries = [
        ":event_registry_interfaces",
    ],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ] + ROSIDL_COMMON_PKGS,
)

msgs_library(
    name = "event_registry_interfaces",
    srcs = glob([
        "msg/*.idl",
    ]),
    visibility = ["//visibility:public"],
    wheel_data = ":event_registry_interfaces_pkg.wheel_data",
)

filegroup(
    name = "doc_files",
    srcs = [
        "msg/AppEventsDefinition.idl",
        "msg/EventDefinition.idl",
        "msg/EventRegistryDefinition.idl",
        "msg/InstanceDefinition.idl",
    ],
    visibility = [
        "//grace/monitoring/dispatcher/design:__subpackages__",
        "//grace/monitoring/event/design:__subpackages__",
        "//grace/monitoring/event_registry/design:__subpackages__",
    ],
)
