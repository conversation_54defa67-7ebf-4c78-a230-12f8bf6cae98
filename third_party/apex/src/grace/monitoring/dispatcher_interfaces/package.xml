<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>dispatcher_interfaces</name>
  <version>0.0.1</version>
  <description>A package containing interfaces for the event system</description>
  <maintainer email="<EMAIL>">Apex.AI</maintainer>
  <license>Apex.AI</license>

  <depend>rclcpp</depend>
  <depend>process_manager_interfaces</depend>
  <depend>storage_msgs</depend>

  <buildtool_depend>rosidl_default_generators</buildtool_depend>
  <buildtool_depend>ament_cmake_auto</buildtool_depend>
  <buildtool_depend>apex_cmake</buildtool_depend>

  <exec_depend>rosidl_default_runtime</exec_depend>

  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_lint_auto</test_depend>

  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
