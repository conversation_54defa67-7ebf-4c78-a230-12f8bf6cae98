/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#include "system_status/json.hpp"

#include <string>
#include <utility>

#include "event_registry/event_registry.hpp"
#include "system_status/common.hpp"

namespace apex::system_status::json
{
json to_json(types::event_cref_t ev)
{
  json result;
  if (ev.event_id != 0) {
    result["id"] = ev.event_id;
    std::string name;
    if (event::is_event_registered(ev.app_id.c_str(), ev.event_id)) {
      name = event::get_event_name(ev.app_id.c_str(), ev.event_id);
    }
    result["name"] = std::move(name);
    result["pid"] = ev.pid;
    result["app-id"] = ev.app_id;
    if (event::is_app_registered(ev.app_id.c_str())) {
      name = event::get_app_name(ev.app_id.c_str());
    } else {
      name = std::string{};
    }
    result["executor-id"] = ev.executor_id;
    result["app-name"] = std::move(name);
    result["instance-id"] = ev.instance_id;
    if (event::is_app_instance_registered(ev.app_id.c_str(), ev.instance_id)) {
      name = event::get_app_instance_name(ev.app_id.c_str(), ev.instance_id);
    } else {
      name = std::string{};
    }
    result["instance-name"] = std::move(name);
    result["int-data"] = ev.int_data;
    result["string-data"] = ev.string_data;
    json metadata;
    metadata["group-name"] = ev.process_manager_metadata.group_name;
    metadata["process-name"] = ev.process_manager_metadata.process_name;
    metadata["id"] = ev.process_manager_metadata.id;
    result["process-manager-metadata"] = std::move(metadata);
  }
  return result;
}

json to_json(const types::process_map & map)
{
  json result;
  result["processes"] = json::array();
  for (const auto & process : map) {
    json j_process;
    j_process["pid"] = process.first;
    j_process["name"] = process.second.name;
    j_process["last-event"] = to_json(process.second.event);
    j_process["executors"] = json::array();
    for (const auto & exec : process.second.executors) {
      json j_executor;
      j_executor["id"] = exec.first;
      j_executor["last-event"] = to_json(exec.second.event);
      j_executor["parent-status"] = types::parent_status::to_string(exec.second.parent_status);
      j_executor["apps"] = json::array();
      for (const auto & app : exec.second.apps) {
        json j_app;
        j_app["id"] = app.first;
        std::string name;
        if (event::is_app_registered(app.first.c_str())) {
          name = event::get_app_name(app.first);
        } else {
          name = std::string{};
        }
        j_app["name"] = std::move(name);
        j_app["instances"] = json::array();
        for (const auto & inst : app.second) {
          json j_instance;
          j_instance["id"] = inst.first;
          if (event::is_app_instance_registered(app.first.c_str(), inst.first)) {
            name = event::get_app_instance_name(app.first.c_str(), inst.first);
          } else {
            name = std::string{};
          }
          j_instance["name"] = std::move(name);
          j_instance["last-event"] = to_json(inst.second.event);
          j_instance["parent-status"] = types::parent_status::to_string(inst.second.parent_status);
          j_app["instances"].push_back(j_instance);
        }
        j_executor["apps"].push_back(std::move(j_app));
      }
      j_process["executors"].push_back(std::move(j_executor));
    }
    result["processes"].push_back(std::move(j_process));
  }
  return result;
}

json to_json(const types::system_status & status)
{
  auto result = to_json(status.processes);
  auto & ts = result["timestamp"];
  const auto t = status.timestamp.time_since_epoch();
  const auto t_seconds = std::chrono::duration_cast<std::chrono::seconds>(t);
  const auto t_nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(t - t_seconds);
  ts["seconds"] = t_seconds.count();
  ts["nanoseconds"] = t_nanoseconds.count();
  ts["datetime"] = common::to_time_string(status.timestamp);
  return result;
}

}  // namespace apex::system_status::json
