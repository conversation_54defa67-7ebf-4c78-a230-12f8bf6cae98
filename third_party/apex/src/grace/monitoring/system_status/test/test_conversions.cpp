// Copyright 2023 Apex.AI, Inc.
// All rights reserved.

#include <limits>
#include <random>
#include <sstream>
#include <string>
#include <vector>

#include "gtest/gtest.h"
#include "system_status/common.hpp"
#include "system_status/json.hpp"
#include "system_status/types.hpp"

namespace types = apex::system_status::types;
namespace json = apex::system_status::json;
namespace common = apex::system_status::common;
using apex::system_status::common::from_message;
using apex::system_status::common::to_message;

namespace
{
class test_conversions : public ::testing::Test
{
protected:
  template <class T = std::int32_t>
  T get_pseudo_random_int(const T & min = std::numeric_limits<T>::min(),
                          const T & max = std::numeric_limits<T>::max())
  {
    std::uniform_int_distribution<T> distrib(min, max);
    return distrib(gen);
  }

  std::string get_pseudo_random_string(std::size_t min = 1, std::size_t max = 255)
  {
    std::stringstream s;
    auto length = get_pseudo_random_int<std::size_t>(min, max);
    while (length != 0U) {
      s << get_pseudo_random_int<char>(32, 126);
      --length;
    }
    return s.str();
  }

  types::event_t create_pseudo_random_event()
  {
    types::event_t result;
    result.app_id = get_pseudo_random_string(1, 32);
    result.instance_id = get_pseudo_random_int<types::instance_id_t>(1);
    result.pid = get_pseudo_random_int<types::pid_t>(1);
    result.event_id = get_pseudo_random_int<types::event_id_t>(1);
    result.int_data = get_pseudo_random_int<types::int_data_t>(1);
    result.string_data = get_pseudo_random_string();
    return result;
  }

  types::parent_status::type create_pseudo_random_parent_status()
  {
    return get_pseudo_random_int<types::parent_status::type>(types::parent_status::Unknown,
                                                             types::parent_status::Error);
  }

  types::process_map create_pseudo_random_map()
  {
    types::process_map map;
    for (auto process_count = 0; process_count < get_pseudo_random_int(20, 50); ++process_count) {
      const auto process_ev = create_pseudo_random_event();
      auto & process_entry = map[process_ev.pid];
      process_entry.name = get_pseudo_random_string(1, 100);
      process_entry.event = process_ev;
      for (auto executor_count = 0; executor_count < get_pseudo_random_int(20, 50);
           ++executor_count) {
        const auto exec_ev = create_pseudo_random_event();
        auto & exec_entry = process_entry.executors[exec_ev.int_data];
        exec_entry.event = exec_ev;
        exec_entry.parent_status = create_pseudo_random_parent_status();
        for (auto app_count = 0; app_count < get_pseudo_random_int(20, 50); ++app_count) {
          auto app_event = create_pseudo_random_event();
          auto & app_entry = exec_entry.apps[app_event.app_id];
          for (auto inst_count = 0; inst_count < get_pseudo_random_int(20, 50); ++inst_count) {
            auto & inst_entry = app_entry[app_event.instance_id];
            inst_entry.event = app_event;
            inst_entry.parent_status = create_pseudo_random_parent_status();
            app_event.instance_id = get_pseudo_random_int(1, 255);
          }
        }
      }
    }
    return map;
  }

  types::event_t event_from_json(const json::json & j) const
  {
    types::event_t result;
    if (!j.is_null()) {
      result.event_id = j["id"].get<types::event_id_t>();
      result.pid = j["pid"].get<types::pid_t>();
      result.executor_id = j["executor-id"].get<types::executor_id_t>();
      result.app_id = j["app-id"].get<types::app_id_t>();
      result.instance_id = j["instance-id"].get<types::instance_id_t>();
      result.int_data = j["int-data"].get<types::int_data_t>();
      result.string_data = j["string-data"].get<types::string_data_t>();
      result.process_manager_metadata.group_name =
        j["process-manager-metadata"]["group-name"].get<types::string_data_t>();
      result.process_manager_metadata.process_name =
        j["process-manager-metadata"]["process-name"].get<types::string_data_t>();
      result.process_manager_metadata.id = j["process-manager-metadata"]["id"].get<types::pid_t>();
      ;
    }
    return result;
  }

  types::system_status from_json(const json::json & j) const
  {
    types::system_status result;
    const std::chrono::seconds sec{j["timestamp"]["seconds"].get<std::int32_t>()};
    const std::chrono::nanoseconds nsec{j["timestamp"]["nanoseconds"].get<std::int32_t>()};
    result.timestamp = types::time_point_t{nsec + sec};
    if (common::to_time_string(result.timestamp) != j["timestamp"]["datetime"].get<std::string>()) {
      throw std::runtime_error{"mismatch"};
    }
    auto & map = result.processes;
    for (const auto & j_process : j["processes"]) {
      auto & process_entry = map[j_process["pid"].get<types::pid_t>()];
      process_entry.name = j_process["name"].get<std::string>();
      process_entry.executors.reserve(j_process["executors"].size());
      process_entry.event = event_from_json(j_process["last-event"]);
      for (const auto & j_executor : j_process["executors"]) {
        auto & executor_entry =
          process_entry.executors[j_executor["id"].get<types::executor_id_t>()];
        executor_entry.event = event_from_json(j_executor["last-event"]);
        executor_entry.parent_status =
          types::parent_status::from_string(j_executor["parent-status"].get<std::string>());
        for (const auto & j_app : j_executor["apps"]) {
          auto & app_entry = executor_entry.apps[j_app["id"].get<types::app_id_t>()];
          for (const auto & j_inst : j_app["instances"]) {
            auto & inst_entry = app_entry[j_inst["id"].get<types::instance_id_t>()];
            inst_entry.event = event_from_json(j_inst["last-event"]);
            inst_entry.parent_status =
              types::parent_status::from_string(j_inst["parent-status"].get<std::string>());
          }
        }
      }
    }
    return result;
  }

  types::time_point_t get_pseudo_random_timestamp()
  {
    std::chrono::nanoseconds dur{get_pseudo_random_int<std::chrono::nanoseconds::rep>(
      1,
      std::chrono::duration_cast<std::chrono::nanoseconds>(
        std::chrono::seconds{std::numeric_limits<std::int32_t>::max()})
        .count())};
    return types::time_point_t{dur};
  }

  types::system_status create_pseudo_random_status()
  {
    types::system_status result;
    result.processes = create_pseudo_random_map();
    result.timestamp = get_pseudo_random_timestamp();
    return result;
  }

  std::mt19937 gen{1};
};

TEST_F(test_conversions, conversions)
{
  const auto status = create_pseudo_random_status();
  const auto msg = to_message(status);
  const auto status2 = from_message(msg);
  ASSERT_EQ(status, status2);
}

TEST_F(test_conversions, partial_entries)
{
  types::system_status status;
  status.timestamp = get_pseudo_random_timestamp();
  auto & map = status.processes;
  auto process_event = create_pseudo_random_event();
  map[process_event.pid].event = process_event;
  ++process_event.pid;
  map[process_event.pid].event = process_event;
  auto exec_event = create_pseudo_random_event();
  map[process_event.pid].executors[exec_event.int_data].event = exec_event;
  const auto msg = to_message(status);
  const auto status2 = from_message(msg);
  ASSERT_EQ(status, status2);
}

TEST_F(test_conversions, out_of_order_entries)
{
  types::system_status status;
  status.timestamp = get_pseudo_random_timestamp();
  auto & map = status.processes;
  auto ev = create_pseudo_random_event();
  map[ev.pid].executors[ev.int_data].apps[ev.app_id][ev.instance_id].event = ev;
  ++ev.pid;
  map[ev.pid].executors[ev.int_data].event = ev;
  const auto msg = to_message(status);
  const auto status2 = from_message(msg);
  ASSERT_EQ(status, status2);
}

TEST_F(test_conversions, json_conversion)
{
  const auto status = create_pseudo_random_status();
  const auto j = json::to_json(status);
  const auto status2 = from_json(j);
  ASSERT_EQ(status, status2);
}

TEST_F(test_conversions, json_empty_cases)
{
  {
    types::system_status status;
    const auto j = json::to_json(status);
    const auto status2 = from_json(j);
    ASSERT_EQ(status, status2);
  }
  {
    types::system_status status;
    auto & map = status.processes;
    map[1].event.event_id = 0;
    map[2].executors[1].event.event_id = 0;
    map[3].executors[2].apps[""][0U].event.event_id = 0;
    const auto j = json::to_json(status);
    const auto status2 = from_json(j);
    ASSERT_EQ(status, status2);
  }
}
}  // namespace
