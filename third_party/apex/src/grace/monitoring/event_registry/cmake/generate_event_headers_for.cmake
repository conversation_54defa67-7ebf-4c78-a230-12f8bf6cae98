function(generate_event_headers_for TARGET_NAME SRC OUT)
    set(SRC_FILE "${CMAKE_CURRENT_SOURCE_DIR}/${SRC}")
    set(OUT_FILE "${CMAKE_CURRENT_BINARY_DIR}/${OUT}")

    get_filename_component(DIR "${CMAKE_CURRENT_BINARY_DIR}/${OUT}" DIRECTORY)
    make_directory("${DIR}")

    add_custom_command(
            OUTPUT ${OUT_FILE}
            COMMAND event_converter --input "${SRC_FILE}" --output "${OUT_FILE}"
            DEPENDS ${SRC_FILE}
            COMMENT "Generating ${OUT_FILE} from ${SRC_FILE}"
    )

    add_custom_target("${TARGET_NAME}_events" ALL DEPENDS "${OUT_FILE}")
    add_dependencies("${TARGET_NAME}" "${TARGET_NAME}_events")

    get_target_property(type "${TARGET_NAME}" TYPE)
    if (NOT ${type} STREQUAL "INTERFACE_LIBRARY")
        target_include_directories("${TARGET_NAME}" PUBLIC
                "${CMAKE_CURRENT_BINARY_DIR}" "${CMAKE_CURRENT_BINARY_DIR}/include"
        )
    endif()

    get_filename_component(HEADER_INSTALL_DIR "${OUT}" DIRECTORY)
    install(
            FILES "${OUT_FILE}"
            DESTINATION "${HEADER_INSTALL_DIR}"
    )

    get_filename_component(YAML_INSTALL_DIR "${SRC}" DIRECTORY)
    install(
            FILES "${SRC_FILE}"
            DESTINATION "share/${YAML_INSTALL_DIR}"
    )
endfunction()