// generated from rosidl_adapter/resource/msg.idl.em
// with input from apex_ecu_monitor_msgs/msg/EcuInfo.msg
// generated code does not contain a copyright notice

#include "apex_ecu_monitor_msgs/msg/CpuInfo.idl"
#include "apex_ecu_monitor_msgs/msg/DiskIOInfo.idl"
#include "apex_ecu_monitor_msgs/msg/MemoryInfo.idl"
#include "apex_ecu_monitor_msgs/msg/NetworkInfo.idl"
#include "apex_ecu_monitor_msgs/msg/ProcessInfo.idl"
#include "apex_ecu_monitor_msgs/msg/TimeSyncInfo.idl"
#include "std_msgs/msg/Header.idl"

//! [EcuInfo.idl]
module apex_ecu_monitor_msgs {
  module msg {
    @verbatim (language="comment", text=
      " Message for ECU vital information")
    struct EcuInfo {
      @verbatim (language="comment", text=
        " Header")
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        " Cpu info")
      sequence<apex_ecu_monitor_msgs::msg::CpuInfo, 1> cpu_info;

      @verbatim (language="comment", text=
        " Memory info")
      sequence<apex_ecu_monitor_msgs::msg::MemoryInfo, 1> memory_info;

      @verbatim (language="comment", text=
        " Disk I/O info")
      sequence<apex_ecu_monitor_msgs::msg::DiskIOInfo, 1> disk_io_info;

      @verbatim (language="comment", text=
        " Network info")
      sequence<apex_ecu_monitor_msgs::msg::NetworkInfo, 1> network_info;

      @verbatim (language="comment", text=
        " Process info")
      sequence<apex_ecu_monitor_msgs::msg::ProcessInfo, 1> process_info;

      @verbatim (language="comment", text=
        " Time sync info")
      sequence<apex_ecu_monitor_msgs::msg::TimeSyncInfo, 1> timesync_info;
    };
  };
};
//! [EcuInfo.idl]
