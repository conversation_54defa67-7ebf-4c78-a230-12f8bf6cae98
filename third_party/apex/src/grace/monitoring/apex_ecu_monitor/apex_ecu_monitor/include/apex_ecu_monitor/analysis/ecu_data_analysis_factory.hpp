// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

/// \file
/// \brief This file defines a function to create a ecu analysis class

#ifndef ECU_DATA_ANALYSIS_BUILDER_HPP
#define ECU_DATA_ANALYSIS_BUILDER_HPP

#include <apex_ecu_monitor/visibility_control.hpp>
#include <apex_ecu_monitor/analysis/ecu_data_analysis.hpp>
#include <apex_ecu_monitor/analysis/ecu_data_analysis_components.hpp>

#include <settings/inspect.hpp>

#include <memory>
#include <vector>

/// \namespace apex
namespace apex
{
/// \namespace apex::ecu_monitor
namespace ecu_monitor
{
/// \namespace apex::ecu_monitor::analysis
namespace analysis
{

APEX_ECU_MONITOR_PUBLIC
Config create_config(const settings::inspect::dictionary_view & cfg);

/// \brief create an Analysis object based on the options and settings passed
/// \param[in] cfg Configuration dictionary for the analysis class construction
/// \cert
APEX_ECU_MONITOR_PUBLIC
std::unique_ptr<AnalysisBase> create(const Config & cfg);

/// \brief create an Analysis object based on the options and settings passed
/// \param[in] cfg Configuration dictionary for the analysis class construction
/// \cert
APEX_ECU_MONITOR_PUBLIC
std::unique_ptr<AnalysisBase> create(const settings::inspect::dictionary_view & cfg);

}  // namespace analysis
}  // namespace ecu_monitor
}  // namespace apex

#endif  // ECU_DATA_ANALYSIS_BUILDER_HPP
