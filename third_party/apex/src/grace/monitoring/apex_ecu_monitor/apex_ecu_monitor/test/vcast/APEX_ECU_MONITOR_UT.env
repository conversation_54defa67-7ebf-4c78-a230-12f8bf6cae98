ENVIRO.NEW
ENVIRO.NAME:APEX_ECU_MONITOR_UT
ENVIRO.COVERAGE_TYPE:Statement+MCDC
ENVIRO.INDUSTRY_MODE:ISO-26262 (Automotive)
ENVIRO.UUT: ecu_data_analysis
ENVIRO.UUT: ecu_data_acquisition_linux
ENVIRO.WHITE_BOX:YES
ENVIRO.MAX_VARY_RANGE: 20
ENVIRO.STUB: ALL_BY_PROTOTYPE
ENVIRO.SEARCH_LIST: $ADE_APEX_WS_ROOT/src/grace/monitoring/apex_ecu_monitor/apex_ecu_monitor/include/
ENVIRO.SEARCH_LIST: $ADE_APEX_WS_ROOT/src/grace/monitoring/apex_ecu_monitor/apex_ecu_monitor/param/
ENVIRO.SEARCH_LIST: $ADE_APEX_WS_ROOT/src/grace/monitoring/apex_ecu_monitor/apex_ecu_monitor/src/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apex_init/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/logging/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apexcpp/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rclcpp/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rcl/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/diagnostic_msgs/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apex_msgs/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apex_ecu_monitor_msgs/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/std_msgs/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rosgraph_msgs/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rcl_interfaces/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/builtin_interfaces/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rmw_apex_middleware/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rosidl_generator_cpp/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rosidl_typesupport_apex_middleware_cpp/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apex_middleware_support/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/dds_typesupport/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/settings/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rmw/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/threading/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rosidl_generator_c/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/interrupt/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/containers/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/system_utils/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rcutils/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/cpputils/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/tracetools/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/cyclone_dds/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apexutils/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apex_malloc/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/rosidl_typesupport_interface/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apex_determinism_check/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/apex_test_tools/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/yaml_cpp_vendor/opt/yaml_cpp_vendor/include/
ENVIRO.LIBRARY_INCLUDE_DIR: /opt/iceoryx/x86_64/include/
ENVIRO.LIBRARY_INCLUDE_DIR: /opt/cyclonedds/x86_64/cyclonedds/include/
ENVIRO.LIBRARY_INCLUDE_DIR: /opt/cyclonedds/x86_64/cyclonedds-cxx/include/ddscxx/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/osrf_testing_tools_cpp/include/
ENVIRO.LIBRARY_INCLUDE_DIR: $ADE_APEX_WS_ROOT/install-vc/mpark_variant_vendor/include/
ENVIRO.TYPE_HANDLED_DIRS_ALLOWED:
ENVIRO.LIBRARY_STUBS:
ENVIRO.END
