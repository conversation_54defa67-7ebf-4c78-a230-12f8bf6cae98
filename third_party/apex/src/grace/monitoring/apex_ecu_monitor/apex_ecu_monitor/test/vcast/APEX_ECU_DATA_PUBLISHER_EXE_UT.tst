-- VectorCAST 21.sp3 (08/04/21)
-- Test Case Script
--
-- Environment    : APEX_ECU_DATA_PUBLISHER_EXE_UT
-- Unit(s) Under Test: ecu_data_publisher_node ecu_data_publisher_node_main apex_ecu_monitor_settings
--
-- Script Features
TEST.SCRIPT_FEATURE:C_DIRECT_ARRAY_INDEXING
TEST.SCRIPT_FEATURE:CPP_CLASS_OBJECT_REVISION
TEST.SCRIPT_FEATURE:MULTIPLE_UUT_SUPPORT
TEST.SCRIPT_FEATURE:REMOVED_CL_PREFIX
TEST.SCRIPT_FEATURE:MIXED_CASE_NAMES
TEST.SCRIPT_FEATURE:STANDARD_SPACING_R2
TEST.SCRIPT_FEATURE:OVERLOADED_CONST_SUPPORT
TEST.SCRIPT_FEATURE:UNDERSCORE_NULLPTR
TEST.SCRIPT_FEATURE:FULL_PARAMETER_TYPES
TEST.SCRIPT_FEATURE:STRUCT_DTOR_ADDS_POINTER
TEST.SCRIPT_FEATURE:STRUCT_FIELD_CTOR_ADDS_POINTER
TEST.SCRIPT_FEATURE:STATIC_HEADER_FUNCS_IN_UUTS
TEST.SCRIPT_FEATURE:VCAST_MAIN_NOT_RENAMED
--

-- Unit: ecu_data_publisher_node_main

-- Subprogram: main

-- Test Case: main.pre_init.std_exception
TEST.UNIT:ecu_data_publisher_node_main
TEST.SUBPROGRAM:main
TEST.NEW
TEST.NAME:main.pre_init.std_exception
TEST.VALUE:USER_GLOBALS_VCAST.<<GLOBAL>>.__ret_pre_init:1
TEST.EXPECTED_USER_CODE:ecu_data_publisher_node_main.main.return
{{ <<ecu_data_publisher_node_main.main.return>> != ( 0 ) }}
TEST.END_EXPECTED_USER_CODE:
TEST.END

-- Test Case: main.std_exception
TEST.UNIT:ecu_data_publisher_node_main
TEST.SUBPROGRAM:main
TEST.NEW
TEST.NAME:main.std_exception
TEST.EXPECTED_USER_CODE:ecu_data_publisher_node_main.main.return
{{ <<ecu_data_publisher_node_main.main.return>> != ( 0 ) }}
TEST.END_EXPECTED_USER_CODE:
TEST.END

-- Test Case: main.unknown_error
TEST.UNIT:ecu_data_publisher_node_main
TEST.SUBPROGRAM:main
TEST.NEW
TEST.NAME:main.unknown_error
TEST.VALUE:ecu_data_publisher_node_main.main.argc:1
TEST.VALUE:ecu_data_publisher_node_main.main.argv:<<malloc 1>>
TEST.EXPECTED_USER_CODE:ecu_data_publisher_node_main.main.return
{{ <<ecu_data_publisher_node_main.main.return>> != ( 0 ) }}
TEST.END_EXPECTED_USER_CODE:
TEST.END
