// Copyright 2018-2022 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>
#include <apex_test_tools/apex_test_tools.hpp>
#include <apex_ecu_monitor/analysis/compute_load.hpp>

#include <fstream>
#include <iostream>
#include <limits>
#include <string>

#include "test_utility_functions.hpp"

namespace
{
using namespace std::chrono_literals;
using apex::ecu_monitor::analysis::ComputeCPULoad;

inline float load(
  std::chrono::nanoseconds active_time_diff,
  std::chrono::nanoseconds total_time_diff)
{
  return static_cast<float>(active_time_diff.count()) /
         static_cast<float>(total_time_diff.count()) * 100.0F;
}

}  // namespace

// cppcheck-suppress syntaxError
TEST(test_compute_load, test_steady_clock_now)
{
  apex::ecu_monitor::analysis::ComputeCPULoad m_cpu_load(1s);
}

TEST(test_compute_load, test_cpu_load_bad_cases)
{
#ifndef APEX_QNX
  apex::testing::memory_test::start();
#endif  // APEX_QNX
  ComputeCPULoad m_cpu_load(std::chrono::duration_cast<std::chrono::seconds>(10ns));
#ifndef APEX_QNX
  apex::testing::memory_test::stop();
#endif  // APEX_QNX
  // when active time and total time diffs are zero's failure case
  EXPECT_THROW(m_cpu_load.get_load(0ns, 0ns), apex::invalid_argument);

  // when total time diff is zero
  EXPECT_NO_THROW(m_cpu_load.get_load(10ns, 20ns));
  EXPECT_NO_THROW(m_cpu_load.get_load(14ns, 20ns));

  // when active time is > total time
  EXPECT_THROW(m_cpu_load.get_load(100ns, 20ns), apex::invalid_argument);

  // when time travels backward
  EXPECT_NO_THROW(m_cpu_load.get_load(150ns, 250ns));
  EXPECT_THROW(m_cpu_load.get_load(110ns, 270ns), apex::invalid_argument);
  EXPECT_NO_THROW(m_cpu_load.get_load(200ns, 350ns));
  EXPECT_THROW(m_cpu_load.get_load(249ns, 349ns), apex::invalid_argument);
  EXPECT_THROW(m_cpu_load.get_load(248ns, 348ns), apex::invalid_argument);
  EXPECT_NO_THROW(m_cpu_load.get_load(260ns, 360ns));
  EXPECT_NO_THROW(m_cpu_load.get_load(261ns, 361ns));
  EXPECT_THROW(m_cpu_load.get_load(259ns, 359ns), apex::invalid_argument);
}

TEST(test_compute_load, test_cpu_load_incrementing)
{
  std::array<std::chrono::nanoseconds, 5> at = {
    483131956123ns, 483131956124ns, 483131956125ns, 483131956126ns, 483131956127ns};
  std::array<std::chrono::nanoseconds, 5> tt = {
    483131956128ns, 483131956129ns, 483131956130ns, 483131956131ns, 483131956132ns};

  // active time constant, total time constant => valid scenario
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[0], tt[3]));
    EXPECT_NO_THROW(m_cpu_load.get_load(at[0], tt[3]));
  }

  // active time incrementing, total time constant => valid scenario
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[0], tt[1]));

    for (size_t i = 1U; i < at.size(); i++) {
      EXPECT_NO_THROW(m_cpu_load.get_load(at[i], tt[1]));
    }
  }

  // active time constant, total time incrementing => valid scenario
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[0], tt[0]));
    for (size_t i = 1U; i < at.size(); i++) {
      EXPECT_NO_THROW(m_cpu_load.get_load(at[0], tt[i]));
    }
  }

  // active time incrementing, total time incrementing => valid scenario
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[0], tt[0]));
    for (size_t i = 1U; i < at.size(); i++) {
      EXPECT_NO_THROW(m_cpu_load.get_load(at[i], tt[i]));
    }
  }

  // active time decrementing, total time constant => not valid, since time shouldn't decrement
  // and total time can't be constant over 1sec
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[4], tt[2]));
    for (size_t i = 1U; i < at.size(); i++) {
      EXPECT_THROW(m_cpu_load.get_load(at[at.size() - i - 1], tt[2]), apex::invalid_argument);
    }
  }

  // active time constant, total time decrementing => not valid, since time shouldn't decrement
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[3], tt[4]));
    for (size_t i = 1U; i < at.size(); i++) {
      EXPECT_THROW(m_cpu_load.get_load(at[3], tt[at.size() - i - 1]), apex::invalid_argument);
    }
  }

  // active time decrementing, total time decrementing => not valid, since time shouldn't decrement
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[4], tt[4]));
    for (size_t i = 1U; i < at.size(); i++) {
      EXPECT_THROW(m_cpu_load.get_load(at[at.size() - i - 1], tt[at.size() - i - 1]),
        apex::invalid_argument);
    }
  }

  // active time incrementing, total time decrementing => not valid, since time shouldn't decrement
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[0], tt[4]));
    for (size_t i = 1U; i < at.size(); i++) {
      EXPECT_THROW(m_cpu_load.get_load(at[i], tt[at.size() - i - 1]), apex::invalid_argument);
    }
  }

  // active time decrementing, total time incrementing => not valid, since time shouldn't decrement
  {
#ifndef APEX_QNX
    apex::testing::memory_test::start();
#endif  // APEX_QNX
    ComputeCPULoad m_cpu_load(1s);
#ifndef APEX_QNX
    apex::testing::memory_test::stop();
#endif  // APEX_QNX
    EXPECT_NO_THROW(m_cpu_load.get_load(at[4], tt[0]));
    for (size_t i = 1U; i < at.size(); i++) {
      EXPECT_THROW(m_cpu_load.get_load(at[at.size() - i - 1], tt[i]), apex::invalid_argument);
    }
  }
}

TEST(test_compute_load, test_cpu_load)
{
#ifndef APEX_QNX
  apex::testing::memory_test::start();
#endif  // APEX_QNX

  {
    ComputeCPULoad m_cpu_load(1s);
    float cpu_load = 0.0F;

    // when total time diffs is below cpu_load_comp_duration_sec there is no update
    cpu_load = m_cpu_load.get_load(5ns, 9ns);
    EXPECT_FLOAT_EQ(cpu_load, 0.0F);

    auto active_time = std::chrono::duration_cast<std::chrono::nanoseconds>(0.9s);
    auto total_time = std::chrono::duration_cast<std::chrono::nanoseconds>(1.1s);
    cpu_load = m_cpu_load.get_load(active_time, total_time);
    auto active_time_diff = active_time - 0ns;
    auto total_time_diff = total_time - 0ns;
    EXPECT_FLOAT_EQ(cpu_load, load(active_time_diff, total_time_diff));
  }

  {
    ComputeCPULoad m_cpu_load(std::chrono::duration_cast<std::chrono::seconds>(1ns));

    float cpu_load;

    // when active and total time diffs are non zero
    cpu_load = m_cpu_load.get_load(5ns, 9ns);
    auto active_time_diff = 5ns - 0ns;
    auto total_time_diff = 9ns - 0ns;
    EXPECT_FLOAT_EQ(cpu_load, load(active_time_diff, total_time_diff));

    // when idle time diff is zero
    // when active time diff and total time diff are same
    cpu_load = m_cpu_load.get_load(12ns, 16ns);
    EXPECT_FLOAT_EQ(cpu_load, 100);

    // when active and total time diffs are non zero
    cpu_load = m_cpu_load.get_load(17ns, 23ns);
    active_time_diff = 17ns - 12ns;
    total_time_diff = 23ns - 16ns;
    EXPECT_FLOAT_EQ(cpu_load, load(active_time_diff, total_time_diff));

    // when active and total time diffs are non zero
    cpu_load = m_cpu_load.get_load(29129ns, 49787ns);
    active_time_diff = 29129ns - 17ns;
    total_time_diff = 49787ns - 23ns;
    EXPECT_FLOAT_EQ(cpu_load, load(active_time_diff, total_time_diff));

    // real data
    const auto active_time_real = 34560967ns;
    const auto total_time_real = 483127517ns;
    cpu_load = m_cpu_load.get_load(active_time_real, total_time_real);
    active_time_diff = 34560967ns - 29129ns;
    total_time_diff = 483127517ns - 49787ns;
    EXPECT_FLOAT_EQ(cpu_load, load(active_time_diff, total_time_diff));
  }

  {
    ComputeCPULoad m_cpu_load(std::chrono::duration_cast<std::chrono::seconds>(100ns));

    float cpu_load;
    static constexpr uint8_t test_iterations = 17U;

    // loop test
    // CPU active time, incrementing randomly for 17 iterations
    std::array<std::chrono::nanoseconds, test_iterations> active_time_a = {34560976ns,
      34560988ns,
      34560999ns,
      34561370ns,
      34561391ns,
      34561412ns,
      34561453ns,
      34561474ns,
      34561615ns,
      34561696ns,
      34561737ns,
      34561818ns,
      34561989ns,
      34562140ns,
      34562191ns,
      34562491ns,
      34562791ns};

    // CPU total time, incrementing randomly for 17 iterations
    std::array<std::chrono::nanoseconds, test_iterations> total_time_a = {483127557ns,
      483127590ns,
      483127823ns,
      483127926ns,
      483128879ns,
      483128902ns,
      483128935ns,
      483128938ns,
      483129141ns,
      483129404ns,
      483129547ns,
      483129750ns,
      483131853ns,
      483131956ns,
      483132059ns,
      483132159ns,
      483133359ns};

    float compute_cpu_load = 0.0F;
    auto at_prev = 0ns;
    auto tt_prev = 0ns;
    // simulating the API being called in a loop (callback)
    for (uint8_t i = 0; i < test_iterations; i++) {
      // get the CPU load
      cpu_load = m_cpu_load.get_load(active_time_a[i], total_time_a[i]);

      // if the calculated CPU load is not equal to the cpu load,
      // this means the CPU load is re-calculated in the ecu monitor
      // also re-calculate the CPU load here
      if (cpu_load != compute_cpu_load) {
        // re calculate CPU load
        compute_cpu_load = load((active_time_a[i] - at_prev), (total_time_a[i] - tt_prev));
        // update previous times
        at_prev = active_time_a[i];
        tt_prev = total_time_a[i];
      }
      EXPECT_FLOAT_EQ(cpu_load, compute_cpu_load);
    }
  }

#ifndef APEX_QNX
  apex::testing::memory_test::stop();
#endif  // APEX_QNX
}

TEST(test_compute_load, test_cpu_load_invalid_args)
{
  ComputeCPULoad m_cpu_load(1s);
  // when active time is negative
  EXPECT_THROW(m_cpu_load.get_load(-17ns, 23ns), apex::invalid_argument);

  // when total time is negative
  EXPECT_THROW(m_cpu_load.get_load(17ns, -23ns), apex::invalid_argument);
}
