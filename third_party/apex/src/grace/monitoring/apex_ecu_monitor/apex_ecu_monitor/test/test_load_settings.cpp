// Copyright 2020-2022 Apex.AI, Inc.
// All rights reserved.

#include <gtest/gtest.h>

#include <memory>
#include <string>
#include <vector>

#include "apex_ecu_monitor/load_settings.hpp"
#include "test_utility_functions.hpp"

using apex::settings::construct::get;
using apex::settings::inspect::floating;
using apex::settings::inspect::integer;
using apex::settings::inspect::string_view;


TEST(load_settings, test_load_publisher_settings)
{
  using apex::settings::inspect::boolean;

#ifdef APEX_CERT
  apex::settings::inspect::dictionary_view dict = apex::ecu_monitor::load_settings();
#else
  CreateSysFiles cf = create_file_system("test_data_publisher");
  std::unique_ptr<char *[]> command_line_args;

  std::vector<std::string> command_line_str = {"ecu_data_analysis_node",
    "--apex-settings-file",
    CMAKE_SOURCE_DIR
    "/param/apex_ecu_monitor_default_settings.yaml"};

  command_line_args = std::make_unique<char *[]>(command_line_str.size());
  for (auto i = 0u; i < command_line_str.size(); ++i) {
    command_line_args[i] = &command_line_str[i][0];
  }

  int m_argc = static_cast<int>(command_line_str.size());
  char ** m_argv = command_line_args.get();
  apex::settings::inspect::dictionary_view dict = apex::ecu_monitor::load_settings(m_argc, m_argv);
#endif

  const auto subdict =
    get<apex::settings::inspect::dictionary_view>(dict, "ecu_data_publisher_node/ros__parameters");

  // node settings
  EXPECT_STREQ("raw_ecu_info", get<apex::string_view>(subdict, "raw_data_topic").data());

  // cpu_info
  EXPECT_EQ(true, get<boolean>(subdict, "cpu_info/enable"));
  EXPECT_STREQ("/proc/stat", get<apex::string_view>(subdict, "cpu_info/proc_stat_path").data());
  EXPECT_STREQ("/sys/class/thermal/thermal_zone0/temp",
    get<apex::string_view>(subdict, "cpu_info/sys_temp_path").data());
  EXPECT_STREQ("/proc/uptime", get<apex::string_view>(subdict, "cpu_info/proc_uptime_path").data());

  // memory_info
  EXPECT_EQ(true, get<boolean>(subdict, "memory_info/enable"));
  EXPECT_STREQ("/proc/meminfo",
    get<apex::string_view>(subdict, "memory_info/proc_mem_info_path").data());

  // disk_io_info
  EXPECT_EQ(true, get<boolean>(subdict, "disk_io_info/enable"));
  EXPECT_STREQ("/proc/diskstats", get<apex::string_view>(subdict, "disk_io_info/path").data());
  EXPECT_STREQ("sdb", get<apex::string_view>(subdict, "disk_io_info/device_name").data());

  // network_info
  EXPECT_EQ(true, get<boolean>(subdict, "network_info/enable"));
  EXPECT_STREQ("/sys/class/net", get<apex::string_view>(subdict, "network_info/path").data());
  EXPECT_STREQ("lo", get<apex::string_view>(subdict, "network_info/iface_name").data());

  // process_info
  EXPECT_EQ(true, get<boolean>(subdict, "process_info/enable"));
  EXPECT_STREQ("/proc", get<apex::string_view>(subdict, "process_info/path").data());
  EXPECT_STREQ("apex_ecu_data_publisher_exe",
    get<apex::string_view>(subdict, "process_info/process_name").data());

  // timesync_info
  EXPECT_EQ(false, get<boolean>(subdict, "timesync_info/enable"));
}

#ifndef APEX_CERT
TEST(load_settings, test_load_publisher_settings_old)
{
  using apex::settings::inspect::boolean;

  CreateSysFiles cf = create_file_system("test_data_publisher");
  std::unique_ptr<char *[]> command_line_args;

  std::vector<std::string> command_line_str = {
    "ecu_data_analysis_node",
    "--apex-settings-file",
    CMAKE_SOURCE_DIR "/param/os-v2-1/apex_ecu_monitor_default_settings.yaml"};

  command_line_args = std::make_unique<char *[]>(command_line_str.size());
  for (auto i = 0u; i < command_line_str.size(); ++i) {
    command_line_args[i] = &command_line_str[i][0];
  }

  int m_argc = static_cast<int>(command_line_str.size());
  char ** m_argv = command_line_args.get();
  apex::settings::inspect::dictionary_view dict = apex::ecu_monitor::load_settings(m_argc, m_argv);

  const auto subdict =
    get<apex::settings::inspect::dictionary_view>(dict, "ecu_data_publisher_node/ros__parameters");

  // node settings
  EXPECT_STREQ("raw_ecu_info", get<apex::string_view>(subdict, "raw_data_topic").data());

  // cpu_info
  EXPECT_STREQ("/proc/stat", get<apex::string_view>(subdict, "proc_stat_path").data());
  EXPECT_STREQ("/sys/class/thermal/thermal_zone0/temp",
    get<apex::string_view>(subdict, "sys_temp_path").data());
  EXPECT_STREQ("/proc/uptime", get<apex::string_view>(subdict, "proc_uptime_path").data());

  // memory_info
  EXPECT_STREQ("/proc/meminfo", get<apex::string_view>(subdict, "proc_mem_info_path").data());

  // disk_io_info
  EXPECT_STREQ("/proc/diskstats", get<apex::string_view>(subdict, "diskstats/path").data());
  EXPECT_STREQ("sdb", get<apex::string_view>(subdict, "diskstats/device_name").data());

  // network_info
  EXPECT_STREQ("/sys/class/net", get<apex::string_view>(subdict, "network_stats/path").data());
  EXPECT_STREQ("lo", get<apex::string_view>(subdict, "network_stats/iface_name").data());

  // process_info
  EXPECT_STREQ("/proc", get<apex::string_view>(subdict, "process_info/path").data());
  EXPECT_STREQ("apex_ecu_data_publisher_exe",
    get<apex::string_view>(subdict, "process_info/process_name").data());
}
#endif

TEST(load_settings, test_load_analysis_settings)
{
  using apex::settings::inspect::boolean;

#ifdef APEX_CERT
  apex::settings::inspect::dictionary_view dict = apex::ecu_monitor::load_settings();
#else
  std::unique_ptr<char *[]> command_line_args;
  std::vector<std::string> command_line_str = {"ecu_data_analysis_node",
    "--apex-settings-file",
    CMAKE_SOURCE_DIR
    "/param/apex_ecu_monitor_default_settings.yaml"};

  command_line_args = std::make_unique<char *[]>(command_line_str.size());
  for (auto i = 0u; i < command_line_str.size(); ++i) {
    command_line_args[i] = &command_line_str[i][0];
  }

  int m_argc = static_cast<int>(command_line_str.size());
  char ** m_argv = command_line_args.get();
  const auto dict = apex::ecu_monitor::load_settings(m_argc, m_argv);
#endif

  const auto subdict =
    get<apex::settings::inspect::dictionary_view>(dict, "ecu_data_analysis_node/ros__parameters");

  // node settings
  EXPECT_STREQ("raw_ecu_info", get<apex::string_view>(subdict, "raw_data_topic").data());
  EXPECT_EQ(1, apex::cast::safe_cast<size_t>(get<integer>(subdict, "expected_num_subscribers")));
  EXPECT_EQ(std::chrono::milliseconds(5000),
    std::chrono::milliseconds{get<integer>(subdict, "init_timeout_ms")});
  EXPECT_STREQ("my_ECU", get<apex::string_view>(subdict, "hw_id").data());

  // cpu_info
  EXPECT_EQ(true, get<boolean>(subdict, "cpu_info/enable"));
  EXPECT_EQ(12,
    apex::cast::safe_cast<uint32_t>(get<integer>(subdict, "cpu_info/expected_cpu_cores")));
  EXPECT_EQ(75.000000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "cpu_info/max_sys_temp_C")));
  EXPECT_EQ(2147483647, get<integer>(subdict, "cpu_info/max_uptime_sec"));
  EXPECT_EQ(
    4000,
    apex::cast::safe_cast<uint32_t>(get<integer>(subdict, "cpu_info/cpu_load_comp_duration_ms")));
  EXPECT_EQ(75.000000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "cpu_info/max_cpu_load_percent")));

  // memory_info
  EXPECT_EQ(true, get<boolean>(subdict, "memory_info/enable"));
  EXPECT_EQ(
    1.000000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "memory_info/expected_ram_available_gb")));
  EXPECT_EQ(
    0.500000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "memory_info/minimum_ram_available_gb")));
  EXPECT_EQ(
    1.000000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "memory_info/expected_swap_available_gb")));
  EXPECT_EQ(
    0.500000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "memory_info/minimum_swap_available_gb")));

  // disk_io_info
  EXPECT_EQ(true, get<boolean>(subdict, "disk_io_info/enable"));

  // network_info
  EXPECT_EQ(true, get<boolean>(subdict, "network_info/enable"));

  // process_info
  EXPECT_EQ(true, get<boolean>(subdict, "process_info/enable"));
  EXPECT_EQ(4000,
    apex::cast::safe_cast<uint32_t>(
      get<integer>(subdict, "process_info/cpu_load_comp_duration_ms")));

  // timesync_info
  EXPECT_EQ(false, get<boolean>(subdict, "timesync_info/enable"));
}

#ifndef APEX_CERT
TEST(load_settings, test_load_analysis_settings_old)
{
  using apex::settings::inspect::boolean;


  std::unique_ptr<char *[]> command_line_args;
  std::vector<std::string> command_line_str = {
    "ecu_data_analysis_node",
    "--apex-settings-file",
    CMAKE_SOURCE_DIR "/param/os-v2-1/apex_ecu_monitor_default_settings.yaml"};

  command_line_args = std::make_unique<char *[]>(command_line_str.size());
  for (auto i = 0u; i < command_line_str.size(); ++i) {
    command_line_args[i] = &command_line_str[i][0];
  }

  int m_argc = static_cast<int>(command_line_str.size());
  char ** m_argv = command_line_args.get();
  const auto dict = apex::ecu_monitor::load_settings(m_argc, m_argv);

  const auto subdict =
    get<apex::settings::inspect::dictionary_view>(dict, "ecu_data_analysis_node/ros__parameters");

  // node settings
  EXPECT_STREQ("raw_ecu_info", get<apex::string_view>(subdict, "raw_data_topic").data());
  EXPECT_EQ(1, apex::cast::safe_cast<size_t>(get<integer>(subdict, "expected_num_subscribers")));
  EXPECT_EQ(std::chrono::milliseconds(5000),
    std::chrono::milliseconds{get<integer>(subdict, "init_timeout_ms")});
  EXPECT_STREQ("my_ECU", get<apex::string_view>(subdict, "hw_id").data());

  // cpu_info
  EXPECT_EQ(12, apex::cast::safe_cast<uint32_t>(get<integer>(subdict, "expected_cpu_cores")));
  EXPECT_EQ(75.000000, apex::cast::safe_cast<float>(get<floating>(subdict, "max_sys_temp_C")));
  EXPECT_EQ(2147483647, get<integer>(subdict, "max_uptime_sec"));
  EXPECT_EQ(4,
    apex::cast::safe_cast<uint32_t>(get<integer>(subdict, "cpu_load_comp_duration_sec")));
  EXPECT_EQ(75.000000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "max_cpu_load_percent")));

  // memory_info
  EXPECT_EQ(1.000000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "expected_ram_available_gb")));
  EXPECT_EQ(0.500000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "minimum_ram_available_gb")));
  EXPECT_EQ(1.000000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "expected_swap_available_gb")));
  EXPECT_EQ(0.500000,
    apex::cast::safe_cast<float>(get<floating>(subdict, "minimum_swap_available_gb")));

  // process_info
  EXPECT_EQ(4,
    apex::cast::safe_cast<uint32_t>(get<integer>(subdict, "cpu_load_comp_duration_sec")));
}
#endif
