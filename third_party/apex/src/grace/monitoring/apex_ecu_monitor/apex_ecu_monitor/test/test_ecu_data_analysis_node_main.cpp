// Copyright 2020 Apex.AI, Inc.
// All rights reserved.
#include <gtest/gtest.h>
#include <apex_ecu_monitor/data_analysis_node.hpp>
#include <apex_test_tools/apex_test_tools.hpp>
#include <cpputils/safe_cast.hpp>
#include <executor2/executor_factory.hpp>
#include <executor2/executor_runner.hpp>
#include <rclcpp/logging.hpp>

#include <memory>
#include <string>
#include <thread>
#include <utility>
#include <vector>

#include "test_utility_functions.hpp"

using namespace std::chrono_literals;
using apex::settings::inspect::get;

class ecu_data_analysis_node_main_test : public ::testing::Test
{
public:
  static void SetUpTestCase()
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
  }

  static void TearDownTestCase()
  {
    rclcpp::shutdown();
  }
};

TEST_F(ecu_data_analysis_node_main_test, create_node)
{
  bool use_old_dict = false;
  apex::settings::repository::set(create_valid_analysis_dict(use_old_dict));
  const auto config_root = apex::settings::repository::get();

  auto analysis_node =
    apex::ecu_monitor::data_analysis_node::create_node("ecu_data_analysis_node", "", config_root);
  const auto exec = apex::executor::executor_factory::create();
  exec->add(std::move(analysis_node));
  const apex::executor::executor_runner analysis_runner{*exec};
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  ASSERT_NO_THROW(analysis_runner.stop());
}

TEST_F(ecu_data_analysis_node_main_test, create_node_with_old_dict)
{
  bool use_old_dict = true;
  apex::settings::repository::set(create_valid_analysis_dict(use_old_dict));
  const auto config_root = apex::settings::repository::get();

  auto analysis_node =
    apex::ecu_monitor::data_analysis_node::create_node("ecu_data_analysis_node", "", config_root);
  const auto exec = apex::executor::executor_factory::create();
  exec->add(std::move(analysis_node));
  const apex::executor::executor_runner analysis_runner{*exec};
  std::this_thread::sleep_for(std::chrono::milliseconds(100));
  ASSERT_NO_THROW(analysis_runner.stop());
}
