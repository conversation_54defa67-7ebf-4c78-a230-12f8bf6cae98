// Copyright 2018-2022 Apex.AI, Inc.
// All rights reserved.

#include "apex_ecu_monitor/load_settings.hpp"

#include <apexutils/apexdef.h>

#ifndef APEX_CERT
  #include <settings/from_yaml.hpp>
#endif  // APEX_CERT

#ifdef APEX_CERT
  #include "../param/apex_ecu_monitor_settings.hpp"
#endif

#include "apex_ecu_monitor/visibility_control.hpp"

namespace apex
{
namespace ecu_monitor
{

using apex::settings::inspect::get;
using apex::settings::inspect::integer;
using apex::settings::inspect::string_view;

#ifdef APEX_CERT
APEX_ECU_MONITOR_PUBLIC
settings::inspect::dictionary_view load_settings()
{
  apex::settings::repository::set(apex::settings::generated::apex_ecu_monitor_settings::create());
  const auto config_root = apex::settings::repository::get();
  return config_root;
}
#else
APEX_ECU_MONITOR_PUBLIC
settings::inspect::dictionary_view load_settings(const std::int32_t argc, char ** const argv)
{
  apex::settings::yaml::load_repository_from_command_line(argc, argv, false);
  const auto config_root = apex::settings::repository::get();
  return config_root;
}
#endif

}  // namespace ecu_monitor
}  // namespace apex
