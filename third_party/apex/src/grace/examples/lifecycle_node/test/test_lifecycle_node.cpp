/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#include <gtest/gtest.h>

#include <atomic>
#include <chrono>
#include <cstdint>
#include <thread>

#include <interrupt/interrupt_handler.hpp>
#include <lifecycle_node/lifecycle_node.hpp>
#include <rclcpp/dynamic_waitset/waitset.hpp>

#include <std_msgs/msg/int32.hpp>
#include <test_msgs/srv/basic_types.hpp>

using apex::lifecycle::CallbackReturn;
using apex::lifecycle::ControlMessageType;
using apex::lifecycle::LifecycleNode;
using apex::lifecycle::NodeState;
using apex::lifecycle::Transition;
namespace dw = rclcpp::dynamic_waitset;
using namespace std::chrono_literals;
using MessageT = std_msgs::msg::Int32;
using ServiceT = test_msgs::srv::BasicTypes;

namespace
{
class test_lifecycle_node : public ::testing::Test
{
protected:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
  }

  void TearDown() override
  {
    rclcpp::shutdown();
  }

  template <class F>
  bool wait_while(const F & condition, std::chrono::milliseconds timeout = 5000ms)
  {
    const auto begin = std::chrono::steady_clock::now();
    while ((std::chrono::steady_clock::now() - begin) < timeout) {
      if (!condition()) {
        return true;
      }
      std::this_thread::sleep_for(1ms);
    }
    return false;
  }

  class empty_test_node : public apex::lifecycle::LifecycleNode
  {
  public:
    using apex::lifecycle::LifecycleNode::LifecycleNode;
    std::size_t configure_count{0};
    std::size_t activate_count{0};
    std::size_t deactivate_count{0};
    std::size_t cleanup_count{0};

  protected:
    CallbackReturn onConfigureImpl(const rclcpp_lifecycle::State & state) override
    {
      if (state.id() != NodeState::PRIMARY_STATE_UNCONFIGURED || state.label() != "unconfigured") {
        throw std::runtime_error{"unexpected state"};
      }
      configure_count++;
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onActivateImpl(const rclcpp_lifecycle::State & state) override
    {
      if (state.id() != NodeState::PRIMARY_STATE_INACTIVE || state.label() != "inactive") {
        throw std::runtime_error{"unexpected state"};
      }
      activate_count++;
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onDeactivateImpl(const rclcpp_lifecycle::State & state) override
    {
      if (state.id() != NodeState::PRIMARY_STATE_ACTIVE || state.label() != "active") {
        throw std::runtime_error{"unexpected state"};
      }
      deactivate_count++;
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onCleanupImpl(const rclcpp_lifecycle::State & state) override
    {
      if (state.id() != NodeState::PRIMARY_STATE_INACTIVE || state.label() != "inactive") {
        throw std::runtime_error{"unexpected state"};
      }
      cleanup_count++;
      return CallbackReturn::SUCCESS;
    }
  };
};

TEST_F(test_lifecycle_node, start_stop_explicit)
{
  empty_test_node node{"test_node"};
  ASSERT_NO_THROW(node.stop());
  ASSERT_EQ(node.configure_count, 0);
  ASSERT_EQ(node.activate_count, 0);
  ASSERT_EQ(node.deactivate_count, 0);
  ASSERT_EQ(node.cleanup_count, 0);
}

TEST_F(test_lifecycle_node, start_stop_implicit)
{
  empty_test_node node{"test_node"};
  SUCCEED();  // no hang or exception
}

TEST_F(test_lifecycle_node, normal_transitions)
{
  empty_test_node node{"test_node"};
  for (auto i = 0; i < 3; ++i) {
    ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_UNCONFIGURED);
    ASSERT_EQ(node.get_current_state().label(), "unconfigured");
    ASSERT_EQ(node.get_user_handler_count(), 0U);
    ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
    ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_INACTIVE);
    ASSERT_EQ(node.get_current_state().label(), "inactive");
    ASSERT_EQ(node.get_user_handler_count(), 0U);
    ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
    ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_ACTIVE);
    ASSERT_EQ(node.get_current_state().label(), "active");
    ASSERT_EQ(node.get_user_handler_count(), 0U);
    ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
    ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_INACTIVE);
    ASSERT_EQ(node.get_current_state().label(), "inactive");
    ASSERT_EQ(node.get_user_handler_count(), 0U);
    ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
    ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_ACTIVE);
    ASSERT_EQ(node.get_current_state().label(), "active");
    ASSERT_EQ(node.get_user_handler_count(), 0U);
    ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
    ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_INACTIVE);
    ASSERT_EQ(node.get_current_state().label(), "inactive");
    ASSERT_EQ(node.get_user_handler_count(), 0U);
    ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::SUCCESS);
  }
  ASSERT_NO_THROW(node.stop());
  ASSERT_EQ(node.configure_count, 3);
  ASSERT_EQ(node.activate_count, 6);
  ASSERT_EQ(node.deactivate_count, 6);
  ASSERT_EQ(node.cleanup_count, 3);
}

TEST_F(test_lifecycle_node, transition_legality)
{
  empty_test_node node{"test_node"};
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::FAILURE);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::FAILURE);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);

  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_INACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_INACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_INACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);

  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_ACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::FAILURE);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_ACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::FAILURE);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_ACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_ACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::FAILURE);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_ACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_INACTIVE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_ACTIVE);
}

TEST_F(test_lifecycle_node, publisher_subscriber)
{
  empty_test_node node{"test_node"};
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  auto pub = node.create_publisher<MessageT>("test_topic", 10);
  std::atomic<std::size_t> count{0};
  auto sub =
    node.create_subscription<MessageT>("test_topic", 10, [&count](const auto & msg) { ++count; });
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  for (auto i = 0; i < 10; ++i) {
    auto msg = pub->borrow_loaned_message();
    msg->data = i;
    pub->publish(std::move(msg));
  }
  ASSERT_TRUE(wait_while([&count] { return count < 10; }));
  sub.reset();
  pub.reset();
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 0U);
}

TEST_F(test_lifecycle_node, messages_but_when_active)
{
  empty_test_node node{"test_node"};
  auto pub = node.create_publisher<MessageT>("test_topic", 10);
  std::atomic<std::size_t> count{0};
  auto sub =
    node.create_subscription<MessageT>("test_topic", 10, [&count](const auto & msg) { ++count; });
  auto send_and_wait = [&pub, &count, this] {
    count = 0;
    auto msg = pub->borrow_loaned_message();
    msg->data = 42;
    pub->publish(std::move(msg));
    return wait_while([&count] { return count == 0; }, 500ms);
  };
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_FALSE(send_and_wait());
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  ASSERT_FALSE(send_and_wait());
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_TRUE(send_and_wait());
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_FALSE(send_and_wait());
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::SUCCESS);
  ASSERT_FALSE(send_and_wait());
}

TEST_F(test_lifecycle_node, sub_on_activate_cleanup)
{
  empty_test_node node{"test_node"};
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  auto pub = node.create_publisher<MessageT>("test_topic", 10);
  std::atomic<std::size_t> count{0};
  auto sub =
    node.create_subscription<MessageT>("test_topic", 10, [&count](const auto & msg) { ++count; });

  for (auto i = 0; i < 10; ++i) {
    auto msg = pub->borrow_loaned_message();
    msg->data = i;
    pub->publish(std::move(msg));
  }
  dw::Waitset ws{sub};
  ASSERT_TRUE(ws.wait(3s));
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_TRUE(sub->take().empty());
}

TEST_F(test_lifecycle_node, timer)
{
  empty_test_node node{"test_node"};
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  std::atomic<std::size_t> count{0};
  auto timer = node.create_wall_timer(10ms, [&count] { ++count; });
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_TRUE(wait_while([&count] { return count < 10; }));
  timer = nullptr;
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 0U);
}

TEST_F(test_lifecycle_node, interrupt_handling_on_throw)
{
  apex::interrupt_handler::installer i;
  empty_test_node node{"test_node"};
  auto pub = node.create_publisher<MessageT>("test_topic", 1);
  auto sub = node.create_subscription<MessageT>(
    "test_topic", 1, [](const auto & msg) { throw std::runtime_error{"oops"}; });
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  auto msg = pub->borrow_loaned_message();
  pub->publish(std::move(msg));
  apex::interrupt_handler::wait();
  ASSERT_THROW(node.stop(), std::runtime_error);
}

TEST_F(test_lifecycle_node, user_handler_errors)
{
  class test_node : public apex::lifecycle::LifecycleNode
  {
  public:
    using apex::lifecycle::LifecycleNode::LifecycleNode;

  protected:
    CallbackReturn onConfigureImpl(const rclcpp_lifecycle::State &) override
    {
      static std::int32_t i = 0;
      switch (i++) {
        case 0:
          throw std::runtime_error{"oops"};
        case 1:
          return CallbackReturn::FAILURE;
        case 2:
          return CallbackReturn::ERROR;
        default:
          return CallbackReturn::SUCCESS;
      }
    }

    CallbackReturn onActivateImpl(const rclcpp_lifecycle::State &) override
    {
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onDeactivateImpl(const rclcpp_lifecycle::State &) override
    {
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onCleanupImpl(const rclcpp_lifecycle::State &) override
    {
      return CallbackReturn::SUCCESS;
    }
  };

  test_node node{"test_node"};
  ASSERT_THROW(node.trigger_transition(Transition::TRANSITION_CONFIGURE), std::runtime_error);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::FAILURE);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::ERROR);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
}

TEST_F(test_lifecycle_node, state_is_published)
{
  rclcpp::Node snode{"sub_node"};
  auto sub =
    snode.create_polling_subscription<NodeState>("/test_node/current_state", rclcpp::DefaultQoS());
  dw::Waitset ws{sub};
  empty_test_node node{"test_node"};
  {
    ASSERT_TRUE(ws.wait(3s));
    const auto msgs = sub->take();
    ASSERT_EQ(msgs.size(), 1U);
    ASSERT_EQ(msgs[0].data().id, NodeState::PRIMARY_STATE_UNCONFIGURED);
  }
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  {
    ASSERT_TRUE(ws.wait(3s));
    const auto msgs = sub->take();
    ASSERT_EQ(msgs.size(), 1U);
    ASSERT_EQ(msgs[0].data().id, NodeState::PRIMARY_STATE_INACTIVE);
  }
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  {
    ASSERT_TRUE(ws.wait(3s));
    const auto msgs = sub->take();
    ASSERT_EQ(msgs.size(), 1U);
    ASSERT_EQ(msgs[0].data().id, NodeState::PRIMARY_STATE_ACTIVE);
  }

  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
  {
    ASSERT_TRUE(ws.wait(3s));
    const auto msgs = sub->take();
    ASSERT_EQ(msgs.size(), 1U);
    ASSERT_EQ(msgs[0].data().id, NodeState::PRIMARY_STATE_INACTIVE);
  }
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::SUCCESS);
  {
    ASSERT_TRUE(ws.wait(3s));
    const auto msgs = sub->take();
    ASSERT_EQ(msgs.size(), 1U);
    ASSERT_EQ(msgs[0].data().id, NodeState::PRIMARY_STATE_UNCONFIGURED);
  }
}

TEST_F(test_lifecycle_node, service)
{
  empty_test_node node{"test_node"};
  auto srv = node.create_service<ServiceT>("test_service", [](const auto & req, auto & res) {
    ASSERT_EQ(req.int32_value, 42);
    res.int32_value = 43;
  });
  rclcpp::Node cnode{"client_node"};
  auto client = cnode.create_polling_client<ServiceT>("test_service");
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  auto req = client->borrow_loaned_request();
  req->int32_value = 42;
  client->async_send_request(std::move(req));
  dw::Waitset ws{client};
  ASSERT_TRUE(ws.wait(3s));
  const auto res = client->take_response();
  ASSERT_EQ(res.size(), 1U);
  ASSERT_EQ(res[0].data().int32_value, 43);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  srv = nullptr;
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 0U);
}

TEST_F(test_lifecycle_node, client)
{
  empty_test_node node{"test_node"};
  std::atomic<std::int64_t> req_id = -1;
  std::atomic<bool> done{false};
  auto cln =
    node.create_client<ServiceT>("test_service", [&req_id, &done](const auto & rsp, auto seq_id) {
      ASSERT_EQ(rsp.int32_value, 43);
      ASSERT_EQ(seq_id, req_id);
      done = true;
    });
  rclcpp::Node snode{"service_node"};
  auto service = snode.create_polling_service<ServiceT>("test_service");
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CONFIGURE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_ACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  auto req = cln->borrow_loaned_request();
  req->int32_value = 42;
  req_id = cln->async_send_request(std::move(req));
  dw::Waitset ws{service};
  ASSERT_TRUE(ws.wait(3s));
  const auto req1 = service->take_request();
  ASSERT_EQ(req1.size(), 1U);
  ASSERT_EQ(req1[0].data().int32_value, 42);
  auto rsp = service->borrow_loaned_response();
  rsp->int32_value = 43;
  auto hdr = req1[0].request_header();
  service->send_response(hdr, std::move(rsp));
  ASSERT_TRUE(wait_while([&done] { return !done; }));
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_DEACTIVATE), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 1U);
  cln = nullptr;
  ASSERT_EQ(node.trigger_transition(Transition::TRANSITION_CLEANUP), CallbackReturn::SUCCESS);
  ASSERT_EQ(node.get_user_handler_count(), 0U);
}

TEST_F(test_lifecycle_node, heartbeat)
{
  empty_test_node node{"test_node", "", nullptr, 10ms};
  rclcpp::Node snode{"sub_node"};
  auto sub =
    snode.create_polling_subscription<NodeState>("/test_node/current_state", rclcpp::DefaultQoS());
  dw::Waitset ws{sub};
  for (auto i = 0; i < 10; ++i) {
    ASSERT_TRUE(ws.wait(1s));
    const auto msgs = sub->take();
    for (const auto & msg : msgs) {
      if (msg.info().valid()) {
        ASSERT_EQ(msg.data().id, NodeState::PRIMARY_STATE_UNCONFIGURED);
      }
    }
  }
}

TEST_F(test_lifecycle_node, transition_to)
{
  empty_test_node node{"test_node"};
  // unconfigured -> unconfigured
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_UNCONFIGURED).id(),
            NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_UNCONFIGURED);
  // unconfigured -> inactive
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_INACTIVE).id(),
            NodeState::PRIMARY_STATE_INACTIVE);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_INACTIVE);
  // inactive -> inactive
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_INACTIVE).id(),
            NodeState::PRIMARY_STATE_INACTIVE);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_INACTIVE);
  // inactive -> active
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_ACTIVE).id(),
            NodeState::PRIMARY_STATE_ACTIVE);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_ACTIVE);
  // active -> active
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_ACTIVE).id(),
            NodeState::PRIMARY_STATE_ACTIVE);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_ACTIVE);
  // active -> inactive
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_INACTIVE).id(),
            NodeState::PRIMARY_STATE_INACTIVE);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_INACTIVE);
  // inactive -> unconfigured
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_UNCONFIGURED).id(),
            NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_UNCONFIGURED);
  // unconfigured -> active
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_ACTIVE).id(),
            NodeState::PRIMARY_STATE_ACTIVE);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_ACTIVE);
  // active -> unconfigured
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_UNCONFIGURED).id(),
            NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_TRUE(node.get_current_state().id() == NodeState::PRIMARY_STATE_UNCONFIGURED);
}

TEST_F(test_lifecycle_node, transition_to_error)
{
  class test_node : public apex::lifecycle::LifecycleNode
  {
  public:
    using apex::lifecycle::LifecycleNode::LifecycleNode;

  protected:
    CallbackReturn onConfigureImpl(const rclcpp_lifecycle::State &) override
    {
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onActivateImpl(const rclcpp_lifecycle::State &) override
    {
      return CallbackReturn::ERROR;
    }

    CallbackReturn onDeactivateImpl(const rclcpp_lifecycle::State &) override
    {
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onCleanupImpl(const rclcpp_lifecycle::State &) override
    {
      return CallbackReturn::SUCCESS;
    }
  };

  test_node node{"test_node"};
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_UNCONFIGURED);
  ASSERT_EQ(node.transition_to(NodeState::PRIMARY_STATE_ACTIVE).id(),
            NodeState::PRIMARY_STATE_INACTIVE);
  ASSERT_EQ(node.get_current_state().id(), NodeState::PRIMARY_STATE_INACTIVE);
}

TEST_F(test_lifecycle_node, control_topic)
{
  empty_test_node node{"test_node", "", nullptr, 10ms, "/control"};
  rclcpp::Node snode{"control_node"};
  auto pub = snode.create_publisher<ControlMessageType>("/control", 10);
  {
    auto msg = pub->borrow_loaned_message();
    msg->data = NodeState::PRIMARY_STATE_ACTIVE;
    pub->publish(std::move(msg));
  }
  ASSERT_TRUE(wait_while(
    [&node] { return node.get_current_state().id() != NodeState::PRIMARY_STATE_ACTIVE; }));
  {
    auto msg = pub->borrow_loaned_message();
    msg->data = NodeState::PRIMARY_STATE_UNCONFIGURED;
    pub->publish(std::move(msg));
  }
  ASSERT_TRUE(wait_while(
    [&node] { return node.get_current_state().id() != NodeState::PRIMARY_STATE_UNCONFIGURED; }));
}

TEST_F(test_lifecycle_node, from_callbacks)
{
  class test_node : public apex::lifecycle::LifecycleNode
  {
  public:
    using apex::lifecycle::LifecycleNode::LifecycleNode;
    std::int32_t m_execution_done{0};

  protected:
    CallbackReturn onConfigureImpl(const rclcpp_lifecycle::State &) override
    {
      m_pub = create_publisher<MessageT>("test_topic", 10);

      m_sub = create_subscription<MessageT>("test_topic", 10, [this](const auto & msg) {
        if (msg.data == 9) {
          ++m_execution_done;
        }
      });
      m_srv = create_service<ServiceT>("test_service", [this](const auto & req, auto & res) {
        res.int32_value = 42;
        if (req.int32_value == 9) {
          ++m_execution_done;
        }
      });

      m_cln = create_client<ServiceT>("test_service", [this](const auto & rsp, auto seq_id) {
        const auto seq_id_it = m_seq_ids.find(seq_id);
        if (seq_id_it == m_seq_ids.end()) {
          throw std::runtime_error{"unexpected sequence id"};
        }
        m_seq_ids.erase(seq_id_it);
        if (rsp.int32_value != 42) {
          throw std::runtime_error{"unexpected response"};
        }
      });

      m_timer = create_wall_timer(10ms, [this] {
        if (m_execution_done == 2) {
          // probably not the best idea ever, but it's a test
          transition_to(NodeState::PRIMARY_STATE_UNCONFIGURED);
          return;
        }

        if (m_counter < 10) {
          auto msg = m_pub->borrow_loaned_message();
          msg->data = m_counter;
          m_pub->publish(std::move(msg));
          ++m_counter;
        }
        if (m_counter_srv < 10) {
          auto req = m_cln->borrow_loaned_request();
          req->int32_value = m_counter_srv;
          m_seq_ids.emplace(m_cln->async_send_request(std::move(req)));
          ++m_counter_srv;
        }
      });

      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onActivateImpl(const rclcpp_lifecycle::State &) override
    {
      m_counter = 0;
      m_counter_srv = 0;
      m_seq_ids.clear();
      m_execution_done = 0;
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onDeactivateImpl(const rclcpp_lifecycle::State &) override
    {
      return CallbackReturn::SUCCESS;
    }

    CallbackReturn onCleanupImpl(const rclcpp_lifecycle::State &) override
    {
      m_timer->cancel();
      m_srv.reset();
      m_cln.reset();
      m_sub.reset();
      m_pub.reset();
      return CallbackReturn::SUCCESS;
    }

  private:
    typename rclcpp::Publisher<MessageT>::SharedPtr m_pub;
    typename rclcpp::PollingSubscription<MessageT>::SharedPtr m_sub;
    typename rclcpp::PollingClient<ServiceT>::SharedPtr m_cln;
    typename rclcpp::PollingService<ServiceT>::SharedPtr m_srv;
    apex::timer_service::timer_ptr m_timer;
    std::int32_t m_counter = 0;
    std::int32_t m_counter_srv = 0;
    std::set<std::int64_t> m_seq_ids;
  };

  test_node node{"test_node", "", nullptr, 1s, "/control"};

  rclcpp::Node snode{"control_node"};
  auto pub = snode.create_publisher<ControlMessageType>("/control", 10);
  {
    auto msg = pub->borrow_loaned_message();
    msg->data = NodeState::PRIMARY_STATE_ACTIVE;
    pub->publish(std::move(msg));
  }
  ASSERT_TRUE(wait_while(
    [&node] { return node.get_current_state().id() != NodeState::PRIMARY_STATE_ACTIVE; }));
  ASSERT_TRUE(wait_while(
    [&node] { return node.get_current_state().id() != NodeState::PRIMARY_STATE_UNCONFIGURED; }));
  {
    auto msg = pub->borrow_loaned_message();
    msg->data = NodeState::PRIMARY_STATE_ACTIVE;
    pub->publish(std::move(msg));
  }
  ASSERT_TRUE(wait_while(
    [&node] { return node.get_current_state().id() != NodeState::PRIMARY_STATE_ACTIVE; }));
  ASSERT_TRUE(wait_while(
    [&node] { return node.get_current_state().id() != NodeState::PRIMARY_STATE_UNCONFIGURED; }));
  ASSERT_NO_THROW(node.stop());
}
}  // namespace
