load("@apex//common/bazel/rules_cc:defs.bzl", "apex_cc_library")
load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "lifecycle_node_pkg",
    cc_libraries = [
        ":lifecycle_node",
    ],
    description = "Implementation of lifecycle node.",
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "lifecycle_node",
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "@apex//common/cpputils:cpputils_pkg",
        "@apex//common/interrupt:interrupt_pkg",
        "@apex//grace/execution/timer_service:timer_service_pkg",
        "@apex//grace/interfaces/lifecycle_msgs:lifecycle_msgs_pkg",
        "@apex//grace/interfaces/std_msgs:std_msgs_pkg",
        "@apex//grace/interfaces/test_msgs:test_msgs_pkg",
        "@apex//grace/monitoring/logging:logging_pkg",
        "@apex//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "@apex//grace/ros/rclcpp/rclcpp_lifecycle:rclcpp_lifecycle_pkg",
    ],
)

apex_cc_library(
    name = "lifecycle_node",
    srcs = [
        "src/lifecycle_node/lifecycle_node.cpp",
    ],
    hdrs = [
        "include/lifecycle_node/lifecycle_node.hpp",
        "include/lifecycle_node/visibility.hpp",
    ],
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    deps = [
        "@apex//common/cpputils",
        "@apex//common/interrupt",
        "@apex//grace/execution/timer_service",
        "@apex//grace/interfaces/lifecycle_msgs",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/monitoring/logging",
        "@apex//grace/ros/rclcpp/rclcpp",
        "@apex//grace/ros/rclcpp/rclcpp_lifecycle",
    ],
)

apex_cc_test(
    name = "test_lifecycle_node",
    srcs = [
        "include/lifecycle_node/lifecycle_node.hpp",
        "test/test_lifecycle_node.cpp",
    ],
    env = {"IDA_RESOURCE_ACQUISITION_STRATEGY": "use_inproc"},
    includes = [
        "include",
    ],
    deps = [
        ":lifecycle_node",
        "@apex//grace/interfaces/test_msgs",
        "@googletest//:gtest_main",
    ],
)
