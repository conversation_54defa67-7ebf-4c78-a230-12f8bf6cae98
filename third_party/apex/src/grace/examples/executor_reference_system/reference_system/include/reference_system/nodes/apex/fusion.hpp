// Copyright 2023 Apex.AI, Inc.
// All rights reserved.
#pragma once

#include <chrono>
#include <string>
#include <utility>

#include "executor2/apex_node_base.hpp"

#include "rclcpp/rclcpp.hpp"
#include "reference_system/msg_types.hpp"
#include "reference_system/nodes/settings.hpp"
#include "reference_system/number_cruncher.hpp"
#include "reference_system/sample_management.hpp"

namespace nodes
{
namespace apex
{
class Fusion : public ::apex::executor::apex_node_base
{
public:
  explicit Fusion(const FusionSettings & settings)
  : ::apex::executor::apex_node_base(settings.node_name),
    number_crunch_limit_(settings.number_crunch_limit)
  {
    subscriptions_[0].subscription =
      get_rclcpp_node().create_polling_subscription<message_t>(
      settings.input_0, 1);

    subscriptions_[1].subscription =
      get_rclcpp_node().create_polling_subscription<message_t>(
      settings.input_1, 1);
    publisher_ =
      get_rclcpp_node().create_publisher<message_t>(settings.output_topic, 1);
  }

  bool is_ready()
  {
    for (uint64_t i = 0; i < 2; ++i) {
      auto cache = subscriptions_[i].subscription->take();
      if (!cache.empty()) {
        subscriptions_[i].cache = std::move(cache);
      }
    }

    return !subscriptions_[0].cache.empty() && !subscriptions_[1].cache.empty();
  }

private:
  bool execute_impl() override
  {
    if(!is_ready()) return false;

    uint64_t timestamp = now_as_int();

    auto number_cruncher_result = number_cruncher(number_crunch_limit_);

    auto output_message = publisher_->borrow_loaned_message();

    uint32_t missed_samples = get_missed_samples_and_update_seq_nr(
      &subscriptions_[0].cache.back().data(),
      subscriptions_[0].sequence_number) +
      get_missed_samples_and_update_seq_nr(
      &subscriptions_[1].cache.back().data(),
      subscriptions_[1].sequence_number);

    output_message.get().size = 0;
    merge_history_into_sample(output_message.get(),
      &subscriptions_[0].cache.back().data());
    merge_history_into_sample(output_message.get(),
      &subscriptions_[1].cache.back().data());
    set_sample(get_rclcpp_node().get_name(), sequence_number_++, missed_samples,
      timestamp, output_message.get());

    output_message.get().data[0] = number_cruncher_result;

    publisher_->publish(std::move(output_message));

    subscriptions_[0].cache.return_loan();
    subscriptions_[1].cache.return_loan();
    return true;
  }

  ::apex::executor::subscription_list get_triggering_subscriptions_impl()
  const override
  {
    return {subscriptions_[0].subscription, subscriptions_[1].subscription};
  }

  struct subscription_t
  {
    rclcpp::PollingSubscription<message_t>::SharedPtr subscription;
    uint32_t sequence_number = 0;
    rclcpp::LoanedSamples<typename message_t::BorrowedType> cache;
  };

  subscription_t subscriptions_[2];
  rclcpp::Publisher<message_t>::SharedPtr publisher_;

  uint64_t number_crunch_limit_;
  uint32_t sequence_number_ = 0;
};

}  // namespace apex
}  // namespace nodes
