// Copyright 2024 Apex.AI, Inc.
// All rights reserved.
#pragma once

#include <chrono>
#include <string>
#include <utility>

#include "executor2/apex_node_base.hpp"

#include "rclcpp/rclcpp.hpp"
#include "reference_system/msg_types.hpp"
#include "reference_system/nodes/settings.hpp"
#include "reference_system/number_cruncher.hpp"
#include "reference_system/sample_management.hpp"

namespace nodes
{
namespace apex
{
class Aggregator : public ::apex::executor::apex_node_base
{
public:
  explicit Aggregator(const AggregatorSettings & settings)
  : ::apex::executor::apex_node_base(settings.node_name),
    m_load(settings.load),
    m_samples_to_aggregate(settings.samples_to_aggregate)
  {
    m_subscription = get_rclcpp_node().create_polling_subscription<message_t>(
      settings.input_topic, 10);
    m_publisher = get_rclcpp_node().create_publisher<message_t>(
      settings.output_topic, 10);
  }

  /// @brief Check and see if this nodes subscription has enough samples yet
  /// Meant to be an execution condition
  bool ready()
  {
    auto loaned_msgs{m_subscription->read()};
    // Check if received samples are less then the desired number of samples
    if (loaned_msgs.size() < m_samples_to_aggregate) {
      return false;
    }
    // Check if all samples are valid
    for (const auto & msg : loaned_msgs) {
      if (!msg.info().valid()) {
        return false;
      }
    }
    // Received the desired number of valid samples
    return true;
  }

  bool execute_impl() override
  {
    uint64_t timestamp = now_as_int();
    auto input_message = m_subscription->take();
    if (input_message.empty()) {
      throw ::apex::runtime_error("no input sample in Aggregator node ",
              get_rclcpp_node().get_name());
    }

    simulate_load(m_load);

    auto output_message = m_publisher->borrow_loaned_message();
    output_message.get().size = 0;
    merge_history_into_sample(output_message.get(),
      &input_message.back().data());

    uint32_t missed_samples = get_missed_samples_and_update_seq_nr(
      &input_message.back().data(), m_input_sequence_number);

    set_sample(get_rclcpp_node().get_name(), m_sequence_number++, missed_samples,
      timestamp, output_message.get());

    output_message.get().data[0] = static_cast<int64_t>(m_wcet_count);
    m_publisher->publish(std::move(output_message));
    return true;
  }

  ::apex::executor::subscription_list get_triggering_subscriptions_impl()
  const override
  {
    return {m_subscription};
  }

  inline void increment_wcet_count() {m_wcet_count++;}

private:
  rclcpp::Publisher<message_t>::SharedPtr m_publisher;
  rclcpp::PollingSubscription<message_t>::SharedPtr m_subscription;
  std::chrono::nanoseconds m_load;
  uint64_t m_samples_to_aggregate;
  uint32_t m_sequence_number = 0;
  uint32_t m_input_sequence_number = 0;
  uint64_t m_wcet_count = 0;
};

}  // namespace apex
}  // namespace nodes
