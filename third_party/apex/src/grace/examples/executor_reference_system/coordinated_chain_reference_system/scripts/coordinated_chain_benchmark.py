#!/usr/bin/env python3
# Copyright 2023 Apex.AI, Inc.
# Generates traces for specified executables and RMWs

from reference_system_py.benchmark import ROS_HOME, setup_benchmark_directory
from reference_system_py.benchmark import available_executables, generate_trace
from reference_system_py.benchmark import roudi_daemon
from reference_system_py.report import generate_report, generate_summary_report

from ament_index_python.packages import get_package_share_directory
import argparse
import itertools

def main():
    parser = argparse.ArgumentParser(description='Benchmark an executor implementation.')
    parser.add_argument('runtimes',
                        help="comma-separated list of runtimes (in seconds)")
    parser.add_argument('executables',
                        help="comma-separated list of target executables")
    parser.add_argument('--trace_types',
                        help="comma-separated list of trace types (default: utilization,std)",
                        default="utilization,std")
    parser.add_argument('--rmws',
                        default="rmw_apex_middleware",
                        help=("comma-separated list of rmw implementations " +
                              "(default: rmw_apex_middleware)"))
    parser.add_argument('--logdir',
                        default=None,
                        help=("The directory where traces and results are placed (default: " +
                              f"{ROS_HOME}/benchmark_<pkg>/<timestamp>)"))
    parser.add_argument('--plot_only',
                        default=False,
                        help="Only plot existing data, do not run new experiments.",
                        action="store_true")
    parser.add_argument('--roudi_config',
                        default=None,
                        help=("Configuration file for the RouDi daemon (default: " +
                              "param/roudi.toml in this package's share directory)." +
                              'Specify "" to use RouDi\'s default configuration.'))
    parser.add_argument('--template_dir',
                        default=None,
                        help='Path to directory where template files are stored')
    parser.add_argument('--template_types',
                        default=['md', 'html'],
                        help='List of template types to use to generate reports')
    parser.add_argument('--summary_only',
                        default=False,
                        help="Only generate summary report from existing data, do not run new " +
                             "experiments or check executables match for all data.",
                        action="store_true")

    cmdline_args = parser.parse_args()
    common_args = {'pkg': 'coordinated_chain_reference_system',
                   'directory': cmdline_args.logdir}

    if common_args['directory'] is None:
        create_dir = (not cmdline_args.plot_only)
        common_args['directory'] = str(setup_benchmark_directory(pkg=common_args["pkg"],
                                                                 create=create_dir))

    runtimes = [int(runtime) for runtime in cmdline_args.runtimes.split(',')]
    exe_patterns, rmws, trace_types = (lst.split(',')
                                       for lst in [cmdline_args.executables,
                                                   cmdline_args.rmws,
                                                   cmdline_args.trace_types])

    if cmdline_args.roudi_config is None:
        roudi_config = get_package_share_directory(common_args['pkg'])+'/param/roudi.toml'
    elif cmdline_args.roudi_config == "":
        roudi_config = None
    else:
        roudi_config = cmdline_args.roudi_config

    exes = [exe for pattern in exe_patterns
            for exe in available_executables(pattern=pattern, pkg=common_args['pkg'])]

    if not cmdline_args.summary_only:
        for runtime, exe, rmw, trace_type in itertools.product(runtimes, exes, rmws, trace_types):
            if not cmdline_args.plot_only:
                with roudi_daemon(roudi_config_path=roudi_config):
                    generate_trace(trace_type, exe, rmw=rmw, runtime_sec=runtime, **common_args)
            generate_report(
                trace_type,
                exe,
                rmw=rmw,
                runtime_sec=runtime,
                template_types=cmdline_args.template_types,
                **common_args)
    if not cmdline_args.template_dir:
        template_dir = get_package_share_directory(common_args['pkg'])+'/cfg/'

    for trace_type, runtime in itertools.product(trace_types, runtimes):
        generate_summary_report(
            trace_type=trace_type,
            runtime_sec=runtime,
            template_dir=template_dir,
            template_types=cmdline_args.template_types,
            **common_args)


if __name__ == '__main__':
    main()
