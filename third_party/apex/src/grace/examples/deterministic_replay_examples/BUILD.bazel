load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "install_space")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test_in_install_space")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_pkg//:mappings.bzl", "pkg_files")

# Setting visibility to public for ease of use.
# Not recommended for production code
package(default_visibility = ["//visibility:public"])

ros_pkg(
    name = "deterministic_replay_examples_pkg",
    description = "Example of deterministic replay with a dummy application",
    lib_executables = [
        "sut_exe",
        "sut_chain_of_nodes_exe",
        "sut_graph_of_nodes_exe",
        "sut_cyclic_timer_exe",
        "node_a",
        "node_b",
        "node_c",
        "node_d",
        "node_e",
    ],
    license = "Apex.AI License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "deterministic_replay_examples",
    share_data = [":share_data"],
    version = "0.0.1",
    visibility = ["//visibility:public"],
    deps = [
        "//common/interrupt:interrupt_pkg",
        "//grace/execution/apex_init:apex_init_pkg",
        "//grace/execution/executor2:executor2_pkg",
        "//grace/interfaces/replay_msgs:replay_msgs_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/recording/replay:replay_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
    ],
)

pkg_files(
    name = "share_data",
    srcs = glob([
        "test/**/*.yaml",
        "test/**/*.db3",
    ]),
    prefix = "rosbag2_deterministic_replay_test",
    visibility = ["//visibility:public"],
)

cc_library(
    name = "sut_lib",
    srcs = ["src/deterministic_node.cpp"],
    hdrs = [
        "include/deterministic_replay_examples/deterministic_node.hpp",
        "include/deterministic_replay_examples/utils.hpp",
    ],
    strip_include_prefix = "include",
    tags = ["exclude_sca"],
    deps = [
        "//grace/execution/executor2",
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp",
    ],
)

cc_binary(
    name = "sut_exe",
    srcs = ["src/main.cpp"],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    defines = ["REPLAY_SUPPORT=TRUE"],
    deps = [
        ":sut_lib",
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/interfaces/std_msgs",
        "//grace/recording/replay",
    ],
)

cc_binary(
    name = "sut_chain_of_nodes_exe",
    srcs = ["src/main_chain_of_nodes.cpp"],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    defines = ["REPLAY_SUPPORT=TRUE"],
    deps = [
        ":sut_lib",
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/interfaces/std_msgs",
        "//grace/recording/replay",
    ],
)

cc_binary(
    name = "sut_graph_of_nodes_exe",
    srcs = ["src/main_graph_of_nodes.cpp"],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    defines = ["REPLAY_SUPPORT=TRUE"],
    deps = [
        ":sut_lib",
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/interfaces/std_msgs",
        "//grace/recording/replay",
    ],
)

cc_binary(
    name = "sut_cyclic_timer_exe",
    srcs = ["src/main_cyclic_timer.cpp"],
    # TODO(kyle.marcey): #24903 Remove line below
    copts = ["-Wno-error"],
    defines = ["REPLAY_SUPPORT=TRUE"],
    deps = [
        ":sut_lib",
        "//common/interrupt",
        "//grace/execution/apex_init",
        "//grace/execution/executor2",
        "//grace/interfaces/std_msgs",
        "//grace/recording/replay",
    ],
)

[
    cc_binary(
        name = "{}".format(name),
        srcs = [
            "src/inter_process/{}.cpp".format(name),
        ],
        defines = ["REPLAY_SUPPORT=TRUE"],
        deps = [
            ":sut_lib",
            "//common/interrupt",
            "//grace/execution/apex_init",
            "//grace/execution/executor2",
            "//grace/interfaces/std_msgs",
            "//grace/recording/replay",
        ],
    )
    for name in [
        "node_a",
        "node_b",
        "node_c",
        "node_d",
        "node_e",
    ]
]

install_space(
    name = "install_space_for_tests",
    message_library_kinds = [
        "introspection_cpp",
        "py",
    ],
    ros_pkgs = [
        "//tools/launch/launch:launch_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        ":deterministic_replay_examples_pkg",
        "//grace/recording/replay:replay_pkg",
        "//grace/recording/rosbag2/rosbag2_storage_sqlite3:rosbag2_storage_sqlite3_pkg",
    ],
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = "@apex//grace/interfaces/std_msgs",
)

launch_test_in_install_space(
    name = "deterministic_replay",
    data = [
        ":node_a",
        ":node_b",
        ":node_c",
        ":node_d",
        ":node_e",
        ":share_data",
    ],
    install_space = ":install_space_for_tests",
    launch_test_file = "test/deterministic_replay.test.py",
    tags = ["constrained_test"],
    deps = [
        requirement("pytest"),
        ":std_msgs_py",
        "//grace/tools/launch_ros/launch_ros",
        "//tools/apex_pytest_utils",
        "//tools/launch/launch",
    ],
)

launch_test_in_install_space(
    name = "deterministic_replay_chain_of_nodes",
    data = [
        ":node_a",
        ":node_b",
        ":node_c",
        ":node_d",
        ":node_e",
        ":share_data",
    ],
    install_space = ":install_space_for_tests",
    launch_test_file = "test/deterministic_replay_chain_of_nodes.test.py",
    # TODO(carlos): this is a long running test, add tag to run the test in a nightly job #24442
    tags = [
        "constrained_test",
        "manual",
    ],
    deps = [
        requirement("pytest"),
        ":std_msgs_py",
        "//grace/tools/launch_ros/launch_ros",
        "//tools/apex_pytest_utils",
        "//tools/launch/launch",
    ],
)

launch_test_in_install_space(
    name = "deterministic_replay_cyclic_timer",
    data = [
        ":node_a",
        ":node_b",
        ":node_c",
        ":node_d",
        ":node_e",
        ":share_data",
    ],
    install_space = ":install_space_for_tests",
    launch_test_file = "test/deterministic_replay_cyclic_timer.test.py",
    # TODO(carlos): this is a long running test, add tag to run the test in a nightly job #24442
    tags = [
        "constrained_test",
        "manual",
    ],
    deps = [
        requirement("pytest"),
        ":std_msgs_py",
        "//grace/tools/launch_ros/launch_ros",
        "//tools/apex_pytest_utils",
        "//tools/launch/launch",
    ],
)

launch_test_in_install_space(
    name = "deterministic_replay_graph_of_nodes",
    data = [
        ":node_a",
        ":node_b",
        ":node_c",
        ":node_d",
        ":node_e",
        ":share_data",
    ],
    install_space = ":install_space_for_tests",
    launch_test_file = "test/deterministic_replay_graph_of_nodes.test.py",
    # TODO(carlos): this is a long running test, add tag to run the test in a nightly job #24442
    tags = [
        "constrained_test",
        "manual",
    ],
    deps = [
        requirement("pytest"),
        ":std_msgs_py",
        "//grace/tools/launch_ros/launch_ros",
        "//tools/apex_pytest_utils",
        "//tools/launch/launch",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "src/deterministic_node.cpp",
        "src/main.cpp",
        "src/main_chain_of_nodes.cpp",
        "src/main_cyclic_timer.cpp",
        "src/main_graph_of_nodes.cpp",
    ],
    visibility = [":__subpackages__"],
)
