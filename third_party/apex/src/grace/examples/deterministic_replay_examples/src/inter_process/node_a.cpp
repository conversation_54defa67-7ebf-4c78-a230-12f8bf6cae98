/// \copyright Copyright 2021 Apex.AI, Inc.
/// All rights reserved.

#include <deterministic_replay_examples/deterministic_node.hpp>

#include <apex_init/apex_init.hpp>
#include <executor2/executor_factory.hpp>
#include <executor2/executor_runner.hpp>
#include <interrupt/interrupt_handler.hpp>
#include <replay/messenger.hpp>

#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace sut = apex::sut;
using namespace std::chrono_literals;

void run(apex::executor::executor_base & exec)
{
  auto node = std::make_shared<sut::DeterministicNode>(
    "node_a", "topic_y", std::vector<apex::string_strict256_t>{"topic_x"});

  exec.add(node);
  if (apex::post_init() != APEX_RET_OK) {
    throw std::runtime_error("Can't post-init Apex.OS");
  }

  const apex::executor::executor_runner runner{exec};
  apex::interrupt_handler::wait();
}
void run_live()
{
  const auto exec = apex::executor::executor_factory::create();
  run(*exec);
}

void run_replay()
{
  apex::replay::messenger messenger("apex_replay_messenger");
  const auto exec = apex::executor::executor_factory::create_for_replay(
    messenger);
  run(*exec);
}
std::int32_t main(const std::int32_t argc, char ** const argv)
{
  if (apex::pre_init(argc, argv, false) != APEX_RET_OK) {
    throw std::runtime_error("Can't pre-init Apex.OS");
  }

  const apex::interrupt_handler::installer interrupt_handler_installer{};

  if (argc > 1 && std::string(argv[1]) == "live") {
    std::cout << "------------- Live mode -------------" << std::endl;
    run_live();

  } else {
    std::cout << "------------- Replay mode -------------" << std::endl;
    run_replay();
  }
}
