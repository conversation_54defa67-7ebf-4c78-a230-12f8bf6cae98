---
tags:
  - Bazel
  - Colcon
  - process_manager
---

# Composition example

## Description

For large launch files it is possible to make use of [settings_extensions](settings-extensions-design.md)
to define process groups in separate files and compose them.

## Launch file

{{ code_snippet(
'grace/examples/process_manager_examples/launch/example_composed.launch.yaml',
{'tag': '# [launch]', "skip_prefix": "#!"},
'yaml') }}

{{ code_snippet(
'grace/examples/process_manager_examples/launch/group1.yaml',
{'tag': '# [launch]'},
'yaml') }}

{{ code_snippet(
'grace/examples/process_manager_examples/launch/group2.yaml',
{'tag': '# [launch]'},
'yaml') }}

## Usage

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_composed
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager apex_process_manager --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_composed.launch.yaml
    ```

<!-- markdownlint-disable restore -->

## Introspection

In order to introspect the launch file it is possible to use the introspection tool:

<!-- markdownlint-disable MD046 -->
=== "Bazel"

    ```shell dollar
    bazel run @apex//grace/examples/process_manager_examples:example_composed.introspection -- --print
    ```

=== "Colcon"

    ```shell ade
    (ade)$ ros2 run process_manager introspection --apex-settings-file \
        $(ros2 pkg prefix --share process_manager_examples)/launch/example_composed.launch.yaml --print
    ```

<!-- markdownlint-disable restore -->

The following information should be printed out, and it can be visualized with a Markdown editor:

- [Introspection output](example-composed-introspection.md)
