# Process manager introspection

This is the result of the `process_manager` introspection tool.

## Process group name: group1

Group options:

```yaml
log file enable: 1
log directory: ""
log file buffering: 1
```

### State: ON

#### Process list

##### Process: minimal_process

Command:

```shell ade
/opt/ApexOS/lib/process_manager/minimal_process --name minimal_process
```

#### Process startup dependencies

```mermaid
graph TD
    n0(minimal_process)
```

#### Process shutdown dependencies

```mermaid
graph TD
    n0(minimal_process)
```

### State: OFF

#### Process list

None

#### Process startup dependencies

Empty

#### Process shutdown dependencies

Empty

### State transition action sequence

OFF --> ON

```mermaid
graph TD
    n0(Start minimal_process)
```

ON --> OFF

```mermaid
graph TD
    n0(Stop minimal_process)
```
