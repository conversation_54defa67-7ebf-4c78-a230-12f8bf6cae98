cmake_minimum_required(VERSION 3.5)

project(timer_service_examples)

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

if((NOT QNX) AND (NOT (CMAKE_SYSTEM_PROCESSOR STREQUAL "aarch64")) AND (NOT APEX_CERT))
  ament_auto_add_executable(minimal_example src/timer_service_examples/minimal_example.cpp)
  apex_set_compile_options(minimal_example)

  ament_auto_add_executable(timer_service_with_replay
      src/timer_service_examples/timer_service_with_replay.cpp)
  apex_set_compile_options(timer_service_with_replay)

  ament_auto_add_executable(timeout_timer
      src/timer_service_examples/timeout_timer.cpp)
  apex_set_compile_options(timeout_timer)

  ament_auto_add_executable(timer_subscription
      src/timer_service_examples/timer_subscription.cpp)
  apex_set_compile_options(timer_subscription)
endif()

ament_auto_package()
