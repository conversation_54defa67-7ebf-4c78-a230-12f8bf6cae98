# Copyright 2019 Apex.AI, Inc.
# All rights reserved.
#! [Minimal CMake]
cmake_minimum_required(VERSION 3.5)

#! [Project]
project(apex_os_minimal_pub_sub)
#! [Project]

#! [Find Dependencies]
find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()
#! [Find Dependencies]

#! [Auto Add Executables]
#! [Build Target Publisher]
ament_auto_add_executable(publisher src/minimal_publisher.cpp)
#! [Build Target Publisher]
ament_auto_add_executable(subscriber src/minimal_subscriber.cpp)
#! [Auto Add Executables]

if(BUILD_TESTING)
  #! [Auto Linters]
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
  #! [Auto Linters]


  if(NOT APEX_CERT)
    find_package(ros_testing REQUIRED)

    add_ros_test(
        test/create_distributed_system.doctest.py
        TIMEOUT "180"
    )
  endif()
endif()

#! [Install Package]
ament_auto_package(
    INSTALL_TO_SHARE
    doc
    launch
)
#! [Install Package]
#! [Minimal CMake]
