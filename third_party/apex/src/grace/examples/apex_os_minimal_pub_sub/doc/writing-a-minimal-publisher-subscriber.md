# Writing a minimal publisher and subscriber

This tutorial covers the basic steps to create, build, and run a new package from scratch:

1. [A quick description of the application](#description-of-the-publisher-and-subscriber-application)
1. [Commands to create a new package in Apex.Grace](#create-an-apexgrace-package)
1. [Walk through of the minimal code of a publisher-subscriber](#the-publisher-node)
1. [Running and introspecting the application](#build-and-run-the-package)

## Description of the publisher and subscriber application

In this tutorial, the publisher application sends data in the form of string messages to the
subscriber node over the `/minimal_chatter` topic. The message is defined in the `String.idl` file,
which is already shipped in the Apex.Grace volume. Code is generated to read and write the messages in
a variety of languages, including C++. For example, observe the generated `struct` for the `String`
message:

```shell ade
cat /opt/ApexOS/include/std_msgs/msg/string__struct.hpp
```

The ROS graph of the system looks as follows:

```mermaid
    flowchart LR
        p-->|topic /minimal_chatter|s
        subgraph Publisher application
        p[Apex.Grace node]
        end
        subgraph Subscriber application
        s[Apex.Grace node]
        end
```

!!! note
    This example is adapted from the [writing a simple C++ publisher and
    subscriber](https://docs.ros.org/en/rolling/Tutorials/Beginner-Client-Libraries/Writing-A-Simple-Cpp-Publisher-And-Subscriber.html)
    ROS 2 article.

## Create an Apex.Grace package

Apex.Grace like ROS organizes the code into packages. A package can be considered as a small contained
unit. Packages make it easier to share code and artifacts to others.

Apex.AI provides the `apex_create_pkg` script that enables developers to generate a boiler-plate
package with the basic configuration to compile and run their application.

The most valuable feature of this boiler-plate package is that it is configured to have all the
style-checker and static-code analyzers enabled and passing by default, such that from the outset,
developers can test that all new code remains compliant.

The packages need to be created in a directory. In the ROS terminology, this directory is called
[workspace](https://docs.ros.org/en/rolling/Tutorials/Beginner-Client-Libraries/Creating-A-Workspace/Creating-A-Workspace.html?highlight=workspace).
For example, Apex.Grace packages live in the [`apex_ws`
directory](https://gitlab-customer.apex.ai/apexai/apexos-src/-/tree/master/apex_ws).

Create a package by either specifying all required information on the command line, as follows:

{{ console_snippet(
    "grace/integration_tests/os_verify_docs/test/test_writing_a_minimal_publisher_subscriber.py",
    config={
      "remove_indent": 4,
      "add_shell_prefix": "(ade)$",
      "tag": "#! CREATE",
      "stdout_tag": "#! CREATE_OUTPUT"
      }
)}}

Or by not passing arguments, in which case the tool will prompt for all required information:

```shell ade
apex_create_pkg
```

By default, the `apex_create_pkg` command creates minimal publisher and minimal subscriber nodes
that can be used as a template for more complex nodes.

See the `apex_create_pkg --help` output for more information on the command-line arguments,
especially for the choices of `--license` and `--copyright`.

{{ console_snippet(
    "grace/integration_tests/os_verify_docs/test/test_writing_a_minimal_publisher_subscriber.py",
    config={
      "remove_indent": 4,
      "add_shell_prefix": "(ade)$",
      "tag": "#! HELP",
      "stdout_tag": "#! HELP_OUTPUT"
      }
)}}

### Package contents

The generated package has the following structure:

```shell ade
(ade)$ tree -L 1 ~/workspace/src/apex_os_minimal_pub_sub
~/workspace/src/apex_os_minimal_pub_sub
├── BUILD.bazel
├── CMakeLists.txt
├── design
├── launch
├── package.xml
├── src
└── test

5 directories, 3 files
```

- `BUILD.bazel` (Bazel only)
    - the equivalent of `CMakeLists.txt` for [Bazel](https://bazel.build/)
- `CMakeLists.txt` (colcon/CMake only)
    - Describes the build configuration
    - Specifies how to compile, test, and install the package
    - See the [ament CMake article](best-practices-for-ament-cmake.md) for more info on how to
      modify the build rules
- `design`
    - A folder to keep documentation on the design of the libraries and executables in the package
    - These design documents describe the details of the code in an organized, human-readable way
      such that others can get an overview of the package
- `launch`
    - A folder for [`process_manager`](process-manager-design.md) configuration files
- `package.xml` (colcon only)
    - Contains information about the package including maintainer, version, a brief description,
      and the packages' dependencies. For more information see
      the [package manifest format three
      specification](http://www.ros.org/reps/rep-0149.html)
- `src`
    - A folder for source files
- `test`
    - A folder for all the test sources
- A package may also include other folders such as `include`, `cmake`, `config`, `msg`, `param`,
  `scripts`, etc, depending on the specific use-case (not covered in this tutorial)
- The Apex.Grace package layout is compatible with the [ROS 2 package
  layout](https://docs.ros.org/en/rolling/Contributing/Developer-Guide.html#filesystem-layout)

!!! tip "Important"
    The generated package from `apex_create_pkg` is slightly different from the included
    `apex_os_minimal_pub_sub` Apex.Grace package, which is referenced below. Both include a
    minimal Apex.Grace publisher node binary and a minimal Apex.Grace subscription node binary,
    however, the setup/configuration of each is slightly different.

## The publisher node

### Included headers

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [Publisher includes]", "skip_prefix": "//!"}, "cpp") }}

1. Includes APIs to initialize the Apex.Grace and ROS 2 context
2. Includes APIs for the Apex.Grace exceptions
3. Includes APIs for the Apex.Grace node
4. Includes APIs to instantiate the Apex.Grace executor
5. Includes APIs to configure the Apex.Grace executor
6. Includes APIs to set up the signal handler
7. Includes APIs for logging macros
8. Includes message definitions
9. Includes APIs for the Apex.Grace timer service
10. Includes APIs for process monitoring

### Constructor implementation

The `MinimalPublisher` class inherits from the `apex_node_base` class.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [Constructor]", "skip_prefix": "//!"}, "cpp") }}

1. The public delegated constructor `apex_node_base` names the node `node_name`
2. The `get_rclcpp_node()` method provides related [](rclcpp::Node)
3. The publisher `m_publisher` is initialized with the `create_publisher()`
   method, `std_msgs::msg::String` message type, the topic name `topic`,
   and the QOS profile [](rclcpp::DefaultQoS)
4. Pointer to timer subscription which will trigger the execution and cause the
   publisher to publish

### Waiting for matched subscriber

In publish/subscribe systems like Apex.Grace, publishers do not know ahead of time how many
other nodes will subscribe to a given topic. They will therefore publish messages as soon as the
`publish` method is invoked, even if not all subscribers have been matched yet.

In deterministic production systems, where the number of expected subscribers is usually known
in advance, it is therefore good practice to explicitly wait for enough subscribers to match
before leaving the node's initialization phase.

In this example, the application waits for at least one subscriber to match, with a timeout of 5
seconds. The method should be called before the start of the executor.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [wait_for_matched]", "skip_prefix": "//!"}, "cpp") }}

### Implementation of the `execute_impl()` method

This is where the algorithm is implemented. In this example, a message is set and published
in the `execute_impl()` method. The `execute_impl()` method will be called by the executor.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [function]", "skip_prefix": "//!"}, "cpp") }}

1. Sets the `std_msgs::msg::String` message data
2. Logs the passed string with the `INFO` level

### Implementation of the `main()` function

In the `main()` function, the task execution strategy is defined,
and interrupt signals, as well as exceptions, are handled.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_publisher.cpp",
    {"tag": "//! [Minimal publisher main function]", "skip_prefix": "//!"}, "cpp") }}

1. Apex.Grace handles the parsing of command line arguments. During the initialization
   phase, memory allocation is allowed
2. [](apex::scoped_init) initializes Apex.Grace and ROS 2.
   This has to happen before a node can be used
3. Create a timer to execute the publisher periodically
4. Instantiates the publisher node
5. Opt in for [process monitoring](event-design.md#process-monitoring). `DoNotSyncWithDispatcher` means
   that no error will be thrown if the Event Dispatcher has not yet been started.
6. Creates an Apex.Grace executor
7. Waits for expected subscribers and prevents unnecessary execution of the node.
   This is a blocking call, it returns once a subscriber has subscribed to
   the publisher's topic or throws on timeout
8. `minimal_publisher` executable item is added to the executor with a
   periodic timeout of `500 ms`. When the timer expires,
   the executor calls the `execute_impl` method of the `minimal_publisher`.
9. Instantiates an RAII runner to run the executor on a separate thread
   while the main thread is handling interrupt signals
10. After this line, memory allocation should not take place. The
   [](apex::event::monitored_process) is passed in
   order to report the process has been initialized successfully to the
   [Process Manager](process-manager-design.md)
11. Starts an execution thread. This call returns immediately
    and the `runner` keeps going indefinitely
12. The method [](apex::interrupt_handler::wait) returns when a signal is received
13. Shuts down the runner
14. Any caught exceptions are then handled. Note that `rclcpp` needs to be
    initialized before calling the Apex.Grace logger (e.g. `APEX_FATAL_R`)

## The subscriber node

### Constructor implementation

The `MinimalSubscriber` class inherits from the `apex_node_base` class.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_subscriber.cpp",
    {"tag": "//! [Constructor]", "skip_prefix": "//!"}, "cpp") }}

1. The subscriber `m_subscription` is initialized with the `create_polling_subscription()` method.
   The topic name, message type, and QOS used by the publisher and subscriber must match to allow
   the publisher and subscriber to communicate. For more details see the
   [QOS policies](configuring-qos.md) article
2. The Apex.Grace logger is initialized with the pointer to the
   related [](rclcpp::Node) and logger name

### Implementation of the `execute_impl()` method

A message is received and processed in the `execute_impl()` method.
The `execute_impl()` method will be called by the executor.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_subscriber.cpp",
    {"tag": "//! [function]", "skip_prefix": "//!"}, "cpp") }}

1. `take()` removes all the messages from the `PollingSubscription` queue while `read()`
   leaves the messages in the queue so that it can be accessed again later.
   Returns a sequence of messages. The size of the sequence corresponds to the number of messages
   in the queue. For more details refer to the [PollingSubscriptions]
   (polling-subscriptions.md) article
2. Checks that the message is valid. The middleware may deliver
   samples that only have a valid header and no valid message. Accessing
   an invalid message payload can lead to undefined behavior
3. The method `data()` is part of the
   [](apex::dds_typesupport::LoanedSample) API and
   the second `data` is a member particular to the current message type,
   in this case the `std_msgs::msg::String` type

### Triggering subscriptions

Override the `get_triggering_subscriptions_impl()` method to declare the item's subscription as
a triggering subscription.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/src/minimal_subscriber.cpp",
    {"tag": "//! [Get triggering subscriptions]", "skip_prefix": "//!"}, "cpp") }}

## package.xml

The `package.xml` manifest lists the dependencies to the other packages.

The following line defines the package name:

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/package.xml",
    {"tag": "<!--! [Name] -->"}, "xml") }}

The following build tool dependencies are always required:

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/package.xml",
    {"tag": "<!--! [Buildtool Dependencies] -->"}, "xml") }}

There are several includes in the `minimal_publisher.cpp` and the `minimal_subscriber.cpp` files,
hence the following build dependencies:

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/package.xml",
    {"tag": "<!--! [Build Dependencies] -->"}, "xml") }}

The `apex_os_minimal_pub_sub` package depends on the following shared libraries when
its code is executed:

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/package.xml",
    {"tag": "<!--! [Execution Dependencies] -->"}, "xml") }}

## CMakeLists.txt

The `CMakeLists.txt` file is the input to the CMake build system for building
software packages.

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/CMakeLists.txt",
    {"tag": "#! [Project]"}, "cmake") }}

defines the name of the project. The name should match the package name defined
in the `package.xml` manifest.

The following lines are always required:

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/CMakeLists.txt",
    {"tag": "#! [Find Dependencies]"}, "cmake") }}

`ament_auto_find_build_dependencies()` uses the `<buildtool_depend>` and
`<build_depend>` entries in the `package.xml` to run `find_package()`.

Then the build targets need to be specified:

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/CMakeLists.txt",
    {"tag": "#! [Auto Add Executables]", "skip_prefix": "#!"}, "cmake") }}

Finally,

{{ code_snippet("grace/examples/apex_os_minimal_pub_sub/CMakeLists.txt",
    {"tag": "#! [Install Package]"}, "cmake") }}

uses the `<exec_depend>`, `<buildtool_export_depend>` and `<build_export_depend>`
entries in the `package.xml` to call `ament_export_dependencies` which ensures
that this package's dependents will load the correct transient dependencies.

### Update the compilation options

<!-- see gc#28464 -->

Call to `apex_set_compile_options` in the `CMakeLists.txt` file
must be disabled, since they define a set of compilation flags
not compatible with Apex.Ida 3

Add the following compilation options for library and binary targets:

```plain
"-Wno-error=overloaded-virtual -Wno-error=missing-field-initializers"
```

If the step above is skipped, the following compilation error will appear:

```plain
/opt/ApexIdaBilbo/include/cyclonedds-cxx/__repo_root__/src/ddscxx/include/dds/core/macros.hpp:71:19: error: ‘dds::core::TEntity<DELEGATE>& dds::core::TEntity<DELEGATE>::operator=(dds::core::TEntity<DELEGATE>&&) [with DELEGATE = org::eclipse::cyclonedds::sub::AnyDataReaderDelegate]’ was hidden [-Werror=overloaded-virtual]
   71 |     virtual TYPE& operator=(TYPE&& tomove) { if (this != &tomove) this->dds::core::Reference<DELEGATE>::operator=(dynamic_cast<dds::core::Reference<DELEGATE>&& >(tomove)); return *this;}
```

For more information, see the [ament_cmake best practices article](best-practices-for-ament-cmake.md).

## Build and run the package

ROS 2 provides a build tool called `colcon` to build and test packages. The `colcon build` command
should always be invoked at the root of the workspace. `colcon` is explained in more details in the
[building, testing, and installing packages with colcon](building-with-colcon.md) tutorial.

Build the package using the `--packages-select <package-name>` argument:

{{ console_snippet(
    "grace/integration_tests/os_verify_docs/test/test_writing_a_minimal_publisher_subscriber.py",
    config={
      "remove_indent": 4,
      "add_shell_prefix": "(ade)$",
      "tag": "#! BUILD",
      "stdout_tag": "#! BUILD_OUTPUT"
      }
)}}

Once the package has been build, the workspace architecture typically looks as follows:

```shell ade
(ade)$ tree -L 1 ~/workspace/
~/workspace/
├── build
├── install
├── log
└── src

4 directories, 0 files
```

- `build`
    - Contains the intermediate build artifacts
- `install`
    - Contains the installed software and scripts to enable it
- `log`
    - Contains console colcon command output
- `src`
    - Contains the source files

Now, run the nodes with `ros2 run`:

{{ console_snippet(
    "grace/integration_tests/os_verify_docs/test/test_writing_a_minimal_publisher_subscriber.py",
    config={
      "remove_indent": 4,
      "add_shell_prefix": "(ade)$",
      "tag": "#! RUN_WITH_ROS2_RUN",
      "stdout_tag": "#! RUN_WITH_ROS2_RUN_OUTPUT"
    }
)}}

Press ++ctrl+c++ to stop the nodes.
