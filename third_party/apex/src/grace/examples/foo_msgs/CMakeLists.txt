# Copyright 2020 Apex.AI, Inc.
# All rights reserved.
cmake_minimum_required(VERSION 3.5)

project(foo_msgs)

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/Bar.idl"
  "msg/BarArray.idl"
  DEPENDENCIES
    "std_msgs" # std_msgs is a dependency to get std_msgs/Header in Bar.idl
)

if(BUILD_TESTING)
  find_package(apex_test_tools REQUIRED)

  # Common linters
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()

  # Unit tests
  find_package(ament_cmake_gtest)
  apex_test_tools_add_gtest(gtest_foo_msgs test/test_foo_msgs.cpp)
  if(TARGET gtest_foo_msgs)
    rosidl_get_typesupport_target(cpp_typesupport_target ${PROJECT_NAME} "rosidl_typesupport_cpp")
    target_link_libraries(gtest_foo_msgs "${cpp_typesupport_target}")
  endif()

  find_package(ament_cmake_pytest REQUIRED)

  ament_add_pytest_test(test_foo_msgs_py
    "test/test_foo_msgs_py.py"
  )
endif()

ament_export_dependencies(
    "std_msgs"
    "rosidl_default_runtime")

ament_auto_package()
