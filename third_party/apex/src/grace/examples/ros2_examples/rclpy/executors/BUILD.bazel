load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@rules_python//python:defs.bzl", "py_library")

# Setting visibility to public for ease of use.
# Not recommended for production code
package(default_visibility = ["//visibility:public"])

# bazelization_report ignore: test/test_copyright.py
# bazelization_report ignore: test/test_flake8.py
# bazelization_report ignore: test/test_pep257.py

ros_pkg(
    name = "examples_rclpy_executors_pkg",
    description = "Examples of creating and using exectors to run multiple nodes in rclpy.",
    license = "Apache License, Version 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "examples_rclpy_executors",
    py_libraries = [
        ":examples_rclpy_executors",
    ],
    version = "0.11.2",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
        "Topic :: Software Development",
    ],
    whl_entry_points = {
        "console_scripts": [
            "listener = examples_rclpy_executors.listener:main",
            "talker = examples_rclpy_executors.talker:main",
            "callback_group = examples_rclpy_executors.callback_group:main",
            "composed = examples_rclpy_executors.composed:main",
            "custom_executor = examples_rclpy_executors.custom_executor:main",
            "custom_callback_group = examples_rclpy_executors.custom_callback_group:main",
        ],
    },
    deps = [
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = "@apex//grace/interfaces/std_msgs",
)

py_library(
    name = "examples_rclpy_executors",
    srcs = [
        "examples_rclpy_executors/__init__.py",
        "examples_rclpy_executors/callback_group.py",
        "examples_rclpy_executors/composed.py",
        "examples_rclpy_executors/custom_callback_group.py",
        "examples_rclpy_executors/custom_executor.py",
        "examples_rclpy_executors/listener.py",
        "examples_rclpy_executors/talker.py",
    ],
    data = [":examples_rclpy_executors_pkg.wheel_data"],
    imports = ["."],
    deps = [
        ":std_msgs_py",
        "//grace/ros/rclpy/rclpy",
    ],
)
