logging:
  level: TRACE
  enabled: true

process-manager:
  process-groups:
    - name: "functional_processes"
      init-state: "ON"
      processes:
        - name: "gateway"
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/francaidl/host1_someip_gateway"
        - name: "publisher"
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/francaidl/publisher"
        - name: "server"
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "../apex/grace/examples/someip_connector/francaidl/server"
      states:
        - name: "ON"
          processes:
            - name: "gateway"
            - name: "publisher"
            - name: "server"
          process-dependencies:
            direct-dependency:
              - process: "server"
                depends-on: "gateway"
              - process: "publisher"
                depends-on: "gateway"
