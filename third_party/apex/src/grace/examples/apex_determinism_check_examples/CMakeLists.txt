cmake_minimum_required(VERSION 3.5)

project(apex_determinism_check_examples)

find_package(ament_cmake_auto REQUIRED)

# If APEX_DETERMINISM_CHECKS is not set, this package has no real purpose
if(NOT APEX_DETERMINISM_CHECKS)
  message(WARNING "APEX_DETERMINISM_CHECKS is not set;
    use '--mixin cert-tracing-checks' to properly test this example.")
endif()

ament_auto_find_build_dependencies()

ament_auto_add_library(${PROJECT_NAME}
  include/apex_determinism_check_examples/header.hpp
  include/apex_determinism_check_examples/visibility_control.hpp
  src/file.cpp)

apex_set_compile_options(${PROJECT_NAME})

# Testing
if(BUILD_TESTING)
  find_package(ament_lint_auto)
  find_package(apex_determinism_check)
  ament_lint_auto_find_test_dependencies()

  ament_add_gtest(test_example
    test/test.cpp
    test/test_example.cpp
    APPEND_ENV ${APEX_DETERMINISM_CHECKS_ENV_LD_PRELOAD})
  #! [apex_determinism_check add target dependencies]
  ament_target_dependencies(test_example apex_determinism_check cpputils)
  #! [apex_determinism_check add target dependencies]
  target_link_libraries(test_example ${PROJECT_NAME})
  ament_add_gtest(
    test_register_callback test/test_register_callback.cpp
    APPEND_ENV ${APEX_DETERMINISM_CHECKS_ENV_LD_PRELOAD})
  target_link_libraries(test_register_callback ${PROJECT_NAME})
endif()

ament_auto_package()
