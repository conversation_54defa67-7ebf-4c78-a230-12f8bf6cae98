// Copyright 2020 Apex.AI, Inc.
// All rights reserved.

#include <apex_determinism_check_examples/header.hpp>
#include <apex_determinism_check/determinism_check.hpp>
#include <cpputils/common_exceptions.hpp>
#include <gtest/gtest.h>

TEST(apex_determinism_check_examples, deterministic_function)
{
  int retval = apex::deterministic_function();
  EXPECT_EQ(retval, 0);
}

TEST(apex_determinism_check_examples, non_deterministic_function)
{
  int retval = 1;

  // This call will not allocate memory
  retval = apex::non_deterministic_function(0);
  EXPECT_EQ(retval, 0);

  // This one will allocate memory and will make the test fail by throwing an exception
  EXPECT_THROW(apex::non_deterministic_function(42), std::runtime_error);
}

/*
 * Test throwing apex customized exception. in this case, the determinism infraction should not
 * be detected by apex_determinism_check.
 */
TEST(apex_determinism_check_examples, throw_apex_custom_exception_not_infract_determinism)
{
  try {
    DETERMINISM_CHECK_START("apex_determinism_check_examples")
    throw apex::runtime_error("No determinism infraction");
  } catch (const apex::runtime_error & e) {
    EXPECT_STREQ(e.what(), "No determinism infraction");
  }
}
