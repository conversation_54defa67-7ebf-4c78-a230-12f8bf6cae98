// Copyright 2022 Apex.AI, Inc.
// All rights reserved.

#include <apex_determinism_check_examples/header.hpp>
#include <gtest/gtest.h>
#include <apex_determinism_check/message.hpp>
//! [apex_determinism_check register callback example]
#include <apex_determinism_check/register_callback.h>

int main(int argc, char * argv[])
{
  // Generates a nonfatal failure with the determinism infraction message
  // when the determinism infraction is detected.
  register_infraction_callback([](const char * msg) {ADD_FAILURE() << msg;});
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
//! [apex_determinism_check register callback example]

TEST(apex_determinism_check, test_register_callback)
{
  static const char * infraction_msg;
  register_infraction_callback([](const char * msg) {infraction_msg = msg;});
  EXPECT_NO_THROW(apex::non_deterministic_function(42));
  EXPECT_STREQ(infraction_msg, INFRACTION_MESSAGE);
}
