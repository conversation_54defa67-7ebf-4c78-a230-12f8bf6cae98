/// \copyright Copyright 2017-2022 Apex.AI, Inc.
/// All rights reserved.

#include <gtest/gtest.h>

#include <executor2/apex_node_base.hpp>
#include <executor2/executor_factory.hpp>
#include <executor2/executor_runner.hpp>
#include <executor2/graph.hpp>
#include <executor2/graph_runner.hpp>
#include <executor2/vertex.hpp>
#include <timer_service/clock_timer_service.hpp>
#include <std_msgs/msg/int32.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <sensor_msgs/msg/temperature.hpp>
#include <rclcpp/rclcpp.hpp>
#include <threading/thread_attributes.hpp>
#include <interrupt/interrupt_handler.hpp>
#include <apexcpp/udp_receiver.hpp>
#include <settings/inspect.hpp>
#include <settings/repository.hpp>
#include <settings/from_yaml.hpp>
#include <cpputils/string_view.hpp>
#include <executor_examples/fusion_workload_nodes.hpp>

#include <poll.h>

#include <fstream>
#include <memory>
#include <chrono>
#include <vector>
#include <cstdio>
#include <utility>
#include <string>
#include <tuple>

class test_fusion_workload : public ::testing::Test
{
protected:
  void SetUp() override
  {
    rclcpp::init(0, nullptr, rclcpp::InitOptions(), false);
    node_ = std::make_unique<rclcpp::Node>("TestNode");
    timer_srv_ = std::make_unique<apex::timer_service::steady_clock_timer_service>();
  }

  void TearDown() override
  {
    timer_srv_.reset();
    rclcpp::shutdown();
  }

  const apex::interrupt_handler::installer interrupt_handler_;
  rclcpp::Node::UniquePtr node_;
  std::unique_ptr<apex::timer_service::steady_clock_timer_service> timer_srv_;
};

TEST_F(test_fusion_workload, camera_preprocessing) {
  auto preproc_node = std::make_shared<CameraPreprocessingNode>("/input", "/output");
  auto exe = apex::executor::executor_factory::create();
  exe->add(preproc_node);
  apex::executor::executor_runner runner{*exe};

  auto sub = node_->create_polling_subscription<sensor_msgs::msg::Image>("/output",
      rclcpp::QoS(1));
  auto pub = node_->create_publisher<sensor_msgs::msg::Image>("/input",
      rclcpp::QoS(1));

  for(auto & sub : preproc_node->get_subscriptions()) {
    sub->wait_for_matched(1);
  }

  sub->wait_for_matched(1);
  pub->wait_for_matched(1);

  auto image = std::make_shared<sensor_msgs::msg::Image>();
  pub->publish(*image);
  rclcpp::dynamic_waitset::Waitset waitset {sub};
  waitset.wait();
  auto msgs = sub->take();
  const auto & msg = msgs.at(0).data();

  EXPECT_EQ(*image, msg);
}

TEST_F(test_fusion_workload, fusion_node_empty) {
  auto fusion_node = std::make_shared<SensorFusionNode>(
          timer_srv_->create_timer(0us, 20us),
          "/input1",
          "/input2",
          "/input3");
  ASSERT_FALSE(fusion_node->matching_inputs_available(false));
  ASSERT_FALSE(fusion_node->matching_inputs_available(true));
}

TEST_F(test_fusion_workload, fusion_node_nonmatched) {
  auto fusion_node = std::make_shared<SensorFusionNode>(
          timer_srv_->create_timer(0us, 20us),
          "/input1",
          "/input2",
          "/input3");

  auto pub1 = node_->create_publisher<SensorFusionNode::RadarMsg>("/input1",
      rclcpp::QoS(1));
  auto pub2 = node_->create_publisher<SensorFusionNode::CameraMsg>("/input2",
      rclcpp::QoS(1));
  auto pub3 = node_->create_publisher<SensorFusionNode::TransformMsg>("/input3",
      rclcpp::QoS(1));

  for(auto & sub : fusion_node->get_subscriptions()) {
    sub->wait_for_matched(1);
  }

  pub1->wait_for_matched(1);
  pub2->wait_for_matched(1);
  pub3->wait_for_matched(1);

  auto image = std::make_shared<sensor_msgs::msg::Image>();
  image->header.stamp.sec = 1;
  image->header.stamp.nanosec = 0;

  auto radar = std::make_shared<sensor_msgs::msg::Temperature>();
  radar->header.stamp.sec = 1;
  radar->header.stamp.nanosec = 200000;

  auto transform = std::make_shared<sensor_msgs::msg::Temperature>();
  *transform = *radar;

  pub1->publish(*radar);
  pub2->publish(*image);
  pub3->publish(*transform);

  auto subscriptions = fusion_node->get_subscriptions();
  auto waitables = rclcpp::dynamic_waitset::as_waitables(subscriptions);
  rclcpp::dynamic_waitset::Waitset waitset(waitables);
  while (!std::all_of(subscriptions.begin(), subscriptions.end(),
    [&waitset](const auto & sub) {return waitset[sub];}))
  {
    waitset.wait();
  }

  ASSERT_FALSE(fusion_node->matching_inputs_available(false));
  ASSERT_TRUE(fusion_node->matching_inputs_available(true));
  fusion_node->execute();
}
TEST_F(test_fusion_workload, fusion_node_matched) {
  auto fusion_node = std::make_shared<SensorFusionNode>(
          timer_srv_->create_timer(0us, 20us),
          "/input1",
          "/input2",
          "/input3");

  auto pub1 = node_->create_publisher<SensorFusionNode::RadarMsg>("/input1",
      rclcpp::QoS(1));
  auto pub2 = node_->create_publisher<SensorFusionNode::CameraMsg>("/input2",
      rclcpp::QoS(1));
  auto pub3 = node_->create_publisher<SensorFusionNode::TransformMsg>("/input3",
      rclcpp::QoS(1));

  for(auto & sub : fusion_node->get_subscriptions()) {
    sub->wait_for_matched(1);
  }

  pub1->wait_for_matched(1);
  pub2->wait_for_matched(1);
  pub3->wait_for_matched(1);

  auto image = std::make_shared<sensor_msgs::msg::Image>();
  image->header.stamp.sec = 1;
  image->header.stamp.nanosec = 0;

  auto radar = std::make_shared<sensor_msgs::msg::Temperature>();
  radar->header.stamp.sec = 1;
  radar->header.stamp.nanosec = 2000;

  auto transform = std::make_shared<sensor_msgs::msg::Temperature>();
  *transform = *radar;

  pub1->publish(*radar);
  pub2->publish(*image);
  pub3->publish(*transform);

  auto subscriptions = fusion_node->get_subscriptions();
  ASSERT_EQ(subscriptions.size(), 4);
  auto waitables = rclcpp::dynamic_waitset::as_waitables(subscriptions);
  rclcpp::dynamic_waitset::Waitset waitset(waitables);
  while (!(waitset[subscriptions[1]] &&
    waitset[subscriptions[2]] &&
    waitset[subscriptions[3]]))
  {
    waitset.wait();
  }

  ASSERT_TRUE(fusion_node->matching_inputs_available(false));
  // fulfill the contract that returning true from matching_inputs_available leads to execute
  fusion_node->execute();
}
