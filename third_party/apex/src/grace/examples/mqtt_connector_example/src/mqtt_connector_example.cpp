/// \copyright Copyright 2021 Apex.AI, Inc.
/// All rights reserved.

#include <apex_init/apex_init.hpp>
#include <interrupt/interrupt_handler.hpp>
#include <mqtt_connector/aws_mqtt_client.hpp>
#include <mqtt_connector/mqtt_connector.hpp>
#include <mqtt_connector/mqtt_client_interface.hpp>
#include <rclcpp/qos.hpp>
#include <settings/from_yaml.hpp>
#include <settings/inspect.hpp>
#include <settings/inspect/types.hpp>
#include <settings/repository.hpp>
#include <std_msgs/msg/bool.hpp>
#include <std_msgs/msg/bool__rosidl_typesupport_protobuf_cpp.hpp>

#include <memory>
#include <utility>

using apex::connectors::mqtt::AwsMqttClient;
using apex::connectors::mqtt::create_aws_mqtt_client_from_files;
using apex::connectors::mqtt::MQTT_QOS_AT_MOST_ONCE;
using apex::connectors::mqtt::MqttConnector;
using apex::settings::inspect::get;
using apex::settings::inspect::string_view;

int32_t main(const int32_t argc, char ** const argv)
{
  int32_t result{};

  try {
    if (apex::pre_init(argc, argv) != APEX_RET_OK) {
      throw std::runtime_error("Can't pre-init Apex.OS");
    }

    if (apex::post_init() != APEX_RET_OK) {
      throw std::runtime_error("Can't post-init Apex.OS");
    }

    //! [tutorial]
    auto aws_api_handle = std::make_unique<Aws::Crt::ApiHandle>();

    auto repo = apex::settings::repository::get();
    auto endpoint =
      get<string_view>(repo,
        "mqtt_connector_example_node/aws_mqtt_client/endpoint").data();  // (1)!
    auto cert_path =
      get<string_view>(repo, "mqtt_connector_example_node/aws_mqtt_client/cert_path").data();
    auto ca_path =
      get<string_view>(repo, "mqtt_connector_example_node/aws_mqtt_client/ca_path").data();
    auto key_path =
      get<string_view>(repo, "mqtt_connector_example_node/aws_mqtt_client/key_path").data();

    auto mqtt_client = create_aws_mqtt_client_from_files(
      *aws_api_handle, "mqtt_client", endpoint, cert_path, ca_path, key_path);  // (2)!

    MqttConnector connector("mqtt_connector_example_node", "", mqtt_client);

    connector.add_ros_to_mqtt_connection<std_msgs::msg::Bool>(
      "from_topic",
      "mqtt_topic",
      rclcpp::DefaultQoS(), MQTT_QOS_AT_MOST_ONCE);  // (3)!

    connector.add_mqtt_to_ros_connection<std_msgs::msg::Bool>(
      "mqtt_topic",
      "to_topic",
      MQTT_QOS_AT_MOST_ONCE, rclcpp::DefaultQoS());  // (4)!


    const apex::interrupt_handler::installer interrupt_handler_installer;
    connector.issue();  // (5)!
    apex::interrupt_handler::wait();
    aws_api_handle.reset();  // (6)!
    //! [tutorial]
  } catch (const std::exception & e) {
    if (rclcpp::ok()) {
      APEX_FATAL_R(e.what());
    } else {
      std::cerr << e.what() << "\n";
    }
    result = 2;
  } catch (...) {
    if (rclcpp::ok()) {
      APEX_FATAL_R("Unknown error occurred");
    } else {
      std::cerr << "Unknown error occurred" <<
        "\n";
    }
    result = -1;
  }
  return result;
}
