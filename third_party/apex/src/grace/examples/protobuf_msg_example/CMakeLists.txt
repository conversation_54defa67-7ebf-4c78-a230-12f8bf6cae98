cmake_minimum_required(VERSION 3.5)

project(protobuf_msg_example)

find_package(ament_cmake_auto REQUIRED)
if(NOT DEFINED ENV{ENABLE_PROTOBUF})
  message(WARNING "ENABLE_PROTOBUF is not set. Skipping ${PROJECT_NAME}")
  ament_package()
  return()
endif()

ament_auto_find_build_dependencies()

find_package(protobuf_typesupport)
find_package(rosidl_typesupport_protobuf)
find_package(std_msgs)
# Avoid issues with protobuf includes not found
if(CMAKE_AARCH64_LINUX OR CMAKE_NVIDIA_LINUX)
  find_package(Protobuf REQUIRED)
  include_directories(${PROTOBUF_INCLUDE_DIRS})
  # Ignore warnings from protobuf
  add_compile_options(-Wno-error=sign-conversion -Wno-error=conversion)
endif()

ament_auto_add_executable(protobuf_msg_example_main src/protobuf_msg_example_main.cpp)
apex_set_compile_options(protobuf_msg_example_main)

target_link_libraries(protobuf_msg_example_main ${std_msgs_LIBRARIES})

ament_target_dependencies(protobuf_msg_example_main
  protobuf_typesupport
  rosidl_typesupport_protobuf
  std_msgs
)

list(APPEND ${PROJECT_NAME}_EXECUTABLES
  protobuf_msg_example_main
)
ament_auto_package()
