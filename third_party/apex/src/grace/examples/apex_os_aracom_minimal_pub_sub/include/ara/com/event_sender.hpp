/// \copyright Copyright 2020 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \include event_sender.hpp
/// \brief a minimal aracom event sender
/// \note this file should have only a .h extension, but the CI assumes those are C source files.
/// The short term solution is to violate the ara specification and use the .hpp extension.

#ifndef ARACOM_MINIMAL_PUB_SUB_EXAMPLE__EVENT_SENDER_HPP_
#define ARACOM_MINIMAL_PUB_SUB_EXAMPLE__EVENT_SENDER_HPP_

#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>

//! [ARA com event sender]
#include <utility>
#include <memory>

#include "ara/types.hpp"  // (1)!

namespace ara
{
namespace com
{
//! [Event sender class]
template<typename T>
class EventSender  // (2)!
//! [Event sender class]
{
public:
  using SampleType = T;

  //! [Constructor]
  EventSender(
    const apex::string_strict256_t & node_name,
    const apex::string_strict256_t & topic,
    const apex::string_strict256_t & node_namespace)  // (3)!
  : m_node_name(node_name),
    m_topic(topic),
    m_node_namespace(node_namespace) {}
  //! [Constructor]

  //! [Send]
  void Send(const SampleType & data)  // (4)!
  {
    auto message{std_msgs::msg::String()};
    message.data = data.Serialize();
    m_publisher->publish(message);
  }
  //! [Send]

  //! [Zero copy API]
  ara::com::SampleAllocateePtr<SampleType> Allocate()  // (5)!
  {
    return std::move(std::unique_ptr<SampleType>(new SampleType()));
  }
  void Send(ara::com::SampleAllocateePtr<SampleType> data)
  {
    Send(*data);
  }
  //! [Zero copy API]

protected:  //  (6)
  //! [Offer]
  void Offer()  // (7)!
  {
    m_node = std::make_shared<rclcpp::Node>(m_node_name, m_node_namespace);
    m_publisher = m_node->create_publisher<std_msgs::msg::String>(
      m_topic.c_str(),
      rclcpp::DefaultQoS());
  }
  //! [Offer]

  //! [StopOffer]
  void StopOffer()    // (8)!
  {
    m_publisher.reset();
    m_node.reset();
  }
  //! [StopOffer]

private:
  //! [Private members]
  apex::string_strict256_t m_node_name;  // (9)!
  apex::string_strict256_t m_topic;
  apex::string_strict256_t m_node_namespace;
  rclcpp::Node::SharedPtr m_node;
  rclcpp::Publisher<std_msgs::msg::String>::SharedPtr m_publisher;
  //! [Private members]
};
}  // namespace com
}  // namespace ara
//! [ARA com event sender]

#endif  // ARACOM_MINIMAL_PUB_SUB_EXAMPLE__EVENT_SENDER_HPP_
