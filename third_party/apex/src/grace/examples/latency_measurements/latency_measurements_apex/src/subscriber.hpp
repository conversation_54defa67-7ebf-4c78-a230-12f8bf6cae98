/// \copyright Copyright 2021 Apex.AI, Inc.
/// All rights reserved.

#include <debug_msgs/msg/array1b.hpp>
#include <debug_msgs/msg/array1kb.hpp>
#include <debug_msgs/msg/array16kb.hpp>
#include <debug_msgs/msg/array256kb.hpp>
#include <debug_msgs/msg/array1mb.hpp>
#include <debug_msgs/msg/array4mb.hpp>
#include <executor2/apex_node_base.hpp>
#include <rclcpp/rclcpp.hpp>
#include <chrono>

class subscriber_node : public apex::executor::apex_node_base
{
#if 1 == MSG_SIZE
  using MsgType = debug_msgs::msg::Array1b;
#elif 1024 == MSG_SIZE
  using MsgType = debug_msgs::msg::Array1kb;
#elif 16384 == MSG_SIZE
  using MsgType = debug_msgs::msg::Array16kb;
#elif 262144 == MSG_SIZE
  using MsgType = debug_msgs::msg::Array256kb;
#elif 1048576 == MSG_SIZE
  using MsgType = debug_msgs::msg::Array1mb;
#elif 4194304 == MSG_SIZE
  using MsgType = debug_msgs::msg::Array4mb;
#else
#error Unsupported message size
#endif

public:
  explicit subscriber_node(const char * name)
  : apex::executor::apex_node_base(name)
  {
  }

private:
  bool execute_impl() override
  {
    auto msgs = sub->take();
    const auto now = std::chrono::steady_clock::now().time_since_epoch();
    for (const auto & msg : msgs) {
      if (msg.info().valid()) {
        if (previous_id + 1 == msg.data().id) {
          const auto publish_time = std::chrono::seconds(msg.data().stamp.sec) +
            std::chrono::nanoseconds(msg.data().stamp.nanosec);
          const auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
            now - publish_time);
          std::cout << latency.count() << std::endl;
        } else {
          std::cerr << previous_id << " has been lost" << std::endl;
        }
        previous_id = msg.data().id;
      }
    }
    if (msgs.size() > 1) {
      std::cerr << "Warning: More than one message in the queue" << msgs.size() << std::endl;
    } else if (msgs.empty()) {
      std::cerr << "No message in the queue" << std::endl;
    }
    return true;
  }

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {sub};
  }

private:
  rclcpp::PollingSubscription<MsgType>::SharedPtr sub{
    get_rclcpp_node().create_polling_subscription<MsgType>("topic", rclcpp::DefaultQoS())};
  int64_t previous_id{-1};
};
