cmake_minimum_required(VERSION 3.5)

project(debug_msgs)

find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(builtin_interfaces REQUIRED)

rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/Array1b.msg"
  "msg/Array1kb.msg"
  "msg/Array16kb.msg"
  "msg/Array256kb.msg"
  "msg/Array1mb.msg"
  "msg/Array4mb.msg"
  DEPENDENCIES builtin_interfaces
)

ament_export_dependencies(rosidl_default_runtime)



ament_package()
