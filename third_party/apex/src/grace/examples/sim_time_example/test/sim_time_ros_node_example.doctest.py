# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

import unittest
import re

from apex_doctest import doc_check_launch_description, DocCheckTests
import launch
import launch.actions
import launch_testing.actions

from launch_pkg_utils.substitutions import ExecutableInPackage


@doc_check_launch_description(
    'using-simulation-time-with-ros-nodes.md',
    'sim_time_example',
)
def generate_test_description():
    clock_publisher_executable = \
        ExecutableInPackage(package='sim_time_example', executable='clock_publisher')
    clock_publisher = launch.actions.ExecuteProcess(cmd=[clock_publisher_executable])
    ros_node_example_executable = \
        ExecutableInPackage(package='sim_time_example', executable='ros_node_example')
    ros_node_clock_no_args = launch.actions.ExecuteProcess(
        cmd=[ros_node_example_executable]
    )
    ros_node_clock = launch.actions.ExecuteProcess(
            cmd=[ros_node_example_executable, '--use_sim_time_in_node']
        )
    ros_clock_global = launch.actions.ExecuteProcess(
        cmd=[ros_node_example_executable, '--enable_global_sim_time']
    )

    ld = launch.LaunchDescription([
        clock_publisher,
        ros_node_clock_no_args,
        ros_node_clock,
        ros_clock_global,
        launch_testing.actions.ReadyToTest()
    ])

    return ld, {
        "clock_publisher": clock_publisher,
        "ros_node_clock_no_args": ros_node_clock_no_args,
        "ros_node_clock": ros_node_clock,
        "ros_clock_global": ros_clock_global
    }


class InstallDocTest(unittest.TestCase):

    def test_clock_publisher_starts(self, proc_info, clock_publisher):
        proc_info.assertWaitForStartup(clock_publisher, timeout=5)

    def test_ros_node_clock_no_args_starts(self, proc_info, ros_node_clock_no_args):
        proc_info.assertWaitForStartup(ros_node_clock_no_args, timeout=5)

    def test_ros_node_clock_no_args_example_output(self, proc_output, ros_node_clock_no_args):
        pattern = re.compile('- Node builtin   | ros_time_is_active: 0, seconds: \d+.\d+, '
                             'nanoseconds: \d+')
        proc_output.assertWaitFor(
            pattern,
            process=ros_node_clock_no_args,
            timeout=5,
            stream='stdout'
        )

    def test_ros_node_clock_starts(self, proc_info, ros_node_clock):
        proc_info.assertWaitForStartup(ros_node_clock, timeout=5)

    def test_ros_node_clock_example_output(self, proc_output, ros_node_clock):
        pattern = re.compile(' - Node builtin   | ros_time_is_active: 1, seconds: \d+.\d+, '
                             'nanoseconds: \d+')
        proc_output.assertWaitFor(
            pattern,
            process=ros_node_clock,
            timeout=5,
            stream='stdout'
        )
        pattern = re.compile(' - ROS clock    | ros_time_is_active: 1, seconds: \d+.\d+, '
                             'nanoseconds: \d+')
        proc_output.assertWaitFor(
            pattern,
            process=ros_node_clock,
            timeout=5,
            stream='stdout'
        )

    def test_ros_clock_global_starts(self, proc_info, ros_clock_global):
        proc_info.assertWaitForStartup(ros_clock_global, timeout=5)

    def test_ros_clock_global_output(self, proc_output, ros_clock_global):
        pattern = re.compile(' - Node global   | ros_time_is_active: 1, seconds: \d+.\d+, '
                             'nanoseconds: \d+')
        proc_output.assertWaitFor(
            pattern,
            process=ros_clock_global,
            timeout=5,
            stream='stdout'
        )


documentation_tests_sim_time_ros_node_example = DocCheckTests(
    'using-simulation-time-with-ros-nodes.md',
    'sim_time_example'
)
