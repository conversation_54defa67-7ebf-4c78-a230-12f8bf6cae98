# Copyright 2022 Apex.AI, Inc.
# All rights reserved.
cmake_minimum_required(VERSION 3.5)

project(sim_time_example)

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

ament_auto_add_executable(ros_node_example src/ros_node_example.cpp)
ament_auto_add_executable(clock_publisher src/clock_publisher.cpp)
ament_auto_add_executable(apex_node_example src/apex_node_example.cpp)
ament_auto_add_executable(publisher src/publisher.cpp)
ament_auto_add_executable(subscriber src/subscriber.cpp)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()

  # Disable integration test which uses python launch APIs with APEX_CERT
  if(NOT APEX_CERT)
    # Integration tests
    find_package(ros_testing)
    add_ros_test(
            test/sim_time_ros_node_example.doctest.py
            TIMEOUT "120"
    )
    add_ros_test(
            test/sim_time_apex_node_example.doctest.py
            TIMEOUT "120"
    )

    find_package(rosbag2)
    # the doctest uses rosbag, skip it if rosbag2 not found
    if(rosbag2_FOUND)
      add_ros_test(
              test/sim_time_rosbag_example.doctest.py
              TIMEOUT "120"
      )
    else()
      message("Test requires rosbag2, but it was not found. Skipping test.")
    endif()
  endif()
endif()

ament_auto_package(INSTALL_TO_SHARE doc launch param)
