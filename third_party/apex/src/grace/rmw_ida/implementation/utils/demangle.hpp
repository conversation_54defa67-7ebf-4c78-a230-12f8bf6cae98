/// \copyright Copyright 2024 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief This file provides utilities for mangling the topic names

#ifndef RMW_IDA__IMPLEMENTATION_UTILS_DEMANGLE_HPP_
#define RMW_IDA__IMPLEMENTATION_UTILS_DEMANGLE_HPP_

#include "grace/rmw_ida/implementation/utils/naming.hpp"
#include "ida/base/core/string.hpp"
#include "ida/common/error.hpp"

namespace apex::rmw_ida::utils
{
/**
 * @brief Demangle topic for any available ros specific prefixes
 * @param topic_name topic name to demangle
 * @return demangled topic name or the original if not a ROS style topic
 */
apex::Result<topicname_t> demangle_ros_topic(base::string_view topic_name);

/**
 * @brief Demangle ros topic
 * @param topic_name topic name
 * @return Return the topic name for a given topic if it is part of one, else ""
 */
apex::Result<topicname_t> demangle_ros_pubsub_topic(base::string_view topic_name);

/**
 * @brief Return the demangled ROS type
 * @param dds_type_string type name to demangle
 * @return demangled ros type name or the original if not a ROS type
 */
apex::Result<typename_t> demangle_ros_pubsub_type(base::string_view dds_type_string);

/**
 * @brief Demangle service name
 * @param topic_name topic name of service
 * @return Return the service name for a given topic if it is part of a service, else ""
 */
apex::Result<topicname_t> demangle_ros_service_topic(base::string_view topic_name);

/**
 * @brief Demangle service request
 * @param topic_name service request topic name
 * @return Return the service name for a given topic if it is part of a service request, else ""
 */
apex::Result<topicname_t> demangle_ros_service_request_topic(base::string_view topic_name);

/**
 * @brief Demangle service reply
 * @param topic_name service reply topic name
 * @return Return the service name for a given topic if it is part of a service reply, else ""
 */
apex::Result<topicname_t> demangle_ros_service_reply_topic(base::string_view topic_name);

/**
 * @brief Demangle service type
 * @param dds_type_name service type name
 * @return  Return the demangled service type if it is a ROS srv type, else ""
 */
apex::Result<typename_t> demangle_ros_service_type(base::string_view dds_type_name);

/**
 * @brief Identity mangle, doesn't mangle anything
 * @details Used when ros names are not mangled
 * @param name name to be mangled
 * @return demanagled name
 */
apex::Result<topicname_t> identity_demangle(base::string_view name);

}  // namespace apex::rmw_ida::utils

#endif  // RMW_IDA__IMPLEMENTATION_UTILS_DEMANGLE_HPP_
