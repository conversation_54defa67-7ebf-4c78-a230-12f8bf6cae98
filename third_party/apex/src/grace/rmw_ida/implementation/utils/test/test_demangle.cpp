// Copyright 2024 Apex.AI, Inc.
// All rights reserved.

#include "grace/rmw_ida/implementation/utils/demangle.hpp"
#include "gtest/gtest.h"
#include "ida/common/test_util.hpp"

namespace
{

using namespace apex::rmw_ida::utils;
using ::apex::test::force_ok;

TEST(Demangle, demangle_ros_topic)
{
  EXPECT_STREQ(force_ok(demangle_ros_topic("rq/test/topic")).c_str(), "/test/topic");
  EXPECT_STREQ(force_ok(demangle_ros_topic("rt/test/topic")).c_str(), "/test/topic");
  EXPECT_STREQ(force_ok(demangle_ros_topic("rr/test/topic")).c_str(), "/test/topic");
  EXPECT_STREQ(force_ok(demangle_ros_topic("/non_ros_topic")).c_str(), "/non_ros_topic");
}

TEST(Demangle, demangle_ros_pubsub_type)
{
  EXPECT_STREQ(force_ok(demangle_ros_pubsub_type("DDS_RAW_TYPE")).c_str(), "DDS_RAW_TYPE");
  EXPECT_STREQ(force_ok(demangle_ros_pubsub_type("builtin_interfaces::msg::dds_::Time")).c_str(),
               "builtin_interfaces::msg::dds_::Time");
  EXPECT_STREQ(force_ok(demangle_ros_pubsub_type("builtin_interfaces::srv::dds_::Time")).c_str(),
               "builtin_interfaces::srv::dds_::Time");
  EXPECT_STREQ(force_ok(demangle_ros_pubsub_type("builtin_interfaces::msg::dds_::Time_")).c_str(),
               "builtin_interfaces/msg/Time");
  EXPECT_STREQ(force_ok(demangle_ros_pubsub_type("builtin_interfaces::srv::dds_::Time_")).c_str(),
               "builtin_interfaces/srv/Time");
}

TEST(Demangle, demangle_ros_pubsub_topic)
{
  EXPECT_STREQ(force_ok(demangle_ros_pubsub_topic("rt/test/topic")).c_str(), "/test/topic");
  EXPECT_STREQ(force_ok(demangle_ros_pubsub_topic("RAW_DDS_TOPIC")).c_str(), "");
}

TEST(Demangle, demangle_ros_service_topic)
{
  // Demangle service
  EXPECT_STREQ(force_ok(demangle_ros_service_topic("rr/topicReply")).c_str(), "/topic");
  EXPECT_STREQ(force_ok(demangle_ros_service_topic("rr/wrong_suffixResponse")).c_str(), "");
}

TEST(Demangle, demangle_ros_service_request_topic)
{
  // Demangle service
  EXPECT_STREQ(force_ok(demangle_ros_service_request_topic("rq/topicRequest")).c_str(), "/topic");
  EXPECT_STREQ(force_ok(demangle_ros_service_request_topic("rq/wrong_suffixReply")).c_str(), "");
}

TEST(Demangle, demangle_ros_service_reply_topic)
{
  // Demangle service
  EXPECT_STREQ(force_ok(demangle_ros_service_reply_topic("rr/topicReply")).c_str(), "/topic");
  EXPECT_STREQ(force_ok(demangle_ros_service_reply_topic("rr/wrong_suffixRequest")).c_str(), "");
}

TEST(Demangle, demangle_ros_service_type)
{
  // Demangle service
  EXPECT_STREQ(
    force_ok(demangle_ros_service_type("builtin_interfaces::srv::dds_::Time_Response_")).c_str(),
    "builtin_interfaces/srv/Time");
  EXPECT_STREQ(
    force_ok(demangle_ros_service_type("builtin_interfaces::srv::dds_::Time_Request_")).c_str(),
    "builtin_interfaces/srv/Time");
}
}  // namespace
