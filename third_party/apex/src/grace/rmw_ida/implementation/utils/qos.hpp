/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief Define qos conversion functionalities

#ifndef RMW_IDA__IMPLEMENTATION_UTILS_QOS_HPP_
#define RMW_IDA__IMPLEMENTATION_UTILS_QOS_HPP_

#include "grace/ros/rmw/include/rmw/qos_profiles.h"
#include "grace/ros/rmw/include/rmw/types.h"
#include "ida/base/core/algorithm.hpp"
#include "ida/base/core/logging.hpp"
#include "ida/common/error.hpp"
#include "ida/config/default_deployment.hpp"
#include "ida/plexus/publisher/data_writer_qos.hpp"
#include "ida/plexus/qos/qos.hpp"
#include "ida/plexus/subscriber/data_reader_qos.hpp"

namespace apex::rmw_ida::utils
{
// TODO(Sumanth.Nirmal) #24895 Add support for payload holding DDS specific QoS

inline bool is_time_default(const rmw_time_t & time_rmw)
{
  static constexpr rmw_time_t duration_unspecified = RMW_DURATION_UNSPECIFIED;
  return (time_rmw.sec == duration_unspecified.sec) && (time_rmw.nsec == duration_unspecified.nsec);
}
/***
 * @brief Translates ROS QoS to Plexus Reader/Writer QoS
 * @tparam PlexusQoST Plexus QoS type
 * @param[in] rmw_qos ROS QoS
 * @param[in, out] dds_qos reference to Plexus QoS that is filled from ROS QoS
 * @return Error if any
 */
template <typename PlexusQoST>
apex::Result<void> rmw_qos_to_dds_qos(const rmw_qos_profile_t & rmw_qos, PlexusQoST & plexus_qos)
{
  // Ensure the history depth is at least the requested queue size
  // expected depth' type size_t to be non-negative for all cases among HISTORY_KEEP_ALL
  if ((rmw_qos.depth == 0U) && (rmw_qos.history == RMW_QOS_POLICY_HISTORY_KEEP_LAST)) {
    return apex::err("History depth must be greater than 0 if history is set to KEEP_LAST");
  }

  // TODO(Sumanth.Nirmal) check if the depth() in plexus can be made size_t

  // history QOS policy
  switch (rmw_qos.history) {
    case RMW_QOS_POLICY_HISTORY_KEEP_LAST: {
      auto depth = static_cast<base::uint32_t>(rmw_qos.depth);
      if (rmw_qos.depth == RMW_QOS_POLICY_DEPTH_SYSTEM_DEFAULT) {
        if (plexus_qos.history().depth().has_value()) {
          depth = plexus_qos.history().depth().has_value();
        } else {
          // system default depth is 1
          depth = 1;
        }
      }
      plexus_qos.set(apex::plexus::qos::History::keep_last(depth));
      break;
    }
    case RMW_QOS_POLICY_HISTORY_KEEP_ALL: {
      // Plexus does not support KEEP_ALL natively, currently its just KEEP_LAST with maximum
      // possible history depth
      auto depth = base::min(static_cast<base::uint32_t>(rmw_qos.max_allocated_samples) - 1,
                             config::MAX_IPC_HISTORY_DEPTH);
      plexus_qos.set(apex::plexus::qos::History::keep_last(depth));
      break;
    }
    case RMW_QOS_POLICY_HISTORY_SYSTEM_DEFAULT:
      // Use Plexus default QoS option
      break;
    case RMW_QOS_POLICY_HISTORY_UNKNOWN:
    default:
      return apex::err("Unknown QOS history policy");
  }

  // reliability QOS policy
  switch (rmw_qos.reliability) {
    case RMW_QOS_POLICY_RELIABILITY_BEST_EFFORT:
      plexus_qos.set(apex::plexus::qos::Reliability::best_effort());
      break;
    case RMW_QOS_POLICY_RELIABILITY_RELIABLE:
      plexus_qos.set(apex::plexus::qos::Reliability::reliable());
      break;
    case RMW_QOS_POLICY_RELIABILITY_SYSTEM_DEFAULT:
      // Use Plexus default QoS option
      break;
    case RMW_QOS_POLICY_RELIABILITY_UNKNOWN:
    default:
      return apex::err("Unknown QOS reliability policy");
  }

  // durability QOS policy
  switch (rmw_qos.durability) {
    case RMW_QOS_POLICY_DURABILITY_TRANSIENT_LOCAL:
      plexus_qos.set(apex::plexus::qos::Durability::transient_local());
      break;
    case RMW_QOS_POLICY_DURABILITY_VOLATILE:
      plexus_qos.set(apex::plexus::qos::Durability::volatile_durability());
      break;
    case RMW_QOS_POLICY_DURABILITY_SYSTEM_DEFAULT:
      // Use Plexus default QoS option
      break;
    case RMW_QOS_POLICY_DURABILITY_UNKNOWN:
    default:
      return apex::err("Unknown QOS durability policy");
  }

  // resource limit QOS policy
  plexus_qos.set(apex::plexus::qos::ResourceLimits(
    rmw_qos.max_allocated_samples, rmw_qos.max_non_self_contained_type_serialized_size));

  // deadline QOS
  if (!is_time_default(rmw_qos.deadline)) {
    // return apex::err("Plexus doesn't support deadline QoS");
    BASE_LOG(ERROR, "rmw_ida doesn't support deadline QoS");
  }

  // lifespan QOS
  if (!is_time_default(rmw_qos.lifespan)) {
    // return apex::err("Plexus doesn't support lifespan");
    BASE_LOG(ERROR, "rmw_ida doesn't support lifespan QoS");
  }

  // liveliness QOS policy
  if (rmw_qos.liveliness != RMW_QOS_POLICY_LIVELINESS_SYSTEM_DEFAULT &&
      rmw_qos.liveliness != RMW_QOS_POLICY_LIVELINESS_UNKNOWN) {
    // return apex::err("Plexus doesnt support liveliness QoS");
    BASE_LOG(ERROR, "rmw_ida doesn't support liveliness QoS");
  }

  // liveliness lease
  if (!is_time_default(rmw_qos.liveliness_lease_duration)) {
    // return apex::err("Plexus doesn't support liveliness lease duration QoS");
    BASE_LOG(ERROR, "rmw_ida doesn't support liveliness lease duration QoS");
  }

  return base::ok();
}


// template <>
// apex::Result<void> rmw_qos_to_dds_qos(const rmw_qos_profile_t & rmw_qos,
//                                       apex::plexus::DataReaderQoS & plexus_qos);
//
// template <>
// apex::Result<void> rmw_qos_to_dds_qos(const rmw_qos_profile_t & rmw_qos,
//                                       apex::plexus::DataWriterQoS & plexus_qos);

/***
 * @brief Translates Plexus Reader/Writer QoS to ROS QoS
 * @tparam PlexusQoST Plexus QoS type
 * @param[in] plexus_qos Plexus Reader/Writer QoS
 * @param[in, out] rmw_qos reference to ROS QoS that is filled from the Plexus QoS
 * @return Error if any
 */
template <typename PlexusQoST>
apex::Result<void> dds_qos_to_rmw_qos(const PlexusQoST & plexus_qos, rmw_qos_profile_t & rmw_qos)
{
  rmw_qos = rmw_qos_profile_system_default;
  // history QOS policy
  switch (plexus_qos.history().kind()) {
    case apex::plexus::qos::HistoryKind::KEEP_LAST:
      rmw_qos.history = RMW_QOS_POLICY_HISTORY_KEEP_LAST;
      break;
    case apex::plexus::qos::HistoryKind::KEEP_ALL:
      rmw_qos.history = RMW_QOS_POLICY_HISTORY_KEEP_ALL;
      break;
    default:
      rmw_qos.history = RMW_QOS_POLICY_HISTORY_UNKNOWN;
      break;
  }
  auto maybe_depth = plexus_qos.history().depth();
  if (maybe_depth.has_value()) {
    rmw_qos.depth = maybe_depth.value();
  } else {
    BASE_LOG(INFO, "Ignoring depth value, history kind set to KEEP_ALL");
  }

  // reliability QOS policy
  switch (plexus_qos.reliability().kind()) {
    case apex::plexus::qos::ReliabilityKind::BEST_EFFORT:
      rmw_qos.reliability = RMW_QOS_POLICY_RELIABILITY_BEST_EFFORT;
      break;
    case apex::plexus::qos::ReliabilityKind::RELIABLE:
      rmw_qos.reliability = RMW_QOS_POLICY_RELIABILITY_RELIABLE;
      break;
    default:
      rmw_qos.reliability = RMW_QOS_POLICY_RELIABILITY_UNKNOWN;
      break;
  }

  // durability QOS policy
  switch (plexus_qos.durability().kind()) {
    case apex::plexus::qos::DurabilityKind::TRANSIENT_LOCAL:
      rmw_qos.durability = RMW_QOS_POLICY_DURABILITY_TRANSIENT_LOCAL;
      break;
    case apex::plexus::qos::DurabilityKind::VOLATILE:
      rmw_qos.durability = RMW_QOS_POLICY_DURABILITY_VOLATILE;
      break;
    default:
      rmw_qos.durability = RMW_QOS_POLICY_DURABILITY_UNKNOWN;
      break;
  }

  // Resource Limits
  rmw_qos.max_allocated_samples = plexus_qos.resource_limits().max_allocated_samples();
  rmw_qos.max_non_self_contained_type_serialized_size =
    plexus_qos.resource_limits().max_non_self_contained_type_serialized_size();

  // No other QoS are supported for now
  return base::ok();
}

// template <>
// apex::Result<void> dds_qos_to_rmw_qos(const apex::plexus::DataReaderQoS & plexus_qos,
//                                       rmw_qos_profile_t & rmw_qos);
//
// template <>
// apex::Result<void> dds_qos_to_rmw_qos(const apex::plexus::DataWriterQoS & plexus_qos,
//                                       rmw_qos_profile_t & rmw_qos);

}  // namespace apex::rmw_ida::utils

#endif  // RMW_IDA__IMPLEMENTATION_UTILS_QOS_HPP_
