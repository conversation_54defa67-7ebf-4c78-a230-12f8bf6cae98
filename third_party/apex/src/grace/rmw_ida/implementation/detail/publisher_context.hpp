/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief

#ifndef RMW_IDA__IMPLEMENTATION_DETAIL_PUBLISHER_CONTEXT_HPP_
#define RMW_IDA__IMPLEMENTATION_DETAIL_PUBLISHER_CONTEXT_HPP_

#include "grace/rmw_ida/common/rmw_allocator.hpp"
#include "grace/rmw_ida/implementation/detail/base_context.hpp"
#include "grace/rmw_ida/implementation/detail/node_context.hpp"
#include "grace/rmw_ida/implementation/utils/qos.hpp"
#include "grace/rmw_ida/typesupport/message.hpp"
#include "grace/rmw_ida/typesupport/publisher.hpp"
#include "ida/base/core/error.hpp"
#include "ida/base/core/fixed_string.hpp"
#include "ida/base/core/memory.hpp"
#include "ida/base/core/optional.hpp"
#include "ida/base/core/string_view.hpp"

namespace apex::rmw_ida::detail
{
/**
 * @brief Data associated with particular rmw publisher, provides an interface to the underlying
 * message publisher
 */
class PublisherContext final : public BaseContext
{
public:
  using MessagePublisherInterface = apex::rmw_ida::typesupport::MessagePublisherInterface;
  using Timeout = base::timeout<os::steady_clock>;

  /**
   * @brief Create a new PublisherContext object for a given node context using the given topic
   * name.
   * @param[in] node_context NodeContext for the node that owns this Publisher
   * @param[in] rmw_qos QoS configuration for this Publisher to use
   * @param[in] message_ts Type support for the Message type being used
   * @param[in] topic_name Name of the topic
   */
  PublisherContext(base::badge<base::creator> badge,
                   base::optional<base::error> & maybe_error,
                   NodeContext & node_context,
                   const rmw_qos_profile_t & rmw_qos,
                   const MessageTypeSupportInterface & message_ts,
                   base::string_view topic_name);

  /**
   * @brief Gets the MessagePublisherInterface used by this publisher
   * @return Publisher interface
   */
  MessagePublisherInterface & get_publisher_interface() const
  {
    return *m_publisher;
  }

  rmw_gid_t rmw_gid() const
  {
    return m_rmw_gid;
  }
  /**
   * @brief Extracts a valid PublisherContext from an opaque handle
   * @param[in] Opaque handle holding a PublisherContext
   * @return Valid PublisherContext
   */
  static base::optional<PublisherContext &> from_void(void * ptr)
  {
    if (ptr == nullptr) {
      return base::nullopt;
    }

    /*
     AXIVION Next Line MisraC++2023-8.2.6: Reason: Code Quality (Functional suitability),
     Justification: The caller ensures this is a valid cast
     */
    return *static_cast<PublisherContext *>(ptr);
  }

  size_t get_matched_subscription_count()
  {
    auto & data_writer = m_publisher->get_datawriter();

    auto status = data_writer.publication_matched_status();
    return static_cast<size_t>(status.current_count);
  }

private:
  apex::rmw_ida::unique_ptr<MessagePublisherInterface> m_publisher;
  rmw_gid_t m_rmw_gid;
};


}  // namespace apex::rmw_ida::detail
#endif
