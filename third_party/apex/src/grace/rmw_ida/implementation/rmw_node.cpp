/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief

#include "grace/rmw_ida/common/identifier.hpp"
#include "grace/rmw_ida/common/rmw_allocator.hpp"
#include "grace/rmw_ida/implementation/detail/error_handling.hpp"
#include "grace/rmw_ida/implementation/detail/node_context.hpp"
#include "grace/ros/rmw/include/rmw/rmw.h"
#include "grace/ros/rmw_dds_common/include/rmw_dds_common/qos.hpp"
#include "grace/tools/ros2_tracing/tracetools/include/tracetools/tracetools.h"
#include "ida/base/core/error.hpp"
#include "ida/base/core/fixed_string.hpp"
#include "rmw/impl/cpp/macros.hpp"
#include "rmw/validate_namespace.h"
#include "rmw/validate_node_name.h"

namespace
{
using RMWAllocator = apex::rmw_ida::RMWAllocator;
using apex::error_to_string;
using apex::rmw_ida::common::get_implementation_identifier;
using apex::rmw_ida::common::RMW_IDA_IDENTIFIER;
using apex::rmw_ida::detail::NodeContext;

#define VALIDATE_NODE_PARAMS(name, namespace_)                                         \
  do {                                                                                 \
    int validation_result = RMW_NODE_NAME_VALID;                                       \
    rmw_ret_t ret = rmw_validate_node_name(name, &validation_result, nullptr);         \
    if (RMW_RET_OK != ret) {                                                           \
      RCUTILS_SET_ERROR_MSG("failed to validate node name");                           \
      return nullptr;                                                                  \
    }                                                                                  \
    if (RMW_NODE_NAME_VALID != validation_result) {                                    \
      const char * reason = rmw_node_name_validation_result_string(validation_result); \
      RCUTILS_SET_ERROR_MSG_WITH_FORMAT_STRING("invalid node name: %s", reason);       \
      return nullptr;                                                                  \
    }                                                                                  \
    validation_result = RMW_NAMESPACE_VALID;                                           \
    ret = rmw_validate_namespace(namespace_, &validation_result, nullptr);             \
    if (RMW_RET_OK != ret) {                                                           \
      RCUTILS_SET_ERROR_MSG("failed to validate node namespace");                      \
      return nullptr;                                                                  \
    }                                                                                  \
    if (RMW_NAMESPACE_VALID != validation_result) {                                    \
      const char * reason = rmw_namespace_validation_result_string(validation_result); \
      RCUTILS_SET_ERROR_MSG_WITH_FORMAT_STRING("invalid node namespace: %s", reason);  \
      return nullptr;                                                                  \
    }                                                                                  \
  } while (false)

rmw_node_t * rmw_create_node_impl(rmw_context_t * context,
                                  const char * name,
                                  const char * namespace_)
{
  RMW_CHECK_ARGUMENT_FOR_NULL(context, nullptr);
  RMW_CHECK_ARGUMENT_FOR_NULL(name, nullptr);
  RMW_CHECK_ARGUMENT_FOR_NULL(namespace_, nullptr);
  RMW_CHECK_TYPE_IDENTIFIERS_MATCH(
    context, context->implementation_identifier, RMW_IDA_IDENTIFIER, return nullptr);

  VALIDATE_NODE_PARAMS(name, namespace_);
  RMW_CHECK_ARGUMENT_FOR_NULL(context->impl, NULL);

  if (context->impl->is_shutdown()) {
    RCUTILS_SET_ERROR_MSG("rmw_context has been shutdown already");
    return nullptr;
  }

  auto rmw_node_context_res = RMWAllocator::create_unique<rmw_node_t>();
  if (rmw_node_context_res.has_error()) {
    RMW_SET_ERROR_MSG(error_to_string(rmw_node_context_res.error()).c_str());
    return nullptr;
  }

  auto rmw_node_context = base::move(rmw_node_context_res.value());
  auto node_context_alloc_result = RMWAllocator::create_unique<NodeContext>(
    base::fixed_string<256>{base::truncate_to_capacity, name},
    base::fixed_string<256>{base::truncate_to_capacity, namespace_},
    *context->impl);

  if (node_context_alloc_result.has_error()) {
    RMW_SET_ERROR_MSG("Failed to create node context");
    return nullptr;
  }

  auto node_context = base::move(node_context_alloc_result.value());
  rmw_node_context->implementation_identifier = get_implementation_identifier();
  rmw_node_context->context = context;
  rmw_node_context->name = node_context->node_name().c_str();
  rmw_node_context->namespace_ = node_context->namespace_name().c_str();
  rmw_node_context->data = node_context.release();

  TRACETOOLS_TRACEPOINT(rmw_create_node,
                        static_cast<const void *>(rmw_node_context.get()),
                        static_cast<const void *>(rmw_node_context->data));
  return rmw_node_context.release();
}

rmw_ret_t rmw_destroy_node_impl(rmw_node_t * node)
{
  RMW_CHECK_ARGUMENT_FOR_NULL(node, RMW_RET_INVALID_ARGUMENT);

  RMW_CHECK_TYPE_IDENTIFIERS_MATCH(node,
                                   node->implementation_identifier,
                                   RMW_IDA_IDENTIFIER,
                                   return RMW_RET_INCORRECT_RMW_IMPLEMENTATION);

  auto maybe_node_context = NodeContext::from_void(node->data);
  if (!maybe_node_context.has_value()) {
    RMW_SET_ERROR_MSG("Failed to extract node context");
    return RMW_RET_INVALID_ARGUMENT;
  }

  auto & node_context = *maybe_node_context;
  RMWAllocator::deallocate_object(&node_context);
  RMWAllocator::deallocate_object(node);
  return RMW_RET_OK;
}

rmw_ret_t rmw_qos_profile_check_compatible_impl(const rmw_qos_profile_t publisher_profile,
                                                const rmw_qos_profile_t subscription_profile,
                                                rmw_qos_compatibility_type_t * compatibility,
                                                char * reason,
                                                size_t reason_size)
{
  return rmw_dds_common::qos_profile_check_compatible(
    publisher_profile, subscription_profile, compatibility, reason, reason_size);
}

const rmw_guard_condition_t * rmw_node_get_graph_guard_condition_impl(const rmw_node_t * node)
{
  RMW_CHECK_ARGUMENT_FOR_NULL(node, nullptr);
  RMW_CHECK_TYPE_IDENTIFIERS_MATCH(
    node, node->implementation_identifier, RMW_IDA_IDENTIFIER, return nullptr);

  auto maybe_node_context = NodeContext::from_void(node->data);
  if (!maybe_node_context.has_value()) {
    return nullptr;
  }
  auto & node_context = *maybe_node_context;
  return &node_context.graph_guard_condition();
}

}  // namespace


rmw_node_t * rmw_create_node(rmw_context_t * context, const char * name, const char * namespace_)
{
  try {
    return rmw_create_node_impl(context, name, namespace_);
  }
  APEX_RMW_IDA_ERROR_CATCHER_PTR()
}

rmw_ret_t rmw_destroy_node(rmw_node_t * node)
{
  try {
    return rmw_destroy_node_impl(node);
  }
  APEX_RMW_IDA_ERROR_CATCHER_RET()
}

rmw_ret_t rmw_qos_profile_check_compatible(const rmw_qos_profile_t publisher_profile,
                                           const rmw_qos_profile_t subscription_profile,
                                           rmw_qos_compatibility_type_t * compatibility,
                                           char * reason,
                                           size_t reason_size)
{
  try {
    return rmw_qos_profile_check_compatible_impl(
      publisher_profile, subscription_profile, compatibility, reason, reason_size);
  }
  APEX_RMW_IDA_ERROR_CATCHER_RET()
}

const rmw_guard_condition_t * rmw_node_get_graph_guard_condition(const rmw_node_t * node)
{
  try {
    return rmw_node_get_graph_guard_condition_impl(node);
  }
  APEX_RMW_IDA_ERROR_CATCHER_PTR()
}
