/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief implements the logic to get the instance of C++ message typesupport

#ifndef TYPESUPPORT__MESSAGE_HPP_
#define TYPESUPPORT__MESSAGE_HPP_

#include "grace/rmw_ida/common/rmw_allocator.hpp"
#include "grace/rmw_ida/ida_support/ida_context.hpp"
#include "grace/rmw_ida/typesupport/publisher.hpp"
#include "grace/rmw_ida/typesupport/subscriber.hpp"
#include "grace/rmw_ida/typesupport/traits.hpp"
#include "ida/base/core/error.hpp"
#include "ida/base/core/expected.hpp"
#include "ida/base/core/not_null.hpp"
#include "ida/plexus/common/def.hpp"
#include "ida/plexus/publisher/data_writer_qos.hpp"
#include "ida/serializers/cdr/serialization.hpp"
#include "rmw/serialized_message.h"

#ifdef APEX_CERT
//  AXIVION Construct MisraC++2023-6.0.3 : This type is required to be in the global namespace
extern "C" struct rosidl_message_type_support_t;

namespace rosidl_typesupport_cpp
{
template <typename T>
const rosidl_message_type_support_t * get_message_type_support_handle();
}  // namespace rosidl_typesupport_cpp
#endif

namespace apex::rmw_ida::typesupport
{

class MessageSerializationInterface
{
public:
  /**
   * @brief Gets the name of the package containing the message type used by the subscriber
   * @return package name string
   */
  virtual const char * package_name() const = 0;

  /**
   * @brief Gets the namespace of the package containing the message type
   * @return message namespace string
   */
  virtual const char * message_namespace_name() const = 0;

  /**
   * @brief Gets the name of the message type. This is the native DDS type
   * @return message name string
   */
  virtual const char * message_name() const = 0;

  /**
   * @brief Converts a non-flat ROS message to the equivalent non-flat DDS message
   * @param[in] untyped_ros_message A pointer to the ROS message that is to be converted to DDS
   * @param[out] untyped_dds_message A pointer to a DDS message structure that will contain the
   * converted message
   * @return True if the conversion was successful, otherwise error
   */
  [[nodiscard]] virtual bool convert_non_flat_ros_to_non_flat_dds(
    base::not_null<const void *> untyped_ros_message,
    base::not_null<void *> untyped_dds_message) const = 0;

  /**
   * @brief Converts a non-flat ROS message to the equivalent flat DDS message
   * @param[in] untyped_ros_message A pointer to the ROS message that is to be converted to DDS
   * @param[out] untyped_dds_message A pointer to a DDS message structure that will contain the
   * converted message
   * @return True if the conversion was successful, otherwise error
   */
  [[nodiscard]] virtual bool convert_non_flat_ros_to_flat_dds(
    base::not_null<const void *> untyped_ros_message,
    base::not_null<void *> untyped_dds_message) const = 0;

  /**
   * @brief Converts a flat ROS message to the equivalent non-flat DDS message
   * @param[in] untyped_ros_message A pointer to the ROS message that is to be converted to DDS
   * @param[out] untyped_dds_message A pointer to a DDS message structure that will contain the
   * converted message
   * @return True if the conversion was successful, otherwise error
   */
  [[nodiscard]] virtual bool convert_flat_ros_to_non_flat_dds(
    base::not_null<const void *> untyped_ros_message,
    base::not_null<void *> untyped_dds_message) const = 0;

  /**
   * @brief Converts a flat ROS message to the equivalent flat DDS message
   * @param[in] untyped_ros_message A pointer to the ROS message that is to be converted to DDS
   * @param[out] untyped_dds_message A pointer to a DDS message structure that will contain the
   * converted message
   * @return True if the conversion was successful, otherwise error
   */
  [[nodiscard]] virtual bool convert_flat_ros_to_flat_dds(
    base::not_null<const void *> untyped_ros_message,
    base::not_null<void *> untyped_dds_message) const = 0;

  /**
   * @brief Converts a non-flat DDS message to the equivalent non-flat ROS message
   * @param[in] untyped_dds_message A pointer to the DDS message that is to be converted to ROS
   * @param[out] untyped_ros_message A pointer to the ROS message structure that will contain
   * the converted message
   * @return True if the conversion was successful, otherwise error
   */
  [[nodiscard]] virtual bool convert_non_flat_dds_to_non_flat_ros(
    base::not_null<const void *> untyped_dds_message,
    base::not_null<void *> untyped_ros_message) const = 0;

  /**
   * @brief Converts a non-flat DDS message to the equivalent flat ROS message
   * @param[in] untyped_dds_message A pointer to the DDS message that is to be converted to ROS
   * @param[out] untyped_ros_message A pointer to the ROS message structure that will contain
   * the converted message
   * @return True if the conversion was successful, otherwise error
   */
  [[nodiscard]] virtual bool convert_non_flat_dds_to_flat_ros(
    base::not_null<const void *> untyped_dds_message,
    base::not_null<void *> untyped_ros_message) const = 0;

  /**
   * @brief Converts a flat DDS message to the equivalent non-flat ROS message
   * @param[in] untyped_dds_message A pointer to the DDS message that is to be converted to ROS
   * @param[out] untyped_ros_message A pointer to the ROS message structure that will contain
   * the converted message
   * @return True if the conversion was successful, otherwise error
   */
  [[nodiscard]] virtual bool convert_flat_dds_to_non_flat_ros(
    base::not_null<const void *> untyped_dds_message,
    base::not_null<void *> untyped_ros_message) const = 0;

  /**
   * @brief Converts a flat DDS message to the equivalent flat ROS message
   * @param[in] untyped_dds_message A pointer to the DDS message that is to be converted to ROS
   * @param[out] untyped_ros_message A pointer to the ROS message structure that will contain
   * the converted message
   * @return True if the conversion was successful, otherwise error
   */
  [[nodiscard]] virtual bool convert_flat_dds_to_flat_ros(
    base::not_null<const void *> untyped_dds_message,
    base::not_null<void *> untyped_ros_message) const = 0;

  /***
   * @brief Deserialize from CDR buffer to ROS message
   *
   * @note The buffer expects to also contain the data encapsulation identifier along with the data
   *
   * @param[out] untyped_ros_message A pointer to the ROS message that will contain
   * the deserialized message
   * @param[in] serialized_message A cdr serialized buffer
   * @return Error if the deserialization fails
   */
  virtual Result<void> deserialize_to_ros_message(
    base::not_null<void *> untyped_ros_message,
    const rmw_serialized_message_t & serialized_message) const = 0;

  /***
   * @brief Deserialize from CDR buffer to a loaned ROS message
   *
   * @note The buffer expects to also contain the data encapsulation identifier along with the data
   *
   * @param[out] loaned_ros_message A pointer to the loaned ROS message that will contain
   * the deserialized message
   * @param[in] serialized_message A cdr serialized buffer
   * @return Error if the deserialization fails
   */
  virtual Result<void> deserialize_to_loaned_ros_message(
    base::not_null<void *> untyped_loaned_ros_message,
    const rmw_serialized_message_t & serialized_message) const = 0;

  /***
   * @brief Serialize a ROS message to the rmw serialized buffer
   *
   * @note The buffer in the rmw serialized structure will be resized based on the size of the
   * message and will also contain the data encapsulation identifier along with the data
   *
   * @param[out] untyped_ros_message A pointer to the ROS message
   * @param[in] serialized_message destination structure for the serialized ROS message
   * @return Error if the serialization fails
   */
  virtual Result<void> serialize_to_rmw_serialized_buffer(
    base::not_null<const void *> untyped_ros_message,
    rmw_serialized_message_t & serialized_message) const = 0;

  virtual ~MessageSerializationInterface() = default;
};

class MessagePublisherSupportInterface
{
public:
  /***
   * @brief Create a Publisher for the requested type on a topic
   * @param ida_context The Ida context that has access to the DomainParticipant
   * @param topic_name Name of the topic the subscriber is subscribed to
   * @param datawriter_qos QoS for the DataWriter used by this publisher
   * @return On success, unique_ptr to the typed publisher
   */
  virtual apex::Result<unique_not_null<MessagePublisherInterface>> create_publisher(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view topic_name,
    const apex::plexus::DataWriterQoS & datawriter_qos) const = 0;

  virtual ~MessagePublisherSupportInterface() = default;
};

template <typename MessageTypeTraits>
class MessagePublisherTypeSupport final : public MessagePublisherSupportInterface
{
public:
  apex::Result<unique_not_null<MessagePublisherInterface>> create_publisher(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view topic_name,
    const apex::plexus::DataWriterQoS & datawriter_qos) const override
  {
    auto publisher_result =
      apex::rmw_ida::RMWAllocator::create_unique<MessagePublisher<MessageTypeTraits>>(
        ida_context, node_namespace, node_name, topic_name, datawriter_qos);

    if (publisher_result.has_error()) {
      return base::err(base::move(publisher_result.error()));
    }
    return base::ok(base::move(publisher_result.value()));
  }
};

class MessageSubscriberSupportInterface
{
public:
  /***
   * @brief Create a Subscriber for the requested type on a topic
   * @param ida_context The Ida context that has access to the DomainParticipant
   * @param topic_name Name of the topic the subscriber is subscribed to
   * @param datareader_qos QoS for the DataReader used by this subscriber
   * @return On success, unique_ptr to the typed subscriber
   */
  virtual apex::Result<unique_not_null<MessageSubscriberInterface>> create_subscriber(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos) const = 0;

  virtual ~MessageSubscriberSupportInterface() = default;
};

template <typename MessageTypeTraits>
class MessageSubscriberTypeSupport final : public MessageSubscriberSupportInterface
{
public:
  apex::Result<unique_not_null<MessageSubscriberInterface>> create_subscriber(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos) const override
  {
    auto subscriber_result =
      apex::rmw_ida::RMWAllocator::create_unique<MessageSubscriber<MessageTypeTraits>>(
        ida_context, node_namespace, node_name, topic_name, datareader_qos);
    if (subscriber_result.has_error()) {
      return base::err(base::move(subscriber_result.error()));
    }

    return base::ok(base::move(subscriber_result.value()));
  }
};

class MessageTypeSupportInterface : public MessageSerializationInterface,
                                    public MessagePublisherSupportInterface,
                                    public MessageSubscriberSupportInterface
{
public:
  virtual ~MessageTypeSupportInterface() = default;
};

/**
 * @brief Implementation of the MessageTypeSupportInterface for a specific type
 * @tparam MessageTypeTraits The type traits struct generated by the rosidl ida typesupport
 */
template <typename MessageTypeTraits>
class MessageTypeSupport final : public MessageTypeSupportInterface
{
  using DDSType = typename MessageTypeTraits::DDSType;
  using NonFlatDDSType = typename MessageTypeTraits::DDSType::NonFlatType;
  using ROSType = typename MessageTypeTraits::ROSType;
  using BorrowedROSType = typename MessageTypeTraits::LoanedROSType;

public:
  MessageTypeSupport()
  : m_pub_impl(MessageTypeTraits::get_publisher_support_interface()),
    m_sub_impl(MessageTypeTraits::get_subscriber_support_interface())
  {
  }

  const char * package_name() const override
  {
    return MessageTypeTraits::package_name;
  }

  const char * message_namespace_name() const override
  {
    return MessageTypeTraits::message_namespace_name;
  }

  const char * message_name() const override
  {
    return MessageTypeTraits::message_name;
  }

  [[nodiscard]] bool convert_non_flat_ros_to_non_flat_dds(
    base::not_null<const void *> untyped_ros_message,
    base::not_null<void *> untyped_dds_message) const override
  {
    const auto & ros_message = *static_cast<const ROSType *>(untyped_ros_message.get());
    auto & dds_message = *static_cast<NonFlatDDSType *>(untyped_dds_message.get());
    return MessageTypeTraits::convert_ros_message_to_dds(ros_message, dds_message);
  }

  [[nodiscard]] bool convert_non_flat_ros_to_flat_dds(
    base::not_null<const void *> untyped_ros_message,
    base::not_null<void *> untyped_dds_message) const override
  {
    const auto & ros_message = *static_cast<const ROSType *>(untyped_ros_message.get());
    auto & dds_message = *static_cast<DDSType *>(untyped_dds_message.get());
    return MessageTypeTraits::convert_ros_message_to_dds(ros_message, dds_message);
  }

  [[nodiscard]] bool convert_flat_ros_to_non_flat_dds(
    base::not_null<const void *> untyped_ros_message,
    base::not_null<void *> untyped_dds_message) const override
  {
    const auto & ros_message = *static_cast<const BorrowedROSType *>(untyped_ros_message.get());
    auto & dds_message = *static_cast<NonFlatDDSType *>(untyped_dds_message.get());
    return MessageTypeTraits::convert_ros_message_to_dds(ros_message, dds_message);
  }

  [[nodiscard]] bool convert_flat_ros_to_flat_dds(
    base::not_null<const void *> untyped_ros_message,
    base::not_null<void *> untyped_dds_message) const override
  {
    const auto & ros_message = *static_cast<const BorrowedROSType *>(untyped_ros_message.get());
    auto & dds_message = *static_cast<DDSType *>(untyped_dds_message.get());
    return MessageTypeTraits::convert_ros_message_to_dds(ros_message, dds_message);
  }

  [[nodiscard]] bool convert_non_flat_dds_to_non_flat_ros(
    base::not_null<const void *> untyped_dds_message,
    base::not_null<void *> untyped_ros_message) const override
  {
    const auto & dds_message = *static_cast<const NonFlatDDSType *>(untyped_dds_message.get());
    auto & ros_message = *static_cast<ROSType *>(untyped_ros_message.get());
    return MessageTypeTraits::convert_dds_message_to_ros(dds_message, ros_message);
  }

  [[nodiscard]] bool convert_non_flat_dds_to_flat_ros(
    base::not_null<const void *> untyped_dds_message,
    base::not_null<void *> untyped_ros_message) const override
  {
    const auto & dds_message = *static_cast<const NonFlatDDSType *>(untyped_dds_message.get());
    auto & ros_message = *static_cast<BorrowedROSType *>(untyped_ros_message.get());
    return MessageTypeTraits::convert_dds_message_to_ros(dds_message, ros_message);
  }

  [[nodiscard]] bool convert_flat_dds_to_non_flat_ros(
    base::not_null<const void *> untyped_dds_message,
    base::not_null<void *> untyped_ros_message) const override
  {
    const auto & dds_message = *static_cast<const DDSType *>(untyped_dds_message.get());
    auto & ros_message = *static_cast<ROSType *>(untyped_ros_message.get());
    return MessageTypeTraits::convert_dds_message_to_ros(dds_message, ros_message);
  }

  [[nodiscard]] bool convert_flat_dds_to_flat_ros(
    base::not_null<const void *> untyped_dds_message,
    base::not_null<void *> untyped_ros_message) const override
  {
    const auto & dds_message = *static_cast<const DDSType *>(untyped_dds_message.get());
    auto & ros_message = *static_cast<BorrowedROSType *>(untyped_ros_message.get());
    return MessageTypeTraits::convert_dds_message_to_ros(dds_message, ros_message);
  }

  Result<void> deserialize_to_ros_message(
    base::not_null<void *> untyped_ros_message,
    const rmw_serialized_message_t & serialized_message) const override
  {
    if constexpr (dds_can_alias) {
      return deserialize_to_loaned_ros_message(untyped_ros_message, serialized_message);
    } else {
      auto dds_message = std::make_unique<DDSType>();
      serialization::Reader reader{base::span<const base::byte>{
        reinterpret_cast<const base::byte *>(serialized_message.buffer),
        serialized_message.buffer_capacity}};
      if (auto deserialize_res = cdr_serialization::deserialize(*dds_message, reader);
          deserialize_res.has_error()) {
        return apex::err(
          "Deserialization from cdr buffer failed",
          APEX_ERROR_FROM_ENUM(apex::serialization::SerializationError, deserialize_res.error()));
      }
      auto & ros_message = *static_cast<ROSType *>(untyped_ros_message.get());
      if (!MessageTypeTraits::convert_dds_message_to_ros(*dds_message, ros_message)) {
        return err("conversion from DDS to ROS failed in deserialize data from CDR", RMW_RET_ERROR);
      }
    }
    return base::ok();
  }

  Result<void> deserialize_to_loaned_ros_message(
    base::not_null<void *> untyped_loaned_ros_message,
    const rmw_serialized_message_t & serialized_message) const override
  {
    auto & dds_message = *reinterpret_cast<DDSType *>(untyped_loaned_ros_message.get());
    serialization::Reader reader{
      base::span<const base::byte>{reinterpret_cast<const base::byte *>(serialized_message.buffer),
                                   serialized_message.buffer_capacity}};
    if (auto deserialize_res = cdr_serialization::deserialize(dds_message, reader);
        deserialize_res.has_error()) {
      return apex::err(
        "Deserialization from cdr buffer failed",
        APEX_ERROR_FROM_ENUM(apex::serialization::SerializationError, deserialize_res.error()));
    }
    return base::ok();
  }

  Result<void> serialize_to_rmw_serialized_buffer(
    base::not_null<const void *> untyped_ros_message,
    rmw_serialized_message_t & serialized_message) const override
  {
    if constexpr (dds_can_alias) {
      const auto & dds_message = *reinterpret_cast<const DDSType *>(untyped_ros_message.get());
      auto size_res = cdr_serialization::get_serialized_size(dds_message);
      if (size_res.has_error()) {
        return apex::err(
          "serialization size computation failed",
          APEX_ERROR_FROM_ENUM(apex::serialization::SerializationError, size_res.error()));
      }
      auto serialized_size = size_res.value();
      if (RMW_RET_OK != rmw_serialized_message_resize(&serialized_message, serialized_size)) {
        return apex::err("Failed to allocate serialized message buffer");
      }
      serialization::Writer writer{
        base::span<base::byte>{reinterpret_cast<base::byte *>(serialized_message.buffer),
                               serialized_message.buffer_capacity}};
      if (auto ser_res = cdr_serialization::serialize(dds_message, writer); ser_res.has_error()) {
        return apex::err(
          "cdr serialization failed",
          APEX_ERROR_FROM_ENUM(apex::serialization::SerializationError, ser_res.error()));
      }
      // set the buffer length after the serialization
      serialized_message.buffer_length = serialized_size;
    } else {
      const auto & ros_message = *static_cast<const ROSType *>(untyped_ros_message.get());
      auto dds_message = std::make_unique<DDSType>();
      if (!MessageTypeTraits::convert_ros_message_to_dds(ros_message, *dds_message)) {
        return err("conversion from ROS to DDS failed in serialize data from CDR", RMW_RET_ERROR);
      }
      auto size_res = cdr_serialization::get_serialized_size(*dds_message);
      auto serialized_size = size_res.value();
      if (size_res.has_error()) {
        return apex::err(
          "serialization size computation failed",
          APEX_ERROR_FROM_ENUM(apex::serialization::SerializationError, size_res.error()));
      }
      if (RMW_RET_OK != rmw_serialized_message_resize(&serialized_message, serialized_size)) {
        return apex::err("Failed to allocate serialized message buffer");
      }
      serialization::Writer writer{
        base::span<base::byte>{reinterpret_cast<base::byte *>(serialized_message.buffer),
                               serialized_message.buffer_capacity}};
      if (auto ser_res = cdr_serialization::serialize(*dds_message, writer); ser_res.has_error()) {
        return apex::err(
          "cdr serialization failed",
          APEX_ERROR_FROM_ENUM(apex::serialization::SerializationError, ser_res.error()));
      }
      // set the buffer length after the serialization
      serialized_message.buffer_length = serialized_size;
    }
    return base::ok();
  }

  apex::Result<unique_not_null<MessageSubscriberInterface>> create_subscriber(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos) const override
  {
    return m_sub_impl->create_subscriber(
      ida_context, node_namespace, node_name, topic_name, datareader_qos);
  }

  apex::Result<unique_not_null<MessagePublisherInterface>> create_publisher(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view topic_name,
    const apex::plexus::DataWriterQoS & datawriter_qos) const override
  {
    return m_pub_impl->create_publisher(
      ida_context, node_namespace, node_name, topic_name, datawriter_qos);
  }

private:
  static constexpr bool dds_can_alias = MessageTypeTraits::dds_can_alias;

  base::not_null<const MessagePublisherSupportInterface *> m_pub_impl;
  base::not_null<const MessageSubscriberSupportInterface *> m_sub_impl;
};

}  // namespace apex::rmw_ida::typesupport

#endif  // TYPESUPPORT__MESSAGE_HPP_
