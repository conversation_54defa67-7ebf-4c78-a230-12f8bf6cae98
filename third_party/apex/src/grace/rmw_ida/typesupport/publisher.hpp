/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.
/// \file
/// \brief

#ifndef RMW_IDA_TYPESUPPORT_PUBLISHER_HPP_
#define RMW_IDA_TYPESUPPORT_PUBLISHER_HPP_

#include "grace/rmw_ida/typesupport/entity.hpp"
#include "grace/rmw_ida/typesupport/loan_tracker.hpp"
#include "ida/common/error.hpp"
#include "ida/common/logging.hpp"
#include "ida/plexus/common/def.hpp"
#include "ida/plexus/domain/data_writer.hpp"
#include "ida/plexus/publisher/data_writer_qos.hpp"
#include "ida/serializers/cdr/serialization.hpp"

namespace apex::rmw_ida::typesupport
{
/**
 * @brief untyped interface for publishing messages and for accessing the underlying DDS Datawriter
 */
class MessagePublisherInterface
{
public:
  /**
   * @brief Publishes a ROS message to the topic
   *
   * @param untyped_ros_message A pointer to the message that is to be published
   * @return True if the message was successfully published, otherwise False
   */
  virtual bool publish(const void * untyped_ros_message) = 0;

  /**
   * @brief  Publishes message that has been already CDR serialized in the buffer
   * @note The buffer expects to also contain the data encapsulation identifier along with the data
   *
   * @param[in] buffer the buffer with serialized message data
   * @param[in] length the length of the buffer in bytes
   */
  virtual Result<void> publish_serialized(const uint8_t * buffer, size_t length) = 0;

  /**
   * @brief Get the inner DDS data writer
   * @return Reference to the data writer
   */
  virtual apex::plexus::AnyDataWriter & get_datawriter() = 0;

  /**
   * @brief Get the global identifier from the inner DDS data writer
   * @return GUID associated with this DDS data writer
   */
  virtual apex::plexus::EndpointId get_gid() = 0;

  /**
   * @brief Get DDS writer QoS
   * @return QoS value
   */
  virtual apex::plexus::DataWriterQoS get_qos() = 0;

  /**
   * @brief Checks is the publisher supports loaning memory
   * @return Ok if loaning is possible, error containing the reason for not supporting loaning
   * otherwise
   */
  virtual Result<void> can_loan_messages() const = 0;

  /**
   * @brief Borrows a loaned message from the middleware
   * @param[in] untyped_ros_message Pointer to the buffer in shared memory
   */
  virtual Result<void> borrow_loaned_message(void ** untyped_ros_message) = 0;

  /**
   * @brief Publishes a loaned ROS message to the topic
   * @param[in] untyped_ros_message A pointer to the loaned message that is to be published
   * @return True if the message was successfully published, otherwise False
   */
  virtual Result<void> publish_loaned_message(const void * untyped_ros_message) = 0;

  /**
   * @brief Return the already requested loan to the middleware
   * @param[in]  loaned_message Pointer to the loan
   */
  virtual Result<void> return_loan(void * loaned_message) = 0;

  /**
   * @brief Return the number of already borrowed messages
   * @return Number of borrowed messages
   */
  virtual size_t borrowed_messages() const = 0;

  virtual ~MessagePublisherInterface() = default;
};

/**
 * @brief Concrete implementation of the MessagePublisherInterface for a given Message type
 * @tparam  MessageTypeTraits The type traits struct generated by
 * rosidl_typesupport_apex_middleware_c/cpp
 */
template <typename MessageTypeTraits>
class MessagePublisher final : public MessagePublisherInterface, Entity
{
private:
  using DDSType = typename MessageTypeTraits::DDSType;
  using DataWriter = apex::plexus::DataWriter<DDSType>;
  using DataWriterQoS = apex::plexus::DataWriterQoS;
  using IdaContext = apex::rmw_ida::IdaContext;
  using Publisher = apex::plexus::Publisher;
  using Loan = apex::plexus::Loan<DDSType>;
  using ROSType = typename MessageTypeTraits::ROSType;

public:
  /**
   * @brief Concrete implementation of the MessagePublisherInterface for a given Message type
   * @param[in] ida_context Middleware context containing the dds publisher/subscriber and domain
   * participants rosidl_typesupport_apex_middleware_c/cpp
   * @param[in] topic_name Topic associated with the publisher to be created
   * @param[in] datawriter_qos DDS datawriter qos
   * @param[in] publisher Publisher associated with the inner DataWriter
   */
  MessagePublisher(base::badge<base::creator> badge,
                   base::optional<base::error> & maybe_error,
                   IdaContext & ida_context,
                   const apex::plexus::EndpointTagValue & node_namespace,
                   const apex::plexus::EndpointTagValue & node_name,
                   base::string_view topic_name,
                   const DataWriterQoS & datawriter_qos)
  : Entity(ida_context),
    m_loan_tracker(badge,
                   maybe_error,
                   allocator(),
                   topic_name,
                   datawriter_qos.resource_limits().max_allocated_samples())
  {
    if (maybe_error.has_value()) {
      return;
    }

    auto topic_result = ida_context.get_domain_participant().create_topic<DDSType>(topic_name);

    if (topic_result.has_error()) {
      maybe_error = base::error("Error creating topic", base::move(topic_result.error()));
      return;
    }

    auto data_writer_res = ida_context.get_publisher().create_datawriter(
      topic_result.value(),
      datawriter_qos,
      {{apex::plexus::NODE_NAMESPACE_KEY, node_namespace},
       {apex::plexus::NODE_NAME_KEY, node_name}});

    if (data_writer_res.has_error()) {
      maybe_error = base::error("Error creating data_writer", base::move(data_writer_res.error()));
      return;
    }

    m_datawriter.emplace(base::move(data_writer_res.value()));
  }

  ~MessagePublisher() noexcept override {}

  apex::plexus::AnyDataWriter & get_datawriter() override
  {
    return *static_cast<apex::plexus::AnyDataWriter *>(&m_datawriter.value());
  }

  apex::plexus::EndpointId get_gid() override
  {
    return m_datawriter->get_context()->id();
  }

  apex::plexus::DataWriterQoS get_qos() override
  {
    return m_datawriter->get_context()->qos();
  }

  bool publish(const void * untyped_ros_message) override
  {
    return publish_impl(untyped_ros_message, base::integral_constant<bool, dds_can_alias>());
  }

  Result<void> publish_serialized(const uint8_t * buffer, size_t length) override
  {
    // TODO(Sumanth.Nirmal) #28506, update this to use the CDR APIs from Ida
    // for now deserialize here and directly publish, as the serialized message is already the DDS
    // type

    // Ida can handle the loan for different types
    auto dds_loaned_sample = m_datawriter->loan();
    if (dds_loaned_sample.has_error()) {
      BASE_LOG(ERROR, "Failed to loan during publish serialized: " << dds_loaned_sample.error());
      return apex::err(base::move(dds_loaned_sample.error()));
    }
    serialization::Reader reader{
      base::span<const base::byte>{reinterpret_cast<const base::byte *>(buffer), length}};

    if (auto deserialize_res =
          cdr_serialization::deserialize(dds_loaned_sample.value().get(), reader);
        deserialize_res.has_error()) {
      return apex::err(
        "Deserialization from cdr buffer failed",
        APEX_ERROR_FROM_ENUM(apex::serialization::SerializationError, deserialize_res.error()));
    }
    auto write_res = m_datawriter->write(base::move(*dds_loaned_sample));
    if (write_res.has_error()) {
      return apex::err(base::move(write_res.error()));
    }

    auto flush_res = m_datawriter->flush();
    if (flush_res.has_error()) {
      BASE_LOG(ERROR, "Failed flush on data writer: " << flush_res.error());
      return apex::err(base::move(flush_res.error()));
    }

    return base::ok();
  }

  Result<void> can_loan_messages() const override
  {
    if (can_loan) {
      return base::ok();
    } else {
      base::fixed_string<128> message_name{base::truncate_to_capacity,
                                           MessageTypeTraits::message_name};

      // Build string with the reason for the publisher not supporting loaning
      base::err_message reason;
      reason.append(base::truncate_to_capacity, "Message \"");
      reason.append(base::truncate_to_capacity, message_name);
      reason.append(base::truncate_to_capacity, "\" is not self contained");
      return apex::err(reason);
    }
  }

  Result<void> borrow_loaned_message(void ** untyped_ros_message) override
  {
    if (auto can_loan_result = can_loan_messages(); can_loan_result.has_error()) {
      return apex::err(base::move(can_loan_result.error()));
    }

    if (nullptr == untyped_ros_message) {
      return apex::err("Invalid argument to assign ros message");
    }

    auto loan_res = m_datawriter->loan();
    if (loan_res.has_error()) {
      return apex::err(base::move(loan_res.error()));
    }

    auto & loan = loan_res.value();
    auto loan_ptr = &loan.get();

    if (!m_loan_tracker.insert(loan_ptr, base::move(loan))) {
      return apex::err("Maximum number of loans reached");
    }

    *untyped_ros_message = loan_ptr;
    return base::ok();
  }

  Result<void> publish_loaned_message(const void * untyped_ros_message) override
  {
    if (nullptr == untyped_ros_message) {
      return apex::err("Invalid argument to assign ros message");
    };

    if (!m_datawriter.has_value()) {
      return apex::err("Attempting to publish message on unitialized data writer");
    }

    auto loan = m_loan_tracker.extract(untyped_ros_message);
    if (loan.has_value()) {
      auto write_res = m_datawriter->write(base::move(loan.value()));
      if (write_res.has_error()) {
        return apex::err("Failed to write message", base::move(write_res.error()));
      }

      auto flush_res = m_datawriter->flush();
      if (flush_res.has_error()) {
        return apex::err("Failed to flush data writer", base::move(flush_res.error()));
      }
    } else {
      // TODO(flavio.silvestre): #28346 Remove this fallback branch
      // User is trying to publish a message type that does not support loaning, we attempt to
      // publish/write by copying the sample instead
      if (!publish(untyped_ros_message)) {
        return apex::err("Failed to publish message");
      }
    }
    return base::ok();
  }

  size_t borrowed_messages() const override
  {
    return m_loan_tracker.size();
  }

  Result<void> return_loan(void * loaned_message) override
  {
    if (!m_loan_tracker.erase(loaned_message)) {
      return apex::err("Attempting to return a loan that doesn't belong to the publisher");
    };
    return base::ok();
  }

private:
  bool publish_impl(const void * untyped_ros_message, base::false_type)
  {
    if ((nullptr != untyped_ros_message) && (m_datawriter.has_value())) {
      auto dds_message = std::make_unique<DDSType>();
      const auto & ros_message = *static_cast<const ROSType *>(untyped_ros_message);
      if (MessageTypeTraits::convert_ros_message_to_dds(ros_message, *dds_message)) {
        auto write_res = m_datawriter->write(*dds_message);
        if (write_res.has_error()) {
          return false;
        }

        auto flush_res = m_datawriter->flush();
        if (flush_res.has_error()) {
          BASE_LOG(ERROR, "Failed flush on data writer: " << flush_res.error());
          return false;
        }
        return true;
      }
    }
    return false;
  }

  // zero-copy publish implementation, ros and dds types can alias
  bool publish_impl(const void * untyped_ros_message, base::true_type)
  {
    if (untyped_ros_message == nullptr) {
      BASE_LOG(ERROR, "Failed to publish: null ros_message passed to interface");
      return false;
    }

    if (!m_datawriter.has_value()) {
      BASE_LOG(ERROR, "Failed to publish: Data writer not initialized");
      return false;
    }

    const DDSType & dds_message = *reinterpret_cast<const DDSType *>(untyped_ros_message);
    auto write_res = m_datawriter->write(dds_message);
    if (write_res.has_error()) {
      return false;
    }

    auto flush_res = m_datawriter->flush();
    if (flush_res.has_error()) {
      BASE_LOG(ERROR, "Failed flush on data writer: " << flush_res.error());
      return false;
    }
    return true;
  }

  static constexpr bool dds_can_alias = MessageTypeTraits::dds_can_alias;
  static constexpr bool can_loan = apex::plexus::SampleTraits<DDSType>::is_self_contained;
  base::optional<DataWriter> m_datawriter{base::nullopt};
  LoanTracker<Loan> m_loan_tracker;
};

}  // namespace apex::rmw_ida::typesupport

#endif  // RMW_IDA_TYPESUPPORT_PUBLISHER_HPP_
