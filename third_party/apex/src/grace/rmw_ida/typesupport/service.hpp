/// \copyright Copyright 2023 Apex.AI, Inc.
/// All rights reserved.

#ifndef TYPESUPPORT__SERVICE_HPP_
#define TYPESUPPORT__SERVICE_HPP_

#include "grace/rmw_ida/common/rmw_allocator.hpp"
#include "grace/rmw_ida/ida_support/ida_context.hpp"
#include "grace/rmw_ida/typesupport/replier.hpp"
#include "grace/rmw_ida/typesupport/requester.hpp"
#include "ida/base/core/error.hpp"
#include "ida/base/core/not_null.hpp"

#ifdef APEX_CERT
//  AXIVION Construct MisraC++2023-6.0.3 : This type is required to be in the global namespace
extern "C" struct rosidl_service_type_support_t;

namespace rosidl_typesupport_cpp
{
template <typename T>
const rosidl_service_type_support_t * get_service_type_support_handle();
}  // namespace rosidl_typesupport_cpp
#endif  // APEX_CERT

namespace apex::rmw_ida::typesupport
{
/**
 * @brief Type support interface for Services to handle interacting with and converting
 * between the ROS and DDS types used by the Service
 */
class ServiceCommonInterface
{
public:
  /**
   * @brief Gets the name of the package containing the Service types
   * @return package name string
   */
  virtual const char * package_name() const = 0;

  /**
   * @brief Gets the namespace of the package containing the Service types
   * @return message namespace string
   */
  virtual const char * service_namespace_name() const = 0;

  /**
   * @brief Gets the name of the Service
   * @return service name
   */
  virtual const char * service_name() const = 0;

  virtual ~ServiceCommonInterface() noexcept = default;
};

class ServiceRequesterSupportInterface
{
public:
  virtual apex::Result<unique_not_null<ServiceRequesterInterface>> create_requester(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view request_topic_name,
    base::string_view response_topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos,
    const apex::plexus::DataWriterQoS & datawriter_qos) const = 0;

  virtual ~ServiceRequesterSupportInterface() noexcept = default;
};

template <typename ServiceTypeTraits>
class ServiceRequesterTypeSupport final : public ServiceRequesterSupportInterface
{
public:
  apex::Result<unique_not_null<ServiceRequesterInterface>> create_requester(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view request_topic_name,
    base::string_view response_topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos,
    const apex::plexus::DataWriterQoS & datawriter_qos) const override
  {
    auto requester_result =
      apex::rmw_ida::RMWAllocator::create_unique<ServiceRequester<ServiceTypeTraits>>(
        ida_context,
        node_namespace,
        node_name,
        request_topic_name,
        response_topic_name,
        datawriter_qos,
        datareader_qos);
    if (requester_result.has_error()) {
      return err("creation of requester failed", base::move(requester_result.error()));
    }

    return base::ok(base::move(requester_result.value()));
  }
};

class ServiceReplierSupportInterface
{
public:
  virtual apex::Result<unique_not_null<ServiceReplierInterface>> create_replier(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view request_topic_name,
    base::string_view response_topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos,
    const apex::plexus::DataWriterQoS & datawriter_qos) const = 0;

  virtual ~ServiceReplierSupportInterface() noexcept = default;
};

template <typename ServiceTypeTraits>
class ServiceReplierTypeSupport final : public ServiceReplierSupportInterface
{
public:
  apex::Result<unique_not_null<ServiceReplierInterface>> create_replier(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view request_topic_name,
    base::string_view response_topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos,
    const apex::plexus::DataWriterQoS & datawriter_qos) const override
  {
    auto replier_result =
      apex::rmw_ida::RMWAllocator::create_unique<ServiceReplier<ServiceTypeTraits>>(
        ida_context,
        node_namespace,
        node_name,
        request_topic_name,
        response_topic_name,
        datawriter_qos,
        datareader_qos);
    if (replier_result.has_error()) {
      return err("creation of replier failed", base::move(replier_result.error()));
    }

    return base::ok(base::move(replier_result.value()));
  }
};

class ServiceTypeSupportInterface : public ServiceCommonInterface,
                                    public ServiceRequesterSupportInterface,
                                    public ServiceReplierSupportInterface
{
public:
  virtual ~ServiceTypeSupportInterface() noexcept = default;
};

/**
 * @brief Implementation of the ServiceTypeSupportInterface for a specific Service type
 * @tparam ServiceTypeTraits The type traits structure generated by the ida rosidl typesupport
 */
template <typename ServiceTypeTraits>
class ServiceTypeSupport final : public ServiceTypeSupportInterface
{
public:
  ServiceTypeSupport()
  : m_requester_impl(ServiceTypeTraits::get_requester_support_interface()),
    m_replier_impl(ServiceTypeTraits::get_replier_support_interface())
  {
  }

  const char * package_name() const override
  {
    return ServiceTypeTraits::service_type_package_name;
  }

  const char * service_namespace_name() const override
  {
    return ServiceTypeTraits::service_type_namespace_name;
  }

  const char * service_name() const override
  {
    return ServiceTypeTraits::service_type_name;
  }

  apex::Result<unique_not_null<ServiceRequesterInterface>> create_requester(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view request_topic_name,
    base::string_view response_topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos,
    const apex::plexus::DataWriterQoS & datawriter_qos) const override
  {
    return m_requester_impl->create_requester(ida_context,
                                              node_namespace,
                                              node_name,
                                              request_topic_name,
                                              response_topic_name,
                                              datareader_qos,
                                              datawriter_qos);
  }

  apex::Result<unique_not_null<ServiceReplierInterface>> create_replier(
    apex::rmw_ida::IdaContext & ida_context,
    const apex::plexus::EndpointTagValue & node_namespace,
    const apex::plexus::EndpointTagValue & node_name,
    base::string_view request_topic_name,
    base::string_view response_topic_name,
    const apex::plexus::DataReaderQoS & datareader_qos,
    const apex::plexus::DataWriterQoS & datawriter_qos) const override
  {
    return m_replier_impl->create_replier(ida_context,
                                          node_namespace,
                                          node_name,
                                          request_topic_name,
                                          response_topic_name,
                                          datareader_qos,
                                          datawriter_qos);
  }

private:
  base::not_null<const ServiceRequesterSupportInterface *> m_requester_impl;
  base::not_null<const ServiceReplierSupportInterface *> m_replier_impl;
};
}  // namespace apex::rmw_ida::typesupport
#endif  // TYPESUPPORT__SERVICE_HPP_
