<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
    <name>demo_storage</name>
    <version>0.0.1</version>
    <description>Demo applications for Apex.OS storage</description>
    <maintainer email="<EMAIL>"><PERSON></maintainer>
    <license>Apex.AI Proprietary License</license>

    <buildtool_depend>ament_cmake</buildtool_depend>
    <buildtool_depend>ament_cmake_auto</buildtool_depend>
    <buildtool_depend>apex_cmake</buildtool_depend>
    <buildtool_depend>ament_cmake_pytest</buildtool_depend>

    <depend>apex_init</depend>
    <depend>cpputils</depend>
    <depend>executor2</depend>
    <depend>interrupt</depend>
    <depend>logging</depend>
    <depend>rclcpp</depend>
    <depend>std_msgs</depend>
    <depend>storage</depend>
    <depend>timer_service</depend>
    <depend>demo_storage_msgs</depend>
    <depend>ros2topic</depend>
    <depend>ros2pkg</depend>
    <depend>apex_verify_docs</depend>

    <test_depend>ament_lint_auto</test_depend>
    <test_depend>ament_lint_common</test_depend>
    <test_depend>apex_test_tools</test_depend>
    <test_depend>apex_integration_test_node</test_depend>

    <export><build_type>ament_cmake</build_type></export>
</package>
