---
tags:
  - storage
---

# Demo: storing parameters

## Define storable data types

The storage database works on the level of ROS messages. The data format to be stored is
thus defined as a regular IDL file, and each query to the storage database operates on one
such message.

As an example, consider a perception stack that stores its configuration parameters
in the storage database. The configuration parameters and their data types are
defined as a ROS message type. Assume this type is called
`demo_storage_msgs::msg::PerceptionParameters`.

To store such a message in the database, the `storage` implementation requires an additional
header with storage-related metadata. Thus, it is necessary to define a wrapper type.
The wrapper type should be called `<underlying_type>_Storage` and should contain two
fields: `header` of type `storage_msgs::msg::DataHeader` and `data` of the underlying type:

{{ code_snippet(
"grace/demos/demo_storage/demo_storage_msgs/msg/PerceptionParameters_Storage.idl",
{ 'skip_prefix': '//' }) }}

!!! info
      While it is not strictly necessary to choose the above name for the message type and the
      `data` field, it is strongly recommended to follow these naming conventions.
      In future versions of Apex.Grace, it is planned to generate these wrapper types
      automatically. The generator will use the naming conventions described above.
      Applications following this convention can thus transition to the message generator with
      no or minimal effort.

## The view API

The view API provides a simple way to keep a view into a given storage store
without explicitly sending and receiving store and fetch commands. The `pipeline_parameters`
demo in the `demo_storage` package contains an example how to use
the view API in practice.

### Implementation

The pipeline parameters demo creates a ROS node that represents a node in a perception pipeline.
The node is configured with some parameters: the minimal cluster size, and the minimal and
maximal cluster thresholds. These parameters are stored in the storage database:
they are part of the fixed configuration data stored on the system, but can be
changed at runtime for debugging or calibration. However, the parameters should only change
between separate pipeline runs; while the processing is going on, the parameters have to
stay stable.

To implement this, the node declares a view object, which provides a read-only view into
a particular data store in the storage database:

{{ code_snippet(
"grace/demos/demo_storage/demo_storage/src/pipeline_parameters_apex_prototype.cpp",
{ 'tag': '//! [pipeline-parameters declaration]'}) }}

When triggered, the node updates the parameters to the latest value from the storage store
and runs its workload. Here, it prints the current parameter values, sleeps for 10 seconds,
then prints the parameter values again. As the parameters are not supposed to change during a
run, both prints always result in the same value.
{{ code_snippet(
     "grace/demos/demo_storage/demo_storage/src/pipeline_parameters_apex_prototype.cpp",
     { 'tag': '//! [pipeline-parameters main loop]'}) }}

1. Note that `update` does not send any query to the storage service. It only
   latches in the latest value based on asynchronously-received update notifications
2. `log_parameters` prints out the value of the parameters. This is used in this demo to
   show how the parameters change over time

## Run the demo

{{ source_admonition("ApexOS") }}

To run the demo, call the provided script:

```shell ade
$(ros2 pkg prefix --share demo_storage)/scripts/start_parameters_demo.sh
```

The demo opens a `tmux` interface with four terminals:

1. The `message_storage_service` terminal runs the storage service, the background server
managing the storage data
2. The `pipeline` terminal runs the simulated pipeline. Whenever the pipeline is
   triggered, it prints the current value of its parameter variable, waits for 10 seconds (to
   simulate a complex computation), and prints the current value of its parameter variable again
3. The `pipeline triggerer` terminal defines the Apex.Grace publish command, which
   sends a message to the pipeline's input topic via the `ros2 pub` command and can be called to
   trigger an instance of the pipeline
4. The `storage_cli` terminal provides a space to update and introspect the storage service.

To reduce typing effort, the `storage_cli` defines a `storage_cli_demo` command, which calls
`storage_cli` but automatically fills in the demo's database name and data type.
It is defined as follows:

{{ code_snippet("grace/demos/demo_storage/demo_storage/scripts/start_parameters_demo.sh",
    {'tag': '#! [storage_cli_demo]', "skip_equals": ['send_command "', '" C-m']})
}}

Call `storage_cli_demo --help` to see the possible commands. To exit the demo, press ++ctrl+b++,
type `:kill-session`, and press ++enter++.

Initially, the parameter data store is empty, as can be verified with the `fetch` command within
the `tmux` session `storage_cli` window pane:

```shell ade title="tmux:storage_cli"
storage_cli_demo --fetch
```

If the data store is not empty, for example from a previous run of this demo, remove the data
store

```shell ade title="tmux:storage_cli"
storage_cli_demo --drop-store
```

and restart the pipeline application

```shell ade title="tmux:pipeline"
^C
ros2 run ...
```

Now start a pipeline run using the `trigger_pipeline` command. Observe the parameters printed
in the `pipeline` terminal. Note that the default values in the passed `defaults.json` file are
visible through the `parameter` object, but are not stored in the storage database

```shell ade title="tmux:pipeline triggerer"
trigger_pipeline
```

Now load a default-constructed all-zero message into the storage store. In the
`storage_cli` terminal, call

```shell ade title="tmux:storage_cli"
storage_cli_demo --generate | storage_cli_demo --save
```

Verify that the all-zero message has been stored in the database using

```shell ade
(ade)$ storage_cli_demo --fetch
{
  "data": {
    "max_cluster_threshold": 0.0,
    "min_cluster_size": 0,
    "min_cluster_threshold": 0.0
  },
  "header": {
    ...
  }
}
```

The content of the parameter is visible in the `data` field. The `header` field contains
metadata used by the storage implementation, such as the creation time.

Now trigger the pipeline again

```shell ade title="tmux:pipeline triggerer"
trigger_pipeline
```

Note that the parameters have changed to all zero, as the pipeline's `parameter.update()` call
loaded the latest value in the storage service into the parameter view.

However, between calls to `update`, the parameter value stays stable. To observe this, start
another run of the pipeline. While the pipeline is processing, modify the parameter value using

```shell ade title="tmux:storage_cli"
storage_cli_demo --update '.data.min_cluster_size = 17'
```

Despite the change, the second print-out `min_cluster_size` remains at zero.

Trigger another run of the pipeline. The new value of `17` has been loaded into the view and is
used for future pipelines.

Simulate a crash of the `message_storage_service`:

```shell ade title="tmux:message_storage_service"
^C
```

Verify that the storage service is now unavailable:

```shell ade title="tmux:storage_cli"
storage_cli_demo --fetch
```

Despite the crash, the pipeline can continue without interruption. Try it out by triggering a new
instance of the pipeline: the existing parameter values are used.

Start the `message_storage_service` again

```shell ade title="tmux:message_storage_service"
message_storage_service
```

Verify that the data stored earlier are still available

```shell ade title="tmux:storage_cli_demo"
storage_cli_demo --fetch
```

Furthermore, the running application automatically reconnects to the new service instance without
any disruption. Try this out by updating the stored data *without* restarting the running pipeline
process:

```shell ade title="tmux:storage_cli"
storage_cli_demo --update '.data.min_cluster_size = 600'
```

Trigger a new iteration of the pipeline: the new value of `600` is applied, despite the underlying
restart of the storage service.

<!-- Other things to discuss in the future:
     - how to use commit and what happens if the server crashes hard without storing
       (can be simulated with SIGKILL or Ctrl-\
     - how transient messages work
     - how to look at the update history
-->

## Further reading

For further details on the shown API as well as additional features not show in this demo
see the full [package documentation](storage-design.md).
