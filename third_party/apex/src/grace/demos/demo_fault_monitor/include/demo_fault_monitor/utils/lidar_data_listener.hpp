// Copyright 2023 Apex.AI, Inc
// All rights reserved.
/// \file
/// \brief Listens on the published lidar point cloud data.

#ifndef DEMO_FAULT_MONITOR__UTILS__LIDAR_DATA_LISTENER_HPP_
#define DEMO_FAULT_MONITOR__UTILS__LIDAR_DATA_LISTENER_HPP_

#include <chrono>
#include <functional>
#include <memory>
#include <vector>

#include "diagnostic_common/node_context.hpp"
#include "executor2/executable_item.hpp"
#include "string/string_strict.hpp"

#include "sensor_msgs/msg/point_cloud2.hpp"

namespace utils
{
struct Statistics
{
  const float expected_period;
  const float current_period;
};

using PeriodValidityCallback = std::function<void(bool, const Statistics)>;

class LidarDataListener : public apex::executor::executable_item
{
public:
  LidarDataListener(const apex::diagnostic_common::NodeContextRef & node_context,
                    const apex::string_strict256_t & topic,
                    const float expected_msg_period_ms,
                    const float relative_tolerance_period,
                    const uint64_t time_window_ms);

  void register_data_callback(PeriodValidityCallback callback);

private:
  bool execute_impl() override;
  void process();
  void update_timestamps();
  const float calculate_period();

  apex::executor::subscription_list get_triggering_subscriptions_impl() const override
  {
    return {m_subscriber};
  }

  const rclcpp::PollingSubscription<sensor_msgs::msg::PointCloud2>::SharedPtr m_subscriber;
  const float m_expected_period_us;
  const float m_relative_tolerance;
  const uint64_t m_time_window_ms;
  PeriodValidityCallback m_callback;
  std::vector<std::chrono::steady_clock::time_point> m_timestamps;
};

}  // namespace utils

#endif  // DEMO_FAULT_MONITOR__UTILS__LIDAR_DATA_LISTENER_HPP_
