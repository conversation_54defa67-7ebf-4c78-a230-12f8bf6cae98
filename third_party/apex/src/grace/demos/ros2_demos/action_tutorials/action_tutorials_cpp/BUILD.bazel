load("@apex//grace/ros/rclcpp/rclcpp_components:rclcpp_component.bzl", "rclcpp_component")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "install_space")

ros_pkg(
    name = "action_tutorials_cpp_pkg",
    description = "Action tutorials action",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "action_tutorials_cpp",
    rclcpp_components = [
        ":action_tutorials",
    ],
    version = "0.14.2",
    visibility = ["//visibility:public"],
    deps = [
        "//grace/demos/ros2_demos/action_tutorials/action_tutorials_interfaces:action_tutorials_interfaces_pkg",
        "//grace/ros/rclcpp/rclcpp:rclcpp_pkg",
        "//grace/ros/rclcpp/rclcpp_action:rclcpp_action_pkg",
        "//grace/ros/rclcpp/rclcpp_components:rclcpp_components_pkg",
    ],
)

cc_library(
    name = "headers",
    hdrs = [
        "include/action_tutorials_cpp/visibility_control.h",
    ],
    strip_include_prefix = "include",
    deps = [
        # TODO(christophe.bedard) remove once rmw_ida fully supports actions
        "@apex//grace/rmw_implementation:cc_only_compatible_with_apex_middleware",
    ],
)

rclcpp_component(
    name = "action_tutorials",
    srcs = [
        "src/fibonacci_action_client.cpp",
        "src/fibonacci_action_server.cpp",
    ],
    executable_names = [
        "fibonacci_action_client",
        "fibonacci_action_server",
    ],
    plugin_names = [
        "action_tutorials_cpp::FibonacciActionClient",
        "action_tutorials_cpp::FibonacciActionServer",
    ],
    tags = ["exclude_sca"],
    visibility = ["//visibility:public"],
    deps = [
        ":headers",
        "//grace/demos/ros2_demos/action_tutorials/action_tutorials_interfaces",
        "//grace/ros/rclcpp/rclcpp",
        "//grace/ros/rclcpp/rclcpp_action",
        "//grace/ros/rclcpp/rclcpp_components:component_manager",
    ],
)

install_space(
    name = "install_space",
    ros_pkgs = [
        ":action_tutorials_cpp_pkg",
        "//grace/cli/ros2cli:ros2cli_pkg",
        "//grace/cli/ros2run:ros2run_pkg",
        "//grace/cli/ros2component:ros2component_pkg",
    ],
)
