<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>demo_nodes_cpp</name>
  <version>0.14.2</version>
  <description>
    C++ nodes which were previously in the ros2/examples repository but are now just used for demo purposes.
  </description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_auto</buildtool_depend>

  <build_depend>example_interfaces</build_depend>
  <build_depend>rclcpp</build_depend>
  <build_depend>rclcpp_components</build_depend>
  <build_depend>rcutils</build_depend>
  <build_depend>rmw</build_depend>
  <build_depend>rmw_implementation_cmake</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>settings</build_depend>

  <exec_depend>example_interfaces</exec_depend>
  <exec_depend>launch_ros</exec_depend>
  <exec_depend>launch_xml</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <exec_depend>rclcpp_components</exec_depend>
  <exec_depend>rcutils</exec_depend>
  <exec_depend>rmw</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <build_depend>settings</build_depend>

  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>launch</test_depend>
  <test_depend>launch_testing</test_depend>
  <test_depend>launch_testing_ament_cmake</test_depend>
  <test_depend>launch_testing_ros</test_depend>
  <test_depend>apex_os_minimal_pub_sub</test_depend>

  <depend>apex_cmake</depend>
  <depend>apexutils</depend>


  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
