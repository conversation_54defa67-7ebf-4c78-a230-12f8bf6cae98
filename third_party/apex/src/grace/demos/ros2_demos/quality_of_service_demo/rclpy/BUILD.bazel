load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_library")
load("@apex//common/bazel/rules_python_extra:defs.bzl", "py_entry_points_binary")
load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ENTRY_POINTS = {
    "console_scripts": [
        "deadline = quality_of_service_demo_py.deadline:main",
        "incompatible_qos = quality_of_service_demo_py.incompatible_qos:main",
        "lifespan = quality_of_service_demo_py.lifespan:main",
        "liveliness = quality_of_service_demo_py.liveliness:main",
        "message_lost_listener = quality_of_service_demo_py.message_lost_listener:main",
        "qos_overrides_listener = quality_of_service_demo_py.qos_overrides_listener:main",
        "qos_overrides_talker = quality_of_service_demo_py.qos_overrides_talker:main",
    ],
}

ros_pkg(
    name = "quality_of_service_demo_py_pkg",
    description = "Python Demo applications for Quality of Service features",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "quality_of_service_demo_py",
    py_libraries = [":quality_of_service_demo_py"],
    version = "0.14.2",
    visibility = ["//visibility:public"],
    whl_entry_points = ENTRY_POINTS,
    deps = [
        "//grace/interfaces/sensor_msgs:sensor_msgs_pkg",
        "//grace/interfaces/std_msgs:std_msgs_pkg",
        "//grace/ros/rclpy/rclpy:rclpy_pkg",
    ],
)

py_msgs_library(
    name = "std_msgs_py",
    msgs = "@apex//grace/interfaces/std_msgs",
)

py_msgs_library(
    name = "sensor_msgs_py",
    msgs = "@apex//grace/interfaces/sensor_msgs",
)

py_entry_points_library(
    name = "quality_of_service_demo_py",
    srcs = glob(["quality_of_service_demo_py/*.py"]),
    entry_points = ENTRY_POINTS,
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        ":sensor_msgs_py",
        ":std_msgs_py",
        "//grace/ros/rclpy/rclpy",
    ],
)

py_entry_points_binary(
    name = "deadline",
    py_entry_points_library = ":quality_of_service_demo_py",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "incompatible_qos",
    py_entry_points_library = ":quality_of_service_demo_py",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "lifespan",
    py_entry_points_library = ":quality_of_service_demo_py",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "liveliness",
    py_entry_points_library = ":quality_of_service_demo_py",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "message_lost_listener",
    py_entry_points_library = ":quality_of_service_demo_py",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "qos_overrides_listener",
    py_entry_points_library = ":quality_of_service_demo_py",
    visibility = ["//visibility:public"],
)

py_entry_points_binary(
    name = "qos_overrides_talker",
    py_entry_points_library = ":quality_of_service_demo_py",
    visibility = ["//visibility:public"],
)
