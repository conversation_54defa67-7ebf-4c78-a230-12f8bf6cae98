<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>demo_nodes_py</name>
  <version>0.14.2</version>
  <description>
    Python nodes which were previously in the ros2/examples repository but are now just used for demo purposes.
  </description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>
  <author><PERSON><PERSON><PERSON></author>
  <author email="<EMAIL>">E<PERSON><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <exec_depend>example_interfaces</exec_depend>
  <exec_depend>rclpy</exec_depend>
  <exec_depend>std_msgs</exec_depend>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
