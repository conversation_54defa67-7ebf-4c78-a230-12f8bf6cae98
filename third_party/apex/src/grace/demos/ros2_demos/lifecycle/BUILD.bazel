load("@apex//grace/rosidl/rules_rosidl:defs.bzl", "py_msgs_library")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/launch/launch_testing/rules:defs.bzl", "launch_test_in_install_space")
load("@rules_pkg//:mappings.bzl", "pkg_files")
load("@rules_pkg//:mappings.bzl", "strip_prefix")

ros_pkg(
    name = "lifecycle",
    description = "Package containing demos for lifecycle implementation",
    lib_executables = [
        "lifecycle_talker",
        "lifecycle_listener",
        "lifecycle_service_client",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "lifecycle",
    share_data = [":share_data"],
    version = "0.14.2",
    visibility = ["//visibility:public"],
)

cc_binary(
    name = "lifecycle_talker",
    srcs = ["src/lifecycle_talker.cpp"],
    tags = ["exclude_sca"],
    target_compatible_with = ["@platforms//os:linux"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp_lifecycle",
    ],
)

cc_binary(
    name = "lifecycle_listener",
    srcs = ["src/lifecycle_listener.cpp"],
    tags = ["exclude_sca"],
    target_compatible_with = ["@platforms//os:linux"],
    deps = [
        "//grace/interfaces/std_msgs",
        "//grace/ros/rclcpp/rclcpp_lifecycle",
    ],
)

cc_binary(
    name = "lifecycle_service_client",
    srcs = ["src/lifecycle_service_client.cpp"],
    tags = ["exclude_sca"],
    target_compatible_with = ["@platforms//os:linux"],
    deps = [
        "//grace/ros/rclcpp/rclcpp_lifecycle",
    ],
)

py_msgs_library(
    name = "lifecycle_msgs_py",
    msgs = "@apex//grace/interfaces/lifecycle_msgs",
)

launch_test_in_install_space(
    name = "test_lifecycle",
    install_space = {
        "ros_pkgs": [
            ":lifecycle",
            "@apex//grace/tools/launch_ros/launch_ros:launch_ros_pkg",
            "@apex//tools/launch/launch:launch_pkg",
            "//grace/interfaces/lifecycle_msgs:lifecycle_msgs_pkg",
        ],
    },
    launch_test_file = "test/test_lifecycle.py",
    tags = [
        "constrained_test",
    ],
    deps = [
        ":lifecycle_msgs_py",
        "@apex//grace/tools/launch_ros/launch_ros",
        "@apex//tools/apex_pytest_utils",
        "@apex//tools/launch/launch",
    ],
)

pkg_files(
    name = "share_data",
    srcs = [
        "launch/lifecycle_demo.launch.py",
    ],
    strip_prefix = strip_prefix.from_pkg(),
)
