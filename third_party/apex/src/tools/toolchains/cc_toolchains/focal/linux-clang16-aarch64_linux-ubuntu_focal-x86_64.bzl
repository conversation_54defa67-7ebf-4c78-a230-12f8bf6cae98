load(":wrapper/linux_ubuntu_wrapper.bzl", _WRAPPER_SCRIPT = "wrapper_script")

_SYSROOT_PACKAGE = {
    "package": "cc_toolchain_linux",
    "version": "2024.5.3",
    "filename": "cc_toolchain_linux_sysroot-linux_ubuntu_focal-x86_64-2024.5.3.tar.xz",
    "sha256": "70c7d14123d475ec369077a0933a9edfaacad4b69630cd0e0b357b9b64337eb4",
}

_SYSROOT_CONFIG = {
    "include_dirs": [
        "/usr/include",
        "/usr/include/x86_64-linux-gnu",
    ],
    "link_dirs": [
        "/usr/lib/x86_64-linux-gnu",
        "/lib/x86_64-linux-gnu",
    ],
}

_TOOLCHAIN_PACKAGE = {
    "package": "cc_toolchain_linux",
    "version": "2024.5.3",
    "filename": "cc_toolchain_linux_toolchain-linux-clang16-aarch64-linux_ubuntu_focal-x86_64-2024.5.3.tar.xz",
    "sha256": "eeea7fc67f619c2e032f0f521fef71b0054cd55966064d3633b135957d2e9142",
}

_TOOLCHAIN_CONFIG = {
    "include_dirs": [
        "/usr/include/c++/9",
        "/usr/x86_64-linux-gnu/include/c++/9/x86_64-linux-gnu",
        "/usr/lib/llvm-16/include",
        "/usr/lib/llvm-16/lib/clang/16/include",
        "/usr/lib/llvm-16/lib/clang/16/share",
    ],
    "link_dirs": [
    ],
    "tool_paths": {
        "cpp": "/usr/bin/clang-cpp-16",
        "gcc": "/usr/bin/clang-16",
        "gcov": "/usr/bin/llvm-profdata-16",
        "ar": "/usr/bin/llvm-ar-16",
        "nm": "/usr/bin/llvm-nm-16",
        "ld": "/usr/bin/llvm-link-16",
        "objdump": "/usr/bin/llvm-objdump-16",
        "objcopy": "/usr/bin/llvm-objcopy-16",
        "strip": "/usr/bin/llvm-strip-16",
        "llvm-cov": "/usr/bin/llvm-cov-16",
        "dwp": "/usr/bin/llvm-dwp-16",
        "llvm-profdata": "/usr/bin/llvm-profdata-16",
        "tidy": "/usr/bin/clang-tidy-16",
        "format": "/usr/bin/clang-format-16",
    },
}

CC_TOOLCHAIN = {
    "compiler": "@bazel_tools//tools/cpp:clang",
    "compiler_version": "@apex//common/platforms/cc_compiler_version:clang16",
    "target_os": "@platforms//os:linux",
    "target_dist": "@apex//common/platforms/os_distro:ubuntu_focal",
    "target_arch": "@platforms//cpu:x86_64",
    "execution_os": "@platforms//os:linux",
    "execution_dist": "@apex//common/platforms/os_distro:ubuntu_focal",
    "execution_arch": "@platforms//cpu:aarch64",
    "cxx_standard": "c++17",
    "target_libc": "glibc-2.31",
    "abi_version": "unknown",
    "abi_libc_version": "unknown",
    "sysroot": _SYSROOT_PACKAGE | _SYSROOT_CONFIG,
    "toolchain": _TOOLCHAIN_PACKAGE | _TOOLCHAIN_CONFIG,
    "wrapper": _WRAPPER_SCRIPT,
    "dependencies": [],
    "internal": {
        "compile_flags": [
            "-nostdinc",
            "-target",
            "x86_64-linux-gnu",
        ],
        "dbg_compile_flags": [
        ],
        "opt_compile_flags": [
        ],
        "coverage_compile_flags": [
            "-fprofile-instr-generate",
            "-fcoverage-mapping",
        ],
        "coverage_link_flags": [
            "-fprofile-instr-generate",
            "-fcoverage-mapping",
        ],
        "link_libs": [
            "-lstdc++",
            "-lstdc++fs",
        ],
        "link_flags": [
            "-target",
            "x86_64-linux-gnu",
            "-stdlib=libstdc++",
            "-fuse-ld=lld-16",
            "-Wl,-no-as-needed",
            "-Wl,-z,relro,-z,now",
            "-ldl",
        ],
        "opt_link_flags": [
        ],
        "cxx_flags": [
            "-nostdinc++",
        ],
    },
    "fallback": {
        "compile_flags": [
            "-U_FORTIFY_SOURCE",
            "-fstack-protector",
            "-Wall",
            "-fno-omit-frame-pointer",
            "-fcolor-diagnostics",
        ],
        "dbg_compile_flags": [
            "-g",
            "-fstandalone-debug",
        ],
        "opt_compile_flags": [
            "-g0",
            "-O2",
            "-DNDEBUG",
        ],
        "link_libs": [
            "-latomic",
            "-lm",
        ],
    },
}
