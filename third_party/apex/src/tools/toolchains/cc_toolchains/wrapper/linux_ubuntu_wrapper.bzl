wrapper_script = \
    """\
#!/bin/bash

if [ $(pwd) == "/proc/self/cwd" ]; then
    cd $(readlink /proc/self/cwd)
fi

this_scripts_path=$(dirname "$0")
parent_dir=${this_scripts_path%/*}
full_tool_path="${parent_dir}/{{toolchain_prefix}}{{tool_path}}"

# When this wrapper is used as a native binary this scripts path
# should not be used as the parent_dir since the toolchain files
# are then runfiles of this script, kept in a different location
if [ ! -f "${full_tool_path}" ]; then
    # Fall back to use the current PWD instead
    parent_dir=$(pwd)
    full_tool_path="${parent_dir}/external/{{toolchain_prefix}}{{tool_path}}"
fi

export LD_PRELOAD=/lib/{{execution_arch}}-linux-gnu/libc.so.6:/lib/{{execution_arch}}-linux-gnu/libpthread.so.0:/lib/{{execution_arch}}-linux-gnu/librt.so.1:/lib/{{execution_arch}}-linux-gnu/libbsd.so.0
export LD_LIBRARY_PATH=${parent_dir}/{{toolchain_prefix}}/lib/{{execution_arch}}-linux-gnu:${parent_dir}/{{toolchain_prefix}}/usr/lib/{{execution_arch}}-linux-gnu
export PATH=${parent_dir}/{{toolchain_prefix}}/usr/bin

tmp_log=$(/usr/bin/mktemp /tmp/gcc_wrapper_error_XXXX.log)

# Make sure to remove the temporary log file on exit
cleanup() {
    /usr/bin/rm -f "$tmp_log"
}
trap cleanup EXIT

# Compile and redirect stderr to the temporary log file
"$full_tool_path" "$@" 2> "$tmp_log"
exit_code=$?

if [ $exit_code -ne 0 ]; then
    echo "[gcc_wrapper] Compilation failed (exit $exit_code)" >&2
    echo "[gcc_wrapper] Error output:" >&2
    echo "[gcc_wrapper START ERROR] ######################################" >&2
    /usr/bin/cat "$tmp_log" >&2
    echo "[gcc_wrapper END ERROR] ########################################" >&2
fi

exit $exit_code
"""
