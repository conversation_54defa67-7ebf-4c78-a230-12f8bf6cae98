load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

COM_GOOGLE_PROTOBUF_VERSION = "3.19.6"
COM_GOOGLE_PROTOBUF_SHA256 = "9a301cf94a8ddcb380b901e7aac852780b826595075577bb967004050c835056"

def load_com_google_protobuf_repositories():
    maybe(
        name = "com_google_protobuf",
        repo_rule = http_archive,
        sha256 = COM_GOOGLE_PROTOBUF_SHA256,
        strip_prefix = "protobuf-{version}".format(version = COM_GOOGLE_PROTOBUF_VERSION),
        urls = [
            "https://github.com/protocolbuffers/protobuf/archive/v{version}.tar.gz".format(version = COM_GOOGLE_PROTOBUF_VERSION),
        ],
        patches = ["@apex//tools/thirdparty/com_google_protobuf_vendor:protobuf.BUILD.patch"],
        patch_args = ["-p1"],
    )
