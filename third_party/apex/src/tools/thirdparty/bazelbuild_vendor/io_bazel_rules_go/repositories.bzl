load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

IO_BAZEL_RULES_GO_VERSION = "0.44.0"

def load_io_bazel_rules_go_repositories():
    maybe(
        name = "io_bazel_rules_go",
        repo_rule = http_archive,
        sha256 = "c8035e8ae248b56040a65ad3f0b7434712e2037e5dfdcebfe97576e620422709",
        urls = [
            "https://mirror.bazel.build/github.com/bazelbuild/rules_go/releases/download/v{version}/rules_go-v{version}.zip".format(version = IO_BAZEL_RULES_GO_VERSION),
            "https://github.com/bazelbuild/rules_go/releases/download/v{version}/rules_go-v{version}.zip".format(version = IO_BAZEL_RULES_GO_VERSION),
        ],
    )
