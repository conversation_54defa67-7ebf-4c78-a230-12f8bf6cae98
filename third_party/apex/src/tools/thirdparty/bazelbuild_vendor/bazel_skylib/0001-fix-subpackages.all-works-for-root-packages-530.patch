From ea8d02875bb945f7305c29974c76a7aa3ce08fca Mon Sep 17 00:00:00 2001
From: "kilian.funk" <<EMAIL>>
Date: Fri, 12 Jul 2024 09:14:02 -0700
Subject: [PATCH] fix: subpackages.all works for root packages (#530)

---
 lib/subpackages.bzl | 5 ++++-
 1 file changed, 4 insertions(+), 1 deletion(-)

diff --git a/lib/subpackages.bzl b/lib/subpackages.bzl
index 5b674fd..1d006e5 100644
--- a/lib/subpackages.bzl
+++ b/lib/subpackages.bzl
@@ -62,7 +62,10 @@ def _all(exclude = [], allow_empty = False, fully_qualified = True):
     return subs
 
 def _fully_qualified(relative_path):
-    return "//%s/%s" % (native.package_name(), relative_path)
+    package_name = native.package_name()
+    if package_name:
+        return "//%s/%s" % (package_name, relative_path)
+    return "//" + relative_path
 
 def _exists(relative_path):
     """Checks to see if relative_path is a direct subpackage of the current package.
-- 
2.34.1

