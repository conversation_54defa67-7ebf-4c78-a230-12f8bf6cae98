load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

VERSION = "1.7.1"

def load_bazel_skylib_repositories():
    maybe(
        name = "bazel_skylib",
        repo_rule = http_archive,
        urls = [
            "https://mirror.bazel.build/github.com/bazelbuild/bazel-skylib/releases/download/{version}/bazel-skylib-{version}.tar.gz".format(version = VERSION),
            "https://github.com/bazelbuild/bazel-skylib/releases/download/{version}/bazel-skylib-{version}.tar.gz".format(version = VERSION),
        ],
        patches = ["@apex//tools/thirdparty/bazelbuild_vendor/bazel_skylib:0001-fix-subpackages.all-works-for-root-packages-530.patch"],  # Todo Remove after https://github.com/bazelbuild/bazel-skylib/pull/531 was released (#30175)
        patch_args = ["-p1"],
        sha256 = "bc283cdfcd526a52c3201279cda4bc298652efa898b10b4db0837dc51652756f",
    )
