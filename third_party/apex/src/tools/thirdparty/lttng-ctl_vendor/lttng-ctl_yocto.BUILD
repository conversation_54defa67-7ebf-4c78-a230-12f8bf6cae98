cc_import(
    name = "lttng-ctl_import",
    interface_library = "@yocto_sysroot//:usr/lib/liblttng-ctl.so",
    shared_library = "@yocto_sysroot//:usr/lib/liblttng-ctl.so.0",
    tags = ["same-ros-pkg-as: lttng-ctl"],
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

cc_library(
    name = "lttng-ctl_internal",
    target_compatible_with = select({
        "@apex//common/platforms/os_distro:poky_dunfell": [],
        "@apex//common/platforms/os_distro:poky_kirkstone": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
    visibility = ["//visibility:public"],
    deps = [
        ":lttng-ctl_import",
    ],
)
