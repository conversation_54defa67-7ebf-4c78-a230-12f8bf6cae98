alias(
    name = "tinyxml2",
    actual = select({
        "@apex//common/platforms/apex_detail:yocto-dunfell-aarch64": "@tinyxml2-yocto-dunfell-aarch64//:tinyxml2",
        "@apex//common/platforms/apex_detail:yocto-kirkstone-aarch64": "@tinyxml2-yocto-kirkstone-aarch64//:tinyxml2",
        "//conditions:default": "@tinyxml2-src//:tinyxml2",
    }),
    visibility = ["//visibility:public"],
)

alias(
    name = "tinyxml2_pkg",
    actual = select({
        "@apex//common/platforms/apex_detail:yocto-dunfell-aarch64": "@tinyxml2-yocto-dunfell-aarch64//:tinyxml2_pkg",
        "@apex//common/platforms/apex_detail:yocto-kirkstone-aarch64": "@tinyxml2-yocto-kirkstone-aarch64//:tinyxml2_pkg",
        "//conditions:default": "@tinyxml2-src//:tinyxml2_pkg",
    }),
    visibility = ["//visibility:public"],
)
