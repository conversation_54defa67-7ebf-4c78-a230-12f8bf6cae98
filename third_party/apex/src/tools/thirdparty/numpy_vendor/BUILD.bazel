load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load(":symlinked_numpy_header_files.bzl", "symlinked_numpy_header_files")

cc_library(
    name = "numpy_headers",
    hdrs = symlinked_numpy_header_files(),
    strip_include_prefix = ".",
    target_compatible_with = ["@platforms//os:linux"],
    visibility = ["//visibility:public"],
    deps = [
        "@python_interpreter//:libpython",
        "@python_interpreter//:python_headers",
    ],
)

ros_pkg(
    name = "numpy_vendor_pkg",
    cc_libraries = [":numpy_headers"],
    description = "Numpy headers.",
    license = "BSD 3-Clause License",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "numpy_vendor",
    version = "1.0.0",
    visibility = ["//visibility:public"],
    deps = [],
)
