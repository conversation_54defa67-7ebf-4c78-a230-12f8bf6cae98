# Copyright 2024 Apex.AI, Inc.
# All rights reserved.

load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@apex//tools/thirdparty/graphviz_vendor:defs.bzl", "GRAPHVIZ_BINS")
load("@bazel_skylib//rules:native_binary.bzl", "native_binary")

[
    native_binary(
        name = bin,
        src = select({
            "@platforms//cpu:x86_64": "@graphviz-linux-x86_64//:{exec}".format(exec = bin),
            "@platforms//cpu:aarch64": "@graphviz-linux-aarch64//:{exec}".format(exec = bin),
            "//conditions:default": "@platforms//:incompatible",
        }),
        out = bin,
        visibility = ["//visibility:public"],
    )
    for bin in GRAPHVIZ_BINS
]

ros_pkg(
    name = "graphviz_pkg",
    bst_substitute_with = {
        "name": "graphviz",
        "cmake_find_package": """
# Graphviz is not linked, it only contains executables
""",
    },
    description = "Graph Visualization Tools",
    license = "EPL-1.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI, Inc.",
    pkg_name = "graphviz_vendor",
    version = "12.1.0",
    visibility = ["//visibility:public"],
)
