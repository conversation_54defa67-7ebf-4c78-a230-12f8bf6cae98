# Copyright 2017 - 2022 Apex.AI, Inc.
# All rights reserved.
#
# This file contains modified code from the following open source projects
# published under the licenses listed below:
#
# Copyright 2008, Google Inc.
# All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#
#     * Redistributions of source code must retain the above copyright
# notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
# copyright notice, this list of conditions and the following disclaimer
# in the documentation and/or other materials provided with the
# distribution.
#     * Neither the name of Google Inc. nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
# "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
# LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
# A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
# OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
# SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
# LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
# DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
# THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

cmake_minimum_required(VERSION 3.5)

if(BUILD_FROM_GOOGLETEST_DISTRIBUTION)
  # Build from googletest-distribution package
  include(CMakeLists.txt.upstream)
else()
  project(gtest_vendor NONE)
  install(
    FILES ${PROJECT_NAME}Config.cmake
    DESTINATION share/${PROJECT_NAME}/cmake
  )
  install(
    FILES package.xml
    DESTINATION share/${PROJECT_NAME}
  )
  install(
    FILES CMakeLists.txt.install
    DESTINATION src/${PROJECT_NAME}
    RENAME CMakeLists.txt
  )
  install(
    DIRECTORY include/gtest
    DESTINATION src/${PROJECT_NAME}/include
  )
  install(
    DIRECTORY src
    DESTINATION src/${PROJECT_NAME}
  )
endif()
