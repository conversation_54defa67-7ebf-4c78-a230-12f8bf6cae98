load(
    "@bazel_tools//tools/build_defs/repo:utils.bzl",
    "update_attrs",
    "workspace_and_buildfile",
)
load(":detail/defs.bzl", "EMPTY_REPOSITORY_ATTRS")
load(":detail/utils.bzl", "build_file_substitutions_to_patch_cmds", _file_exists = "file_exists")

def _apply_patch_cmds(ctx, patch_cmds):
    for patch_cmd in patch_cmds:
        exec_result = ctx.execute(["bash", "-c", patch_cmd])
        if exec_result.return_code != 0:
            fail("Failed to run patch command ({patch_cmd}) ({return_code}): {stderr} {stdout}".format(
                patch_cmd = patch_cmd,
                return_code = exec_result.return_code,
                stderr = exec_result.stderr,
                stdout = exec_result.stdout,
            ))

def empty_repository(**kwargs):
    """Equivalent to `configurable_repository` rule."""
    return configurable_repository(**kwargs)

_configurable_repository_attrs = EMPTY_REPOSITORY_ATTRS | {
    "configurable_files_content": attr.string_dict(
        doc =
            "Dictionary of files to create with content strings",
    ),
    "configurable_files": attr.string_keyed_label_dict(
        doc =
            "Dictionary of target locations and files to be placed in the new repository.",
    ),
}

def _configurable_repository_impl(ctx):
    """Implementation of the _archive rule."""

    workspace_and_buildfile(ctx)
    if not _file_exists(ctx, "WORKSPACE"):
        ctx.symlink("WORKSPACE", "WORKSPACE.bazel")

    patch_cmds = build_file_substitutions_to_patch_cmds(ctx.attr.build_file_substitutions)
    _apply_patch_cmds(ctx, patch_cmds)

    for name, content in ctx.attr.configurable_files_content.items():
        ctx.file(name, content)

    for name, file in ctx.attr.configurable_files.items():
        content = ctx.read(file)
        ctx.file(name, content)

    return update_attrs(ctx.attr, _configurable_repository_attrs.keys(), {})

_configurable_repository = repository_rule(
    implementation = _configurable_repository_impl,
    attrs = _configurable_repository_attrs,
    local = True,
    doc =
        """Creates an repository populated with a WORKSPACE and BUILD file.
        Additional files can be specified with `configurable_files_content` and `configurable_files`.
        The given BUILD file can be used as a template and will be patched with the given substitutions.

        Example:

        ```python
        configurable_repository(
              name = "my_repository",
            build_file = "//my_repository:BUILD.bazel",
            build_file_substitutions = {
                "{{MY_VAR}}": "my_value", # Replace `{{MY_VAR}}` with `my_value` in the BUILD file
            },
            configurable_files_content = {
                "my_file.txt": "Hello, World!",
            },
            configurable_files = {
                "subfolder/my_second_file.txt": "//my_repository:my_file.txt",
            },
        )
        ```
        """,
)

# #25095 Todo: Remove this facade after bumping to newer Stardoc version, allowing for `string_keyed_label_dict` attributes.
def configurable_repository(
        *,
        name,
        build_file = None,
        build_file_substitutions = {},
        configurable_files_content = {},
        configurable_files = {},
        **kwargs):
    """Creates a repository populated with a WORKSPACE and BUILD file.

    Additional files can be specified with `configurable_files_content` and `configurable_files`.
    The given BUILD file can be used as a template and will be patched with the given substitutions.

    Example:

    ```python
    configurable_repository(
        name = "my_repository",
        build_file = "//my_repository:BUILD.bazel",
        build_file_substitutions = {
            "{{MY_VAR}}": "my_value", # Replace `{{MY_VAR}}` with `my_value` in the BUILD file
        },
        configurable_files_content = {
            "my_file.txt": "Hello, World!",
        },
        configurable_files = {
            "subfolder/my_second_file.txt": "//my_repository:my_file.txt",
        },
    )
    ```
    """
    inv_configurable_files = {v: k for k, v in configurable_files.items()}
    if len(configurable_files) != len(inv_configurable_files):
        fail("Duplicate target locations in `configurable_files`")

    return _configurable_repository(
        name = name,
        build_file = build_file,
        build_file_substitutions = build_file_substitutions,
        configurable_files_content = configurable_files_content,
        configurable_files = inv_configurable_files,
        **kwargs
    )
