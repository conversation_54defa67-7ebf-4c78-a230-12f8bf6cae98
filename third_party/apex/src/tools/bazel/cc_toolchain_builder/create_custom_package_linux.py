#!/usr/bin/env python3

# Copyright 2023 Apex.AI, Inc.
# All rights reserved.

import logging
import os
import shutil

import click

from bazel_toolchain_pack import get_output_directory, get_package_version
from bazel_toolchain_pack.debian_packager import (
    create_app_package,
    resolve_sysroot,
    ubuntu_dist_code,
)
from bazel_toolchain_pack.make_package import make_package

logging.basicConfig(level=logging.INFO)

_PACKAGE_SETS = {
    "qt5": [
        "qtbase5-dev",
        "qtbase5-dev-tools",
        "qtdeclarative5-dev",
        "qtdeclarative5-dev-tools",
        "qml",
        "qtchooser",
        "libgl-dev",
    ],
    "ogre": [
        "libogre-1.12-dev",
    ],
    "gstreamer": [
        "libgstreamer1.0-dev",
        "libgstreamer-plugins-base1.0-dev",
    ],
    "opencv": [
        "libopencv-dev",
    ],
    "babeltrace": [
        "libbabeltrace-dev",
        "python3-babeltrace",
    ],
    "lttng-ust": [
        "liblttng-ust-dev",
    ],
    "lttng-ctl": [
        "liblttng-ctl-dev",
    ],
    "libpcap": [
        "libpcap-dev",
    ],
    "graphviz": [
        "graphviz",
    ],
}

_PACKAGE_EXCLUDE_PATTERNS = {
    "lttng-ust": [
        "*liblttng-ust-ctl*",
        "*lttng-gen-tp*",
        "ust-ctl.h",
    ],
}


@click.command()
@click.option(
    "--cache-directory",
    default=os.path.join(
        os.path.expanduser("~/.cache"), os.path.splitext(os.path.basename(__file__))[0]
    ),
    help="Set directory to be used for caching.",
    metavar="<string>",
)
@click.option(
    "--rebuild-package/--no-rebuild-package",
    default=True,
    help="Force rebuild of package. If not forced a cached build will be reused.",
    metavar="<bool>",
)
@click.option(
    "--package-create/--no-package-create",
    default=True,
    help="Create package for toolchain",
    metavar="<bool>",
)
@click.option(
    "--package-version",
    help="Set package version to be used for creating this package (If unset version will derive from date)",
    default=None,
    metavar="<string>",
)
@click.option(
    "--package-output-directory",
    help="Set package output directory (If unset package directory inside package cache will be used)",
    default=None,
    metavar="<string>",
)
@click.option(
    "--package-name",
    help='Package name prefix (default to "cc_toolchain_linux"',
    default=None,
    required=True,
    metavar="<string>",
)
@click.option(
    "--package-format",
    help="Package format to use (available format are tar, tar.gz, tar.bz2, tar.xz)",
    default="tar.xz",
    metavar="<string>",
)
@click.option(
    "--ubuntu-version",
    help="Set ubuntu version to use",
    default="20.04",
    metavar="<string>",
)
@click.option(
    "--target-architecture",
    "target_architectures",
    help="Prepare compiler toolchain for selected target architectures",
    default=["aarch64", "x86_64"],
    multiple=True,
    metavar="<list>",
)
def create_custom_package_linux(**kwargs):
    ubuntu_version = kwargs.get("ubuntu_version")

    target_archs = kwargs.pop("target_architectures")

    rebuild_package = kwargs.pop("rebuild_package")
    package_create = kwargs.pop("package_create")

    cache_directory = kwargs.get("cache_directory")
    package_name = kwargs.get("package_name")

    dist_name = "ubuntu"
    dist_code = ubuntu_dist_code(ubuntu_version)
    dist_version = ubuntu_version

    installation_root = os.path.join(cache_directory, "install", package_name)
    if rebuild_package and os.path.isdir(installation_root):
        shutil.rmtree(installation_root)

    package_output_directory = kwargs.get("package_output_directory")
    output_directory = get_output_directory(cache_directory, package_output_directory)
    package_name = kwargs.get("package_name")
    package_version = get_package_version(kwargs.get("package_version"))
    package_format = kwargs.get("package_format")

    for target_arch in target_archs:
        installation_root_arch = os.path.join(installation_root, target_arch)

        if not os.path.exists(installation_root_arch):
            os.makedirs(installation_root_arch, exist_ok=True)
            stop_packages = resolve_sysroot(
                cache_directory=cache_directory,
                installation_dir=installation_root_arch,
                target_arch=target_arch,
                dist_name=dist_name,
                dist_version=dist_version,
                packages=[
                    "libc6-dev",
                    "libattr1-dev",
                    "libacl1-dev",
                    "libncurses5-dev" if dist_version == "18.04" else "libncurses-dev",
                    "libssl-dev",
                    "zlib1g-dev",
                ],
                custom_args=[],
            )
            create_app_package(
                cache_directory=cache_directory,
                installation_dir=installation_root_arch,
                target_arch=target_arch,
                dist_name=dist_name,
                dist_version=dist_version,
                packages=_PACKAGE_SETS[package_name],
                stop_pkg_names=stop_packages,
            )

            x11_dir = os.path.join(installation_root_arch, "usr", "bin", "X11")
            if os.path.islink(x11_dir):
                os.remove(x11_dir)

        if package_create:
            make_package(
                create_package=package_create,
                cache_directory=cache_directory,
                output_directory=output_directory,
                installation_dir=installation_root_arch,
                asset_name=package_name
                + "-"
                + dist_name
                + "-"
                + dist_code
                + "-"
                + target_arch,
                package_name=package_name,
                package_version=package_version,
                package_format=package_format,
                exclude_patterns=_PACKAGE_EXCLUDE_PATTERNS.get(package_name, []),
            )


if __name__ == "__main__":
    create_custom_package_linux()
