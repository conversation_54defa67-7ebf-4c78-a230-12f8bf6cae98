load("@rules_cc//cc:defs.bzl", "cc_binary")
load(":detail/moc_compile.bzl", "moc_files")
load(":detail/rcc_compile.bzl", "resource_files")

def qt5_cc_binary(*, name, srcs = [], hdrs = [], moc_hdrs = [], resources = {}, **kwargs):
    moc_srcs = moc_files(moc_hdrs)
    rcc_srcs = resource_files(resources)
    cc_binary(
        name = name,
        srcs = srcs + moc_srcs + moc_hdrs + rcc_srcs,
        **kwargs
    )
