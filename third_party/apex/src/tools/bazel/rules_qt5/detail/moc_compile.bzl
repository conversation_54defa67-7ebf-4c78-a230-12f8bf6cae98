def _moc_compile_impl(ctx):
    """Implementation of bazel function to call qt5 moc

    Args:
        ctx: Bazel Context
    """
    args = ctx.actions.args()
    args.add(ctx.file.src.path)
    args.add("-o")
    base_filename = ctx.file.src.basename.removesuffix(".hpp").removesuffix(".h").removesuffix(".H")
    out = ctx.actions.declare_file("{}_moc.cpp".format(base_filename))
    args.add(out.path)

    ctx.actions.run(
        inputs = [ctx.file.src],
        outputs = [out],
        arguments = [args],
        executable = ctx.executable._moc_exe,
    )

    out_depset = depset([out])
    return [DefaultInfo(files = out_depset)]

moc_compile = rule(
    implementation = _moc_compile_impl,
    attrs = {
        "src": attr.label(
            doc = "Input (source) file",
            allow_single_file = True,
            mandatory = True,
        ),
        "_moc_exe": attr.label(
            executable = True,
            cfg = "exec",
            default = "@qt5//:moc_exe",
        ),
    },
    provides = [DefaultInfo],
)

def moc_files(header_files):
    results = []
    for file_name in header_files:
        target_name = "_{}_moc".format(file_name.replace(".", "_"))
        moc_compile(
            name = target_name,
            src = file_name,
        )
        results.append(":" + target_name)
    return results
