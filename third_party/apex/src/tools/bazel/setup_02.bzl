load("@apex//tools/thirdparty/bazelbuild_vendor/aspect_bazel_lib:setup.bzl", "setup_aspect_bazel_lib_repositories")
load("@apex//tools/thirdparty/bazelbuild_vendor/bazel_gazelle:setup.bzl", "setup_bazel_gazelle_repositories")
load("@apex//tools/thirdparty/bazelbuild_vendor/bazel_skylib:setup.bzl", "setup_bazel_skylib_repositories")
load("@apex//tools/thirdparty/bazelbuild_vendor/io_bazel_rules_go:setup.bzl", "setup_io_bazel_rules_go_repositories")
load("@apex//tools/thirdparty/bazelbuild_vendor/rules_foreign_cc:setup.bzl", "setup_rules_foreign_cc_repositories")
load("@apex//tools/thirdparty/bazelbuild_vendor/rules_pkg:setup.bzl", "setup_rules_pkg_repositories")
load("@apex//tools/thirdparty/bazelbuild_vendor/rules_python:setup.bzl", "setup_rules_python_repositories")
load("@apex//tools/thirdparty/com_google_protobuf_vendor:setup.bzl", "setup_com_google_protobuf_repositories")
load("@apex//tools/toolchains/cc_toolchain_loader:setup.bzl", "setup_cc_toolchain_loader_repositories")

def setup_02(**kwargs):
    setup_com_google_protobuf_repositories()
    setup_io_bazel_rules_go_repositories()
    setup_bazel_gazelle_repositories()
    setup_rules_python_repositories()
    setup_aspect_bazel_lib_repositories()
    setup_bazel_skylib_repositories()
    setup_rules_pkg_repositories()
    setup_rules_foreign_cc_repositories()
    setup_cc_toolchain_loader_repositories(**kwargs)
