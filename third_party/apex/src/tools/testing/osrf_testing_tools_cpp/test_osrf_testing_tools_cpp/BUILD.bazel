load("@apex//tools/bazel/rules_cc:defs.bzl", "apex_cc_test")

#! [osrf_memory_tools_usage_example]

apex_cc_test(
    name = "test_example_memory_tools_gtest",
    srcs = [
        "test/test_example_memory_tools.cpp",
    ],
    linkopts = select({
        "@platforms//os:linux": ["-lpthread"],
        "//conditions:default": [],
    }),
    tags = [
        "exclude_sca",
        "skip_asan",  # FIXME: fix asan findings and remove this tag
        "skip_tsan",  # FIXME: fix tsan findings and remove this tag
        "skip_ubsan",  # FIXME: fix ubsan findings and remove this tag
    ],
    deps = [
        "//tools/testing/osrf_testing_tools_cpp/osrf_testing_tools_cpp:memory_tools",
        "@googletest//:gtest_main",
    ],
)
#! [osrf_memory_tools_usage_example]

apex_cc_test(
    name = "test_is_not_working_gtest",
    srcs = [
        "test/test_is_not_working.cpp",
    ],
    local_defines = ["BAZEL=1"],
    tags = ["exclude_sca"],
    deps = [
        "//tools/testing/apex_test_tools",
        "//tools/testing/osrf_testing_tools_cpp/osrf_testing_tools_cpp:memory_tools",
        "@googletest//:gtest_main",
    ],
)

filegroup(
    name = "doc_files",
    srcs = ["BUILD.bazel"],
    visibility = ["//grace/doc/tutorials/environment_configuration_and_building/building_with_bazel:__pkg__"],
)
