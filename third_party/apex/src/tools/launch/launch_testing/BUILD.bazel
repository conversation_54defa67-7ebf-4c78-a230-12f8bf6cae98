load("@apex//common/bazel/rules_pytest:defs.bzl", "apex_py_test")
load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")
load("@python_vendor//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary", "py_library")

ros_pkg(
    name = "launch_testing_pkg",
    description = "A package to create tests which involve launch files and multiple processes.",
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    pkg_name = "launch_testing",
    py_libraries = [":launch_testing"],
    version = "0.17.0",
    visibility = ["//visibility:public"],
    whl_classifiers = [
        "Intended Audience :: Developers",
        "License :: OSI Approved :: Apache Software License",
        "Programming Language :: Python",
        "Topic :: Software Development",
    ],
    whl_entry_points = {
        "console_scripts": ["launch_test=launch_testing.launch_test:main"],
        "pytest11": ["launch = launch_testing.pytest.hooks"],
    },
    deps = [
        "//tools/ament/osrf_pycommon:osrf_pycommon_pkg",
        "//tools/launch/launch:launch_pkg",
    ],
)

py_library(
    name = "launch_testing",
    srcs = glob(
        ["launch_testing/**/*.py"],
        exclude = [
            "launch_testing/legacy/*.py",  # needs full install space (with ament_index_python)
        ],
    ),
    data = [":launch_testing_pkg.wheel_data"],
    imports = ["."],
    visibility = ["//visibility:public"],
    deps = [
        "//tools/ament/osrf_pycommon",
        "//tools/launch/launch",
        requirement("pytest"),
    ] + select({
        # this is the "default" BST value, so when BST is not enabled
        "//common/build_system_transfer:invalid": ["@rules_python//python/runfiles"],
        "//conditions:default": [],
    }),
)

# This is not generally that useful, because the launched test file can not
# import any other packages (except those that launch_testing itself happens
# to depend on). Therefore it's a private target that is only used by this
# package's own unit tests. Use the launch_test or launch_test_in_install_space
# macros for tests.
py_binary(
    name = "launch_test",
    srcs = ["@apex//tools/launch/launch_testing/rules:launch_test_main.py"],
    main = "@apex//tools/launch/launch_testing/rules:launch_test_main.py",
    deps = [
        ":launch_testing",
        "@apex//grace/tools/ros_domain_coordinator:ros_domain_coordinator_py",
    ],
)

apex_py_test(
    name = "test_launch_testing",
    srcs = glob(
        [
            "test/**/*.py",
            "test/conftest.py",
        ],
        exclude = [
            "test/launch_testing/test_flake8.py",
            "test/launch_testing/test_pep257.py",
            "test/launch_testing/test_copyright.py",
            "test/launch_testing/examples/*.py",  # example tests invoked from unit tests
            "test/launch_testing/legacy/*.py",  # see above
        ],
    ),
    args = [
        "-c $(rootpath :pytest_bazel.ini)",
    ],
    data = glob(
        [
            "example_processes/*",
            "test/launch_testing/examples/*",
        ],
    ) + [
        ":launch_test",
        ":pytest_bazel.ini",
    ],
    tags = [
        "constrained_test",
        "skip_coverage",
    ],
    deps = [
        ":launch_testing",
    ],
)

filegroup(
    name = "doc_files",
    srcs = [
        "launch_testing/launch_test.py",
    ],
)
