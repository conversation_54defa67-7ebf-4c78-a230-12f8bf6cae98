load("@apex//common/bazel/aspects/rule_meta_aspect:providers.bzl", "RuleMetaInfo")
load(":detail/resource_types.bzl", "handle_resource")

def _ament_pkg_resources_impl(ctx):
    """
    Args:
        name: The name of the target
        package: The name of the package to associate the resources with
        resources: The resources to declare/symlink into the ament prefix directory
        **kwargs: Additional arguments to be passed along
    """

    # Declare this package in the ament index
    resources = handle_resource(
        ctx,
        resource_type = "package",
        package = ctx.attr.package,
        resource = ctx.attr.name,
    )

    # Add any additional resources to the index
    runfiles = []
    for target, resource_types in ctx.attr.resources.items():
        for resource_type in resource_types.split(","):
            resources += handle_resource(
                ctx,
                resource_type = resource_type,
                package = ctx.attr.package,
                resource = target,
            )

        # Also add any runfiles if they are available from the target
        runfiles.append(target[DefaultInfo].data_runfiles)

    for target in ctx.attr.data:
        runfiles.append(target[DefaultInfo].data_runfiles)
    runfiles.append(ctx.runfiles(files = ctx.files.data))

    for resource in resources:
        runfiles += resource.runfiles

    return [
        DefaultInfo(
            files = depset([resource.file for resource in resources]),
            runfiles = ctx.runfiles(
                root_symlinks = {
                    resource.path: resource.file
                    for resource in resources
                },
            ).merge_all(runfiles),
        ),
        RuleMetaInfo(),
    ]

ament_pkg_resources = rule(
    implementation = _ament_pkg_resources_impl,
    doc = """Declare runtime resources provided through the ament index.

Ament index offers a runtime resource lookup index. The purpose of this rule is
to merge the runtime requirements of the ament index with that of Bazel runfiles
- symlinking the declared resources to the proper location so that the ament_index
search methods and other tools can find them.

The following types of resources are implemented:

  * `executable`: executable target will be added to `lib/<package>`
  * `lib`: cc_shared_library or apex_cc_library will be added to `lib/<package>`
  * `share`: resource will be added to `share/<package>`

    """,
    attrs = {
        "package": attr.string(
            mandatory = True,
            doc = "The name of the package to associate all below resources with",
        ),
        "resources": attr.label_keyed_string_dict(
            mandatory = True,
            allow_files = True,
            allow_empty = True,
            doc = """The resources to declare/symlink into the ament prefix directory.

                     *keys*: Resources to be added to the runfiles index (file or target).
                     *values*: Type of ament resources. The type determines how the resource will
                       be presented in the runfiles.
            """,
        ),
        "data": attr.label_list(
            doc = "Additional runtime dependencies",
            mandatory = False,
            allow_files = True,
            allow_empty = True,
        ),
    },
)
