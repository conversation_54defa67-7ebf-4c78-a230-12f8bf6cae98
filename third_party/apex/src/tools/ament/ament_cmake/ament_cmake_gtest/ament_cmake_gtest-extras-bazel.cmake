macro(_ament_cmake_gtest_find_gtest)
  find_package(googletest QUIET REQUIRED)
  set(GTEST_FOUND ${googletest_FOUND})
  set(GTEST_INCLUDE_DIRS ${googletest_INCLUDE_DIRS})
  set(GTEST_LIBRARY_DIRS ${googletest_LIBRARY_DIRS})
  set(GTEST_BOTH_LIBRARIES ${googletest_LIBRARIES})
  set(GTEST_MAIN_LIBRARIES "${googletest_LIBRARIES}")
  list(FILTER GTEST_MAIN_LIBRARIES INCLUDE REGEX "libgtest_main.so$")
  set(GTEST_LIBRARIES "${googletest_LIBRARIES}")
  list(FILTER GTEST_LIBRARIES INCLUDE REGEX "libgtest.so$")
endmacro()

include("${ament_cmake_gtest_DIR}/ament_add_gtest.cmake")
include("${ament_cmake_gtest_DIR}/ament_add_gtest_executable.cmake")
include("${ament_cmake_gtest_DIR}/ament_add_gtest_test.cmake")
include("${ament_cmake_gtest_DIR}/ament_find_gtest.cmake")