load("@apex//tools/bazel/rules_ros_pkg:defs.bzl", "ros_pkg")

ros_pkg(
    name = "ament_cmake_pkg",
    description = "The entry point package for the ament buildsystem in CMake.",
    export_dependencies = [
        "@apex//tools/ament/ament_cmake/ament_cmake_core:ament_cmake_core_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_export_definitions:ament_cmake_export_definitions_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_export_dependencies:ament_cmake_export_dependencies_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_export_include_directories:ament_cmake_export_include_directories_pkg",
        # "@apex//tools/ament/ament_cmake/ament_cmake_export_interfaces:ament_cmake_export_interfaces_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_export_libraries:ament_cmake_export_libraries_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_export_link_flags:ament_cmake_export_link_flags_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_export_targets:ament_cmake_export_targets_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_libraries:ament_cmake_libraries_pkg",
        # "@apex//tools/ament/ament_cmake/ament_cmake_python:ament_cmake_python_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_target_dependencies:ament_cmake_target_dependencies_pkg",
        "@apex//tools/ament/ament_cmake/ament_cmake_test:ament_cmake_test_pkg",
        # "@apex//tools/ament/ament_cmake/ament_cmake_version:ament_cmake_version_pkg",
    ],
    license = "Apache License 2.0",
    maintainer_email = "<EMAIL>",
    maintainer_name = "Apex.AI",
    version = "0.9.4",
    visibility = ["//visibility:public"],
)
