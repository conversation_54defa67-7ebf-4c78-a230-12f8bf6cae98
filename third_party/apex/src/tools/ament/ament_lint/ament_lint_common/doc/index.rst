ament_lint_common
=================

A mechanism for running the following set of common linters:

* `ament_cmake_copyright <https://github.com/ament/ament_lint/blob/master/ament_cmake_copyright/doc/index.rst>`_ : a copyright linter which checks that copyright statements and license headers are present and correct

* `ament_cmake_cppcheck <https://github.com/ament/ament_lint/blob/master/ament_cmake_cppcheck/doc/index.rst>`_ : a C++ checker which can also find some logic tests

* `ament_cmake_cpplint <https://github.com/ament/ament_lint/blob/master/ament_cmake_cpplint/doc/index.rst>`_ : a C++ style checker (e.g. comment style)

* `ament_cmake_flake8 <https://github.com/ament/ament_lint/blob/master/ament_cmake_flake8/doc/index.rst>`_ : a style checker for Python files

* `ament_cmake_lint_cmake <https://github.com/ament/ament_lint/blob/master/ament_cmake_lint_cmake/doc/index.rst>`_ : a cmake linter

* `ament_cmake_pep257 <https://github.com/ament/ament_lint/blob/master/ament_cmake_pep257/doc/index.rst>`_ : a style checker for Python docstrings

* `ament_cmake_uncrustify <https://github.com/ament/ament_lint/blob/master/ament_cmake_uncrustify/doc/index.rst>`_ : a C++ style checker

* `ament_cmake_xmllint <https://github.com/ament/ament_lint/blob/master/ament_cmake_xmllint/doc/index.rst>`_ : an xml linter

The `ament_lint_auto <https://github.com/ament/ament_lint/blob/master/ament_lint_auto/doc/index.rst>`_ documentation provides information on using ament_lint_common.
