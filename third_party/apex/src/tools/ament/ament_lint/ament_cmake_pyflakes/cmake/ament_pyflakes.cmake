# Copyright 2014-2015 Open Source Robotics Foundation, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# Add a test to check the code for compliance with pyflakes.
#
# :param TESTNAME: the name of the test, default: "pyflakes"
# :type TESTNAME: string
# :param ARGN: the files or directories to check
# :type ARGN: list of strings
#
# @public
#
function(ament_pyflakes)
  cmake_parse_arguments(ARG "" "TESTNAME" "" ${ARGN})
  if(NOT ARG_TESTNAME)
    set(ARG_TESTNAME "pyflakes")
  endif()

  find_program(ament_pyflakes_BIN NAMES "ament_pyflakes")
  if(NOT ament_pyflakes_BIN)
    message(FATAL_ERROR "ament_pyflakes() could not find program 'ament_pyflakes'")
  endif()

  set(result_file "${AMENT_TEST_RESULTS_DIR}/${PROJECT_NAME}/${ARG_TESTNAME}.xunit.xml")
  set(cmd "${ament_pyflakes_BIN}" "--xunit-file" "${result_file}")
  list(APPEND cmd ${ARG_UNPARSED_ARGUMENTS})
  if(ARG_UNPARSED_ARGUMENTS)
    set(runfiles ${ARG_UNPARSED_ARGUMENTS})
  else()
    set(runfiles "${CMAKE_CURRENT_SOURCE_DIR}")
  endif()

  file(MAKE_DIRECTORY "${CMAKE_BINARY_DIR}/ament_pyflakes")
  list(APPEND runfiles "${CMAKE_BINARY_DIR}/ament_pyflakes")
  ament_add_test(
    "${ARG_TESTNAME}"
    COMMAND ${cmd}
    RUNFILES ${runfiles}
    OUTPUT_FILE "${CMAKE_BINARY_DIR}/ament_pyflakes/${ARG_TESTNAME}.txt"
    RESULT_FILE "${result_file}"
    WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
  )
  set_tests_properties(
    "${ARG_TESTNAME}"
    PROPERTIES
    LABELS "pyflakes;linter"
  )
endfunction()
