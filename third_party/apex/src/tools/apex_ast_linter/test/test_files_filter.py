# Copyright 2022 Apex.AI, Inc.
# All rights reserved.

import os

from pathlib import Path

from apex_ast_linter.tool import FilesFilter

from dataclasses import dataclass

import pytest
# TODO(#18233) from apex_pytest_utils import APEX_SKIP_TEST_IF


disable = os.environ.get('ARCH', 'x86_64') != 'x86_64'


@dataclass
class MockFile:
    name: Path


@dataclass
class MockLocation:
    file: MockFile


class MockCursor:
    """Mock for clang.cindex.Cursor."""

    def __init__(self, filename, children):
        self.location = MockLocation(MockFile(Path(filename)))
        self.children = children

    def get_children(self):
        return self.children


def visit(node, files_filter):
    for child in files_filter.get_children_filtered(node):
        visit(child, files_filter)


# TODO(#18233) @APEX_SKIP_TEST_IF(13736, "Clang is only installed on x86_64", disable)
@pytest.mark.skipif(disable, reason="Clang is only installed on x86_64")
def test_root_node_is_visited():
    """Test that the root node is seen unconditionally."""
    cursor = MockCursor('a', [
        MockCursor('b', [
            MockCursor('d', [])
        ]),
        MockCursor('c', [
            MockCursor('e', []),
        ])
    ])
    files_filter = FilesFilter({})
    visit(cursor, files_filter)
    assert files_filter.seen_files == {Path('a')}


# TODO(#18233) @APEX_SKIP_TEST_IF(13736, "Clang is only installed on x86_64", disable)
@pytest.mark.skipif(disable, reason="Clang is only installed on x86_64")
def test_files_are_visited_recursively():
    """Test that recursion descends only into children in the files filter."""
    cursor = MockCursor('a', [
        MockCursor('b', [
            MockCursor('d', [])
        ]),
        MockCursor('c', [
            MockCursor('e', [
                MockCursor('f', [])
            ]),
        ])
    ])
    files_filter = FilesFilter({Path(x) for x in ['a', 'b', 'd', 'e']})
    visit(cursor, files_filter)
    assert files_filter.seen_files == {Path(x) for x in ['a', 'b', 'd']}
