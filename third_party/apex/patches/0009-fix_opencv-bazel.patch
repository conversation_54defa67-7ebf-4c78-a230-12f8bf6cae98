diff --git a/grace/thirdparty/opencv_vendor/opencv.local.BUILD b/grace/thirdparty/opencv_vendor/opencv.local.BUILD
index c9520dff..719ba1e2 100644
--- a/grace/thirdparty/opencv_vendor/opencv.local.BUILD
+++ b/grace/thirdparty/opencv_vendor/opencv.local.BUILD
@@ -11,16 +11,16 @@ cc_library(
     visibility = ["//visibility:public"],
 )
 
-apex_cc_library(
+cc_library(
     name = "core",
-    srcs = ["lib/libopencv_core.a"] + glob([
+    srcs = glob([
         "lib/libopencv_core.so*",
     ]),
     hdrs = [
         "include/opencv2/core.hpp",
         "include/opencv2/opencv_modules.hpp",
     ] + glob(["include/opencv2/core/**"]),
-    disallow_undefined_symbols = True,
+    # disallow_undefined_symbols = True,
     linkopts = [
         "-lstdc++",
         "-lrt",
@@ -29,7 +29,7 @@ apex_cc_library(
     visibility = ["//visibility:public"],
     deps = [
         ":tbb",
-        "@zlib",
+        # "@zlib",
     ],
 )
 
@@ -95,11 +95,9 @@ apex_cc_library(
     deps = [":core"],
 )
 
-apex_cc_library(
+cc_library(
     name = "highgui",
-    srcs = [
-        "lib/libopencv_highgui.a",
-    ] + glob([
+    srcs = glob([
         "lib/libopencv_highgui.so*",
     ]),
     hdrs = ["include/opencv2/highgui.hpp"] + glob(["include/opencv2/highgui/**"]),
@@ -108,14 +106,13 @@ apex_cc_library(
     deps = [
         ":imgcodecs",
         ":imgproc",
+        ":videoio",
     ],
 )
 
-apex_cc_library(
+cc_library(
     name = "imgcodecs",
-    srcs = [
-        "lib/libopencv_imgcodecs.a",
-    ] + glob([
+    srcs = glob([
         "lib/libopencv_imgcodecs.so*",
     ]),
     hdrs = ["include/opencv2/imgcodecs.hpp"] + glob(["include/opencv2/imgcodecs/**"]),
@@ -129,11 +126,9 @@ cc_library(
     srcs = ["lib/libOpenCL.so.1"],
 )
 
-apex_cc_library(
+cc_library(
     name = "imgproc",
-    srcs = [
-        "lib/libopencv_imgproc.a",
-    ] + glob([
+    srcs = glob([
         "lib/libopencv_imgproc.so*",
     ]),
     hdrs = ["include/opencv2/imgproc.hpp"] + glob(["include/opencv2/imgproc/**"]),
@@ -162,11 +157,9 @@ apex_cc_library(
     ],
 )
 
-apex_cc_library(
+cc_library(
     name = "videoio",
-    srcs = [
-        "lib/libopencv_videoio.a",
-    ] + glob([
+    srcs = glob([
         "lib/libopencv_videoio.so*",
     ]),
     hdrs = ["include/opencv2/videoio.hpp"] + glob(["include/opencv2/videoio/**"]),
