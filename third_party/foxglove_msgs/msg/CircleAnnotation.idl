// Generated by https://github.com/foxglove/foxglove-sdk

#include "foxglove_msgs/msg/Color.idl"
#include "foxglove_msgs/msg/Point2.idl"
#include "foxglove_msgs/msg/Time.idl"

module foxglove_msgs { module msg {

// A circle annotation on a 2D image
struct CircleAnnotation {
  // Timestamp of circle
  Time timestamp;

  // Center of the circle in 2D image coordinates (pixels).
  // The coordinate uses the top-left corner of the top-left pixel of the image as the origin.
  Point2 position;

  // Circle diameter in pixels
  double diameter;

  // Line thickness in pixels
  double thickness;

  // Fill color
  Color fill_color;

  // Outline color
  Color outline_color;
};

};

};
