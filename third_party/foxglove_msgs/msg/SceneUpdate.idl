// Generated by https://github.com/foxglove/foxglove-sdk

#include "foxglove_msgs/msg/SceneEntity.idl"
#include "foxglove_msgs/msg/SceneEntityDeletion.idl"

module foxglove_msgs { module msg {

// An update to the entities displayed in a 3D scene
struct SceneUpdate {
  // Scene entities to delete
  sequence<SceneEntityDeletion, 16> deletions;

  // Scene entities to add or replace
  sequence<SceneEntity, 16> entities;
};

};

};
