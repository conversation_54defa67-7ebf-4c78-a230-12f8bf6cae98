// Generated by https://github.com/foxglove/foxglove-sdk

#include "foxglove_msgs/msg/NumericType.idl"

module foxglove_msgs { module msg {

// A field present within each element in a byte array of packed elements.
struct PackedElementField {
  // Name of the field
  string name;

  // Byte offset from start of data buffer
  uint32 offset;

  // Type of data in the field. Integers are stored using little-endian byte order.
  NumericType type;
};

};

};
