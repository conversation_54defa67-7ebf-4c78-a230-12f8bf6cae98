// Generated by https://github.com/foxglove/foxglove-sdk

#include "foxglove_msgs/msg/Quaternion.idl"
#include "foxglove_msgs/msg/Time.idl"
#include "foxglove_msgs/msg/Vector3.idl"

module foxglove_msgs { module msg {

// A transform between two reference frames in 3D space
struct FrameTransform {
  // Timestamp of transform
  Time timestamp;

  // Name of the parent frame
  string parent_frame_id;

  // Name of the child frame
  string child_frame_id;

  // Translation component of the transform
  Vector3 translation;

  // Rotation component of the transform
  Quaternion rotation;
};

};

};
