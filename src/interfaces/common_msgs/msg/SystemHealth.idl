#include "common_msgs/msg/Header.idl"
#include "common_msgs/msg/Health.idl"

module common_msgs {
  module msg {
    @verbatim (language="comment", text=
      "Health summary of all components")
    struct SystemHealth {
      common_msgs::msg::Header header;

    @verbatim (language="comment", text=
      "Overall status of the system")
      common_msgs::msg::Status status;

    @verbatim (language="comment", text=
      "Health of each component")
      sequence<common_msgs::msg::Health, 50> component_healths;
    };
  };
};
