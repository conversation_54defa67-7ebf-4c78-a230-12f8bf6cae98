#include "common_msgs/msg/Header.idl"

module common_msgs {
  module msg {

    @verbatim (language="comment", text=
      "Takeover/override requests based on health")
    struct AutonomousDrivingAllowed {

      common_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Whether to require override from the health monitor")
      boolean require_override;

      @verbatim (language="comment", text=
        "Whether to require takeover from the health monitor")
      boolean require_takeover;
    };
  };
};
