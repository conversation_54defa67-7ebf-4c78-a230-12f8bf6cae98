#include "planning_trajectory_msgs/msg/TrajectoryPoint.idl"

module planning_msgs {
  module msg {

    /**
     * Input and intermediate data used internally by the planning module.
     */
    struct PlanningData {
      /**
       * Initial planning point used as the starting reference.
       */
      planning_trajectory_msgs::msg::TrajectoryPoint init_point;

      /**
       * The name of the best STL (Space-Time Lattice) frame used.
       */
      string best_stl_frame_name;

      /**
       * Relative map input used in planning.
       */
      // apollo::relative_map::MapMsg relative_map;

      /**
       * The forward clear distance (in meters) available to plan the path.
       */
      @default (value=0.0)
      double front_clear_distance;

      /**
       * Maximum allowed speed based on map conditions (m/s).
       */
      @default (value=0.0)
      double map_max_speed;
    };
  };
};
