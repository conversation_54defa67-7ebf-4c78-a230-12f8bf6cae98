module canbus_msgs {
  module msg {
    struct MPCTuning {
      @default (value=1.0)
      float r_angular_velocity;
      @default (value=1.0)
      float r_d_angular_velocity;
      @default (value=1.0)
      float q_lateral_error;
      @default (value=1.0)
      float q_heading_error;
      @default (value=1.0)
      float q_lateral_errorsum;
      @default (value=1.0)
      float q_beta;
      @default (value=1.0)
      float q_last_lateral_error;
      @default (value=1.0)
      float q_last_heading_error;
      @default (value=1.0)
      float q_last_lateral_errorsum;
      @default (value=1.0)
      float q_last_beta;
    };
  };
};
