// Copyright (c) 2024-2025 T2 Inc. All rights reserved

#pragma once

#include <string>
#include <unordered_map>

#include <prediction_msgs/msg/lane_graph.hpp>
#include <prediction_msgs/msg/lane_sequence.hpp>
#include <prediction_msgs/msg/nearby_obstacle.hpp>

namespace t2::prediction {

// もともとproto定義なのでidlにすべきだが、メッセージの一部ではないようなのでここで定義する
struct LaneObstacle {
  std::int32_t obstacle_id;
  std::string lane_id;
  double lane_s;
  double lane_l;
};
}  // namespace t2::prediction

namespace t2 {
namespace prediction {

/**
 * @brief Collection of observed obstacles in HD map. This class stores
 * obstacles associated with the lane information in order to query them by lane
 * information.
 */
class ObstacleClusters {
 public:
  using LaneSequence = ::prediction_msgs::msg::LaneSequence;
  using NearbyObstacle = ::prediction_msgs::msg::NearbyObstacle;

 public:
  /**
   * @brief Constructor
   */
  ObstacleClusters() = default;

  /**
   * @brief Remove all obstacles.
   */
  void Clear();

  /**
   * @brief Insert obstacle to the cluster.
   *
   * @param obstacle_id ID of the obstacle newly inserted.
   * @param lane_id
   * @param lane_s
   * @param lane_l
   */
  void InsertObstacle(const int obstacle_id, const std::string& lane_id, const double lane_s,
                      const double lane_l);

  /**
   * @brief Sort lane obstacles by lane s
   */
  void SortObstacles();

  /**
   * @brief Get the nearest obstacle on the given lane sequence from the given s
   * coordinate value in the forward direction (increasing s direction)
   * @param lane_sequence lane sequence to search
   * @param obstacle_s s offset in the first lane of the lane sequence. The
   * search starts from this s.
   * @param obstacle_l lateral offset from the center line of the lane sequence.
   * @param nearby_obstacle_ptr the destination found obstacle is written.
   * @return If the forward obstacle is found
   */
  bool ForwardNearbyObstacle(const LaneSequence& lane_sequence, const int obstacle_id,
                             const double obstacle_s, const double obstacle_l,
                             NearbyObstacle* const nearby_obstacle_ptr) const;

  /**
   * @brief Get the backward nearest obstacle on lane sequence at s
   * @param Lane sequence
   * @param s offset in the first lane of the lane sequence
   * @param the forward obstacle on lane
   * @return If the backward obstacle is found
   */
  bool BackwardNearbyObstacle(const LaneSequence& lane_sequence, const int obstacle_id,
                              const double obstacle_s, const double obstacle_l,
                              NearbyObstacle* const nearby_obstacle_ptr) const;

  /**
   * @brief Get map object mapping lane_id to list of obstacles on that lane.
   *
   * @return map object.
   */
  const std::unordered_map<std::string, std::vector<LaneObstacle>>& GetLaneObstacles() const;

 private:
  std::unordered_map<std::string, std::vector<LaneObstacle>> lane_obstacles_;
  std::unordered_map<int32_t, std::string> obstacle_id_to_lane_id_;
};

}  // namespace prediction
}  // namespace t2
