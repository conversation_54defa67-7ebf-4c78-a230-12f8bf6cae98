// Copyright (c) 2024-2025 T2 Inc. All rights reserved

#include "cost_evaluator.hpp"

#include <cmath>

#include <prediction_msgs/msg/lane_graph.hpp>

#include "src/common/contract_assertions/contract_assertions.hpp"
#include "src/common/core/logging.hpp"
#include "src/perception/common/idl_util/optional_util.hpp"
#include "src/prediction/common/map/prediction_map.hpp"
#include "src/prediction/proto/prediction_conf.pb.h"

namespace t2::prediction {

using Feature = Evaluator::Feature;
using LaneSequence = ::prediction_msgs::msg::LaneSequence;
using namespace t2::perception::common::idl_util;

namespace {
// util functions

struct ObstacleDimension {
  double length;
  double width;
};

/**
 * @brief Get the Obstacle Dimension information from feature.
 *
 * @param obstacle_latest_feature feature object to get obstacle dimension from.
 * @return obstacle dimension object. If the values are not set inside
 */
const ObstacleDimension GetObstacleDimension(const Feature& obstacle_latest_feature) {
  precondition(std::isfinite(obstacle_latest_feature.length));
  precondition(std::isfinite(obstacle_latest_feature.width));

  ObstacleDimension result;
  result.length = obstacle_latest_feature.length;
  result.width = obstacle_latest_feature.width;

  return result;
}

/**
 * @brief Get the minumum angle difference from lane sequences object. The angle
 * difference is the difference value between the obstacle heading angle and the
 * heading angle of the lane sequence.
 *
 * @param lane_sequences lane sequences to be searched.
 * @return found minimum angle difference. The angle value is in [0, 2*pi)
 * radians.
 */
template <typename LaneSequence>
double GetMinumumAngleDiffFromLaneSequences(const LaneSequence& lane_sequences) {
  // NOLINTNEXTLINE(cppcoreguidelines-avoid-magic-numbers)
  double min_angle_diff = M_PI * 2.0;
  for (const auto& lane_sequence : lane_sequences) {
    if (lane_sequence.lane_segment.empty() || lane_sequence.lane_segment[0].lane_point.empty()) {
      T2_DEBUG << "Empty lane sequence found in id [" << lane_sequence.lane_sequence_id << "].";
      continue;
    }
    const auto& lane_point = lane_sequence.lane_segment[0].lane_point[0];
    const double angle_diff = std::abs(lane_point.angle_diff);
    min_angle_diff = std::min(min_angle_diff, angle_diff);
  }
  return min_angle_diff;
}

}  // namespace

CostEvaluator::CostEvaluator() : Evaluator(t2::prediction::ObstacleConf::COST_EVALUATOR) {}

bool CostEvaluator::Evaluate(int obstacle_id, ObstaclesContainer& obstacles_container) {
  auto& obstacle_ptr = obstacles_container.GetObstacle(obstacle_id);
  if (!obstacle_ptr) {
    T2_ERROR << "Obstacle [" << obstacle_id << "] not found";
    return false;
  }
  auto& obstacle_latest_feature = obstacle_ptr->LatestFeature();
  if (!obstacle_latest_feature) {
    T2_ERROR << "Obstacle [" << obstacle_id << "] has no feature";
    return false;
  }

  if (!Evaluator::SanityCheck(*obstacle_latest_feature)) {
    return false;
  }
  if (IsEmpty(obstacle_latest_feature->lane)) {
    T2_ERROR << "Obstacle [" << obstacle_id << "] has no lane.";
    return false;
  }

  obstacle_ptr->SetEvaluatorType(evaluator_type_);
  const auto obstacle_dim = GetObstacleDimension(*obstacle_latest_feature);

  auto& lane_graph = Value(obstacle_latest_feature->lane).lane_graph;
  if (lane_graph.lane_sequence.empty()) {
    T2_ERROR << "Obstacle [" << obstacle_id << "] has no lane sequences.";
    return false;
  }

  const double min_angle_diff = GetMinumumAngleDiffFromLaneSequences(lane_graph.lane_sequence);

  for (auto& lane_sequence : lane_graph.lane_sequence) {
    if (lane_sequence.lane_segment.empty() || lane_sequence.lane_segment[0].lane_point.empty()) {
      T2_DEBUG << "Empty lane sequence found id [" << lane_sequence.lane_sequence_id << "]";
      lane_sequence.probability = 0.0;
      continue;
    }

    Eigen::Vector2d position(obstacle_latest_feature->position.x,
                             obstacle_latest_feature->position.y);
    if (std::abs(obstacle_latest_feature->speed) > 1.0) {
      constexpr double kHalfFactor = 2.0;
      const double heading = obstacle_latest_feature->velocity_heading;

      // bboxの先端をpositionとして求める
      position = Eigen::Vector2d(obstacle_latest_feature->position.x +
                                     obstacle_dim.length / kHalfFactor * std::cos(heading),
                                 obstacle_latest_feature->position.y +
                                     obstacle_dim.length / kHalfFactor * std::sin(heading));
    }

    // We have already checked the lane sequence has non zero lane segment
    const auto lane_info = PredictionMap::LaneById(lane_sequence.lane_segment[0].lane_id);
    double lane_s = 0.0;
    double lane_l = 0.0;
    if (!PredictionMap::GetProjection(position, lane_info, &lane_s, &lane_l)) {
      T2_ERROR << "Failed in getting lane s and lane l";
      lane_sequence.probability = 0.0;
      continue;
    }

    expect_ignore(lane_sequence.lane_segment.empty() == false);
    expect_ignore(lane_sequence.lane_segment.lane_point.empty() == false);

    const double abs_lane_l = std::abs(lane_l);
    const auto& lane_point = lane_sequence.lane_segment[0].lane_point[0];
    // lane_point.width()はその点でのレーンの幅（のはず
    const double half_lane_width = lane_point.width / 2.0;
    // TODO(yokesaku) check this logic is valid.
    const double buffer = std::max(0.5, std::min(1.0, obstacle_dim.width / 2.0 - 0.1));

    // 車線中心からの距離、車線中心からずれているほど小さい値を取る
    const auto distance = half_lane_width - abs_lane_l + buffer;
    // 標準シグモイド関数による写像
    // 車線内に収まっている場合は0.5未満になることはない
    double probability = t2::common::math::Sigmoid(distance);

    // NOLINTNEXTLINE(cppcoreguidelines-avoid-magic-numbers)
    if (min_angle_diff + M_PI * 0.1 < std::abs(lane_point.angle_diff)) {
      // If the angle difference between obstacle and lane sequence is large,
      // lower the probability.
      // ここに来ると、probabilityの値は[0.25, 0.5]内の値になる
      probability *= 0.5;  // NOLINT(cppcoreguidelines-avoid-magic-numbers)
    }

    lane_sequence.probability = probability;
  }
  return true;
}

std::string CostEvaluator::GetEvaluatorName() const { return "cost_evaluator"; }

}  // namespace t2::prediction
