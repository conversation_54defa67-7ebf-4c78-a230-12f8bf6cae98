load("@rules_cc//cc:defs.bzl", "cc_library", "cc_test")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "obstacles_container",
    srcs = [
        "obstacle.cpp",
        "obstacles_container.cpp",
    ],
    hdrs = [
        "obstacle.hpp",
        "obstacles_container.hpp",
    ],
    deps = [
        ":feature_buffer",
        "//src/common/contract_assertions",
        "//src/common/core",
        "//src/interfaces/prediction_msgs",
        "//src/perception/common/idl_util:optional_util",
        "//src/prediction/common:prediction_gflags",
        "//src/prediction/common/map:lane_graph_builder",
        "//src/prediction/proto:prediction_conf_cc_proto",
        "//src/prediction/scene:obstacle_clusters",
    ],
)

cc_test(
    name = "obstacles_container_test",
    size = "small",
    srcs = ["obstacles_container_test.cpp"],
    deps = [
        ":branch_and_merging_test_map_based_test",
        ":obstacles_container",
        "//src/interfaces/prediction_msgs",
        "//src/prediction/common:prediction_gflags",
        "@googletest//:gtest_main",
    ],
)

cc_test(
    name = "obstacle_test",
    size = "small",
    srcs = ["obstacle_test.cpp"],
    deps = [
        ":branch_and_merging_test_map_based_test",
        ":obstacles_container",
        "//src/interfaces/prediction_msgs",
        "//src/prediction/common:prediction_gflags",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "feature_buffer",
    srcs = ["feature_buffer.cpp"],
    hdrs = ["feature_buffer.hpp"],
    deps = [
        "//src/common/core",
        "//src/interfaces/prediction_msgs",
        "//src/prediction/common:prediction_gflags",
    ],
)

cc_test(
    name = "feature_buffer_test",
    size = "small",
    srcs = ["feature_buffer_test.cpp"],
    deps = [
        ":feature_buffer",
        "//src/interfaces/prediction_msgs",
        "//src/prediction/common:prediction_gflags",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "obstacle_status_updater",
    srcs = [
        "obstacle_status_updater.cpp",
    ],
    hdrs = [
        "obstacle_status_updater.hpp",
    ],
    deps = [
        ":obstacles_container",
        "//src/prediction/common/map:lane_graph_builder",
        "//src/prediction/scene:obstacle_prioritizer",
    ],
)

cc_library(
    name = "branch_and_merging_test_map_based_test",
    hdrs = ["branch_and_merging_test_map_based_test.hpp"],
    data = [
        "//src/prediction:prediction_testdata",
    ],
    deps = [
        "//src/common/config:map_config",
        "@googletest//:gtest_main",
    ],
)
