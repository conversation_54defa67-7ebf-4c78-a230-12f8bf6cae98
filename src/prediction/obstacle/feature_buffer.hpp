// Copyright (c) 2024-2025 T2 Inc. All rights reserved
#pragma once

#include <deque>
#include <memory>

#include <prediction_msgs/msg/feature.hpp>

namespace t2 {
namespace prediction {

class FeatureBuffer {
 public:
  using Feature = ::prediction_msgs::msg::Feature;

 public:
  /**
   * @brief Constructor.
   */
  FeatureBuffer();

  /**
   * @brief Insert feature to the buffer. If a feature with an older timestamp
   * is inserted, the buffer will be cleared and then the feature is inserted.
   *
   * @param feature feature object to be inserted.
   * @return true if insertion is accepted.
   * @return false if insertion is failed.
   */
  bool Insert(const Feature& feature);

  /**
   * @brief Get a pointer to the latest feature data if the buffer
   * has. If the buffer is empty nullptr is returned.
   *
   * @return constant reference to the latest feature if the buffer has.
   */
  const std::unique_ptr<Feature>& LatestFeature() const;

  /**
   * @brief Get a pointer to the mutable latest feature data if the buffer
   * has. If the buffer is empty nullptr is returned.
   *
   * @return constant reference to the latest feature if the buffer has.
   */
  const std::unique_ptr<Feature>& MutableLatestFeature();

  /**
   * @brief Get a pointer to the oldest feature data if the buffer has.
   *
   * @return const std::unique_ptr<prediction_msgs::msg::Feature>&
   */
  const std::unique_ptr<Feature>& OldestFeature();

  /**
   * @brief Get the current number of the features in the buffer.
   *
   * @return the number of the current features.
   */
  size_t Size() const;

  /**
   * @brief Check the buffer is empty or not.
   *
   * @return true if the buffer is empty.
   */
  bool Empty() const;

  /**
   * @brief Check whether the latest feature in the buffer is still or moving.
   *
   * @return true if the latest feature in the buffer is still. Also return true
   * if the buffer is empty.
   * @return false if the latest feature in the buffer is moving.
   */
  bool IsStill() const;

 private:
  /**
   * @brief Check the given timestamp is older than the latest feature in this
   * buffer.
   *
   * @param[in] timestamp timestamp to checked
   * @return true if the timestamp is older than the latest feature. Return
   * false if the timestamp is newer than the latest one, or the buffer is
   * empty,
   */
  bool IsOlderTimestamp(const double timestamp) const;

  /**
   * @brief Trim out the outdated feature history from this instance. This
   * method removes all features with timestamp older than the latest feature
   * timestamp - predefined delta time.
   *
   */
  void DiscardOutdatedHistory();

  /**
   * @brief Set the Motion Status object such as the latest feature is still or
   * moving.
   */
  void SetMotionStatus();

  // list of features put to this buffer.
  std::deque<std::unique_ptr<Feature>> feature_history_;

  const std::unique_ptr<Feature> nullptr_ref_;
};

}  // namespace prediction
}  // namespace t2
