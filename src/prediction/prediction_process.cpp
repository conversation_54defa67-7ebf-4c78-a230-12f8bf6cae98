// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "prediction_process.hpp"

namespace t2::prediction {
using PerceptionObstacles = PredictionProcess::PerceptionObstacles;
using PredictionObstacles = PredictionProcess::PredictionObstacles;
using LocalizationEstimate = PredictionProcess::LocalizationEstimate;

bool PredictionProcess::Init([[maybe_unused]] const t2::prediction::PredictionConf& conf) {
  return true;
}

std::optional<PredictionObstacles> PredictionProcess::DoProcess(
    [[maybe_unused]] const PerceptionObstacles& perception_obstacles,
    [[maybe_unused]] const LocalizationEstimate& localization_msg) {
  return {};
}

void PredictionProcess::OnLocalization(
    [[maybe_unused]] const LocalizationEstimate& localization_estimate) {}

void PredictionProcess::SetRelativeOrientation(
    [[maybe_unused]] PredictionObstacles& prediction_result,
    [[maybe_unused]] const LocalizationEstimate& localization_estimate) {}
}  // namespace t2::prediction
