// Copyright (c) 2025 T2 Inc. All rights reserved
#pragma once

#include <algorithm>
#include <atomic>
#include <chrono>
#include <cmath>
#include <condition_variable>
#include <cstddef>
#include <cstdint>
#include <deque>
#include <iterator>
#include <mutex>
#include <thread>
#include <type_traits>
#include <utility>
#include <vector>

#include <boost/range/adaptor/filtered.hpp>
#include <boost/range/adaptor/reversed.hpp>
#include <boost/range/algorithm/copy.hpp>
#include <boost/range/algorithm/find_if.hpp>
#include <boost/range/sub_range.hpp>
#include <radar_msgs/msg/detection_data.hpp>

#include "src/common/contract_assertions/contract_assertions.hpp"
#include "src/common/core/chrono_consts.hpp"
#include "src/common/core/logging.hpp"

namespace t2::perception::radar::detail {
template <typename C, typename Message, typename = void>
inline constexpr bool kIsSequenceContainerV = false;

template <typename C, typename Message>
inline constexpr bool kIsSequenceContainerV<
    C, Message, std::void_t<decltype(std::declval<C&>().emplace_back(std::declval<Message&&>()))>> =
    true;

}  // namespace t2::perception::radar::detail

// NOLINTBEGIN(cppcoreguidelines-macro-usage, bugprone-macro-parentheses)
#define SequenceContainer(C)                                                                   \
  typename C,                                                                                  \
      std::enable_if_t<::t2::perception::radar::detail::kIsSequenceContainerV<C, MessageType>, \
                       std::nullptr_t> = nullptr
// NOLINTEND(cppcoreguidelines-macro-usage, bugprone-macro-parentheses)

namespace t2::perception::radar {

using DetectionData = ::radar_msgs::msg::DetectionData;

// ADLカスタマイズポイント（ちゃんとするなら非ADLにして検出すべき・・・）
// inline std::uint64_t GetRadarTimestamp(const ::apollo::drivers::radar::ObjectData& msg) {
//   // creation_timestamp == measurement_timestamp
//   // これらはns単位の値
//   return msg.header().measurement_timestamp();
// }

inline std::uint64_t GetRadarTimestamp(const DetectionData& msg) {
  // creation_timestamp == measurement_timestamp
  // これらはns単位の値
  return msg.header.measurement_timestamp;
}

template <typename RadarMessage>
class MessageStorage {
  using MessageType = RadarMessage;
  using OneStorageType = std::deque<MessageType>;

  static_assert(
      ::t2::perception::radar::detail::kIsSequenceContainerV<OneStorageType, MessageType>);

  // 内部ストレージ関連
  std::vector<OneStorageType> storage_;
  std::atomic_uint32_t push_counter_ = 0;
  const std::uint32_t max_publisher_;

  // 同期関連
  std::condition_variable cv_;
  std::mutex mtx_;
  std::atomic_bool is_fetching_and_deleting_ = false;

  void Wait() {
    std::unique_lock lock{mtx_};
    // is_fetching_and_deleting_がfalseになるまで待機
    cv_.wait(lock, [this] { return this->is_fetching_and_deleting_.load() == false; });
  }

  void Notify() { cv_.notify_all(); }

  static std::uint64_t TimestampDiff(std::uint64_t lhs, std::uint64_t rhs) {
    if (lhs <= rhs) {
      return rhs - lhs;
    } else {
      return lhs - rhs;
    }
  }

  // クエリ処理のクリティカルセクション構成処理をまとめる関数
  template <typename Proc>
  void CriticalSectionForFetch(Proc&& query_process) {
    // 事前条件
    precondition(push_counter_ < max_publisher_);

    // 他スレッドを待機させる
    is_fetching_and_deleting_ = true;
    {
      if (push_counter_ != 0) {
        // 他スレッドの登録処理完了を待機
        do {
          std::this_thread::yield();
        } while (0 < push_counter_);
      }

      contract_assert(push_counter_ == 0);

      // メインのクエリ処理
      query_process();
    }
    // 登録処理の再開（待機解除
    is_fetching_and_deleting_ = false;
    Notify();

    // 事後条件
    postcondition(push_counter_ < max_publisher_);
    postcondition(is_fetching_and_deleting_ == false);
  }

 public:
  /**
   * @brief コンストラクタ
   * @param num_of_publishers メッセージを登録しうるスレッドの最大数
   */
  explicit MessageStorage(std::uint32_t num_of_publishers)
      : storage_(num_of_publishers), max_publisher_{num_of_publishers} {}

  /**
   * @brief メッセージを登録する
   * @param publisher_id 登録者のid（0-indexed
   * @param msg 登録するメッセージ
   * @details
   * 同じpublisher_idで複数のスレッドから登録すると、データ競合を起こす。publisher_idが異なっている限り、複数のスレッドからの登録は安全
   */
  void Push(std::uint32_t publisher_id, MessageType&& msg) & {
    // 事前条件
    precondition(publisher_id < max_publisher_);

    if (is_fetching_and_deleting_ == true) {
      // 取り出し中なら待機する
      Wait();
    }

    push_counter_ += 1;
    {
      // 登録処理
      // 同じpublisher_idが同時に登録しないことを仮定している
      // これはSILSのreaderにおいては自然な仮定なはず
      auto& container = storage_[publisher_id];
      container.push_back(std::move(msg));
    }
    push_counter_ -= 1;

    // 事後条件
    postcondition(push_counter_ < max_publisher_);
  }

  /**
   * @brief タイムスタンプによってメッセージをクエリする
   * @param timestamp_ns
   * ほしいメッセージのタイミングを指定するタイムスタンプ（ナノ秒）
   * @param accept_time_gap_ns 許容時間差（ナノ秒）
   * @param output 取得したメッセージを出力するコンテナ（vectorやlsitなど
   * @details
   * メッセージが取得された場合、そのメッセージを含めてそれ以前のメッセージは削除される
   * @details
   * 処理後のoutputには、max_publisher_個の要素が追加されている（メッセージの取得に失敗した場合、デフォルト構築したメッセージが入っている
   */
  template <SequenceContainer(Container)>
  void FetchAndDeleteBeforeTimestamp(const std::uint64_t timestamp_ns,
                                     const std::uint64_t accept_time_gap_ns, Container& output) & {
    // 事前条件
    precondition(0 < timestamp_ns);

    this->CriticalSectionForFetch([&, this] {
      for (auto& vec : storage_) {
        // 取り出し処理
        auto first = vec.begin();
        auto last = vec.end();

        // メッセージ到着順が因果律逆転していないことを仮定
        // 指定されたタイムスタンプ以上で最も小さいもの、を選択
        auto infimum_pos = std::lower_bound(
            first, last, timestamp_ns,
            [](const auto& elem, std::uint64_t value) { return GetRadarTimestamp(elem) < value; });

        if (infimum_pos == last) {
          // 近傍が見つからない場合でも、空のデータを登録しておく
          // 見つからない場合というのは、データが1つも登録されていないはず
          output.emplace_back();

          continue;
        }

        auto [near_pos, time_diff] = [&timestamp_ns, &infimum_pos, &first] {
          const auto diff_infimum = TimestampDiff(timestamp_ns, GetRadarTimestamp(*infimum_pos));

          if (infimum_pos == first) {
            return std::make_pair(infimum_pos, diff_infimum);
          }

          // 1つ前の点を調べる
          auto prev_near_pos = std::prev(infimum_pos);

          const auto diff_prev = TimestampDiff(timestamp_ns, GetRadarTimestamp(*prev_near_pos));

          if (diff_infimum <= diff_prev) {
            return std::make_pair(infimum_pos, diff_infimum);
          } else {
            return std::make_pair(prev_near_pos, diff_prev);
          }
        }();

        // タイムスタンプからのズレの許容値
        if (time_diff < accept_time_gap_ns) {
          output.emplace_back(std::move(*near_pos));
        } else {
          output.emplace_back();
        }

        // 取り出した要素を含む、それ以前のデータを削除
        vec.erase(first, near_pos);
      }
    });

    // 事後条件
    postcondition(max_publisher_ == output.size());
  }

  /**
   * @brief タイムスタンプによってメッセージをクエリする
   * @param timestamp_ns
   * ほしいメッセージのタイミングを指定するタイムスタンプ（ナノ秒単位）
   * @param duration 集約期間 [ms]
   * @param output 取得したメッセージを出力するコンテナ（vectorやlistなど
   * @details
   * メッセージが取得された場合、取得された最も古いメッセージより前のものは削除される
   * @details
   * 処理後のoutputには、max_publisher_個の要素が追加されている
   * outputの各要素は指定した時間範囲のRadarメッセージ配列（集約対象のメッセージ列
   * 返されるメッセージは、[timestamp_ns - duration, timestamp_ns]
   * の範囲内にあるもののみ
   * この範囲内に該当するメッセージがない場合、内側の各コンテナは空になる
   */
  template <typename SequenceContainer>
  void FetchMessagesInDurationAndDeleteBefore(const std::uint64_t timestamp_ns,
                                              std::chrono::milliseconds duration,
                                              SequenceContainer& output) & {
    // 要は、vector<MessageType>のvectorのようになっていてほしいということ
    // コンセプト的マクロの準備は手がかかるので省略
    static_assert(::t2::perception::radar::detail::kIsSequenceContainerV<
                  typename SequenceContainer::value_type, MessageType>);

    // 事前条件
    precondition(0 < timestamp_ns);

    this->CriticalSectionForFetch([&, this] {
      for (auto& vec : storage_) {
        // センサ毎の出力先コンテナ
        // このコンテナに新しいタイムスタンプのものからメッセージをpushしていく
        auto& per_sensor_output = output.emplace_back();

        // 空なら検索しない
        if (vec.empty()) {
          continue;
        }

        // 取り出し処理
        const auto first = vec.begin();
        const auto last = vec.end();

        // メッセージ到着順が因果律逆転していないことを仮定
        // 指定されたタイムスタンプよりも大きい最初のメッセージを探す
        const auto one_step_behind_pos = std::upper_bound(
            first, last, timestamp_ns,
            [](std::uint64_t value, const auto& elem) { return value < GetRadarTimestamp(elem); });

        if (one_step_behind_pos == first) {
          // 指定したタイムスタンプよりも古いメッセージはない
          continue;
        }

        /// 最新時点から逆向きに遡って、duration期間にあるメッセージを出力する

        // 指定時点からdurationの期間trueを返す
        auto aggregation_period =
            [duration_ns = static_cast<std::uint64_t>(std::chrono::nanoseconds{duration}.count()),
             epoch = timestamp_ns](const MessageType& elem) -> bool {
          // epochのほうが常に大きいはずではある
          return TimestampDiff(epoch, GetRadarTimestamp(elem)) <= duration_ns;
        };

        // 最新メッセージより前のメッセージ範囲を時間を遡る方向に向けて探索する
        // clang-format off
        auto range_of_aggregation_target = boost::sub_range<OneStorageType>{first, one_step_behind_pos}
                                         | boost::adaptors::reversed
                                         | boost::adaptors::filtered(aggregation_period);
        // clang-format on

        // 100msを超える集約範囲の場合、2フレーム以上に渡って同じメッセージを出力する必要があるため、コピーする
        boost::range::copy(range_of_aggregation_target, std::back_inserter(per_sensor_output));

        if (per_sensor_output.empty()) {
          // ストレージにはデータが存在するが、古すぎてアグリゲーション期間に入っていない場合、ここにくる
          // この場合コンテナを全クリアする（利用シーンから考えると、このデータが拾われることはない=メモリリークしうる
          vec.clear();
          continue;
        }

        // 取り出した一番古い要素のイテレータ
        const auto oldest_aggregation_target_pos =
            boost::range::find_if(vec,
                                  [oledest_timestamp = GetRadarTimestamp(per_sensor_output.back())](
                                      const MessageType& elem) -> bool {
                                    return GetRadarTimestamp(elem) == oledest_timestamp;
                                  });

        // これfalseになることある・・・？
        if (oldest_aggregation_target_pos != last) {
          // 取り出した要素より前のデータを削除
          vec.erase(first, oldest_aggregation_target_pos);
        }
      }
    });

    // 事後条件
    postcondition(max_publisher_ == output.size());
  }

  /**
   * @brief
   * 現在のオブジェクトに対して登録及びクエリが行われていないことを確認する
   * @details
   * 安全なシャットダウンを行うためのもの。この関数の外でこのオブジェクトへのアクセスを行わないようにした上でこの関数の呼び出しがリターンすると、このオブジェクトにアクセス中及び以降アクセスするスレッドは居なくなる
   */
  void Draining() & {
    using std::chrono_literals::operator""ms;

    precondition(push_counter_ < max_publisher_);

    if (is_fetching_and_deleting_ == true) {
      // クエリ処理の完了を待機
      std::unique_lock lock{mtx_};
      do {
        // is_fetching_and_deleting_のチェックからロック取得の間に終わってる可能性があるので、wait_for()を使用
        cv_.wait_for(lock, 50ms);
      } while (is_fetching_and_deleting_.load() == true);
    }

    if (push_counter_ != 0) {
      // 登録処理完了を待機
      do {
        std::this_thread::sleep_for(30ms);
      } while (0 < push_counter_);
    }

    postcondition(push_counter_ == 0);
    postcondition(is_fetching_and_deleting_ == false);
  }
};
}  // namespace t2::perception::radar

#undef SequenceContainer
