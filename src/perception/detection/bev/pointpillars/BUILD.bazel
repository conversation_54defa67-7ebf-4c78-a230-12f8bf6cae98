load("@rules_cuda//cuda:defs.bzl", "cuda_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "common",
    hdrs = ["common.hpp"],
)

cuda_library(
    name = "pointpillars_postprocess",
    srcs = ["pointpillars_postprocess.cu"],
    hdrs = ["pointpillars_postprocess.hpp"],
    deps = [
        ":common",
        ":nms_cuda",
    ],
)

cuda_library(
    name = "nms_cuda",
    srcs = ["nms_cuda.cu"],
    hdrs = ["nms_cuda.hpp"],
    deps = [
        ":common",
        "//src/common/cuda:cuda_utils",
    ],
)

cuda_library(
    name = "preprocess_points_cuda",
    srcs = ["preprocess_points_cuda.cu"],
    hdrs = ["preprocess_points_cuda.hpp"],
    deps = [
        ":common",
        "//src/common/cuda:cuda_utils",
    ],
)

cc_library(
    name = "pointpillars_utils",
    srcs = [
        "pointpillars_utils.cpp",
    ],
    hdrs = [
        "pointpillars_utils.hpp",
    ],
    deps = [
        "//src/interfaces/perception_msgs",
        "//src/perception/base:object",
        "//src/perception/detection/types:lidar_data",
        "@eigen",
    ],
)
