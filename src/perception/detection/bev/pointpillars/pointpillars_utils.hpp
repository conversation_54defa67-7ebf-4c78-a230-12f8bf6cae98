// Copyright (c) 2024-2025 T2 Inc. All rights reserved.

#pragma once

#include <vector>

#include <Eigen/Dense>

#include <perception_msgs/msg/perception_obstacles.hpp>

namespace t2::perception::detection {

void ToPerceptionObstacles(std::shared_ptr<perception_msgs::msg::PerceptionObstacles>& ret,
                           const Eigen::Affine3d& pose, const std::vector<float>& detections,
                           const std::vector<int>& labels, const std::vector<float>& scores,
                           const int num_output_box_feature,
                           const perception_msgs::msg::SensorType sensor_type);

}  // namespace t2::perception::detection
