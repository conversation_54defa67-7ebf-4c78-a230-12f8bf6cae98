# アルゴ説明資料

## EstimateOcclusion関数の処理内容

### 概要

LiDARで検出された車両の遮蔽（オクルージョン）を推定する処理を行う。

### In/Out

In

| In/Out | 変数名 | データ型                                           | 役割                       | 備考                           |
| ------ | ------ | -------------------------------------------------- | -------------------------- | ------------------------------ |
| In     | msg    | std::shared_ptr\<perception::PerceptionObstacles\> | Detection結果              |                                |
| In     | pose   | Eigen::Affine3d                                    | 自車のTransformMatrix      | TFと呼ばれているやつ           |
| In     | config | OcclusionFilterConfig                              | 遮蔽判定アルゴのパラメータ | 現在angle_expansion_degreeのみ |
| Out    | msg    | std::shared_ptr\<perception::PerceptionObstacles\> | Detection結果              | occludedフラグが更新される     |

### 大まかなアルゴリズム

検出された各オブジェクトの自車座標系における頂点の存在範囲（角度）を計算。  
オブジェクトペアのうち、遠いオブジェクトの自車からの角度が、より近いオブジェクトの存在範囲に重なっていた場合に遠い方のオブジェクトにoccludeフラグを付与する

### 主な処理フロー

1. 座標変換の準備
    - 自車の向き（ego_heading）を取得
    - 世界座標系から自車座標系への変換関数を定義
1. 障害物データの前処理
    - 各障害物に対して以下の処理を実行
      - 自車座標系への変換
      - 障害物のポリゴン頂点を取得
      - 各頂点の角度を計算（0-2πの範囲に正規化）
      - 角度の最大差分を計算し、0をまたぐかどうかを判定
      - 角度範囲の拡張（設定値に基づく）
1. 遮蔽判定
    - 自車からの距離で障害物をソート
    - 各障害物ペアに対して
      - 小さい物体（高さ1.0m未満）は遮蔽判定から除外
      - 近い物体が遠い物体を遮蔽するかどうかを判定
      - 角度範囲が重なる場合に遮蔽と判定
1. 結果の反映
    - 遮蔽された車両のフラグを設定

### 問題点

遮蔽する物体1つずつ遮蔽判定をしているため、以下のように2つのオブジェクトによりほとんど遮蔽されているケースでも、遮蔽される物体と自車中心を結ぶ直線が遮蔽されない場合はOcclude判定されない。
```
A: 遮蔽される物体
B: 遮蔽する物体1
C: 遮蔽する物体2
+: 自車中心
     [  A  ]
 [  B  ] [  C  ]
     [  +  ]
```

複数のオブジェクトを考慮した判定はアルゴや条件設定が複雑化するため、少しでも遮蔽されていたらOcclude判定するように切り替え、後段の挙動を調整する方が素直
