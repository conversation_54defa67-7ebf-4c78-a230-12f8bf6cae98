// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "multi_object_tracker.hpp"

#include <cmath>
#include <memory>
#include <random>

#include <google/protobuf/io/zero_copy_stream_impl.h>
#include <google/protobuf/text_format.h>

#include <perception_msgs/msg/perception_obstacle.hpp>
#include <perception_msgs/msg/perception_obstacles.hpp>

#include "gtest/gtest.h"
#include "src/common/config/load_proto_file.hpp"
#include "src/perception/tracking/proto/tracking_conf.pb.h"
#include "src/perception/tracking/utils/utils.hpp"

using t2::common::proto::LoadProtoFromText;

namespace t2::perception::tracking {

template <class T>
class RandomGenerator {
 public:
  RandomGenerator(T random_min, T random_max)
      : distr_(std::uniform_real_distribution<T>(random_min, random_max)) {
    std::random_device rd;
    eng_ = std::default_random_engine(rd());
  };

  T GenerateRandom() { return distr_(eng_); }

 private:
  std::uniform_real_distribution<T> distr_;
  std::default_random_engine eng_;
};

using PerceptionObstacle = MultiObjectTracker::PerceptionObstacle;
using PerceptionObstacles = MultiObjectTracker::PerceptionObstacles;

class MultiObjectTrackerTest : public ::testing::Test {
  TrackingConf tracking_conf_;

 public:
  void SetUp() override {
    ASSERT_TRUE(
        LoadProtoFromText(tracking_conf_, "src/perception/tracking/conf/tracking_conf.txtpb"));
  }

  const TrackingConf& GetTrackingConfig() { return tracking_conf_; }
};

// NOLINTBEGIN(cppcoreguidelines-avoid-magic-numbers,cppcoreguidelines-avoid-non-const-global-variables,cppcoreguidelines-owning-memory)
TEST_F(MultiObjectTrackerTest, Case1) {
  const auto& tracking_conf = GetTrackingConfig();

  // Dump the evaluation data
  const std::filesystem::path dump_directory = "tmp/MOT_evaluation_data_dump_test";
  {
    namespace fs = std::filesystem;

    if (fs::exists(dump_directory)) {
      fs::remove_all(dump_directory);
    }

    ASSERT_TRUE(fs::create_directories(dump_directory));
  }

  // Scope for post-destruction testing of MultiObjectTracker
  {
    MultiObjectTracker mot(tracking_conf, true, dump_directory);

    RandomGenerator<float> rg(-0.1, 0.1);

    constexpr double kDeltaTime = 0.1;
    constexpr std::size_t kNumIteration = 50;
    for (std::size_t idx = 0; idx < kNumIteration; ++idx) {
      PerceptionObstacles msg{};
      msg.header.sequence_number = idx;
      const double timestamp = static_cast<double>(idx) * kDeltaTime;
      msg.timestamp = timestamp;
      auto& obstacle = msg.perception_obstacle.emplace_back();
      obstacle.position.x = 10.0 + static_cast<double>(idx) * 2.0;
      obstacle.position.y = 10.0 + static_cast<double>(idx) * 2.0;
      obstacle.position.z = 0.0;
      obstacle.velocity.x = 2.0;
      obstacle.velocity.y = 2.0;
      obstacle.velocity.z = 0.0;
      obstacle.lane_id = "lane1";
      const float rn = rg.GenerateRandom();
      obstacle.theta = M_PI / 4 + rn;
      obstacle.length = 4.0;
      obstacle.width = 2.0;
      obstacle.height = 2.0;
      obstacle.type = perception_msgs::msg::ClassType::VEHICLE;
      obstacle.timestamp = timestamp;
      obstacle.confidence = 1.0;

      // execute MOT.
      const auto ret = mot.Update(msg);
      ASSERT_NEAR(timestamp, ret.timestamp, 1e-9);

      if (idx < 2) {
        // In the first 2 iteration MOT does not publish the result as the alive
        // count threshold is set to 2. See 'tracking_conf.pb.txt'
        ASSERT_EQ(ret.perception_obstacle.size(), 0);
      } else {
        // In the later iteration MOT should publish the result with only one
        // tracked object.
        ASSERT_EQ(ret.perception_obstacle.size(), 1);
        ASSERT_EQ(ret.perception_obstacle[0].id, 1);
      }
    }
  }

  namespace fs = std::filesystem;
  const fs::path dump_file = dump_directory / "tracking_result.txt";
  ASSERT_TRUE(fs::exists(dump_file));

  ASSERT_LE(1ull, fs::file_size(dump_file));

  // Delete the entire tmp directory
  fs::remove_all(dump_directory.parent_path());
}
// NOLINTEND(cppcoreguidelines-avoid-magic-numbers,cppcoreguidelines-avoid-non-const-global-variables,cppcoreguidelines-owning-memory)

struct ObstacleParameter {
  int sequence_num;
  double x_position;
  double y_postion;
  double x_velocity;
  double y_velocity;
  double theta = 0.0;
  ::perception_msgs::msg::ClassType obstacle_type = ::perception_msgs::msg::ClassType::VEHICLE;
  double length = 4.0;
  double width = 2.0;
  double height = 2.0;
  double local_x = 0.0;
  double local_y = 0.0;
  double local_theta = 0.0;
};

static constexpr double kDeltaTime = 0.1;

PerceptionObstacles MakeObstacle(const ObstacleParameter& param) {
  PerceptionObstacles msg{};

  msg.header.sequence_number = param.sequence_num;
  const double timestamp = static_cast<double>(param.sequence_num) * kDeltaTime;
  msg.timestamp = timestamp;

  auto& obstacle = msg.perception_obstacle.emplace_back();
  obstacle.position.x = param.x_position;
  obstacle.position.y = param.y_postion;
  obstacle.position.z = 0.0;
  obstacle.velocity.x = param.x_velocity;
  obstacle.velocity.y = param.y_velocity;
  obstacle.velocity.z = 0.0;
  obstacle.lane_id = "lane1";
  obstacle.theta = param.theta;
  obstacle.length = param.length;
  obstacle.width = param.width;
  obstacle.height = param.height;
  obstacle.type = param.obstacle_type;
  obstacle.timestamp = timestamp;
  obstacle.confidence = 1.0;
  obstacle.local_position.x = param.local_x;
  obstacle.local_position.y = param.local_y;
  return msg;
}

void ShowObstacleState(const ::perception_msgs::msg::PerceptionObstacle& obs) {
  T2_INFO << "-------object stat-------";
  T2_INFO << "position().x: " << obs.position.x;
  T2_INFO << "position().y: " << obs.position.y;
  T2_INFO << "velocity().x: " << obs.velocity.x;
  T2_INFO << "velocity().y: " << obs.velocity.y;
  T2_INFO << "theta: " << obs.theta;
}

TEST_F(MultiObjectTrackerTest, YawRotationSingularity) {
  const auto& tracking_conf = GetTrackingConfig();

  const std::filesystem::path dump_directory = tracking_conf.mot_dump_directory();
  // トラッキング結果は出力しないでおく
  MultiObjectTracker mot(tracking_conf, false, dump_directory);

  // 仮定メモ
  // UTM座標系のX軸正方向は東、Y軸正方向は北
  // Yaw = 0 [rad]は真東向き
  // Yawの範囲は [-pi, +pi] （端点の包含は不明
  // 正回転（+θ）は半時計回り、+pi/2回転は真北を向く

  // テスト概要
  // 1. 真西（X軸負方向）に4フレーム直進
  // 2. +178°のYawを入力
  // 3. このときの推定値をチェック
  // 4. -178°のYawを入力4フレーム継続
  // 5. このときの推定値をチェック
  // 角度差分の正しい値は-4°
  // 角度推定値は-178°くらいになるはず？

  constexpr double kVelocity = 2.0;  // [m/s]
  int sequence_num = 1;
  double x_position = 0.0;  // [m]
  double y_position = 0.0;  // [m]
  double x_velocity = 0.0;  // [m/s]
  double y_velocity = 0.0;  // [m/s]

  auto update_state = [&](double theta) -> void {
    ++sequence_num;
    x_velocity = std::cos(theta) * kVelocity;
    y_velocity = std::sin(theta) * kVelocity;
    x_position += x_velocity * kDeltaTime;
    y_position += y_velocity * kDeltaTime;
    T2_INFO << "=======update_state==========";
    T2_INFO << "x_velocity: " << x_velocity;
    T2_INFO << "y_velocity: " << y_velocity;
    T2_INFO << "x_position: " << x_position;
    T2_INFO << "y_position: " << y_position;
    T2_INFO << "theta: " << theta;
  };
  update_state(M_PI);

  // とりあえず3フレーム入力
  for ([[maybe_unused]] auto _ : {1, 2, 3}) {
    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = x_position,
                             .y_postion = y_position,
                             .x_velocity = x_velocity,
                             .y_velocity = y_velocity,
                             .theta = M_PI});

    // execute MOT.
    [[maybe_unused]] const auto ret = mot.Update(msg);
    if (ret.perception_obstacle.size() != 0) {
      ShowObstacleState(ret.perception_obstacle.at(0));
    }
    update_state(M_PI);
  }

  // 4フレーム目
  {
    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = x_position,
                             .y_postion = y_position,
                             .x_velocity = x_velocity,
                             .y_velocity = y_velocity,
                             .theta = M_PI});

    // execute MOT.
    const auto ret = mot.Update(msg);

    ASSERT_EQ(ret.perception_obstacle.size(), 1);
    // idの生成元はstatic変数なので、一連のテストで共有されている
    ASSERT_EQ(ret.perception_obstacle.at(0).id, 2);
    ShowObstacleState(ret.perception_obstacle.at(0));

    update_state(M_PI);
  }

  // 5フレーム目（+178°のYawを入力
  {
    // +178°のYaw値 [rad]
    constexpr double kYaw178Degree = 178.0 * M_PI / 180.0;

    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = x_position,
                             .y_postion = y_position,
                             .x_velocity = x_velocity,
                             .y_velocity = y_velocity,
                             .theta = kYaw178Degree});

    // execute MOT.
    const auto ret = mot.Update(msg);

    ASSERT_EQ(ret.perception_obstacle.size(), 1);
    ASSERT_EQ(ret.perception_obstacle.at(0).id, 2);

    const auto& obs = ret.perception_obstacle.at(0);

    // 178.0 <= theta <= 180.0
    ASSERT_LE(kYaw178Degree, obs.theta);
    ASSERT_LE(obs.theta, M_PI);

    update_state(kYaw178Degree);
  }

  // 6フレーム目（-178°のYawを入力
  {
    // -178°のYaw値 [rad]
    constexpr double kYaw178DegreeCW = -178.0 * M_PI / 180.0;

    // とりあえず3フレーム入力
    for ([[maybe_unused]] auto _ : {1, 2, 3}) {
      auto msg = MakeObstacle({.sequence_num = sequence_num,
                               .x_position = x_position,
                               .y_postion = y_position,
                               .x_velocity = x_velocity,
                               .y_velocity = y_velocity,
                               .theta = kYaw178DegreeCW});

      // execute MOT.
      const auto ret = mot.Update(msg);
      ShowObstacleState(ret.perception_obstacle.at(0));

      // idの生成元はstatic変数なので、一連のテストで共有されている
      ASSERT_EQ(ret.perception_obstacle.at(0).id, 2);

      update_state(kYaw178DegreeCW);
    }

    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = x_position,
                             .y_postion = y_position,
                             .x_velocity = x_velocity,
                             .y_velocity = y_velocity,
                             .theta = kYaw178DegreeCW});

    // execute MOT.
    const auto ret = mot.Update(msg);

    ASSERT_EQ(ret.perception_obstacle.size(), 1);
    // idの生成元はstatic変数なので、一連のテストで共有されている
    ASSERT_EQ(ret.perception_obstacle.at(0).id, 2);

    ShowObstacleState(ret.perception_obstacle.at(0));

    const auto& obs = ret.perception_obstacle.at(0);

    // -180.0 <= theta <= -178.0
    ASSERT_LE(-M_PI, obs.theta);
    ASSERT_LE(obs.theta, kYaw178DegreeCW);

    update_state(kYaw178DegreeCW);
  }
}

TEST_F(MultiObjectTrackerTest, BikeTrackerTest) {
  const auto& tracking_conf = GetTrackingConfig();

  const std::filesystem::path dump_directory = tracking_conf.mot_dump_directory();
  // トラッキング結果は出力しないでおく
  MultiObjectTracker mot(tracking_conf, false, dump_directory);

  int sequence_num = 1;

  // とりあえず3フレーム入力（トラッキング開始まで2フレームかかる
  for (auto pos : {1.0, 1.1, 1.2}) {
    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = pos,
                             .y_postion = 0.0,
                             .x_velocity = 1.0,
                             .y_velocity = 0.0,
                             .theta = 0.0,
                             .obstacle_type = ::perception_msgs::msg::ClassType::BICYCLE});

    // execute MOT.
    auto ret = mot.Update(msg);

    ++sequence_num;
  }

  // その後2フレーム入力してテスト
  for (auto pos : {1.3, 1.4}) {
    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = pos,
                             .y_postion = 0.0,
                             .x_velocity = 1.0,
                             .y_velocity = 0.0,
                             .theta = 0.0,
                             .obstacle_type = ::perception_msgs::msg::ClassType::BICYCLE});

    // execute MOT.
    const auto ret = mot.Update(msg);
    ASSERT_EQ(ret.perception_obstacle.size(), 1);
    ASSERT_EQ(ret.perception_obstacle.at(0).type, ::perception_msgs::msg::ClassType::BICYCLE);
    // idの生成元はstatic変数なので、一連のテストで共有されている
    ASSERT_EQ(ret.perception_obstacle.at(0).id, 3);

    ++sequence_num;
  }
}

TEST_F(MultiObjectTrackerTest, Height0BikeTrackerTest) {
  const auto& tracking_conf = GetTrackingConfig();

  const std::filesystem::path dump_directory = tracking_conf.mot_dump_directory();
  // トラッキング結果は出力しないでおく
  MultiObjectTracker mot(tracking_conf, false, dump_directory);

  int sequence_num = 1;

  // とりあえず3フレーム入力（トラッキング開始まで2フレームかかる
  for (auto pos : {1.0, 1.1, 1.2}) {
    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = pos,
                             .y_postion = 0.0,
                             .x_velocity = 1.0,
                             .y_velocity = 0.0,
                             .theta = 0.0,
                             .obstacle_type = ::perception_msgs::msg::ClassType::BICYCLE,
                             .height = 0.0});

    // execute MOT.
    mot.Update(msg);

    ++sequence_num;
  }

  // その後2フレーム入力してテスト
  for (auto pos : {1.3, 1.4}) {
    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = pos,
                             .y_postion = 0.0,
                             .x_velocity = 1.0,
                             .y_velocity = 0.0,
                             .theta = 0.0,
                             .obstacle_type = ::perception_msgs::msg::ClassType::BICYCLE,
                             .height = 0.0});

    // execute MOT.
    const auto ret = mot.Update(msg);
    ASSERT_EQ(ret.perception_obstacle.size(), 1);
    ASSERT_EQ(ret.perception_obstacle.at(0).type, ::perception_msgs::msg::ClassType::BICYCLE);
    // idの生成元はstatic変数なので、一連のテストで共有されている
    ASSERT_EQ(ret.perception_obstacle.at(0).id, 4);

    ++sequence_num;
  }
}

TEST_F(MultiObjectTrackerTest, ShapeFilterTest) {
  const auto& tracking_conf = GetTrackingConfig();

  const std::filesystem::path dump_directory = tracking_conf.mot_dump_directory();
  // トラッキング結果は出力しないでおく
  MultiObjectTracker mot(tracking_conf, false, dump_directory);

  int sequence_num = 1;
  const double initial_w = 1.8;
  const double changed_w = 2.0;
  for (const auto w : {initial_w, initial_w, initial_w}) {
    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = 0.0,
                             .y_postion = 0.0,
                             .x_velocity = 0.0,
                             .y_velocity = 0.0,
                             .theta = 0.0,
                             .obstacle_type = ::perception_msgs::msg::ClassType::VEHICLE,
                             .length = 4.0,
                             .width = w,
                             .height = 0.0});

    // execute MOT.
    mot.Update(msg);
    ++sequence_num;
  }

  for (const auto w : {changed_w, changed_w}) {
    auto msg = MakeObstacle({.sequence_num = sequence_num,
                             .x_position = 0.0,
                             .y_postion = 0.0,
                             .x_velocity = 1.0,
                             .y_velocity = 0.0,
                             .theta = 0.0,
                             .obstacle_type = ::perception_msgs::msg::ClassType::VEHICLE,
                             .length = 4.0,
                             .width = w,
                             .height = 0.0});

    // execute MOT.
    const auto ret = mot.Update(msg);
    ASSERT_EQ(ret.perception_obstacle.size(), 1);
    // width value check. current width should be greater than initial val and
    // smaller than latest measurement
    ASSERT_TRUE(ret.perception_obstacle.at(0).width >= initial_w);
    ASSERT_TRUE(ret.perception_obstacle.at(0).width <= changed_w);

    ++sequence_num;
  }
}

TEST_F(MultiObjectTrackerTest, VisibleSurfaceSelfOcclusionTest) {
  int sequence_num = 1;
  auto msg = MakeObstacle({.sequence_num = sequence_num,
                           .x_position = 0.0,
                           .y_postion = 0.0,
                           .x_velocity = 0.0,
                           .y_velocity = 0.0,
                           .theta = 0.0,
                           .obstacle_type = ::perception_msgs::msg::ClassType::VEHICLE,
                           .length = 5.0,
                           .width = 2.0,
                           .height = 1.5,
                           .local_x = 10.0,
                           .local_y = 0.0,
                           .local_theta = 0.0});

  auto msg2 = MakeObstacle({.sequence_num = sequence_num,
                            .x_position = 0.0,
                            .y_postion = 0.0,
                            .x_velocity = 0.0,
                            .y_velocity = 0.0,
                            .theta = 0.0,
                            .obstacle_type = ::perception_msgs::msg::ClassType::VEHICLE,
                            .length = 5.0,
                            .width = 2.0,
                            .height = 1.5,
                            .local_x = 50.0,
                            .local_y = 0.0,
                            .local_theta = 0.0});

  const auto target = t2::perception::tracking::ToLocalPolygon2d(msg.perception_obstacle.at(0));
  const auto obstacle = t2::perception::tracking::ToLocalPolygon2d(msg2.perception_obstacle.at(0));

  // target object is located in straight forward. ony rear is visible
  auto visibility = ComputeVisibleSurface(target, obstacle);

  ASSERT_FALSE(visibility[0]);  // left surface
  ASSERT_TRUE(visibility[1]);   // rear
  ASSERT_FALSE(visibility[2]);  // right
  ASSERT_FALSE(visibility[3]);  // front

  // swap object, all corner is not visible
  visibility = ComputeVisibleSurface(obstacle, target);
  ASSERT_FALSE(visibility[0]);  // left surface
  ASSERT_FALSE(visibility[1]);  // rear
  ASSERT_FALSE(visibility[2]);  // right
  ASSERT_FALSE(visibility[3]);  // front

  // check flip object visibility is Not changed
  auto msg3 = MakeObstacle({.sequence_num = sequence_num,
                            .x_position = 0.0,
                            .y_postion = 0.0,
                            .x_velocity = 0.0,
                            .y_velocity = 0.0,
                            .theta = 0.0,
                            .obstacle_type = ::perception_msgs::msg::ClassType::VEHICLE,
                            .length = 5.0,
                            .width = 2.0,
                            .height = 1.5,
                            .local_x = 10.0,
                            .local_y = 10.0,
                            .local_theta = M_PI});
  auto msg4 = MakeObstacle({.sequence_num = sequence_num,
                            .x_position = 0.0,
                            .y_postion = 0.0,
                            .x_velocity = 0.0,
                            .y_velocity = 0.0,
                            .theta = 0.0,
                            .obstacle_type = ::perception_msgs::msg::ClassType::VEHICLE,
                            .length = 5.0,
                            .width = 2.0,
                            .height = 1.5,
                            .local_x = 10.0,
                            .local_y = 10.0,
                            .local_theta = 0.0});
  const auto target_flip =
      t2::perception::tracking::ToLocalPolygon2d(msg3.perception_obstacle.at(0));
  const auto target_no_flip =
      t2::perception::tracking::ToLocalPolygon2d(msg4.perception_obstacle.at(0));
  const auto visiblity_flip = ComputeVisibleSurface(target_flip, obstacle);
  visibility = ComputeVisibleSurface(target_no_flip, obstacle);
  for (std::size_t i = 0; i < visibility.size(); ++i) {
    ASSERT_EQ(visibility[i], visiblity_flip[i]);
  }
}
}  // namespace t2::perception::tracking
