// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "load.hpp"

#include <perception_msgs/msg/perception_obstacle.hpp>

namespace t2::perception::tracking {

bool LoadSingleMatrix(std::ifstream& fin, Eigen::MatrixXd* matrix) {
  const std::size_t max_obj_type =
      static_cast<std::size_t>(perception_msgs::msg::ClassType::MAX_OBJECT_TYPE);

  *matrix = Eigen::MatrixXd::Zero(max_obj_type, max_obj_type);
  for (size_t row = 0; row < max_obj_type; ++row) {
    for (size_t col = 0; col < max_obj_type; ++col) {
      fin >> (*matrix)(row, col);
    }
  }
  return true;
}

bool LoadSingleMatrixFile(const std::string& filename, Eigen::MatrixXd* matrix) {
  if (matrix == nullptr) {
    return false;
  }
  std::ifstream fin(filename);
  if (!fin.is_open()) {
    return false;
  }
  LoadSingleMatrix(fin, matrix);
  fin.close();
  return true;
}

bool LoadMultipleMatricesFile(const std::string& filename,
                              EigenMap<std::string, Eigen::MatrixXd>* matrices) {
  if (matrices == nullptr) {
    return false;
  }
  std::ifstream fin(filename);
  if (!fin.is_open()) {
    return false;
  }
  matrices->clear();
  size_t num = 0;
  fin >> num;
  if (num > 100) {
    fin.close();
    return false;
  }
  for (size_t i = 0; i < num; ++i) {
    std::string name;
    fin >> name;
    Eigen::MatrixXd matrix;
    LoadSingleMatrix(fin, &matrix);
    matrices->emplace(name, matrix);
  }
  fin.close();
  return true;
}
}  // namespace t2::perception::tracking
