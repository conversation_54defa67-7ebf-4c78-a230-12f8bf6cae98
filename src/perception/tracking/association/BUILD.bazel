load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "data_association",
    srcs = [
        "cbiou_association.cpp",
    ],
    hdrs = [
        "association.hpp",
        "cbiou_association.hpp",
    ],
    deps = [
        "//src/common/core",
        "//src/common/math:polygon2d",
        "//src/interfaces/perception_msgs",
        "//src/perception/common/graph:gated_hungarian_bigraph_matcher",
        "//src/perception/common/graph:secure_matrix",
        "//src/perception/tracking/proto:tracking_conf_cc_proto",
        "//src/perception/tracking/tracker",
        "//src/perception/tracking/utils:load_utils",
    ],
)
