// Copyright (c) 2024-2025 T2 Inc. All rights reserved

#include "cbiou_association.hpp"

#include <cstddef>
#include <iomanip>
#include <numeric>
#include <unordered_map>

#include "Eigen/Core"
#include "src/common/core/logging.hpp"
#include "src/perception/tracking/utils/load.hpp"
#include "src/perception/tracking/utils/utils.hpp"

namespace t2::perception::tracking {

namespace {
using PerceptionObstacle = ::perception_msgs::msg::PerceptionObstacle;

struct TrackStruct {
  PerceptionObstacle msg;
  ::t2::common::math::Polygon2d polygon;
};

float ComputeCost(const TrackerBase& track, const PerceptionObstacle& detect,
                  const float invalid_cost, const Eigen::MatrixXd& class_transition_matrix,
                  const Eigen::MatrixXd& max_dist_matrix, const TrackStruct& track_struct,
                  const float buffer) {
  float cost = 0.0;
  const Eigen::Vector2d detect_pos(detect.position.x, detect.position.y);

  const std::size_t detect_type = static_cast<std::size_t>(detect.type);
  const std::size_t track_type = static_cast<std::size_t>(track.Type());

  if (static_cast<int>(class_transition_matrix(detect_type, track_type)) == 0) {
    return invalid_cost;
  }
  const auto& track_msg = track_struct.msg;
  const auto& track_polygon = track_struct.polygon;

  const Eigen::Vector2d track_pos = track.PositionXY();
  const auto center_dist = static_cast<float>(
      std::hypot(track_pos.x() - detect_pos.x(), track_pos.y() - detect_pos.y()));
  constexpr double kCenterDistanceThreshold = 15.0;
  if (center_dist > kCenterDistanceThreshold) {
    return invalid_cost;
  }

  bool is_valid = true;
  float dist = 0.0;
  if (detect.width > 0.0 && detect.height > 0.0 && track_msg.width > 0.0 &&
      track_msg.height > 0.0) {
    // Distance between centers
    dist = center_dist;
    if (dist > max_dist_matrix(detect_type, track_type)) {
      return invalid_cost;
    } else {
      cost += dist;
    }
    constexpr double kPercentile95 = 2.448;
    const auto mahalanobis_dist =
        ComputeMahalanobisDistance(detect_pos, track_pos, track.PositionXYCovariance());
    const bool outside_percentile = mahalanobis_dist > kPercentile95;
    constexpr double kIoUThreshold = 0.1;
    // IsMovingObjectOverlap()内でtrack_msg.type()のチェックがある
    const bool is_moving_object_overlap =
        IsMovingObjectOverlap(detect, track_msg, kIoUThreshold, buffer);
    if (outside_percentile && !is_moving_object_overlap) {
      is_valid = false;
      // NOLINTNEXTLINE
      T2_DEBUG << "as1: " << track.ID() << ", " << dist << ", " << outside_percentile << ", "
               << ComputeIoU(detect, track_msg);
    }
  } else {
    // ここのパスに来るのは実質トラッカー/検出bboxのheightが0以下になってる場合のみ
    // ここでのハンドリングや処理には不明瞭な点が多いので調査とリファクタリングが必要

    const auto detect_polygon = ToPolygon2d(detect, 1.0F, 0.0F, buffer);

    if (detect_polygon.num_points() == 0) {
      return invalid_cost;
    }

    // Distance from the edge
    // Nearest point distance to the tracking polygon?
    // TODO(someone): Edge distance threshold
    constexpr double kEdgeDistanceThershold = 3.0;
    dist = static_cast<float>(detect_polygon.DistanceTo(track_polygon));

    const bool should_compute_iou = [tracker_type = detect.type,
                                     detection_type = track.Type()]() -> bool {
      using ObstacleType = perception_msgs::msg::ClassType;

      // 車トラッカーのassociation実行
      if (tracker_type == ObstacleType::VEHICLE) {
        if (detection_type == ObstacleType::VEHICLE || detection_type == ObstacleType::UNKNOWN) {
          return true;
        } else {
          return false;
        }
      }

      // バイクトラッカーのassociation実行
      if (tracker_type == ObstacleType::BICYCLE) {
        if (detection_type == ObstacleType::BICYCLE || detection_type == ObstacleType::UNKNOWN) {
          return true;
        } else {
          return false;
        }
      }

      // TODO(someone): Type validation
      // UNKNOWN_MOVABLEは無視でいいのか、PEDESTRIANへの対応など
      return false;
    }();

    double iou = 0.0;
    if (should_compute_iou) {
      // TODO(someone): If yaw angle is estimated by the motion, we might be
      // able to construct bounding box with the pre-determined size for the
      // VEHICLE type.
      if ((track_msg.length > 0.0 && track_msg.height > 0.0) || track.AliveCount() > 1) {
        const auto extended_det_box = ExtendBoundingBoxWithTarget(detect, track_msg);
        iou = ComputeIoU(extended_det_box, track_msg);
      }
    }

    T2_INFO << "Track id: " << track.ID() << ", " << detect.id << ", " << dist << " < "
            << max_dist_matrix(detect_type, track_type) << ", " << detect_type << ", " << track_type
            << ", iou: " << iou;

    constexpr double kIoUThreshold = 0.1;
    if ((iou < kIoUThreshold && dist > max_dist_matrix(detect_type, track_type)) ||
        dist > kEdgeDistanceThershold) {
      return invalid_cost;
    } else {
      cost += dist;
    }

    T2_INFO << "as2: " << track.ID() << ", " << dist << ", " << center_dist << ", " << iou;

    // TODO(someone): Tune
    constexpr double kPolygonAdditionalCost = 4.0;
    cost += kPolygonAdditionalCost;
  }
  if (!is_valid && track.AliveCount() > 2 && dist > 1.0) {
    return invalid_cost;
  }
  return cost;
}
}  // namespace

CBIoUAssociator::CBIoUAssociator(const ::t2::perception::tracking::DataAssociationConf& config)
    : Associator(),
      score_cost_thres_{config.score_cost_thres()},
      high_confidence_threshold_{config.high_confidence_thres()},
      low_confidence_threshold_{config.low_confidence_thres()} {
  ::t2::perception::tracking::EigenMap<std::string, Eigen::MatrixXd> property_map;
  if (!LoadMultipleMatricesFile(config.property_file(), &property_map)) {
    throw std::runtime_error("DataAssociation property file has problems");
  }
  Eigen::MatrixXd class_transition_matrix;
  Eigen::MatrixXd max_dist_matrix;
  for (const auto& prop : property_map) {
    if (prop.first == "class_transition") {
      class_transition_matrix = prop.second;
    } else if (prop.first == "max_dist") {
      max_dist_matrix = prop.second;
    }
  }

  const std::size_t max_obj_type =
      static_cast<std::size_t>(perception_msgs::msg::ClassType::MAX_OBJECT_TYPE);
  if (class_transition_matrix.rows() != max_obj_type ||
      class_transition_matrix.cols() != max_obj_type || max_dist_matrix.rows() != max_obj_type ||
      max_dist_matrix.cols() != max_obj_type) {
    throw std::runtime_error("DataAssociation property data has wrong shape");
  }
  Init(class_transition_matrix, max_dist_matrix);
}

void CBIoUAssociator::Init(const Eigen::MatrixXd& class_transition_matrix,
                           const Eigen::MatrixXd& max_dist_matrix) {
  class_transition_matrix_ = class_transition_matrix;
  max_dist_matrix_ = max_dist_matrix;

  T2_INFO << "Class transition matrix\n" << class_transition_matrix_;
  T2_INFO << "Max dist matrix\n" << max_dist_matrix_;

  constexpr std::size_t kReserveHight = 1000;
  constexpr std::size_t kReserveWidth = 1000;
  matcher_.mutable_global_costs()->Reserve(kReserveHight, kReserveWidth);

  T2_INFO << "Initialization of C-BIoU associator is finished";
}

DataAssociationResult CBIoUAssociator::Associate(
    const std::vector<std::unique_ptr<TrackerBase>>& tracked_objects,
    const PerceptionObstacles& detected_objects) {
  const size_t detection_size = detected_objects.perception_obstacle.size();

  if (tracked_objects.size() == 0 || detection_size == 0) {
    std::vector<size_t> unassociated_detected_indexes(detection_size);
    std::iota(unassociated_detected_indexes.begin(), unassociated_detected_indexes.end(), 0);

    return DataAssociationResult({}, std::move(unassociated_detected_indexes));
  }

  std::vector<std::size_t> high_confident_detected_indices;
  high_confident_detected_indices.reserve(detection_size);

  std::vector<std::size_t> low_confident_detected_indices;
  low_confident_detected_indices.reserve(detection_size);

  for (size_t didx = 0; didx < detection_size; ++didx) {
    const float confidence =
        static_cast<float>(detected_objects.perception_obstacle[didx].confidence);
    if (confidence > high_confidence_threshold_) {
      high_confident_detected_indices.push_back(didx);
    } else if (confidence > low_confidence_threshold_) {
      low_confident_detected_indices.push_back(didx);
    }
  }

  std::vector<std::size_t> tracked_indices(tracked_objects.size());
  std::iota(tracked_indices.begin(), tracked_indices.end(), 0);

  const auto cbiou_first_result = AssociateWithBuffer(
      tracked_objects, detected_objects, high_confident_detected_indices, tracked_indices, 0.0F);

  // Merge unmatched detections into low confident detections
  auto unmatched_detected_indices = cbiou_first_result.unassociated_detected_indices;
  unmatched_detected_indices.insert(unmatched_detected_indices.end(),
                                    low_confident_detected_indices.begin(),
                                    low_confident_detected_indices.end());

  // Try association with unmatched tracks and unmatched detections
  // for second match with larger buffer.
  const auto cbiou_second_result =
      AssociateWithBuffer(tracked_objects, detected_objects, unmatched_detected_indices,
                          cbiou_first_result.unassociated_tracked_indices, 1.0F);

  // merge two results
  std::vector<std::pair<std::size_t, std::size_t>> detection_to_tracking;
  for (const auto& pair : cbiou_first_result.detected_id_to_tracked_id) {
    detection_to_tracking.emplace_back(pair);
  }
  for (const auto& pair : cbiou_second_result.detected_id_to_tracked_id) {
    detection_to_tracking.emplace_back(pair);
  }

  // We need to convert the indices to the original indices.
  // The cbiou_second_result.unassociated_detected_indices points indices
  // in the cbiou_first_result.unassociated_detected_indices that passed to
  // AssociatedWithBuffer call.
  std::vector<std::size_t> last_unmatched_detections;
  for (const auto& didx : cbiou_second_result.unassociated_detected_indices) {
    last_unmatched_detections.emplace_back(unmatched_detected_indices[didx]);
  }

  return DataAssociationResult(std::move(detection_to_tracking),
                               std::move(last_unmatched_detections));
}

CBIoUAssociateResult CBIoUAssociator::AssociateWithBuffer(
    const std::vector<std::unique_ptr<TrackerBase>>& tracked_objects,
    const PerceptionObstacles& detected_objects, const std::vector<std::size_t>& detected_indices,
    const std::vector<std::size_t>& tracked_indices, const float buffer) {
  const size_t detection_size = detected_indices.size();
  const size_t tracked_object_size = tracked_indices.size();

  if (tracked_object_size == 0 || detection_size == 0) {
    std::vector<std::size_t> d(detection_size);
    std::vector<std::size_t> t(tracked_object_size);
    std::iota(d.begin(), d.end(), 0);
    std::iota(t.begin(), t.end(), 0);
    return CBIoUAssociateResult({}, std::move(d), std::move(t));
  }

  // Compute distance matrix between tracked object and detected objects
  auto cost_matrix = matcher_.mutable_global_costs();
  cost_matrix->Resize(detection_size, tracked_object_size);
  const float invalid_cost = score_cost_thres_ * 2.0f;

  std::unordered_map<std::size_t, TrackStruct> track_polygon_list;
  track_polygon_list.reserve(tracked_object_size);
  for (std::size_t tidx = 0; tidx < tracked_object_size; ++tidx) {
    const auto tidx_map = tracked_indices[tidx];
    const auto msg = tracked_objects[tidx_map]->GeneratePerceptionObstacle();
    track_polygon_list.insert({tidx_map, TrackStruct{msg, ToPolygon2d(msg, 1.0F, 0.0F, buffer)}});
  }

  for (std::size_t didx = 0; didx < detection_size; ++didx) {
    const auto didx_map = detected_indices[didx];
    const auto& detect = detected_objects.perception_obstacle[didx_map];
    for (std::size_t tidx = 0; tidx < tracked_object_size; ++tidx) {
      const auto tidx_map = tracked_indices[tidx];
      const auto& track = tracked_objects[tidx_map];
      const auto& track_struct = track_polygon_list[tidx_map];
      (*cost_matrix)(didx, tidx) =
          ComputeCost(*track, detect, invalid_cost, class_transition_matrix_, max_dist_matrix_,
                      track_struct, buffer);
    }
  }

  std::vector<std::pair<size_t, size_t>> detected_to_tracked_map;
  std::vector<size_t> unassociated_detected_indexes;
  std::vector<size_t> unassociated_tracked_indexes;
  matcher_.Match(score_cost_thres_,
                 ::t2::perception::common::GatedHungarianMatcher<float>::OptimizeFlag::OPTMIN,
                 &detected_to_tracked_map, &unassociated_detected_indexes,
                 &unassociated_tracked_indexes);

  std::vector<std::pair<size_t, size_t>> d_to_t_map;

  for (const auto& d_to_t : detected_to_tracked_map) {
    d_to_t_map.emplace_back(detected_indices[d_to_t.first], tracked_indices[d_to_t.second]);
  }

  return CBIoUAssociateResult(std::move(d_to_t_map), std::move(unassociated_detected_indexes),
                              std::move(unassociated_tracked_indexes));
}

const CBIoUAssociator::SecureMat& CBIoUAssociator::GetCostMatrix() const noexcept {
  return this->matcher_.global_costs();
}

}  // namespace t2::perception::tracking
