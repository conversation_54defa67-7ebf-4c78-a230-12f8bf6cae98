// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "string.hpp"

#include <algorithm>

namespace t2::perception::tools::deploy_util {

std::vector<std::string> Split(const std::string& s, const char delimiter) {
  std::vector<std::string> tokens;
  std::istringstream iss(s);
  std::string token;
  while (std::getline(iss, token, delimiter)) {
    tokens.push_back(token);
  }
  return tokens;
}

std::string Trim(const std::string& str) {
  const std::string whitespace = " \n\r\t\f\v";
  auto trim_l = [whitespace](const std::string& s) {
    size_t start = s.find_first_not_of(whitespace);
    return (start == std::string::npos) ? "" : s.substr(start);
  };
  auto trim_r = [whitespace](const std::string& s) {
    size_t end = s.find_last_not_of(whitespace);
    return (end == std::string::npos) ? "" : s.substr(0, end + 1);
  };
  return trim_r(trim_l(str));
}

std::string Replace(std::string str, const std::string& from, const std::string& to) {
  std::string result = str;
  if (!from.empty()) {
    size_t start_pos = 0;
    while ((start_pos = result.find(from, start_pos)) != std::string::npos) {
      result.replace(start_pos, from.length(), to);
      start_pos += to.length();
    }
  }
  return result;
}

bool IsDigit(const std::string& str) {
  return !str.empty() && std::all_of(str.begin(), str.end(), ::isdigit);
}

}  // namespace t2::perception::tools::deploy_util
