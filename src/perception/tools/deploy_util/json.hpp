// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <nlohmann/json.hpp>

#include "logger.hpp"
#include "string.hpp"

namespace t2::perception::tools::deploy_util {
using json = nlohmann::json;

json ReadJson(const std::string& filename);
void WriteJson(const json& src_json, const std::string& filename, const int indent = 2);

/**
 * @brief Safely retrieves a value from a JSON object using a sequence of keys
 * @details This function traverses a JSON object using the provided keys and
 * returns the value at the final location. If any key in the sequence doesn't
 * exist or the value cannot be converted to the specified type, it returns a
 *          default-constructed value of type T.
 * @tparam T The type of the value to retrieve
 * @tparam Keys Types of the keys (usually string or integer number to access
 * elements in array)
 * @param j The JSON object to extract the value from
 * @param keys Variable number of keys to access nested JSON values
 * @return The extracted value of type T, or a default-constructed value if
 * extraction fails
 * @note This function is used internally by GET_JSON_VAL macro
 */
template <typename T, typename... Keys>
T GetJsonValue(const json& j, const Keys... keys) {
  try {
    const json* current = &j;
    ((current = &current->at(keys)), ...);
    return current->get<T>();
  } catch (...) {
    return T();
  }
}

// return first element in array_j that satisfies element_j[key] == val
template <typename T>
json GetJsonElement(const json& array_j, const std::string& key, const T& val) {
  json dst;
  for (const auto& element_j : array_j) {
    if (element_j[key].get<T>() == val) {
      dst = element_j;
      break;
    }
  }
  return dst;
}

};  // namespace t2::perception::tools::deploy_util

/**
 * @brief Macro to safely retrieve a JSON value with error logging
 * @details This macro creates a lambda function that safely retrieves a value
 * from a JSON object. If the key doesn't exist or the value cannot be converted
 * to the specified type, it logs an error message and returns an empty value.
 * @param json_obj The JSON object to extract the value from
 * @param ... Variable number of keys to access nested JSON values
 * @return The extracted JSON value, or an empty JSON object if extraction fails
 * @note This macro is a wrapper around GetJsonValue that adds error logging
 */
// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define GET_JSON_VAL(json_obj, ...)                                                              \
  ([&]() {                                                                                       \
    const auto val_j =                                                                           \
        t2::perception::tools::deploy_util::GetJsonValue<nlohmann::json>(json_obj, __VA_ARGS__); \
    if (val_j.empty()) {                                                                         \
      LOG_ERROR("cannot get json value: `{}`",                                                   \
                t2::perception::tools::deploy_util::GetKeyString(__VA_ARGS__));                  \
    }                                                                                            \
    return val_j;                                                                                \
  }())
