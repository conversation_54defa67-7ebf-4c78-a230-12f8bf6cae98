// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include "sensor_msgs/msg/point_cloud2.hpp"
#include "src/common/transform/transform.hpp"
#include "src/perception/preprocessor/point_cloud/common/utils.hpp"
#include "src/perception/preprocessor/point_cloud/proto/preprocessor_conf.pb.h"

namespace t2::preprocessor {

class PointCloudPreprocessor {
 public:
  explicit PointCloudPreprocessor(const PreprocessSharedConf& config);
  ~PointCloudPreprocessor() = default;
  PointCloudPreprocessor(const PointCloudPreprocessor&) = delete;
  PointCloudPreprocessor& operator=(const PointCloudPreprocessor&) = delete;
  PointCloudPreprocessor(PointCloudPreprocessor&&) = delete;
  PointCloudPreprocessor& operator=(PointCloudPreprocessor&&) = delete;

  bool CropAndRemove(const Eigen::Vector3f& point) const;

  bool Apply(sensor_msgs::msg::PointCloud2::BorrowedType& output_pcd,
             const sensor_msgs::msg::PointCloud2& input_pcd,
             const t2::common::transform::TransformStamped& sensor_pose);

 private:
  std::vector<RemoveFunctor> remove_functors_;
  std::vector<CropFunctor> crop_functors_;
};
}  // namespace t2::preprocessor
