cc_library(
    name = "core",
    srcs = [
        "logging.cpp",
    ],
    hdrs = [
        "chrono_consts.hpp",
        "logging.hpp",
        "span.hpp",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_google_glog//:glog",
    ],
)

cc_test(
    name = "core_test",
    srcs = [
        "test/span_test.cpp",
    ],
    deps = [
        ":core",
        "@googletest//:gtest_main",
    ],
)
