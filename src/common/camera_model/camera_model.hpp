// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once
#include <memory>

#include "Eigen/Eigen"
#include "image_msgs/msg/intrinsics.hpp"

// Eigen 3.3.7: #define ALIVE (0)
// fastrtps: enum ChangeKind_t { ALIVE, ... };
#if defined(ALIVE)
#undef ALIVE
#endif
namespace t2 {
namespace common {
namespace camera_model {

/**
 * @brief Abstract camera models with various mapping.
 *
 */
class CameraModel {
 public:
  CameraModel(size_t width, size_t height) : width_(width), height_(height) {}
  virtual ~CameraModel() {}

  /**
   * @brief project a 3D point to a 2D point.
   * @param p 3D point to be projected.
   * @return Projected 2D point.
   */
  virtual Eigen::Vector2f project(const Eigen::Vector3f& p) = 0;

  /**
   * @brief unproject a 2D point to a 3D point.
   * @param uv 2D point to be unprojected.
   * @return Unprojected 3D point.
   */
  virtual Eigen::Vector3f unproject(const Eigen::Vector2f& uv) = 0;

  /**
   * @brief scale the camera model.
   * @param rx Scale factor in x direction.
   * @param ry Scale factor in y direction.
   * @return Shared pointer to the scaled camera model.
   */
  virtual std::shared_ptr<CameraModel> scale(float rx, float ry) const = 0;

  /**
   * @brief resize the camera model with a new width and height.
   * @param width New width of the camera model.
   * @param height New height of the camera model.
   * @return Shared pointer to the resized camera model.
   */
  std::shared_ptr<CameraModel> resize(size_t width, size_t height) const {
    return this->scale(static_cast<float>(width) / static_cast<float>(width_),
                       static_cast<float>(height) / static_cast<float>(height_));
  }

  /**
   * @brief generate undistorted camera model(EUCM mode with alpha=0, beta=1)
   * @return Shared pointer to the undistorted camera model.
   */
  std::shared_ptr<CameraModel> undistort() const { return this->extended_undistort(0, 0, 1.f); }

  /**
   * @brief extend size of the camera model
   * @param extend_width Increased width of the camera model.
   * @param extend_height Increased height of the camera model.
   * @param focal_scale Scale the focal length of the camera model.
   */
  virtual std::shared_ptr<CameraModel> extended_undistort(size_t extend_width, size_t extend_height,
                                                          float focal_scale = 1.0) const = 0;
  size_t width() const { return width_; }
  size_t height() const { return height_; }
  size_t size() const { return width_ * height_; }

  /**
   * @brief get the camera intrinsics.
   * @return intrinsics
   */
  virtual image_msgs::msg::Intrinsics intrinsics() const = 0;

 protected:
  size_t width_;
  size_t height_;
};

class EucmCameraModel : public CameraModel {
 public:
  EucmCameraModel(float alpha, float beta, float fx, float fy, float cx, float cy, size_t width,
                  size_t height);

  Eigen::Vector2f project(const Eigen::Vector3f& p) override;
  Eigen::Vector3f unproject(const Eigen::Vector2f& uv) override;
  std::shared_ptr<CameraModel> scale(float rx, float ry) const override;
  std::shared_ptr<CameraModel> extended_undistort(size_t extend_width, size_t extend_height,
                                                  float focal_scale = 1.0) const override;

  image_msgs::msg::Intrinsics intrinsics() const override;

 private:
  float alpha_;
  float beta_;
  float fx_;
  float fy_;
  float cx_;
  float cy_;
};

/**
 * @brief Distorted Pinhole Camera Model. AKA, Brown's Distortion Mode.
 *
 */
class DistortedPinholeCameraModel : public CameraModel {
 public:
  DistortedPinholeCameraModel(float fx, float fy, float cx, float cy, float k1, float k2, float p1,
                              float p2, size_t width, size_t height);

  Eigen::Vector2f project(const Eigen::Vector3f& p) override;
  Eigen::Vector3f unproject(const Eigen::Vector2f& uv) override;
  std::shared_ptr<CameraModel> scale(float rx, float ry) const override;
  std::shared_ptr<CameraModel> extended_undistort(size_t extend_width, size_t extend_height,
                                                  float focal_scale = 1.0) const override;

  image_msgs::msg::Intrinsics intrinsics() const override;

 private:
  float fx_;
  float fy_;
  float cx_;
  float cy_;
  float k1_;
  float k2_;
  float p1_;
  float p2_;

  /**
   * @brief Apply distortion to input point (from the normalized plane) and
   * calculate jacobian
   *
   * @param xy undistorted x,y coordinate of point on the normalized plane
   * @param jacobian optional jacobian
   * @return delta to obtain the distorted point : distorted_xy = xy + delta
   */
  Eigen::Vector2f distortion(const Eigen::Vector2f& xy, Eigen::Matrix2f* jacobian = nullptr) const;
};

}  // namespace camera_model
}  // namespace common
}  // namespace t2
