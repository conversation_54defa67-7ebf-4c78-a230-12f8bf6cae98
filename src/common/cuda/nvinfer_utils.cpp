// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "nvinfer_utils.hpp"

#include <filesystem>

namespace t2 {
namespace common {
namespace cuda {

std::vector<char> read_engine_file(const std::string& path) {
  std::ifstream file(path, std::ios::in | std::ios::binary);
  if (!file) {
    throw std::runtime_error("Failed to open file: " + path);
  }
  file.seekg(0, file.end);
  size_t size = file.tellg();
  file.seekg(0, file.beg);

  std::vector<char> buffer(size);
  file.read(buffer.data(), size);
  file.close();
  return buffer;
}

bool FileExists(const std::string file_name) {
  std::ifstream file(file_name);
  return file.good();
}

std::vector<unsigned char> LoadFile(const std::string& file) {
  std::ifstream in(file, std::ios::in | std::ios::binary);
  if (!in.is_open()) {
    return {};
  }

  in.seekg(0, std::ios::end);
  size_t length = in.tellg();

  std::vector<uint8_t> data;
  if (length > 0) {
    in.seekg(0, std::ios::beg);
    data.resize(length);
    in.read(reinterpret_cast<char*>(&data[0]), length);
  }
  in.close();
  return data;
}

std::string PrintDims(const nvinfer1::Dims& dims) {
  std::ostringstream oss;
  oss << "[";
  for (int i = 0; i < dims.nbDims; i++) {
    oss << std::fixed << dims.d[i];
    if (i != dims.nbDims - 1) {
      oss << " x ";
    }
  }
  oss << "]";
  return oss.str();
}

std::string PrintTensor(const float* tensor, int size) {
  std::ostringstream oss;
  oss << "[ ";
  for (int i = 0; i < size; i++) {
    oss << std::fixed << std::setprecision(4) << tensor[i];
    if (i != size - 1) {
      oss << ", ";
    }
  }
  oss << " ]";
  return oss.str();
}

std::string ChangePath(const std::string& src_path, const std::string& relative_path,
                       const std::string& postfix, const std::string& tag) {
  std::filesystem::path source_path(src_path);
  std::filesystem::path base_path = source_path.parent_path();
  std::filesystem::path filename = source_path.stem();

  std::filesystem::path new_path = base_path / relative_path / filename;

  if (!tag.empty()) {
    new_path += "-" + tag;
  }

  new_path += postfix;

  return new_path.string();
}

std::string GetFileType(const std::string& file_path) {
  std::string::size_type pos = file_path.rfind(".");
  std::string suffix;
  suffix = file_path.substr(pos, file_path.length());
  return suffix;
}

std::string GetFileName(const std::string& file_path) {
  std::string::size_type pos = file_path.rfind("/");
  std::string suffix;
  suffix = file_path.substr(pos + 1, file_path.length());
  return suffix;
}

std::string GetPrecision(nvinfer1::DataType type) {
  switch (type) {
    case nvinfer1::DataType::kFLOAT:
      return "FP32";
    case nvinfer1::DataType::kHALF:
      return "FP16";
    case nvinfer1::DataType::kINT32:
      return "INT32";
    case nvinfer1::DataType::kINT8:
      return "INT8";
    case nvinfer1::DataType::kUINT8:
      return "UINT8";
    default:
      return "unknown";
  }
}

std::string GetTensorRTVersion() {
  return std::to_string(NV_TENSORRT_MAJOR) + "." + std::to_string(NV_TENSORRT_MINOR) + "." +
         std::to_string(NV_TENSORRT_PATCH);
}

}  // namespace cuda
}  // namespace common
}  // namespace t2
