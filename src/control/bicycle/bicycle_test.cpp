// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "bicycle.hpp"

#include <cmath>
#include <fstream>

#include <gtest/gtest.h>

#include <sophus/se2.hpp>

#include "src/common/core/logging.hpp"

// #define BICYCLE_DUMP

namespace t2::control::bicycle {
// for slip-angle compensation assuming steady-circle turning (TaaS #501)
double steering_angle_in_steady_turning(const t2::control::bicycle::BicycleConf& conf,
                                        const double r, const double v) {
  const auto lf = conf.lf();
  const auto lr = conf.lr();
  const auto m = conf.mass();
  const auto cr = conf.cr();
  const auto cf = conf.cf();
  // See formula 3.38 in (安倍正人)「自動車の運転と制御」 for detail
  const auto l = lf + lr;
  // steering angle when v << 0
  const auto delta_semistatic = l / r;
  // coefficient due to steering characteristic (over-steer/under-steer)
  const auto steer_char_coeff = 1 - (m * (cf * lf - cr * lr) * v * v) / (l * l * cf * cr);
  const auto delta = steer_char_coeff * delta_semistatic;
  T2_INFO << "delta_semistatic: " << delta_semistatic;
  T2_INFO << "steer_char_coeff: " << steer_char_coeff;
  T2_INFO << "delta: " << delta;
  return delta;
}

// for slip-angle compensation assuming steady-circle turning (TaaS #501)
double slip_angle_in_steady_turning(const t2::control::bicycle::BicycleConf& conf, const double r,
                                    const double v) {
  const auto lf = conf.lf();
  const auto lr = conf.lr();
  const auto m = conf.mass();
  const auto cr = conf.cr();
  // See formula 3.40 in (安倍正人)「自動車の運転と制御」 for detail
  const auto l = lf + lr;
  // slip angle when v << 0
  const auto beta_semistatic = lr / r;
  // coefficient due to centrifugal force
  const auto cent_coeff = 1 - (m * lf * v * v) / (l * lr * cr);
  const auto beta = cent_coeff * beta_semistatic;
  T2_INFO << "beta_semistatic: " << beta_semistatic;
  T2_INFO << "cent_coeff: " << cent_coeff;
  T2_INFO << "beta: " << beta;
  return beta;
}

t2::control::bicycle::BicycleConf neutral_steer() {
  t2::control::bicycle::BicycleConf conf;

  conf.set_mass(10000);
  conf.set_iz(100000);
  conf.set_lf(3);
  conf.set_lr(3);
  conf.set_cf(200000);
  conf.set_cr(200000);
  conf.set_minimum_speed_protection(0.1);
  return conf;
}

t2::control::bicycle::BicycleConf neutral_steer_lf_neq_lr() {
  t2::control::bicycle::BicycleConf conf;

  conf.set_mass(10000);
  conf.set_iz(100000);
  conf.set_lf(3);
  conf.set_lr(2);
  conf.set_cf(300000);
  conf.set_cr(200000);
  conf.set_minimum_speed_protection(0.1);
  return conf;
}

t2::control::bicycle::BicycleConf over_steer() {
  t2::control::bicycle::BicycleConf conf;

  conf.set_mass(10000);
  conf.set_iz(100000);
  conf.set_lf(3);
  conf.set_lr(3);
  conf.set_cf(300000);
  conf.set_cr(200000);
  conf.set_minimum_speed_protection(0.1);
  return conf;
}

t2::control::bicycle::BicycleConf under_steer() {
  t2::control::bicycle::BicycleConf conf;

  conf.set_mass(10000);
  conf.set_iz(100000);
  conf.set_lf(3);
  conf.set_lr(3);
  conf.set_cf(200000);
  conf.set_cr(300000);
  conf.set_minimum_speed_protection(0.1);
  return conf;
}

struct BicycleSteadyCircularTestParam {
  double r;
  double v;
  t2::control::bicycle::BicycleConf conf;
};

std::ostream& operator<<(std::ostream& stream, const BicycleSteadyCircularTestParam& p) {
  return stream << "r: " << p.r << ", v: " << p.v << ", conf: " << p.conf.ShortDebugString();
}

class BicycleSteadyCircularTest : public ::testing::TestWithParam<BicycleSteadyCircularTestParam> {
};

TEST_P(BicycleSteadyCircularTest, Basic) {
  const auto param = GetParam();
  Bicycle bicycle;

  bicycle.conf = param.conf;

  const double r = param.r;
  const double v = param.v;
  const double dt = 1e-4;
  const double omega = v / r;

  const double duration = 100.;

  const double beta = slip_angle_in_steady_turning(bicycle.conf, r, v);
  const double delta = steering_angle_in_steady_turning(bicycle.conf, r, v);

  bicycle.state.x = r;
  bicycle.state.y = 0;
  bicycle.state.xdot = 0;
  bicycle.state.ydot = v;
  bicycle.state.xddot = 0;
  bicycle.state.yddot = 0;
  bicycle.state.theta = M_PI_2 - beta;
  bicycle.state.thetadot = omega;

  T2_INFO << "xdot:" << bicycle.state.xdot << ", ydot:" << bicycle.state.ydot
          << ", norm:" << Eigen::Vector2d(bicycle.state.xdot, bicycle.state.ydot).norm();

#ifdef BICYCLE_DUMP
  std::ofstream fs("/apollo/data/log/bicycle_test_dump.jsonl");
#endif

  for (int i = 0; i * dt < duration; i++) {
    const double t = i * dt;

    ASSERT_NEAR(Eigen::Vector2d(bicycle.state.xdot, bicycle.state.ydot).norm(), v,
                std::max(1e-4, 0.0002 * t));

    ASSERT_NEAR(
        std::remainder(std::atan2(bicycle.state.ydot, bicycle.state.xdot) - (omega * t + M_PI / 2),
                       M_PI),
        0, std::max(1e-4, 0.0003 * t));
    ASSERT_NEAR(bicycle.state.thetadot, omega, std::max(1e-4, 0.000003 * t));
    ASSERT_NEAR(Eigen::Vector2d(bicycle.state.x, bicycle.state.y).norm(), r,
                std::max(1e-4, 0.001 * t));
    ASSERT_NEAR(bicycle.state.x, r * std::cos(omega * t), std::max(1e-4, 0.002 * t));
    ASSERT_NEAR(bicycle.state.y, r * std::sin(omega * t), std::max(1e-4, 0.002 * t));

    const auto control = Bicycle::Control{.steering_angle = delta, .acceleration = 0.0};
#ifdef BICYCLE_DUMP
    fs << bicycle.dump(control);
#endif
    bicycle.step(dt, control);
  }
}

INSTANTIATE_TEST_SUITE_P(
    BicycleSteadyCircularTestInstance, BicycleSteadyCircularTest,
    testing::Values(
        BicycleSteadyCircularTestParam{.r = 100, .v = 1, .conf = neutral_steer()},
        BicycleSteadyCircularTestParam{.r = 100, .v = 10, .conf = neutral_steer()},
        BicycleSteadyCircularTestParam{.r = 100, .v = 1, .conf = neutral_steer_lf_neq_lr()},
        BicycleSteadyCircularTestParam{.r = 100, .v = 10, .conf = neutral_steer_lf_neq_lr()},
        BicycleSteadyCircularTestParam{.r = 100, .v = 1, .conf = under_steer()},
        BicycleSteadyCircularTestParam{.r = 100, .v = 10, .conf = under_steer()},
        BicycleSteadyCircularTestParam{.r = 100, .v = 1, .conf = over_steer()},
        BicycleSteadyCircularTestParam{.r = 100, .v = 10, .conf = over_steer()}));
}  // namespace t2::control::bicycle
