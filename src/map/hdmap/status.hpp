/* Copyright 2017 The Apollo Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
=========================================================================*/
#pragma once

#include "src/common/core/logging.hpp"
#include "src/common/status/status.hpp"

namespace t2::map::hdmap::adapter {
using t2::common::Status;

#if !defined(RETURN_IF_ERROR)
#define RETURN_IF_ERROR(expr)                  \
  do {                                         \
    const t2::common::Status status_ = (expr); \
    if (!status_.ok()) return status_;         \
  } while (0)
#endif

#if !defined(RETURN_IF_NULL)
#define RETURN_IF_NULL(ptr)             \
  if (ptr == nullptr) {                 \
    T2_ERROR << #ptr << " is nullptr."; \
    return;                             \
  }
#endif

#if !defined(RETURN_VAL_IF_NULL)
#define RETURN_VAL_IF_NULL(ptr, val)    \
  if (ptr == nullptr) {                 \
    T2_ERROR << #ptr << " is nullptr."; \
    return val;                         \
  }
#endif

#if !defined(RETURN_IF)
#define RETURN_IF(condition)              \
  if (condition) {                        \
    T2_ERROR << #condition << " is met."; \
    return;                               \
  }
#endif

#if !defined(RETURN_VAL_IF)
#define RETURN_VAL_IF(condition, val)     \
  if (condition) {                        \
    T2_ERROR << #condition << " is met."; \
    return val;                           \
  }
#endif

}  // namespace t2::map::hdmap::adapter
