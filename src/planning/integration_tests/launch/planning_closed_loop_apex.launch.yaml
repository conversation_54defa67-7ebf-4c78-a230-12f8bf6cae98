# Copyright 2023-2024 Apex.AI, Inc.
# All rights reserved.

process-manager:
  log-file-enable: true
  log-file-buffering: true
  logs-directory: "$(env HOME)/data/log"
  process-groups:
    - name: "planning_closed_loop_apex"
      init-state: "ON"
      processes:
        - name: "planning_closed_loop_apex"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "src/planning/integration_tests/planning_closed_loop_apex"
      states:
        - name: "ON"
          processes:
            - name: "planning_closed_loop_apex"
    - name: "monitor"
      init-state: "ON"
      processes:
        - name: "execution_monitor_service"
          stdout-handlers: ["Console"]
          stderr-handlers: ["Console"]
          default-startup-config:
            path: "external/apex/grace/monitoring/execution_monitor_service/execution_monitor_service_exe"
            args:
              - "--apex-settings-file"
              - "src/planning/integration_tests/param/execution_monitor_service.yaml"
      states:
        - name: "ON"
          processes:
            - name: "execution_monitor_service"
    - name: recording
      init-state: "ON"
      group-log-handlers: ["Console", "LogFile"]
      processes:
        - name: bag_recorder
          stdout-handlers: [LogFile]
          stderr-handlers: [LogFile]
          default-startup-config:
            path: src/system/cli/cli
            args:
              - bag
              - record
              - --disable-keyboard-controls
              - -a
              - -o
              - "$(env HOME)/data/rosbag2"
      states:
        - name: "ON"
          processes:
            - name: "bag_recorder"
