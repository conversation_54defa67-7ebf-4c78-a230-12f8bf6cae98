
  // std::vector<std::string> lanes{
  //   /* Ayase to Nakai */
  //   // Slide 3
  //     "5130010000001972038",
  //     "5130010000000242944",  // end
  //   /* Gotemba to Nishinomiya */
  //     // I (Slide 5a)
  //     "5130010000001800662",
  //     "5130010000001798141",  // end
  //     // II (Slide 5b)
  //     "5130010000001827573",
  //     "5130010000001847539",
  //     // III (Slide 5c-6)
  //     "5130010000001834053",
  //     "5130010000001828012",
  //     // IV (Slide 6)
  //     "5130010000002071141",
  //     "5130010000000219311",
  //     // V (Slide 7)
  //     "5130010000000219311",
  //     "5130010000000222594",
  //     // VI (Slide 8-9a)
  //     "5130010000000222594",
  //     "5130010000002021053",
  //     // VII (Slide 9-10)
  //     "5130010000000837486",
  //     "5130010000000394329",
  // };

  // for (size_t i = 0; i < lanes.size(); ++i) {
  //   auto& lane_id = lanes[i];
  //   auto lane_ptr = test::GetLaneById(lane_id);
  //   if(i % 2 == 0) {
  //     lane_ptr = test::GetPredecessorLane(lane_ptr);
  //   }
  //   auto [x, y] = test::GetXYFromLanePosition(lane_ptr, 0);
  //   T2_INFO << std::setprecision(12) << "x=" << x << ", y=" << y
  //           << ", curr_lane=" << lane_ptr->id().id();
  // }

  // auto lane_ptr = test::GetLaneById("5130010000001847612");
  // while (lane_ptr) {
  //   auto pred_lane = test::GetPredecessorLane(lane_ptr);
  //   if (pred_lane) {
  //     auto [x, y] = test::GetXYFromLanePosition(lane_ptr, 0);
  //     // T2_INFO << std::setprecision(12) << "x=" << x << ", y=" << y
  //     //         << ", curr_lane=" << lane_ptr->id().id()
  //     //         << ", pred_lane=" << pred_lane->id().id();
  //     lane_ptr = pred_lane;
  //   } else {
  //     break;
  //   }
  // }
  // auto [lane_ptr, _, __] = test::GetLanePositionFromXY(localization_info.x,
  // localization_info.y);
  std::string start_lane_id = "";
  if (planning_test_input.start_lane_id.size()) {
    start_lane_id = planning_test_input.start_lane_id;
    T2_ERROR << "Start from start_lane_id=" << start_lane_id;
  }
  auto lane_ptr = test::GetLaneById(start_lane_id);
  {
    auto [x, y] = test::GetXYFromLanePosition(lane_ptr, 0);
    T2_ERROR << std::setprecision(12) << "lane=" << lane_ptr->id().id()
             << ", x=" << x << ", y=" << y;
  }
  const auto hdmap = hdmap::HDMapUtil::BaseMapPtr();
  while (lane_ptr) {
    const size_t n_succ = lane_ptr->lane().successor_id_size();
    bool found = false;
    for (size_t i = 0; i < n_succ; ++i) {
      auto succ_lane = hdmap->GetLaneById(lane_ptr->lane().successor_id(i));
      if (!succ_lane) {
        continue;
      }
      auto branch_direction = succ_lane->lane().merge_info().branch_direction();
      if (branch_direction != hdmap::MergeInfo_BranchDirection_UNKNOWN) {
        auto [x, y] = test::GetXYFromLanePosition(lane_ptr, 0);
        T2_ERROR << std::setprecision(12)
                 << "succ_lane=" << succ_lane->id().id() << ", x=" << x
                 << ", y=" << y << " has "
                 << MergeInfo_BranchDirection_Name(branch_direction)
                 << ", so do not go (" << i << "/" << n_succ << ")";
        continue;
      }
      lane_ptr = succ_lane;
      found = true;
      break;
    }

    auto [x, y] = test::GetXYFromLanePosition(lane_ptr, 0);
    if (found) {
      T2_ERROR << std::setprecision(12) << "lane=" << lane_ptr->id().id()
               << ", x=" << x << ", y=" << y;
    } else {
      T2_ERROR << std::setprecision(12) << "lane=" << lane_ptr->id().id()
               << ", x=" << x << ", y=" << y << " does not have a successor";
      break;
    }
  }
  return 0;


template <typename T>
std::optional<T> GetClosestEvent(const PlannerConfig& planner_config,
                                 const std::map<double, T>& event_map,
                                 const double simulation_time) {
  auto it = event_map.lower_bound(simulation_time);
  std::optional<T> opt_obj;
  if (it != event_map.end()) {
    auto [event_time, obj] = *it;
    if (event_time - simulation_time < planner_config.dt) {
      opt_obj = obj;
      T2_INFO << "Found event_time=" << event_time << ", "
              << planning::ToJsonString(it->second);
    }
  }
  return opt_obj;
}
