#include "state_machine.cpp"
namespace BPlanner {
class BehavioralPlanner {
 private:
 public:
  StateMachine mFsm;
  boost::sml::sm<StateMachine> sm;
  BehavioralPlanner(const std::string initial_state) : mFsm(initial_state), sm(mFsm) {}
  double getRefSpeed(int iteration) {
    if (iteration > 100) sm.process_event(transitions::FAST_to_SLOW{});
    return mFsm.computed_behavior.speed;
  }
};
}  // namespace BPlanner
