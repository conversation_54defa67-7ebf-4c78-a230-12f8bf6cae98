// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "path_planner_nlp.hpp"  // PathPlannerNLP

#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException

// Suppress the warning for Ipopt APIs
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"

template <typename T>
std::string vec_to_string(const std::vector<T>& v, const std::string_view delim = ",") {
  std::stringstream ss;
  ss << std::setprecision(8) << "[";
  for (const auto& vi : v) {
    ss << vi << delim;
  }
  ss << "]";
  return ss.str();
}

namespace t2::planning {

const bool print_log = MiscellaneousConfig::getConfig().print_log;

PathPlannerNLP::PathPlannerNLP(const Ipopt::SmartPtr<Ipopt::IpoptApplication>& app,
                               const PlannerConfig& planner_config)
    : app_(app), planner_config_(planner_config) {}

/**
 * Get information of the non-linear programming.
 *
 * @param[out] n number of variables
 * @param[out] m number of constraints
 * @param[out] nnz_jac_g number of nonzero in the Jacobian
 * @param[out] nnz_h_lag number of nonzero in the Hessian
 * @param[out] index_style style of indexing, whether it's C (0-based) or
 * Fortran (1-based)
 * @return true if the complete info is returned
 *
 * c.f https://coin-or.github.io/Ipopt/classIpopt_1_1TNLP.html
 */
bool PathPlannerNLP::get_nlp_info(Index& n, Index& m, Index& nnz_jac_g, Index& nnz_h_lag,
                                  IndexStyleEnum& index_style) {
  n = num_points_;  ///< number of design variables
  m = 0;            ///< number of constraints
  nnz_jac_g = 0;    ///< number of nnz in Jacobian for constraints

  m += num_points_ - 1;  // to ensure monotonic lateral values
  nnz_jac_g += 2 * (num_points_ - 1);

  m += 2 * (num_points_ - 2) +  ///< constraints for D2l
       2 * (num_points_ - 3) +  ///< constraints for D3l
       2 * (num_points_ - 4);   ///< constraints for D4l

  nnz_jac_g += 3 * 2 * (num_points_ - 2) +  ///< l0, l1, l2
               4 * 2 * (num_points_ - 3) +  ///< l0, l1, l2, l3
               5 * 2 * (num_points_ - 4);   ///< l0, l1, l2, l3, l4

  cached_jac_.resize(nnz_jac_g);
  cached_jac_[0] = std::nan("");  ///< so that has_computed_jac==false in the 1st run

  if (approximate_hessian) {
    nnz_h_lag = 0;
  } else {
    nnz_h_lag = num_points_;
  }

  index_style = Ipopt::TNLP::C_STYLE;  // 0-based: index starts with 0

  if (D3l_max > D2l_max * 2.0 / delta_s) {
    /*
         |(l3-2*l2+l1) - (l2-2*l1+l0)| / s^3
      <= (|(l3-2*l2+l1)| / s^2 + |l2-2*l1+l0)| / s^2) / s
      <= (D2l_max + D2l_max) / s
      = D2l_max * 2 / s
    */
    T2_WARN << "D3l_max=" << D3l_max << ">" << D2l_max * 2.0 / delta_s
            << "= D2l_max * 2.0 / delta_s, so the constraint for D3l is "
               "not active";
  }

  if (D4l_max > D3l_max * 2.0 / delta_s) {
    T2_WARN << "D4l_max=" << D4l_max << ">" << D3l_max * 2.0 / delta_s
            << "= D3l_max * 2.0 / delta_s, so the constraint for D4l is "
               "not active";
  }

  return true;
}

/**
 * Sets the bounds of variables and constraints.
 *
 * c.f
 * https://coin-or.github.io/Ipopt/classIpopt_1_1TNLP.html
 */
bool PathPlannerNLP::get_bounds_info(Index n, Number* x_l, Number* x_u, Index m, Number* g_l,
                                     Number* g_u) {
  // bounds of design variables
  std::copy(vec_l_min_.begin(), vec_l_min_.end(), x_l);
  std::copy(vec_l_max_.begin(), vec_l_max_.end(), x_u);

  x_l[0] = x_u[0] = l0_;
  x_l[1] = x_u[1] = l1_;

  if (target_offset_to_target_lane_ == 0.0) {
    if (fabs(l0_) >= lane_changing_threshold) {
      // LANE_CHANGING
      if (l0_ > 0) {
        // left --> right, need to ensure l>=0
        std::fill(x_l + 2, x_l + n, 0.0);
      } else {
        // right --> left, need to ensure l<=0
        std::fill(x_u + 2, x_u + n, 0.0);
      }
    } else {
      // LANE_FOLLOW
    }
  } else {
    // TO_CHANGE_WITH_WINKER
  }

  // bounds of constraints (all <= 0)
  std::fill(g_u, g_u + m, 0);
  std::fill(g_l, g_l + m, numeric_lowest);

  T2_INFO << "get_bounds_info: l0=" << l0_ << ", l1_=" << l1_ << ", lane_change_direction="
          << static_cast<std::underlying_type_t<decltype(lane_change_direction)>>(
                 lane_change_direction);

  return true;
}

bool PathPlannerNLP::get_starting_point(Index n, bool init_x, Number* l, bool init_z, Number* z_L,
                                        Number* z_U, Index m, bool init_lambda, Number* lambda) {
  std::fill(l + 2, l + n, target_offset_to_target_lane_);

  l[0] = l0_;
  l[1] = l1_;
  const double delta_l = l1_ - l0_;
  for (int i = 2; i < n; ++i) {
    l[i] = l[i - 1] + delta_l;
    if (delta_l > 0.0 && l[i] > target_offset_to_target_lane_) {
      // toward left
      l[i] = target_offset_to_target_lane_;
      break;
    }
    if (delta_l < 0.0 && l[i] < target_offset_to_target_lane_) {
      // toward right
      l[i] = target_offset_to_target_lane_;
      break;
    }
  }

  if (print_log) {
    std::vector<double> vec_l(l, l + n);
    T2_INFO << "get_starting_point: vec_l=" << vec_to_string(vec_l);
  }
  return true;
}

bool PathPlannerNLP::eval_f(Index n, const Number* l, bool new_x, Number& f) {
  // objective function is \sum (l - mid_l)^2
  f = 0.0;
  for (size_t i = 0; i < num_points_; ++i) {
    const double diff_l = l[i] - target_offset_to_target_lane_;
    f += diff_l * diff_l;
  }
  return true;
}

double PathPlannerNLP::get_obj_value() {
  if (soln_.empty()) {
    return numeric_max;
  }
  double obj_value;
  eval_f(0, soln_.data(), false, obj_value);
  return obj_value;
}

bool PathPlannerNLP::eval_grad_f(Index n, const Number* l, bool new_x, Number* grad_f) {
  for (size_t i = 0; i < num_points_; ++i) {
    const double diff_l = l[i] - target_offset_to_target_lane_;
    grad_f[i] = 2.0 * diff_l;
  }
  return true;
}

bool PathPlannerNLP::eval_g(Index n, const Number* l, bool new_x, Index m, Number* g) {
  size_t i = 0;  ///< index of constraint
  size_t j = 0;  ///< index of design variable
  size_t start_i = 0;

  const double inv_ds = 1.0 / delta_s;
  double sign = 0.0;
  switch (lane_change_direction) {
    case lane_change_trigger::LaneChangeDirection::LEFT: {
      sign = -1.0;  // -1.0 * (l[j + 1] - l[j]) = g[i] < 0
      break;
    }
    case lane_change_trigger::LaneChangeDirection::RIGHT: {
      sign = 1.0;  // 1.0 * (l[j + 1] - l[j]) = g[i] < 0
      break;
    }
    case lane_change_trigger::LaneChangeDirection::NONE: {
      // Lane Keep
      if (l0_ <= l1_ && l1_ <= 0) {
        // similar to LEFT
        sign = -1.0;
      } else if (l0_ >= l1_ && l1_ >= 0) {
        // similar to RIGHT
        sign = 1.0;
      }
    }
    default: {
    }
  }

  // monotonic lateral values
  start_i = i;
  g[i++] = 0;  ///< do not check l[0] and l[1]
  for (j = 1; i < start_i + (num_points_ - 1); ++i, ++j) {
    g[i] = sign * (l[j + 1] - l[j]);
  }

  /* Constraints for D2l */
  start_i = i;
  const double inv_ds2 = inv_ds * inv_ds;  // 4

  for (j = 0; i < start_i + 2 * (num_points_ - 2); i += 2, ++j) {
    const double D2l = (l[j + 2] - 2 * l[j + 1] + l[j]) * inv_ds2;

    // upper bound of a: -a_max + a <= 0
    g[i] = (-D2l_max + D2l) * scaling_factors[0];
    // lower bound of a: -a_max - a <= 0
    g[i + 1] = (-D2l_max - D2l) * scaling_factors[0];
  }

  T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 2));

  /* Constraints for D3l */
  start_i = i;
  const double inv_ds3 = inv_ds2 * inv_ds;  // 8
  for (j = 0; i < start_i + 2 * (num_points_ - 3); i += 2, ++j) {
    const double D3l = (l[j + 3] - 3 * l[j + 2] + 3 * l[j + 1] - l[j]) * inv_ds3;

    // upper bound of a: -a_max + a <= 0
    g[i] = (-D3l_max + D3l) * scaling_factors[1];
    // lower bound of a: -a_max - a <= 0
    g[i + 1] = (-D3l_max - D3l) * scaling_factors[1];
  }

  T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 3));

  /* Constraints for D4l */
  start_i = i;
  const double inv_ds4 = inv_ds2 * inv_ds2;  // 16
  for (j = 0; i < start_i + 2 * (num_points_ - 4); i += 2, ++j) {
    const double D4l = (l[j + 4] - 4 * l[j + 3] + 6 * l[j + 2] - 4 * l[j + 1] + l[j]) * inv_ds4;

    // upper bound of a: -a_max + a <= 0
    g[i] = (-D4l_max + D4l) * scaling_factors[2];
    // lower bound of a: -a_max - a <= 0
    g[i + 1] = (-D4l_max - D4l) * scaling_factors[2];
  }

  T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 4));
  return true;
}

bool PathPlannerNLP::eval_jac_g(Index n, const Number* l, bool new_x, Index m, Index nele_jac,
                                Index* iRow, Index* jCol, Number* values) {
  if (!values) {
    // Jacobian structure
    Index idx = 0;

    size_t i = 0;  ///< index of constraint
    size_t j = 0;  ///< index of design variable

    size_t start_i = 0;

    // monotonic lateral values
    start_i = i;
    for (j = 0; i < start_i + (num_points_ - 1); ++i, ++j) {
      iRow[idx] = i;
      jCol[idx++] = j + 1;  // l_{j+1}

      iRow[idx] = i;
      jCol[idx++] = j;  // l_{j}
    }

    /* Constraints for D2l */
    start_i = i;
    for (j = 0; i < start_i + 2 * (num_points_ - 2); i += 2, ++j) {
      // upper bound
      iRow[idx] = i;
      jCol[idx++] = j + 2;  // l_{j+2}

      iRow[idx] = i;
      jCol[idx++] = j + 1;  // l_{j+1}

      iRow[idx] = i;
      jCol[idx++] = j;  // l_{j}

      // lower bound
      iRow[idx] = i + 1;
      jCol[idx++] = j + 2;  // l_{j+2}

      iRow[idx] = i + 1;
      jCol[idx++] = j + 1;  // l_{j+1}

      iRow[idx] = i + 1;
      jCol[idx++] = j;  // l_{j}
    }
    T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 2));

    /* Constraints for D3l */
    start_i = i;
    for (j = 0; i < start_i + 2 * (num_points_ - 3); i += 2, ++j) {
      // upper bound
      iRow[idx] = i;
      jCol[idx++] = j + 3;  // l_{j+3}

      iRow[idx] = i;
      jCol[idx++] = j + 2;  // l_{j+2}

      iRow[idx] = i;
      jCol[idx++] = j + 1;  // l_{j+1}

      iRow[idx] = i;
      jCol[idx++] = j;  // l_{j}

      // lower bound
      iRow[idx] = i + 1;
      jCol[idx++] = j + 3;  // l_{j+3}

      iRow[idx] = i + 1;
      jCol[idx++] = j + 2;  // l_{j+2}

      iRow[idx] = i + 1;
      jCol[idx++] = j + 1;  // l_{j+1}

      iRow[idx] = i + 1;
      jCol[idx++] = j;  // l_{j}
    }
    T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 3));

    /* Constraints for D4l */
    start_i = i;
    for (j = 0; i < start_i + 2 * (num_points_ - 4); i += 2, ++j) {
      // upper bound
      iRow[idx] = i;
      jCol[idx++] = j + 4;  // l_{j+4}

      iRow[idx] = i;
      jCol[idx++] = j + 3;  // l_{j+3}

      iRow[idx] = i;
      jCol[idx++] = j + 2;  // l_{j+2}

      iRow[idx] = i;
      jCol[idx++] = j + 1;  // l_{j+1}

      iRow[idx] = i;
      jCol[idx++] = j;  // l_{j}

      // lower bound
      iRow[idx] = i + 1;
      jCol[idx++] = j + 4;  // l_{j+4}

      iRow[idx] = i + 1;
      jCol[idx++] = j + 3;  // l_{j+3}

      iRow[idx] = i + 1;
      jCol[idx++] = j + 2;  // l_{j+2}

      iRow[idx] = i + 1;
      jCol[idx++] = j + 1;  // l_{j+1}

      iRow[idx] = i + 1;
      jCol[idx++] = j;  // l_{j}
    }
    T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 4));

  } else {
    const bool has_computed_jac = std::isfinite(cached_jac_[0]);
    if (has_computed_jac) {
      std::copy(cached_jac_.begin(), cached_jac_.end(),
                values);  ///< copy it back
    }

    Index idx = 0;
    size_t i = 0;  ///< index of constraint
    size_t j = 0;  ///< index of design variable
    size_t start_i = 0;
    if (lane_change_direction != lane_change_trigger::LaneChangeDirection::NONE) {
      // monotonic lateral values
      start_i = i;
      const double sign =
          lane_change_direction == lane_change_trigger::LaneChangeDirection::LEFT ? -1.0 : 1.0;
      values[idx++] = 0;  ///< do not check l[0] and l[1]
      values[idx++] = 0;
      ++i;
      for (j = 1; i < start_i + (num_points_ - 1); ++i, ++j) {
        values[idx++] = sign;   // l_{j+1}
        values[idx++] = -sign;  // l_{j}
      }
    } else {
      // Lane Keep
      // monotonic lateral values
      start_i = i;
      double sign = 0.0;
      if (l0_ <= l1_ && l1_ <= 0) {
        // similar to LEFT
        sign = -1.0;
      } else if (l0_ >= l1_ && l1_ >= 0) {
        // similar to RIGHT
        sign = 1.0;
      }

      values[idx++] = 0;  ///< do not check l[0] and l[1]
      values[idx++] = 0;
      ++i;
      for (j = 1; i < start_i + (num_points_ - 1); ++i, ++j) {
        values[idx++] = sign;   // l_{j+1}
        values[idx++] = -sign;  // l_{j}
      }
    }

    if (!has_computed_jac) {
      /* Constraints for D2l */
      const double inv_ds = 1.0 / delta_s;
      const double inv_ds2 = inv_ds * inv_ds;
      start_i = i;
      for (j = 0; i < start_i + 2 * (num_points_ - 2); i += 2, ++j) {
        const double dg_dl2 = inv_ds2 * scaling_factors[0];
        const double dg_dl1 = -inv_ds2 * 2.0 * scaling_factors[0];
        const double dg_dl0 = dg_dl2;

        // upper bound
        values[idx++] = dg_dl2;  // l_{j+2}
        values[idx++] = dg_dl1;  // l_{j+1}
        values[idx++] = dg_dl0;  // l_{j}

        // lower bound
        values[idx++] = -dg_dl2;  // l_{j+2}
        values[idx++] = -dg_dl1;  // l_{j+1}
        values[idx++] = -dg_dl0;  // l_{j}
      }
      T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 2));

      /* Constraints for D3l */
      const double inv_ds3 = inv_ds2 * inv_ds;  // 8
      start_i = i;
      for (j = 0; i < start_i + 2 * (num_points_ - 3); i += 2, ++j) {
        const double dg_dl3 = inv_ds3 * scaling_factors[1];
        const double dg_dl2 = -inv_ds3 * 3.0 * scaling_factors[1];
        const double dg_dl1 = -dg_dl2;
        const double dg_dl0 = -dg_dl3;

        // upper bound
        values[idx++] = dg_dl3;  // l_{j+3}
        values[idx++] = dg_dl2;  // l_{j+2}
        values[idx++] = dg_dl1;  // l_{j+1}
        values[idx++] = dg_dl0;  // l_{j}

        // lower bound
        values[idx++] = -dg_dl3;  // l_{j+3}
        values[idx++] = -dg_dl2;  // l_{j+2}
        values[idx++] = -dg_dl1;  // l_{j+1}
        values[idx++] = -dg_dl0;  // l_{j}
      }
      T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 3));

      /* Constraints for D4l */
      const double inv_ds4 = inv_ds2 * inv_ds2;  // 16
      start_i = i;
      for (j = 0; i < start_i + 2 * (num_points_ - 4); i += 2, ++j) {
        const double dg_dl4 = inv_ds4 * scaling_factors[2];
        const double dg_dl3 = -inv_ds4 * 4.0 * scaling_factors[2];
        const double dg_dl2 = inv_ds4 * 6.0 * scaling_factors[2];
        const double dg_dl1 = dg_dl3;
        const double dg_dl0 = dg_dl4;

        // upper bound
        values[idx++] = dg_dl4;  // l_{j+4}
        values[idx++] = dg_dl3;  // l_{j+3}
        values[idx++] = dg_dl2;  // l_{j+2}
        values[idx++] = dg_dl1;  // l_{j+1}
        values[idx++] = dg_dl0;  // l_{j}

        // lower bound
        values[idx++] = -dg_dl4;  // l_{j+4}
        values[idx++] = -dg_dl3;  // l_{j+3}
        values[idx++] = -dg_dl2;  // l_{j+2}
        values[idx++] = -dg_dl1;  // l_{j+1}
        values[idx++] = -dg_dl0;  // l_{j}
      }
      T2_PLAN_CHECK(i - start_i == 2 * (num_points_ - 4));
      std::copy(values, values + cached_jac_.size(),
                cached_jac_.begin());  /// cache Jacobian values in cached_jac_
    }
  }

  return true;
}

bool PathPlannerNLP::eval_h(Index n, const Number* l, bool new_x, Number obj_factor, Index m,
                            const Number* lambda, bool new_lambda, Index nele_hess, Index* iRow,
                            Index* jCol, Number* values) {
  if (approximate_hessian) {
    return true;
  }

  if (!values) {
    // Define the structure of the Hessian
    // Diagonal of Hessian: objective function f
    for (Index i = 0; i < n; ++i) {
      iRow[i] = jCol[i] = i;
    }
  } else {
    // Compute the values of the Hessian
    std::fill(values, values + num_points_, 2.0 * obj_factor);
  }

  return true;
}

void PathPlannerNLP::init_path_bound_and_target_lateral(
    const std::vector<PathBoundPoint>& path_bound, const double target_offset_to_target_lane) {
  num_points_ = path_bound.size();
  T2_PLAN_CHECK(num_points_ > 2) << "Too few path bound points: num_points_=" << num_points_;
  vec_l_min_.resize(num_points_);
  vec_l_max_.resize(num_points_);
  size_t i = 0;
  for (const auto& point : path_bound) {
    vec_l_min_[i] = point.l_min;
    vec_l_max_[i] = point.l_max;
    ++i;
  }

  if (print_log) {
    T2_INFO << "PathPlanner: \nvec_l_min_=" << vec_to_string(vec_l_min_)
            << "\nvec_l_max_=" << vec_to_string(vec_l_max_)
            << ", target_offset_to_target_lane=" << target_offset_to_target_lane;
  }
}

void PathPlannerNLP::init_lateral_values(const double l0, const double l1) {
  l0_ = l0;
  l1_ = l1;
}

std::vector<double> diff(std::vector<double> v) {
  size_t n = v.size();
  if (n < 2) {
    return {};
  }
  for (size_t i = 0; i < n - 1; ++i) {
    v[i] = v[i + 1] - v[i];
  }
  v.pop_back();
  return v;
}

double inf_norm(const std::vector<double>& v) {
  if (v.empty()) {
    return std::nan("");
  }
  double norm = 0;
  for (auto vi : v) {
    norm = std::max(norm, fabs(vi));
  }
  return norm;
}

void PathPlannerNLP::finalize_solution(Ipopt::SolverReturn status, Index n, const Number* l,
                                       const Number* z_L, const Number* z_U, Index m,
                                       const Number* g, const Number* lambda, Number obj_value,
                                       const Ipopt::IpoptData* ip_data,
                                       Ipopt::IpoptCalculatedQuantities* ip_cq) {
  success = false;

  const auto& stats = app_->Statistics();
  Ipopt::Number dual_inf, constr_viol, varbounds_viol, complementarity, kkt_error;

  Index num_iter = stats->IterationCount();
  if (IsValid(stats)) {
    stats->Infeasibilities(dual_inf, constr_viol, varbounds_viol, complementarity, kkt_error);
    T2_INFO << "constr_viol=" << constr_viol << ", " << constr_viol_tol
            << "=constr_viol_tol, num_iter=" << num_iter;
  }

  switch (status) {
    case Ipopt::SolverReturn::SUCCESS:
    case Ipopt::SolverReturn::STOP_AT_ACCEPTABLE_POINT: {
      success = true;
      break;
    }
    default: {
      success = constr_viol <= constr_viol_tol;
      if (success) {
        T2_WARN << "finalize_solution: feasible solution, status=" << static_cast<int>(status);
      } else {
        T2_ERROR << "finalize_solution: no solution, status=" << static_cast<int>(status);
      }
    }
  }  // end-switch

  soln_ = std::vector<double>(l, l + num_points_);

  auto Dl = diff(soln_);
  auto D2l = diff(Dl);
  auto D3l = diff(D2l);
  auto D4l = diff(D3l);

  const double inv_ds = 1.0 / delta_s;
  const double inv_ds2 = inv_ds * inv_ds;
  const double inv_ds3 = inv_ds * inv_ds2;
  const double inv_ds4 = inv_ds2 * inv_ds2;

  T2_INFO << "finalize_solution: success=" << std::boolalpha << success
          << ", lane_change_direction="
          << static_cast<std::underlying_type_t<decltype(lane_change_direction)>>(
                 lane_change_direction);
  if (print_log) {
    T2_INFO << "soln_=" << vec_to_string(soln_);
    T2_INFO << ", l0=" << l0_ << ", l1=" << l1_ << ", delta_s=" << delta_s
            << ", obj_fcn=" << get_obj_value() << ", ||l||=" << inf_norm(soln_)
            << ", ||Dl||=" << inf_norm(Dl) * inv_ds << ", ||D2l||=" << inf_norm(D2l) * inv_ds2
            << ", ||D3l||=" << inf_norm(D3l) * inv_ds3 << ", ||D4l||=" << inf_norm(D4l) * inv_ds4;
  }
}

}  // namespace t2::planning

#pragma GCC diagnostic pop
