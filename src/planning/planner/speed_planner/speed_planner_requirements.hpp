// Copyright (c) 2025 T2 Inc. All rights reserved.

/**
 * @file speed_planner_requirements.h
 *
 * @brief
 */
#pragma once

namespace t2::planning {

double ComputeTargetDistance(double v_ego, double v_front, double d0_target, double a_ego_comf,
                             double a_front_max);

double ComputeStrictMinimumDistance(double v_ego, double v_front, double d0_min, double a_ego_max,
                                    double a_front_max);

double ComputeRelaxedMinimumDistance(double v_ego, double a_ego_max);

double ComputeTTC(double d_front, double v_front, double v_ego, double max_ttc);

double ComputeLimitTTC(const double ttc, const double a_min, const double a_min_ttc,
                       const double min_ttc, const double max_ttc);

}  // namespace t2::planning
