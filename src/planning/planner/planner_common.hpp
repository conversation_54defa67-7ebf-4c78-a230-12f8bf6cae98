// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once
#include <string>

#include <coin/IpIpoptApplication.hpp>  // Ipopt::IpoptApplication

#include "path_bound_point.hpp"
#include "src/planning/common/intention_task_data.hpp"     // IntentionTaskData
#include "src/planning/common/path/frenet_frame_path.hpp"  // FrenetFramePath
#include "src/planning/config/planner_config.hpp"          // PlannerConfig

namespace t2::planning {

std::vector<std::pair<double, double>> ConvertPathBoundToBoundPairs(
    const std::vector<PathBoundPoint>& path_bound);

class PathPlannerNLP;  // forward declaration

/* ========== Used by PathPlanner ========== */
/**
 * Plans a smooth path.
 *
 * @param path_bound[in] path bound points containing s, l_min, l_max
 *
 * @return lateral values for all s in path_bound, so all (s,l) form a path.
 */
std::vector<double> GenerateSmoothPath(
    const Ipopt::SmartPtr<Ipopt::IpoptApplication>& app, const Ipopt::SmartPtr<PathPlannerNLP>& nlp,
    const std::vector<PathBoundPoint>& path_bound, const double offset_to_target_lane,
    const PlannerConfig::PathPlannerConfig::PathPlannerNLPOptions& path_planner_nlp_options);

std::vector<double> CalculateTangentHeading(const std::vector<double>& path_reference_l,
                                            const double delta_s);

std::vector<double> CalculateTangentChangeOfHeading(const std::vector<double>& v_tan_headings);

double GetMaxChangeOfHeading(const std::vector<double>& path_reference_l, const double delta_s);

}  // namespace t2::planning
