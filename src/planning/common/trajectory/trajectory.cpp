// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/common/trajectory/trajectory.hpp"

#include "src/planning/planning_module_exception.hpp"  // PlanningModuleException
namespace t2::planning {
namespace trajectory {

void PopulateTrajectoryProtobuf(ADCTrajectory& trajectory_pb,
                                const PlanningTrajectory& trajectory_points,
                                const rclcpp::Clock::SharedPtr& clock) {
  trajectory_pb.header.creation_timestamp = trajectory_points.header_time;
  trajectory_pb.header.creation_timestamp = clock->now().seconds() * 1e9;

  trajectory_pb.trajectory_point = {trajectory_points.trajectory.begin(),
                                    trajectory_points.trajectory.end()};
  if (!trajectory_points.trajectory.empty()) {
    const auto& last_tp = trajectory_points.trajectory.back();
    trajectory_pb.debug.total_path_length = last_tp.path_point.s;
    trajectory_pb.debug.total_path_time = last_tp.relative_time;
  }
}

size_t QueryLowerBoundPoint(const std::vector<TrajectoryPoint>& trajectory_points,
                            const double relative_time, const double epsilon) {
  T2_PLAN_CHECK(!trajectory_points.empty());

  if (relative_time >= trajectory_points.back().relative_time) {
    return trajectory_points.size() - 1;
  }
  auto func = [&epsilon](const TrajectoryPoint& tp, const double time) {
    return tp.relative_time + epsilon < time;
  };
  auto it_lower =
      std::lower_bound(trajectory_points.begin(), trajectory_points.end(), relative_time, func);
  return std::distance(trajectory_points.begin(), it_lower);
}

size_t QueryNearestPoint(const std::vector<TrajectoryPoint>& trajectory_points,
                         const common::math::Vec2d& position) {
  double dist_sqr_min = std::numeric_limits<double>::max();
  size_t index_min = 0;
  for (size_t i = 0; i < trajectory_points.size(); ++i) {
    const common::math::Vec2d curr_point(trajectory_points[i].path_point.x,
                                         trajectory_points[i].path_point.y);

    const double dist_sqr = curr_point.DistanceSquareTo(position);
    if (dist_sqr < dist_sqr_min) {
      dist_sqr_min = dist_sqr;
      index_min = i;
    }
  }
  return index_min;
}

size_t QueryNearestPointWithBuffer(const std::vector<TrajectoryPoint>& trajectory_points,
                                   const common::math::Vec2d& position, const double buffer) {
  double dist_sqr_min = std::numeric_limits<double>::max();
  size_t index_min = 0;
  for (size_t i = 0; i < trajectory_points.size(); ++i) {
    const common::math::Vec2d curr_point(trajectory_points[i].path_point.x,
                                         trajectory_points[i].path_point.y);

    const double dist_sqr = curr_point.DistanceSquareTo(position);
    if (dist_sqr < dist_sqr_min + buffer) {
      dist_sqr_min = dist_sqr;
      index_min = i;
    }
  }
  return index_min;
}

void PrependTrajectoryPoints(const std::vector<TrajectoryPoint>& points,
                             std::vector<TrajectoryPoint>& trajectory_points) {
  if (!trajectory_points.empty() && points.size() > 1) {
    T2_PLAN_CHECK(points.back().relative_time < trajectory_points.front().relative_time);
  }
  trajectory_points.insert(trajectory_points.begin(), std::move_iterator(points.begin()),
                           std::move_iterator(points.end()));
}

}  // namespace trajectory
}  // namespace t2::planning
