// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <map>

#include <planning_msgs/msg/sl_boundary.hpp>                    // SLBoundary
#include <planning_trajectory_msgs/msg/frenet_frame_point.hpp>  // FrenetFramePoint
#include <planning_trajectory_msgs/msg/path_point.hpp>          // PathPoint
#include <planning_trajectory_msgs/msg/sl_point.hpp>            // SLPoint
#include <planning_trajectory_msgs/msg/trajectory_point.hpp>    // TrajectoryPoint

#include "src/map/proto/map.pb.h"                              // Polygon
#include "src/planning/planning_macros.hpp"                    // REGISTER_*_MSG
#include "src/planning/route_lane_manager/path.hpp"            // MapPathPoint
#include "src/planning/route_lane_manager/route_segments.hpp"  // route_lane_manager::RouteSegments

namespace t2::planning {

REGISTER_INTER_TRAJECTORY_MSG(SLPoint);
REGISTER_INTER_TRAJECTORY_MSG(TrajectoryPoint);
REGISTER_INTER_TRAJECTORY_MSG(FrenetFramePoint);
REGISTER_INTER_TRAJECTORY_MSG(PathPoint);

REGISTER_INTER_PLANNING_MSG(SLBoundary);

class ReferenceLine {
 public:
  ReferenceLine() = default;
  explicit ReferenceLine(const ReferenceLine& reference_line) = default;
  template <typename Iterator>
  ReferenceLine(const Iterator begin, const Iterator end)
      : map_path_points_(begin, end),
        map_path_(std::move(std::vector<route_lane_manager::MapPathPoint>(begin, end))) {}
  explicit ReferenceLine(const std::vector<route_lane_manager::MapPathPoint>& map_path_points);
  explicit ReferenceLine(const route_lane_manager::Path& map_path);

  bool Segment(const common::math::Vec2d& point, const double distance_backward,
               const double distance_forward);

  bool Segment(const double s, const double distance_backward, const double distance_forward);

  route_lane_manager::MapPathPoint GetMapPathPoint(const double s) const;

  FrenetFramePoint GetFrenetPoint(const PathPoint& path_point) const;

  std::pair<std::array<double, 3>, std::array<double, 3>> ToFrenetFrame(
      const TrajectoryPoint& traj_point) const;

  std::vector<route_lane_manager::MapPathPoint> GetMapPathPoints(double start_s,
                                                                 double end_s) const;

  size_t GetNearestReferenceIndex(const double s) const;

  std::vector<route_lane_manager::LaneSegment> GetLaneSegments(const double start_s,
                                                               const double end_s) const;

  route_lane_manager::MapPathPoint GetMapPathPoint(const double x, const double y) const;

  bool GetSLBoundary(const common::math::Box2d& box, SLBoundary* const sl_boundary) const;
  bool GetSLBoundary(const map::hdmap::Polygon& polygon, SLBoundary* const sl_boundary) const;

  // TO-DO: to deprecate these SLToXY, XYToSL in future
  bool SLToXY(const SLPoint& sl_point, common::math::Vec2d* const xy_point) const;
  bool XYToSL(const common::math::Vec2d& xy_point, SLPoint* const sl_point) const;

  template <class XYPoint>
  bool XYToSL(const XYPoint& xy, SLPoint* const sl_point) const {
    if constexpr (std::is_member_object_pointer_v<decltype(&XYPoint::x)> &&
                  std::is_member_object_pointer_v<decltype(&XYPoint::y)>) {
      return XYToSL(common::math::Vec2d(xy.x, xy.y), sl_point);
    } else {
      return XYToSL(common::math::Vec2d(xy.x(), xy.y()), sl_point);
    }
  }

  // Use the following SLToXY, XYToSL from now on
  std::optional<SLPoint> XYToSL(const common::math::Vec2d& xy_point) const;

  std::optional<common::math::Vec2d> SLToXY(const SLPoint& sl_point) const;

  bool GetLaneWidth(const double s, double& lane_left_width, double& lane_right_width) const;

  std::pair<double, double> GetLaneWidth(const double s) const;

  bool GetRoadWidth(const double s, double* const road_left_width,
                    double* const road_right_width) const;

  map::hdmap::LaneInfoConstPtr GetLaneFromS(const double s) const;

  /**
   * @brief: check if a box/point is on lane along reference line
   */
  bool IsOnLane(const SLPoint& sl_point) const;
  bool IsOnLane(const common::math::Vec2d& vec2d_point) const;

  bool IsOnLaneWithNarrowingBuffer(const SLBoundary& sl_boundary, double narrowing_buffer) const;
  bool IsOnLane(const SLBoundary& sl_boundary) const {
    return IsOnLaneWithNarrowingBuffer(sl_boundary, 0.0);
  }

  double Length() const { return map_path_.length(); }

  std::string DebugString() const;

  bool IsEqualTo(const ReferenceLine& other) const;

  std::vector<map::hdmap::LaneInfoConstPtr> GetLaneInfoPtrList() const;

  bool is_on_segment = false;  ///< May be a temporary attribute, we are trying
                               ///< to remove the use of RouteSegment in Frame

  std::vector<route_lane_manager::MapPathPoint> map_path_points_;
  route_lane_manager::Path map_path_;

 private:
  /**
   * @brief Linearly interpolate p0 and p1 by s0 and s1.
   * The input has to satisfy condition: s0 <= s <= s1
   * p0 and p1 must have lane_waypoint.
   * Note: it requires p0 and p1 are on the same lane, adjacent lanes, or
   * parallel neighboring lanes. Otherwise the interpolated result may not
   * valid.
   * @param p0 the first anchor point for interpolation.
   * @param s0 the longitutial distance (s) of p0 on current reference line.
   * s0 <= s && s0 <= s1
   * @param p1 the second anchor point for interpolation
   * @param s1 the longitutial distance (s) of p1 on current reference line.
   * s1
   * @param s identifies the middle point that is going to be
   * interpolated.
   * s >= s0 && s <= s1
   * @return The interpolated MapPathPoint.
   */
  static route_lane_manager::MapPathPoint Interpolate(const route_lane_manager::MapPathPoint& p0,
                                                      const double s0,
                                                      const route_lane_manager::MapPathPoint& p1,
                                                      const double s1, const double s);
  route_lane_manager::MapPathPoint InterpolateWithMatchedIndex(
      const route_lane_manager::MapPathPoint& p0, const double s0,
      const route_lane_manager::MapPathPoint& p1, const double s1,
      const route_lane_manager::InterpolatedIndex& index) const;

  static double FindMinDistancePoint(const route_lane_manager::MapPathPoint& p0, const double s0,
                                     const route_lane_manager::MapPathPoint& p1, const double s1,
                                     const double x, const double y);
};
struct ReferenceLineAndRouteSegments {
  ReferenceLine reference_line;
  route_lane_manager::RouteSegments route_segments;
  bool empty() const { return reference_line.map_path_points_.empty(); }
};

/**
 * Keys of ReferenceLineMap are
 *
 * - CURRENT: reference line (RL) of the current lane;
 *              can change when crossing border by lane-change (LC)
 * - LEFT: RL of the left lane
 * - RIGHT: RL of the right lane
 * - LANE_CHANGE_SOURCE: RL of the LC source/original lane
 * - LANE_CHANGE_TARGET: RL of the LC target/destination lane
 **/

enum class ReferenceLineType : int {
  INVALID = 0,
  CURRENT = 1,
  LEFT = 2,
  RIGHT = 3,
  LANE_CHANGE_SOURCE = 4,
  LANE_CHANGE_TARGET = 5,
};

using ReferenceLineMap = std::map<ReferenceLineType, ReferenceLineAndRouteSegments>;

const ReferenceLine& GetSuitableReferenceLine(const bool is_lane_change,
                                              const ReferenceLineMap& reference_line_map);

ReferenceLine& GetSuitableReferenceLine(const bool is_lane_change,
                                        ReferenceLineMap& reference_line_map);

std::pair<SLPoint, double> GetSLAndHeadingToReferenceLine(
    const TrajectoryPoint& planning_start_point, const ReferenceLine& reference_line);

}  // namespace t2::planning
