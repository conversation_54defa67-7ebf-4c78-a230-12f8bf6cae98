// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "common/hesai_ptc.hpp"

#include <gtest/gtest.h>

#include <apex_integration_test_node/apex_integration_test_node.hpp>
#include <settings/from_yaml.hpp>

#include "common/hesai_config_validation.hpp"
#include "common/lidar_settings_parser.hpp"
#include "hesai_msgs/msg/hesai_config.hpp"
#include "tools/cpp/runfiles/runfiles.h"

const char* ip = "*************";  // The IP address of the LiDAR

namespace t2::drivers::hesai::ptc {

using apex::settings::construct::dictionary;
using apex::settings::construct::get;
using bazel::tools::cpp::runfiles::Runfiles;
using IntegrationTestNodeFixture = apex::tools::apex_integration_test_node::ApexIntegrationTestNode;

std::string generate_path(std::string subdir) {
  // Use Bazel runfiles to get the path
  std::unique_ptr<Runfiles> runfiles(bazel::tools::cpp::runfiles::Runfiles::Create(""));
  return runfiles->Rlocation("t2" + subdir);
}

class HesaiPtcTest : public IntegrationTestNodeFixture {
 protected:
  void SetUp() override {
    client_ = std::make_unique<TcpCommandClient>(node_, ip, kCommandPort, 200);
    // Get the LiDAR configuration from the YAML file
    const std::string subdir = "/modules/drivers/lidar/common/tests/";
    const std::string config_yaml = generate_path(subdir + "hesai_config_test.yaml");
    dictionary config_dict;
    apex::settings::yaml::from_file(config_yaml, config_dict);
    const auto hesai_driver_settings =
        get<dictionary>(config_dict, "hesai_driver_test/hesai_config");
    // Parse the settings
    hesai_config_ = t2::drivers::lidar::helpers::parse_hesai_config(hesai_driver_settings);
  }
  rclcpp::Node node_{"hesai_ptc_test_node"};
  hesai_msgs::HesaiConfig hesai_config_;
  std::unique_ptr<TcpCommandClient> client_;
};

TEST_F(HesaiPtcTest, test_config_info) {
  /**
   * @brief Get the LiDAR configuration information
   */
  apex::optional<ConfigInfo> maybe_config_info = client_->GetConfigInfo();
  ASSERT_TRUE(maybe_config_info.has_value());
  EXPECT_TRUE(lidar::ValidateConfigInfo(hesai_config_.config_info, *maybe_config_info));
}

TEST_F(HesaiPtcTest, test_azimuthfov0) {
  /**
   * @brief Get the azimuth FOV for method 0
   */
  std::optional<AzimuthFOV0> maybe_azimuth_fov0 = client_->GetAzimuthFOV();
  ASSERT_TRUE(maybe_azimuth_fov0.has_value());
  EXPECT_TRUE(lidar::ValidateAzimuthFOV0(hesai_config_.azimuth_fov0, *maybe_azimuth_fov0));
}

TEST_F(HesaiPtcTest, test_horizontalresolutionmode) {
  /**
   * @brief Get the horizontal resolution mode
   */
  apex::optional<HorizontalResolutionMode> maybe_horizontal_resolution_mode =
      client_->GetHorizontalResolutionMode();
  ASSERT_TRUE(maybe_horizontal_resolution_mode.has_value());
  EXPECT_TRUE(lidar::ValidateHorizontalResolutionMode(hesai_config_.horizontal_resolution_mode,
                                                      *maybe_horizontal_resolution_mode));
}

TEST_F(HesaiPtcTest, test_inventoryinfo) {
  /**
   * @brief Get the inventory information
   */
  apex::optional<InventoryInfo> maybe_inventory_info = client_->GetInventoryInfo();
  ASSERT_TRUE(maybe_inventory_info.has_value());
  EXPECT_TRUE(lidar::ValidateInventoryInfo(hesai_config_.inventory_info, *maybe_inventory_info));
}

TEST_F(HesaiPtcTest, test_lidarstatus) {
  /**
   * @brief Get the LiDAR status
   */
  apex::optional<LidarStatus> maybe_lidar_status = client_->GetLidarStatus();
  ASSERT_TRUE(maybe_lidar_status.has_value());
}

}  // namespace t2::drivers::hesai::ptc
