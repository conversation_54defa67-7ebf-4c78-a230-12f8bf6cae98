#include "std_msgs/msg/Header.idl"
#include "hesai_msgs/msg/HesaiScanPacket.idl"

module hesai_msgs {
  enum Model {
    UNKNOWN,
    HESAI_OT128
  };

  module msg {
    @verbatim (language="comment", text=
    "Represent a scan, composed of multiple packets.")
    struct HesaiScan {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
      "Hesai device model.")
      Model model;

      @verbatim (language="comment", text=
      "Sequence of Hesai scan packets.")
      sequence<hesai_msgs::msg::HesaiScanPacket, 100> firing_pkts;
    };
  };
};
