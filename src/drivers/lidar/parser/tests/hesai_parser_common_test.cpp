// Copyright (c) 2024 T2 Inc. All rights reserved.

#include "parser/hesai_parser_common.hpp"

#include <cstdint>
#include <fstream>

#include <fmt/format.h>
#include <gtest/gtest.h>

#include "common/packet_hesai.hpp"
#include "tools/cpp/runfiles/runfiles.h"

namespace t2::drivers::hesai {

using bazel::tools::cpp::runfiles::Runfiles;

std::string generate_path(std::string subdir) {
  // Use Bazel runfiles to get the path
  std::unique_ptr<Runfiles> runfiles(bazel::tools::cpp::runfiles::Runfiles::Create(""));
  return runfiles->Rlocation("t2" + subdir);
}

TEST(HesaiParserCommon, VerifyCRCFieldsOT128) {
  constexpr uint64_t kNumPackets = 3600;
  constexpr uint64_t kNumChunks = 8;
  std::vector<RawPacket> packets;
  const std::string subdir = "/src/drivers/lidar/parser/tests/";
  const std::string packet_sample = generate_path(subdir + "hesai_ot128_3600_packets");
  for (size_t i = 0; i < kNumChunks; i++) {
    const std::string fname = fmt::format("{}_{:d}.bin", packet_sample, i);
    std::ifstream ifs(fname, std::ios::binary);
    ASSERT_TRUE(ifs.is_open());

    // check if the file size is multiple of packet size
    ifs.seekg(0, std::ios::end);
    std::ifstream::pos_type contents_size = ifs.tellg();
    ASSERT_EQ(contents_size, kNumPackets / kNumChunks * sizeof(RawPacket));

    ifs.seekg(0, std::ios::beg);
    while (ifs.peek() != EOF) {
      RawPacket packet;
      ifs.read(reinterpret_cast<char*>(&packet), sizeof(packet));
      packets.push_back(packet);
    }
  }

  ASSERT_EQ(packets.size(), kNumPackets);

  for (const RawPacket& packet : packets) {
    ASSERT_TRUE(VerifyCRCFieldsOT128(packet));
  }
}

TEST(HesaiParserCommon, CalculateSinCos) {
  // 0 degree
  EXPECT_NEAR(CalculateCos(0.0F), 1.0F, 1e-6);
  EXPECT_NEAR(CalculateSin(0.0F), 0.0F, 1e-6);

  // 90 degree
  EXPECT_NEAR(CalculateCos(90.0F), 0.0F, 1e-6);
  EXPECT_NEAR(CalculateSin(90.0F), 1.0F, 1e-6);

  // 180 degree
  EXPECT_NEAR(CalculateCos(180.0F), -1.0F, 1e-6);
  EXPECT_NEAR(CalculateSin(180.0F), 0.0F, 1e-6);

  // 270 degree
  EXPECT_NEAR(CalculateCos(270.0F), 0.0F, 1e-6);
  EXPECT_NEAR(CalculateSin(270.0F), -1.0F, 1e-6);

  // 360 degree
  EXPECT_NEAR(CalculateCos(360.0F), 1.0F, 1e-6);
  EXPECT_NEAR(CalculateSin(360.0F), 0.0F, 1e-6);
}

TEST(HesaiParserCommon, LoadCalibration) {
  std::array<AngularPosition, kChannels> channel_distribution_deg;
  const std::string subdir = "/src/drivers/lidar/parser/tests/";
  const std::string calibration_file = generate_path(subdir + "ot128.csv");
  ASSERT_TRUE(LoadCalibration(calibration_file, channel_distribution_deg));
  EXPECT_FLOAT_EQ(channel_distribution_deg[0].vertical, 14.985F);
  EXPECT_FLOAT_EQ(channel_distribution_deg[0].horizontal, 0.186F);
  EXPECT_FLOAT_EQ(channel_distribution_deg[127].vertical, -24.765F);
  EXPECT_FLOAT_EQ(channel_distribution_deg[127].horizontal, 0.286F);
}

}  // namespace t2::drivers::hesai
