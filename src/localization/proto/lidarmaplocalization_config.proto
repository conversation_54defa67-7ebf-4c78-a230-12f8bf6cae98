syntax = "proto2";

package t2.localization.proto;

message LiDARMapLocalizationConfig {
  required float angle_std_x = 1;
  required float angle_std_y = 2;
  required float angle_std_z = 3;
  required float max_height = 4;
  required float max_side_distance = 5;
  required float grid_size = 6;
  required float intensity_lambda = 7;
  required float altitude_lambda = 8;
  required string calibration_file_path = 10;
  required int32 calibration_beam_num = 11;
  required float calibration_max_intensity = 12;
  required float calibration_distance_step = 13;
  required float calibration_max_distance = 14;
  required int32 calibration_max_map_id_num = 15;
  required bool enable_undistortion = 16;
  required float mahalanobis_distance_threshold = 17;
  required float sins_cov_failure_count_threshold = 18;
  required float failure_score_time_decay_s = 19;
  required float failure_score_threshold = 20;
}
