#!/usr/bin/env bash

###############################################################################
# Copyright 2018 The Apollo Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
###############################################################################

# Fail on first error.
set -e

cd "$(dirname "${BASH_SOURCE[0]}")"
. ./installer_base.sh

apt_get_update_and_install gfortran libblas-dev liblapack-dev

BUILD_DIR="ipopt-build"
mkdir $BUILD_DIR
pushd $BUILD_DIR
  VERSION="3.14.10"
  wget https://raw.githubusercontent.com/coin-or/coinbrew/v2.0/coinbrew
  chmod u+x coinbrew
  ./coinbrew fetch Ipopt@$VERSION --no-prompt
  if [[ "${TARGET_ARCH}" == "x86_64" ]]; then
    ./coinbrew build Ipopt --prefix=/usr --test --no-prompt --verbosity=3 --with-lapack-lflags="-L/usr/local/lib -Wl,--no-as-needed -lmkl_intel_lp64 -lmkl_sequential -lmkl_core -lm"
  else
    ./coinbrew build Ipopt --prefix=/usr --test --no-prompt --verbosity=3
  fi
  ./coinbrew install Ipopt --no-prompt
  mv /usr/include/coin-or /usr/include/coin
popd

rm -rf $BUILD_DIR
apt-get clean && \
    rm -rf /var/lib/apt/lists/*

#FIXME(all): dirty hack here.
sed -i '/#define __IPSMARTPTR_HPP__/a\#define HAVE_CSTDDEF' \
    /usr/include/coin/IpSmartPtr.hpp

# Source Code Package Link: https://github.com/coin-or/Ipopt/releases
