# Fail on first error.
set -e

cd "$(dirname "${BASH_SOURCE[0]}")"
. ./installer_base.sh

BUILD_DIR="blasfeo-build"
PKG_NAME="0.1.3.tar.gz"

mkdir $BUILD_DIR
pushd $BUILD_DIR
    CHECKSUM="c33eb6467a90d6075a8db34e5ab239ecdda38c5261ae096def122fe7a0f98780"
    DOWNLOAD_LINK="https://github.com/giaf/blasfeo/archive/refs/tags/${PKG_NAME}"
    download_if_not_cached "${PKG_NAME}" "${CHECKSUM}" "${DOWNLOAD_LINK}"

    tar xzf "${PKG_NAME}"
    pushd "blasfeo-0.1.3"

        mkdir build && cd build
        cmake .. \
            -DCMAKE_INSTALL_PREFIX="${SYSROOT_DIR}" \
            -DCMAKE_BUILD_TYPE=Release
        make -j"$(nproc)"
        make install
    popd
popd

rm -rf $BUILD_DIR

ok "Successfully installed ${PKG_NAME}"
